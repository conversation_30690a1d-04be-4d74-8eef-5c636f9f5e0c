
import { Injectable, EnvironmentInjector, inject, runInInjectionContext } from '@angular/core';
import { Observable, from, of, map, switchMap, catchError, BehaviorSubject, firstValueFrom } from 'rxjs';
import { User } from '../models/user.model';
import { UserProfile } from '../models/supabase.models';
import { getTitleByLevel } from '../utils/user.utils';
import { SupabaseService, supabase } from './supabase.service'; // Import both service and singleton
import { XpService, EntityType } from './xp.service';

@Injectable({ providedIn: 'root' })
export class UserService {
  private injector = inject(EnvironmentInjector);
  private xpService = inject(XpService);
  private _currentUserProfile = new BehaviorSubject<UserProfile | null>(null);
  currentUserProfile$ = this._currentUserProfile.asObservable();

  // Use the singleton instance directly to avoid circular dependency
  private supabaseClient = supabase;
  private supabaseService = inject(SupabaseService);

  constructor() {
    // Initialize after a short delay to ensure services are ready
    setTimeout(() => {
      // Subscribe to auth changes to load user profile when auth state changes
      this.supabaseService.currentUser$.subscribe(authUser => {
        if (authUser) {
          console.log('UserService: Auth user changed, loading profile for:', authUser.id);
          this.getUserById(authUser.id).subscribe(profile => {
            if (profile) {
              console.log('UserService: Profile loaded:', profile);
              this._currentUserProfile.next(profile as unknown as UserProfile);
            } else {
              console.log('UserService: No profile found for user:', authUser.id);
            }
          });
        } else {
          console.log('UserService: Auth user is null, clearing current profile');
          this._currentUserProfile.next(null);
        }
      });
    }, 100);
  }

  getUser(userId: string): Observable<User | null> {
    return runInInjectionContext(this.injector, () => {
      console.log('UserService: Getting user with ID:', userId);

      // If userId looks like an email, try to find by email field
      if (userId.includes('@')) {
        console.log('UserService: ID looks like an email, trying to query by email field:', userId);
        return this.getUserByEmail(userId);
      }

      return from(
        this.supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single()
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('UserService: Error getting user:', response.error);
            return null;
          }

          if (!response.data) {
            console.log('UserService: No user document found with ID:', userId);
            return null;
          }

          console.log('UserService: Found user document by ID:', userId);
          const userData = response.data as User;
          console.log('UserService: User data:', userData);
          return userData;
        }),
        catchError(error => {
          console.error('UserService: Error getting user:', error);
          return of(null);
        })
      );
    });
  }

  getUserByEmail(email: string): Observable<User | null> {
    return runInInjectionContext(this.injector, () => {
      console.log('UserService: Getting user by email:', email);

      return from(
        this.supabaseClient
          .from('profiles')
          .select('*')
          .eq('email', email)
          .single()
      ).pipe(
        map(response => {
          if (response.error) {
            console.log('UserService: No user document found with email:', email);
            return null;
          }

          console.log('UserService: Found user document by email query:', response.data.id);
          console.log('UserService: User data:', response.data);

          return response.data as User;
        }),
        catchError(error => {
          console.error('UserService: Error getting user by email:', error);
          return of(null);
        })
      );
    });
  }

  /**
   * Get a user by ID
   */
  getUserById(userId: string): Observable<User | null> {
    console.log('UserService: Getting user with ID:', userId);

    // Use a more reliable approach to fetch the user
    return from(
      this.supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('UserService: Error getting user:', response.error);
          return null;
        }

        if (!response.data || response.data.length === 0) {
          console.log('UserService: No user found with ID:', userId);
          return null;
        }

        console.log('UserService: Found user:', response.data[0]);
        return response.data[0] as User;
      }),
      catchError(error => {
        console.error('UserService: Error getting user:', error);
        return of(null);
      })
    );
  }

  /**
   * Creates a user document in Supabase if it doesn't exist
   * @param authUser The Supabase Auth user object
   * @returns Observable of the user document (either existing or newly created)
   */
  ensureUserExists(authUser: any): Observable<User | null> {
    if (!authUser || !authUser.id) {
      console.error('UserService: Cannot ensure user exists - no auth user or ID provided');
      return of(null);
    }

    console.log('UserService: Ensuring user exists for ID:', authUser.id);

    // First try to find user by ID (primary lookup method)
    return this.getUserById(authUser.id).pipe(
      switchMap(userById => {
        if (userById) {
          console.log('UserService: User already exists by ID:', authUser.id);
          return of(userById);
        }

        // If not found by ID and user has email, try by email as fallback
        if (authUser.email) {
          console.log('UserService: User not found by ID, trying email:', authUser.email);
          return this.getUserByEmail(authUser.email).pipe(
            switchMap(userByEmail => {
              if (userByEmail) {
                console.log('UserService: User already exists by email:', authUser.email);
                return of(userByEmail);
              }

              // If user doesn't exist, create a new one
              console.log('UserService: Creating new user document for ID:', authUser.id);

              // Create default user object
              const newUser: User = {
                id: authUser.id,
                email: authUser.email || '',
                username: authUser.email ? authUser.email.split('@')[0] : 'user_' + Date.now(),
                name: authUser.user_metadata?.['name'] || '',
                registration_date: new Date(),
                last_login: new Date(),
                active: true,
                level: 1,
                title: '🥚 Beginner',
                strength_xp: 0,
                money_xp: 0,
                health_xp: 0,
                knowledge_xp: 0,
                bio: '',
                affiliate_code_used: '',
                timezone: 'UTC',
                auto_renew: true,
                sidequests_switch: true,
                show_celebration: true,
                celebration_name: 'Another Day, Another W',
                celebration_description: "You've completed all your quests for today. Keep up the great work!",
                celebration_emoji: '🦅',
                subscription_status: 'email marketing'
              };

              // Create the profile directly - no users table
              return from(
                this.supabaseClient
                  .from('profiles')
                  .insert(newUser)
                  .select()
              ).pipe(
                map(response => {
                  if (response.error) {
                    console.error('UserService: Error creating user:', response.error);
                    throw new Error(response.error.message);
                  }

                  console.log('UserService: Created new user document with ID:', authUser.id);
                  return newUser;
                }),
                catchError(error => {
                  console.error('UserService: Error creating user:', error);
                  return of(null);
                })
              );
            })
          );
        } else {
          // If no email is available, create a new user with just the ID
          console.log('UserService: No email available, creating user with just ID:', authUser.id);

          // Create default user object
          const newUser: User = {
            id: authUser.id,
            email: '',
            username: 'user_' + Date.now(),
            name: authUser.user_metadata?.['name'] || '',
            registration_date: new Date(),
            last_login: new Date(),
            active: true,
            level: 1,
            title: '🥚 Beginner',
            strength_xp: 0,
            money_xp: 0,
            health_xp: 0,
            knowledge_xp: 0,
            bio: '',
            affiliate_code_used: '',
            timezone: 'UTC',
            auto_renew: true,
            sidequests_switch: true,
            show_celebration: true,
            celebration_name: 'Another Day, Another W',
            celebration_description: "You've completed all your quests for today. Keep up the great work!",
            celebration_emoji: '🦅',
            subscription_status: 'email marketing'
          };

          // Create the profile directly - no users table
          return from(
            this.supabaseClient
              .from('profiles')
              .insert(newUser)
              .select()
          ).pipe(
            map(response => {
              if (response.error) {
                console.error('UserService: Error creating user:', response.error);
                throw new Error(response.error.message);
              }

              console.log('UserService: Created new user document with ID:', authUser.id);
              return newUser;
            }),
            catchError(error => {
              console.error('UserService: Error creating user:', error);
              return of(null);
            })
          );
        }
      })
    );
  }

  async createUser(user: Omit<User, 'registration_date' | 'last_login' | 'title'>) {
    const newUser: User = {
      ...user,
      registration_date: new Date(),
      last_login: new Date(),
      title: getTitleByLevel(user.level),
      // Initialize XP fields
      strength_xp: 0,
      money_xp: 0,
      health_xp: 0,
      knowledge_xp: 0,
      // Initialize profile fields
      bio: '',
      timezone: 'UTC',
      // Initialize settings
      sidequests_switch: true,
      show_celebration: true,
      celebration_name: 'Another Day, Another W',
      celebration_description: "You've completed all your quests for today. Keep up the great work!",
      celebration_emoji: ''
    };

    // No need to check for users table - it doesn't exist
    // We'll directly create the profile

    // Now create the profile
    const { error } = await this.supabaseClient
      .from('profiles')
      .insert(newUser);

    if (error) {
      console.error('UserService: Error creating user:', error);
      throw new Error(error.message);
    }
  }

  async updateUserLevel(userId: string, newLevel: number) {
    const newTitle = getTitleByLevel(newLevel);

    const { error } = await this.supabaseClient
      .from('profiles')
      .update({
        level: newLevel,
        title: newTitle,
      })
      .eq('id', userId);

    if (error) {
      console.error('UserService: Error updating user level:', error);
      throw new Error(error.message);
    }
  }

  async updateUserBio(userId: string, bio: string) {
    // If bio contains only whitespace, set it to empty string
    const trimmedBio = bio.trim();
    const bioValue = trimmedBio === '' ? '' : trimmedBio;

    console.log('UserService: Updating bio to:', bioValue === '' ? 'empty string' : bioValue);

    const { error } = await this.supabaseClient
      .from('profiles')
      .update({ bio: bioValue })
      .eq('id', userId);

    if (error) {
      console.error('UserService: Error updating user bio:', error);
      throw new Error(error.message);
    }
  }

  async updateUserProfile(userId: string, data: Partial<User>) {
    // Create a copy of the data to avoid modifying the original
    const updatedData = { ...data };

    // If bio contains only whitespace, set it to empty string
    if (updatedData.bio !== undefined && updatedData.bio !== null) {
      const trimmedBio = updatedData.bio.trim();
      updatedData.bio = trimmedBio === '' ? '' : trimmedBio;
      console.log('UserService: Setting bio in profile update to:', updatedData.bio === '' ? 'empty string' : updatedData.bio);
    }

    const { error } = await this.supabaseClient
      .from('profiles')
      .update(updatedData)
      .eq('id', userId);

    if (error) {
      console.error('UserService: Error updating user profile:', error);
      throw new Error(error.message);
    }
  }

  async updateUser(userId: string, data: Partial<User>): Promise<void> {
    const { data: updatedUser, error } = await this.supabaseClient
      .from('profiles')
      .update(data)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('UserService: Error updating user:', error);
      throw new Error(error.message);
    }

    // Update the current user profile in memory if this is the current user
    const currentProfile = this._currentUserProfile.getValue();
    if (currentProfile && currentProfile.id === userId) {
      console.log('UserService: Updating current user profile in memory');
      this._currentUserProfile.next({
        ...currentProfile,
        ...updatedUser
      } as unknown as UserProfile);
    }
  }

  /**
   * Refresh the current user profile from the database
   */
  async refreshCurrentUserProfile(): Promise<void> {
    const currentProfile = this._currentUserProfile.getValue();
    if (currentProfile) {
      console.log('UserService: Refreshing current user profile');
      const profile = await firstValueFrom(this.getUserById(currentProfile.id));
      if (profile) {
        this._currentUserProfile.next(profile as unknown as UserProfile);
      }
    }
  }

  getTopUsers(count: number = 10): Observable<User[]> {
    return runInInjectionContext(this.injector, () => {
      console.log('UserService: Getting top users, limit:', count);

      return from(
        this.supabaseClient
          .from('profiles')
          .select('*')
          .order('level', { ascending: false })
          .limit(count * 2) // Get more users than needed to sort by XP
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('UserService: Error getting top users:', response.error);
            return [];
          }

          console.log('UserService: Found', response.data.length, 'top users');

          // Sort users by level (descending) and then by total XP (descending) when levels are equal
          const sortedUsers = response.data.sort((a, b) => {
            // First sort by level (descending)
            if (b.level !== a.level) {
              return b.level - a.level;
            }

            // If levels are equal, sort by total XP (descending)
            const aTotalXp = (a.strength_xp || 0) + (a.money_xp || 0) + (a.health_xp || 0) + (a.knowledge_xp || 0);
            const bTotalXp = (b.strength_xp || 0) + (b.money_xp || 0) + (b.health_xp || 0) + (b.knowledge_xp || 0);
            return bTotalXp - aTotalXp;
          });

          // Limit to the requested count after sorting
          const limitedUsers = sortedUsers.slice(0, count);

          console.log('UserService: Sorted and limited to', limitedUsers.length, 'users');
          console.log('UserService: Sample user data:', limitedUsers.length > 0 ? limitedUsers[0] : 'No users found');

          return limitedUsers as User[];
        }),
        catchError(error => {
          console.error('UserService: Error getting top users:', error);
          return of([]);
        })
      );
    });
  }

  getUserProfile(userId: string): Observable<any> {
    console.log('UserService: Getting user profile for ID:', userId);

    return from(
      this.supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('UserService: Error getting user profile:', response.error);
          return null;
        }

        console.log('UserService: Found user profile:', response.data);
        return response.data;
      }),
      catchError(error => {
        console.error('UserService: Error getting user profile:', error);
        return of(null);
      })
    );
  }

  getUserStats(userId: string): Observable<any> {
    console.log('UserService: Getting user stats for ID:', userId);

    // Since user_stats table doesn't exist, we'll just return default stats
    // This can be replaced with actual implementation once the table is created
    return of({
      user_id: userId,
      total_xp: 0,
      completed_quests: 0,
      max_streak: 0
    });

    /* Original implementation - commented out until user_stats table is created
    return from(
      this.supabaseService.getClient()
        .from('user_stats')
        .select('*')
        .eq('user_id', userId)
        .single()
    ).pipe(
      map(response => {
        if (response.error && response.error.code !== 'PGRST116') {
          console.error('UserService: Error getting user stats:', response.error);
          return null;
        }

        if (!response.data) {
          // Return default stats if no stats record exists
          return {
            user_id: userId,
            total_xp: 0,
            completed_quests: 0,
            max_streak: 0
          };
        }

        console.log('UserService: Found user stats:', response.data);
        return response.data;
      }),
      catchError(error => {
        console.error('UserService: Error getting user stats:', error);
        return of({
          user_id: userId,
          total_xp: 0,
          completed_quests: 0,
          max_streak: 0
        });
      })
    );
    */
  }

  async checkAndLevelUp(userId: string): Promise<boolean> {
    return runInInjectionContext(this.injector, async () => {
      console.log('Checking if user can level up:', userId);

      // Get the user
      const { data: user, error } = await this.supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error || !user) {
        console.log('User document not found:', userId);
        return false;
      }

      console.log('Current user level:', user.level);

      // Max level is 100
      if (user.level >= 100) {
        console.log('User already at max level (100)');
        return false;
      }

      // Get required XP for next level from the XP service
      const requiredXP = await firstValueFrom(this.xpService.getRequiredXpForNextLevel(user.level, EntityType.USER));
      console.log('Required XP for next level:', requiredXP);

      // Check if user has enough XP in all categories
      const categories = ['strength', 'money', 'health', 'knowledge'];
      const hasEnoughXP = categories.every(category => {
        const fieldName = `${category}_xp`;
        const xpValue = user[fieldName];
        const hasEnough = typeof xpValue === 'number' && xpValue >= requiredXP;
        console.log(`${category}_xp: ${xpValue}/${requiredXP} (${hasEnough ? 'enough' : 'not enough'})`);
        return hasEnough;
      });

      if (hasEnoughXP) {
        console.log('User has enough XP to level up!');
        // Level up and deduct required XP from each category
        const updates: any = {
          level: user.level + 1
        };

        categories.forEach(category => {
          const fieldName = `${category}_xp`;
          const currentXP = user[fieldName];
          updates[fieldName] = currentXP - requiredXP;
        });

        // Update title based on new level
        updates.title = getTitleByLevel(user.level + 1);
        console.log('New title:', updates.title);

        // Update the user
        const { error: updateError } = await this.supabaseClient
          .from('profiles')
          .update(updates)
          .eq('id', userId);

        if (updateError) {
          console.error('Error updating user for level up:', updateError);
          return false;
        }

        console.log('User updated with new level and XP values');

        // Update badges based on level
        await this.updateBadgesBasedOnLevel(userId, user.level + 1);

        return true;
      }

      console.log('User does not have enough XP to level up');
      return false;
    });
  }

  private async updateBadgesBasedOnLevel(userId: string, newLevel: number): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      console.log('Updating badges based on new level:', newLevel);

      // Get user badges
      const { data: badges, error } = await this.supabaseClient
        .from('user_badges')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Error getting user badges:', error);
        return;
      }

      // If no badges document exists, create one
      if (!badges) {
        console.log('No badges document found for user:', userId);

        const newBadges = {
          user_id: userId,
          created_at: new Date(),
          updated_at: new Date()
        };

        const { error: insertError } = await this.supabaseClient
          .from('user_badges')
          .insert(newBadges);

        if (insertError) {
          console.error('Error creating badges document:', insertError);
          return;
        }
      }

      // Update badges based on level
      const updates: any = {
        updated_at: new Date()
      };

      if (newLevel >= 5) {
        updates.badge_newbie = true;
      }
      if (newLevel >= 10) {
        updates.badge_warrior = true;
      }
      if (newLevel >= 15) {
        updates.badge_monk = true;
      }
      if (newLevel >= 20) {
        updates.badge_nonchalant = true;
      }
      if (newLevel >= 25) {
        updates.badge_hardcore = true;
      }
      if (newLevel >= 30) {
        updates.badge_disciplined_machine = true;
      }
      if (newLevel >= 35) {
        updates.badge_high_performer = true;
      }
      if (newLevel >= 40) {
        updates.badge_master_of_consistency = true;
      }
      if (newLevel >= 50) {
        updates.badge_peak_performer = true;
      }
      if (newLevel >= 60) {
        updates.badge_elite_operator = true;
      }
      if (newLevel >= 75) {
        updates.badge_indestructible = true;
      }
      if (newLevel >= 90) {
        updates.badge_ultra_human = true;
      }
      if (newLevel >= 100) {
        updates.badge_professional = true;
      }

      // Only update if there are changes
      if (Object.keys(updates).length > 1) { // More than just updated_at
        console.log('Updating badges:', updates);

        const { error: updateError } = await this.supabaseClient
          .from('user_badges')
          .update(updates)
          .eq('user_id', userId);

        if (updateError) {
          console.error('Error updating badges:', updateError);
          return;
        }

        console.log('Badges updated successfully');
      } else {
        console.log('No badge updates needed');
      }
    });
  }
}
