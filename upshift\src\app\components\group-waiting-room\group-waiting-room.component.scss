:host {
  display: block;
  min-height: 100vh;
  background-color: var(--bg, #0a0b0f);
  color: var(--text, #ffffff);
  padding-bottom: 70px; /* Space for bottom navigation */
}

.waiting-room-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 70px);
  padding: 20px;
  text-align: center;
  background-color: var(--bg, #0a0b0f);
  color: var(--text, #ffffff);
}

.waiting-room-content {
  max-width: 500px;
  padding: 30px;
  border-radius: 16px;
  background-color: var(--surface, #16171c);
  border: 1px solid var(--border, #26272e);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.waiting-room-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

h2 {
  font-size: 24px;
  margin-bottom: 16px;
  color: var(--accent, #4169e1);
  font-weight: 600;
}

.waiting-message {
  font-size: 18px;
  margin-bottom: 12px;
  color: var(--text, #ffffff);
}

.sub-message {
  font-size: 14px;
  margin-bottom: 30px;
  color: var(--text-secondary, #a0a3b1);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.settings-button, .back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.settings-button {
  background-color: var(--accent, #4169e1);
  color: white;
}

.back-button {
  background-color: var(--surface-alt, #1e1f25);
  border: 1px solid var(--border, #26272e);
  color: var(--text, #ffffff);
}

.settings-button:hover {
  transform: translateY(-2px);
  background-color: var(--accent-hover, #5277e8);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.back-button:hover {
  transform: translateY(-2px);
  background-color: var(--surface, #16171c);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

ion-icon {
  font-size: 20px;
}
