{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { isStorageError, StorageError, StorageUnknownError } from '../lib/errors';\nimport { get, head, post, remove } from '../lib/fetch';\nimport { recursiveToCamel, resolveFetch } from '../lib/helpers';\nconst DEFAULT_SEARCH_OPTIONS = {\n  limit: 100,\n  offset: 0,\n  sortBy: {\n    column: 'name',\n    order: 'asc'\n  }\n};\nconst DEFAULT_FILE_OPTIONS = {\n  cacheControl: '3600',\n  contentType: 'text/plain;charset=UTF-8',\n  upsert: false\n};\nclass StorageFileApi {\n  constructor(url, headers = {}, bucketId, fetch) {\n    this.url = url;\n    this.headers = headers;\n    this.bucketId = bucketId;\n    this.fetch = resolveFetch(fetch);\n  }\n  /**\n   * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n   *\n   * @param method HTTP method.\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  uploadOrUpdate(method, path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let body;\n        const options = Object.assign(Object.assign({}, DEFAULT_FILE_OPTIONS), fileOptions);\n        let headers = Object.assign(Object.assign({}, this.headers), method === 'POST' && {\n          'x-upsert': String(options.upsert)\n        });\n        const metadata = options.metadata;\n        if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n          body = new FormData();\n          body.append('cacheControl', options.cacheControl);\n          if (metadata) {\n            body.append('metadata', this.encodeMetadata(metadata));\n          }\n          body.append('', fileBody);\n        } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n          body = fileBody;\n          body.append('cacheControl', options.cacheControl);\n          if (metadata) {\n            body.append('metadata', this.encodeMetadata(metadata));\n          }\n        } else {\n          body = fileBody;\n          headers['cache-control'] = `max-age=${options.cacheControl}`;\n          headers['content-type'] = options.contentType;\n          if (metadata) {\n            headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata));\n          }\n        }\n        if (fileOptions === null || fileOptions === void 0 ? void 0 : fileOptions.headers) {\n          headers = Object.assign(Object.assign({}, headers), fileOptions.headers);\n        }\n        const cleanPath = this._removeEmptyFolders(path);\n        const _path = this._getFinalPath(cleanPath);\n        const res = yield this.fetch(`${this.url}/object/${_path}`, Object.assign({\n          method,\n          body: body,\n          headers\n        }, (options === null || options === void 0 ? void 0 : options.duplex) ? {\n          duplex: options.duplex\n        } : {}));\n        const data = yield res.json();\n        if (res.ok) {\n          return {\n            data: {\n              path: cleanPath,\n              id: data.Id,\n              fullPath: data.Key\n            },\n            error: null\n          };\n        } else {\n          const error = data;\n          return {\n            data: null,\n            error\n          };\n        }\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Uploads a file to an existing bucket.\n   *\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  upload(path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      return this.uploadOrUpdate('POST', path, fileBody, fileOptions);\n    });\n  }\n  /**\n   * Upload a file with a token generated from `createSignedUploadUrl`.\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param token The token generated from `createSignedUploadUrl`\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  uploadToSignedUrl(path, token, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const cleanPath = this._removeEmptyFolders(path);\n      const _path = this._getFinalPath(cleanPath);\n      const url = new URL(this.url + `/object/upload/sign/${_path}`);\n      url.searchParams.set('token', token);\n      try {\n        let body;\n        const options = Object.assign({\n          upsert: DEFAULT_FILE_OPTIONS.upsert\n        }, fileOptions);\n        const headers = Object.assign(Object.assign({}, this.headers), {\n          'x-upsert': String(options.upsert)\n        });\n        if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n          body = new FormData();\n          body.append('cacheControl', options.cacheControl);\n          body.append('', fileBody);\n        } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n          body = fileBody;\n          body.append('cacheControl', options.cacheControl);\n        } else {\n          body = fileBody;\n          headers['cache-control'] = `max-age=${options.cacheControl}`;\n          headers['content-type'] = options.contentType;\n        }\n        const res = yield this.fetch(url.toString(), {\n          method: 'PUT',\n          body: body,\n          headers\n        });\n        const data = yield res.json();\n        if (res.ok) {\n          return {\n            data: {\n              path: cleanPath,\n              fullPath: data.Key\n            },\n            error: null\n          };\n        } else {\n          const error = data;\n          return {\n            data: null,\n            error\n          };\n        }\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a signed upload URL.\n   * Signed upload URLs can be used to upload files to the bucket without further authentication.\n   * They are valid for 2 hours.\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n   */\n  createSignedUploadUrl(path, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let _path = this._getFinalPath(path);\n        const headers = Object.assign({}, this.headers);\n        if (options === null || options === void 0 ? void 0 : options.upsert) {\n          headers['x-upsert'] = 'true';\n        }\n        const data = yield post(this.fetch, `${this.url}/object/upload/sign/${_path}`, {}, {\n          headers\n        });\n        const url = new URL(this.url + data.url);\n        const token = url.searchParams.get('token');\n        if (!token) {\n          throw new StorageError('No token returned by API');\n        }\n        return {\n          data: {\n            signedUrl: url.toString(),\n            path,\n            token\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Replaces an existing file at the specified path with a new one.\n   *\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  update(path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      return this.uploadOrUpdate('PUT', path, fileBody, fileOptions);\n    });\n  }\n  /**\n   * Moves an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n   * @param options The destination options.\n   */\n  move(fromPath, toPath, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/move`, {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Copies an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n   * @param options The destination options.\n   */\n  copy(fromPath, toPath, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/copy`, {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n        }, {\n          headers: this.headers\n        });\n        return {\n          data: {\n            path: data.Key\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  createSignedUrl(path, expiresIn, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let _path = this._getFinalPath(path);\n        let data = yield post(this.fetch, `${this.url}/object/sign/${_path}`, Object.assign({\n          expiresIn\n        }, (options === null || options === void 0 ? void 0 : options.transform) ? {\n          transform: options.transform\n        } : {}), {\n          headers: this.headers\n        });\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? '' : options.download}` : '';\n        const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`);\n        data = {\n          signedUrl\n        };\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n   * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   */\n  createSignedUrls(paths, expiresIn, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/sign/${this.bucketId}`, {\n          expiresIn,\n          paths\n        }, {\n          headers: this.headers\n        });\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? '' : options.download}` : '';\n        return {\n          data: data.map(datum => Object.assign(Object.assign({}, datum), {\n            signedUrl: datum.signedURL ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`) : null\n          })),\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n   *\n   * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  download(path, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n      const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object';\n      const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n      const queryString = transformationQuery ? `?${transformationQuery}` : '';\n      try {\n        const _path = this._getFinalPath(path);\n        const res = yield get(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n          headers: this.headers,\n          noResolveJson: true\n        });\n        const data = yield res.blob();\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Retrieves the details of an existing file.\n   * @param path\n   */\n  info(path) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const _path = this._getFinalPath(path);\n      try {\n        const data = yield get(this.fetch, `${this.url}/object/info/${_path}`, {\n          headers: this.headers\n        });\n        return {\n          data: recursiveToCamel(data),\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Checks the existence of a file.\n   * @param path\n   */\n  exists(path) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const _path = this._getFinalPath(path);\n      try {\n        yield head(this.fetch, `${this.url}/object/${_path}`, {\n          headers: this.headers\n        });\n        return {\n          data: true,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error) && error instanceof StorageUnknownError) {\n          const originalError = error.originalError;\n          if ([400, 404].includes(originalError === null || originalError === void 0 ? void 0 : originalError.status)) {\n            return {\n              data: false,\n              error\n            };\n          }\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n   * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n   *\n   * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n   * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  getPublicUrl(path, options) {\n    const _path = this._getFinalPath(path);\n    const _queryString = [];\n    const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `download=${options.download === true ? '' : options.download}` : '';\n    if (downloadQueryParam !== '') {\n      _queryString.push(downloadQueryParam);\n    }\n    const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n    const renderPath = wantsTransformation ? 'render/image' : 'object';\n    const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n    if (transformationQuery !== '') {\n      _queryString.push(transformationQuery);\n    }\n    let queryString = _queryString.join('&');\n    if (queryString !== '') {\n      queryString = `?${queryString}`;\n    }\n    return {\n      data: {\n        publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`)\n      }\n    };\n  }\n  /**\n   * Deletes files within the same bucket\n   *\n   * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n   */\n  remove(paths) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield remove(this.fetch, `${this.url}/object/${this.bucketId}`, {\n          prefixes: paths\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Get file metadata\n   * @param id the file id to retrieve metadata\n   */\n  // async getMetadata(\n  //   id: string\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n  //     throw error\n  //   }\n  // }\n  /**\n   * Update file metadata\n   * @param id the file id to update metadata\n   * @param meta the new file metadata\n   */\n  // async updateMetadata(\n  //   id: string,\n  //   meta: Metadata\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await post(\n  //       this.fetch,\n  //       `${this.url}/metadata/${id}`,\n  //       { ...meta },\n  //       { headers: this.headers }\n  //     )\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n  //     throw error\n  //   }\n  // }\n  /**\n   * Lists all the files within a bucket.\n   * @param path The folder path.\n   */\n  list(path, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const body = Object.assign(Object.assign(Object.assign({}, DEFAULT_SEARCH_OPTIONS), options), {\n          prefix: path || ''\n        });\n        const data = yield post(this.fetch, `${this.url}/object/list/${this.bucketId}`, body, {\n          headers: this.headers\n        }, parameters);\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  encodeMetadata(metadata) {\n    return JSON.stringify(metadata);\n  }\n  toBase64(data) {\n    if (typeof Buffer !== 'undefined') {\n      return Buffer.from(data).toString('base64');\n    }\n    return btoa(data);\n  }\n  _getFinalPath(path) {\n    return `${this.bucketId}/${path}`;\n  }\n  _removeEmptyFolders(path) {\n    return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/');\n  }\n  transformOptsToQueryString(transform) {\n    const params = [];\n    if (transform.width) {\n      params.push(`width=${transform.width}`);\n    }\n    if (transform.height) {\n      params.push(`height=${transform.height}`);\n    }\n    if (transform.resize) {\n      params.push(`resize=${transform.resize}`);\n    }\n    if (transform.format) {\n      params.push(`format=${transform.format}`);\n    }\n    if (transform.quality) {\n      params.push(`quality=${transform.quality}`);\n    }\n    return params.join('&');\n  }\n}\n//# sourceMappingURL=StorageFileApi.js.map\nexport { StorageFileApi as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}