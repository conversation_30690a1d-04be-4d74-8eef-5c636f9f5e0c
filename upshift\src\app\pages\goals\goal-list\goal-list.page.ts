import { Component, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { GoalService } from '../../../services/goal.service';
import { Goal, GoalUnit } from '../../../models/goal.model';
import { Subscription, combineLatest, map, of, switchMap, take } from 'rxjs';
import { NavigationComponent } from '../../../components/navigation/navigation.component';
import { SupabaseService } from '../../../services/supabase.service';
import { EmojiInputDirective } from '../../../directives/emoji-input.directive';

interface GoalDisplay extends Goal {
  progressPercent: number;
  totalMicrogoals: number;
  completedMicrogoals: number;
}

@Component({
  selector: 'app-goal-list',
  templateUrl: './goal-list.page.html',
  styleUrls: ['./goal-list.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent, EmojiInputDirective]
})
export class GoalListPage implements OnInit, OnDestroy {
  // User data
  userId: string | null = null;

  // Goals
  goals: GoalDisplay[] = [];

  // Add Goal Modal
  showAddGoalModal = false;
  newGoal = this.getEmptyGoal();

  // Subscriptions
  private userSubscription: Subscription | null = null;

  private supabaseService = inject(SupabaseService);
  private goalService = inject(GoalService);

  constructor() {
    console.log('GoalListPage: Constructor called');
  }

  ngOnInit() {
    console.log('GoalListPage: ngOnInit called');
    // Get the current user
    this.userSubscription = this.supabaseService.currentUser$.pipe(
      take(1)
    ).subscribe(user => {
      if (user) {
        this.userId = user.id;
        this.loadGoals();
      }
    });
  }

  ionViewWillEnter() {
    console.log('GoalListPage: ionViewWillEnter called');
    // Reload goals data when the page is about to be displayed
    if (this.userId) {
      console.log('GoalListPage: Reloading goals data');
      this.loadGoals();
    }
  }

  ngOnDestroy() {
    console.log('GoalListPage: ngOnDestroy called');
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  loadGoals() {
    if (!this.userId) {
      console.error('Cannot load goals: No user ID available');
      return;
    }

    console.log('GoalListPage: Loading goals for user:', this.userId);

    // Clear existing goals to show loading state
    this.goals = [];

    this.goalService.getGoals(this.userId).pipe(
      switchMap(goals => {
        console.log('GoalListPage: Retrieved goals from Supabase:', goals);

        if (goals.length === 0) {
          console.log('GoalListPage: No goals found for user');
          return of([]);
        }

        // Get microgoals for each goal
        const goalObservables = goals.map(goal =>
          this.goalService.getMicroGoals(goal.id!).pipe(
            map(microgoals => {
              console.log(`GoalListPage: Retrieved ${microgoals.length} microgoals for goal ${goal.id}`);

              const totalMicrogoals = microgoals.length;
              const completedMicrogoals = microgoals.filter(m => m.completed).length;
              const progressPercent = goal.goal_value > 0
                ? Math.min(100, Math.round((goal.current_value / goal.goal_value) * 100))
                : 0;

              return {
                ...goal,
                progressPercent,
                totalMicrogoals,
                completedMicrogoals
              } as GoalDisplay;
            })
          )
        );

        return combineLatest(goalObservables);
      })
    ).subscribe({
      next: goalsWithMicrogoals => {
        console.log('GoalListPage: Goals with microgoals loaded:', goalsWithMicrogoals.length);
        this.goals = goalsWithMicrogoals;
      },
      error: error => {
        console.error('GoalListPage: Error loading goals:', error);
        this.goals = [];
      }
    });
  }

  openAddGoalModal(event: Event) {
    event.preventDefault();
    this.showAddGoalModal = true;
    this.newGoal = this.getEmptyGoal();
  }

  closeAddGoalModal() {
    this.showAddGoalModal = false;
  }

  createGoal() {
    if (!this.userId) {
      console.error('Cannot create goal: No user ID available');
      return;
    }

    if (!this.newGoal.name) {
      console.error('Cannot create goal: Goal name is required');
      return;
    }

    const goalToCreate: Goal = {
      name: this.newGoal.name,
      description: this.newGoal.description || '',
      emoji: this.newGoal.emoji || '🎯',
      goal_value: this.newGoal.goal_value || 0,
      goal_unit: this.newGoal.goal_unit || 'count',
      user_id: this.userId,
      start_date: new Date(),
      current_value: 0
    };

    console.log('Creating goal:', goalToCreate);

    this.goalService.createGoal(goalToCreate)
      .then((goalId) => {
        console.log('Goal created successfully with ID:', goalId);
        this.closeAddGoalModal();
        this.loadGoals();
      })
      .catch(error => {
        console.error('Error creating goal:', error);
      });
  }

  private getEmptyGoal(): Partial<Goal> {
    return {
      name: '',
      description: '',
      emoji: '🎯',
      goal_value: 100,
      goal_unit: 'count' as GoalUnit
    };
  }
}
