import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { QuestService } from '../../services/quest.service';
import { SideQuestService } from '../../services/sidequest.service';
import { UserService } from '../../services/user.service';
import { SupabaseService } from '../../services/supabase.service';
import { Quest, QuestCategory, QuestGoalUnit, QuestPeriod, QuestPriority, QuestProgress, QuestType } from '../../models/quest.model';
import { User } from '../../models/user.model';
import { Observable, Subscription, forkJoin, map, of, switchMap, take, firstValueFrom } from 'rxjs';
import { NavigationComponent } from '../../components/navigation/navigation.component';
import { CelebrationComponent } from '../../components/celebration/celebration.component';
import { Activated<PERSON>out<PERSON>, Router } from '@angular/router';
import { PreferencesService } from '../../services/preferences.service';
import { EmojiInputDirective } from '../../directives/emoji-input.directive';
import { StreakCalculatorService } from '../../services/streak-calculator';
import { HeaderComponent } from 'src/app/components/header/header.component';
import { AuroraComponent } from 'src/app/components/aurora/aurora.component';


interface WeekDate {
  date: string; // YYYY-MM-DD format
  day: number;
  is_today: boolean;
  is_selected: boolean;
  is_future: boolean;
  total_quests: number;
  completed_quests: number;
  completion_percentage: number;
}

interface DailyQuest {
  id: string;
  current_quest: {
    id: string;
    name: string;
    description: string;
    goal_value: number;
    goal_unit: string;
  };
  streak: number;
  completed: boolean;
  value_achieved: number;
  emoji: string;
}

interface QuestDisplay extends Quest {
  completed: boolean;
  value_achieved: number;
}

@Component({
  selector: 'app-today',
  templateUrl: './today.page.html',
  styleUrls: ['./today.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, NavigationComponent, CelebrationComponent, EmojiInputDirective, HeaderComponent, AuroraComponent]
})
export class TodayPage implements OnInit, OnDestroy {
  @ViewChild('emojiInput') emojiInput: any;
  // User data
  user$: Observable<User | null> = of(null);
  userId: string | null = null;
  userSubscription: Subscription | undefined;
  showSidequests = true;

  // Date and calendar
  selectedDate: Date = new Date();
  weekDates: WeekDate[] = [];
  dayNames: string[] = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
  headerText: string = 'Today';
  weekOffset: number = 0;
  selectedDateData: any;


  // Quests
  quests: QuestDisplay[] = [];
  dailyQuest: DailyQuest | null = null;
  currentStep = 5;
  totalSteps = 5;

  emojis: string[] = [
    '🚀', '🪐', '⏳', '💊', '⚔️', '🧠', '🦷', '👨‍🍳',
    '🏃', '🥬', '🏆', '🎮', '🎯', '💻', '🚴‍♂️', '🏋️‍♂️',
    '💰', '💸', '🪬', '🧪', '😴', '📈', '📚', '❌',
    '🎓', '💪', '🧘‍♂️', '📵', '🚭', '💧'
  ];


  // Cache for quest data to improve performance
  private questCache: { [dateKey: string]: QuestDisplay[] } = {};

  // Flag to track if we're currently loading data
  private isLoadingData = false;

  // Method to load daily side quest
  private loadDailySideQuest() {
    // Only load for today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(this.selectedDate);
    selectedDate.setHours(0, 0, 0, 0);
    const isTodaySelected = selectedDate.getTime() === today.getTime();

    // Reset dailyQuest if not today's date
    if (!isTodaySelected) {
      this.dailyQuest = null;
      return;
    }

    if (this.showSidequests && isTodaySelected && this.userId) {
      // Use the ensureUserHasDailySideQuests method
      this.sideQuestService.ensureUserHasDailySideQuests(this.userId!).pipe(
        take(1)
      ).subscribe({
        next: (sideQuests) => {
          if (sideQuests && sideQuests.length > 0) {
            const sideQuest = sideQuests[0];

            // Get the quest details from the pool
            this.supabaseService.getClient()
              .from('daily_sidequest_pool')
              .select('*')
              .eq('id', sideQuest.current_quest_id)
              .single()
              .then(response => {
                if (response.error) {
                  return;
                }

                const questDetails = response.data;

                // Create the daily quest object
                this.dailyQuest = {
                  id: sideQuest.id!,
                  current_quest: {
                    id: sideQuest.current_quest_id!,
                    name: questDetails.name || 'Daily Side Quest',
                    description: questDetails.description || 'Complete this daily side quest',
                    goal_value: questDetails.goal_value || 1,
                    goal_unit: questDetails.goal_unit || 'count'
                  },
                  streak: sideQuest.streak || 0,
                  completed: sideQuest.completed || false,
                  value_achieved: sideQuest.value_achieved || 0,
                  emoji: questDetails.emoji || '🎯'
                };
              });
          } else {
            this.dailyQuest = null;
          }
        },
        error: () => {
          this.dailyQuest = null;
        }
      });
    }
  }

  // Add Quest Modal
  showAddQuestModal = false;
  newQuest = this.getEmptyQuest();
  hasHighPriorityQuest = false;

  // Animation states
  questTypeAnimated = false;
  questTypeAnimating = false;
  selectedQuestType = '';
  categoryAnimated = false;
  categoryAnimating = false;
  categorySelected = false;
  selectedCategory = '';
  priorityAnimated = false;
  priorityAnimating = false;
  goalAnimated = false;

  // Celebration Modal
  showCelebration = false;
  currentUser: User | null = null;
  celebrationShownDates: string[] = [];

  // Days selection for new quest
  weekDays = [
    { value: 'Sun', label: 'Su' },
    { value: 'Mon', label: 'Mo' },
    { value: 'Tue', label: 'Tu' },
    { value: 'Wed', label: 'We' },
    { value: 'Thu', label: 'Th' },
    { value: 'Fri', label: 'Fr' },
    { value: 'Sat', label: 'Sa' }
  ];
  monthDays = Array.from({ length: 31 }, (_, i) => i + 1);
  selectedDaysOfWeek: string[] = [];
  selectedDaysOfMonth: number[] = [];

  // Use inject instead of constructor injection
  private questService = inject(QuestService);
  private sideQuestService = inject(SideQuestService);
  private userService = inject(UserService);
  private supabaseService = inject(SupabaseService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private preferencesService = inject(PreferencesService);
  private streakCalculator = inject(StreakCalculatorService);
  private isRedirecting = false; // Flag to prevent multiple redirects

  constructor() {
    // Subscribe to query params to get date and week_offset from URL
    this.route.queryParams.subscribe(params => {
      const dateParam = params['date'];
      const weekOffsetParam = params['week_offset'];

      console.log('TodayPage: Date param from URL query:', dateParam);
      console.log('TodayPage: Week offset param from URL query:', weekOffsetParam);

      // Process week offset parameter
      if (weekOffsetParam !== undefined) {
        try {
          this.weekOffset = parseInt(weekOffsetParam);
          console.log('TodayPage: Week offset set to:', this.weekOffset);
        } catch (error) {
          console.error('TodayPage: Error parsing week offset:', error);
          this.weekOffset = 0;
        }
      } else {
        this.weekOffset = 0;
      }

      // Process date parameter
      if (dateParam) {
        try {
          // Validate date format (YYYY-MM-DD)
          if (/^\d{4}-\d{2}-\d{2}$/.test(dateParam)) {
            this.selectedDate = new Date(dateParam);
            console.log('TodayPage: Selected date from URL query:', this.selectedDate);
          } else {
            console.error('TodayPage: Invalid date format in URL query:', dateParam);
            this.selectedDate = new Date(); // Default to today
          }
        } catch (error) {
          console.error('TodayPage: Error parsing date from URL query:', error);
          this.selectedDate = new Date(); // Default to today
        }
      } else {
        this.selectedDate = new Date(); // Default to today
      }

      // Initialize week dates based on selected date and week offset
      this.generateWeekDates();

      // Update header text and load data
      this.updateHeaderText();

      // Only load data if we have a userId
      if (this.userId) {
        this.loadData();
      }
    });

    // Subscribe to auth state changes
    this.userSubscription = this.supabaseService.currentUser$.subscribe(authUser => {


      if (!authUser) {
        console.log('TodayPage: No authenticated user, but not redirecting');
        // Removed redirect to allow direct access
        return;
      }

      // User is authenticated, get user data
      this.userId = authUser.id;


      // Get user data from Supabase
      this.userService.getUserById(authUser.id).subscribe(userData => {
        if (!userData) {
          console.log('TodayPage: No user data found, but not redirecting');
          // Removed redirect to allow direct access
          return;
        }

        console.log('TodayPage: User data loaded:', userData);
        this.loadData();
      });
    });

    // Set up user$ observable for template binding
    this.user$ = this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (!authUser) {
          return of(null);
        }

        return this.userService.getUserById(authUser.id);
      })
    );

    // Subscribe to user$ to get user preferences
    const userDataSubscription = this.user$.subscribe({
      next: (user) => {
        if (user) {
          this.showSidequests = user.sidequests_switch;
          this.currentUser = user;
        }
      }
    });

    // Add the subscription to be cleaned up
    this.userSubscription = new Subscription();
    this.userSubscription.add(userDataSubscription);
  }

  ngOnInit() {
    // Generate week dates and preload data for all days
    this.generateWeekDates();

    // Preload data for all days in the week
    setTimeout(() => {
      this.preloadWeekData();
    }, 0);

    // Load celebration shown dates from localStorage and clean up old ones
    try {
      // Get today's date
      const today = new Date();
      const todayStr = this.formatDate(today);

      // First, collect all localStorage keys
      const allKeys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          allKeys.push(key);
        }
      }

      // Find and remove all celebration_shown keys except today's
      allKeys.forEach(key => {
        if (key.startsWith('celebration_shown_') && key !== `celebration_shown_${todayStr}`) {
          localStorage.removeItem(key);
        }
      });

      // Check if we have a celebration shown for today
      const todayCelebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);

      // Add to our tracking array if found
      this.celebrationShownDates = [];
      if (todayCelebrationShown) {
        this.celebrationShownDates.push(todayStr);
      }
    } catch (error) {
      this.celebrationShownDates = [];
    }
  }

  async ionViewWillEnter() {
    // Authentication is now handled by the AuthGuard
    // Just get the current user and load data
    const authUser = this.supabaseService._currentUser.value;

    if (!authUser) {
      console.log('TodayPage: No authenticated user, but not redirecting');
      return;
    }

    // Use the UserService to get or create the user document
    this.userService.ensureUserExists(authUser).subscribe(userData => {
      if (!userData) {
        this.router.navigateByUrl('/signup');
        return;
      }

      // Get end date
      let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
      const currentDate = new Date();

      // Compare dates properly
      let isValidPlan = false;
      if (endDate instanceof Date) {
        isValidPlan = endDate > currentDate;
      }

      if (!isValidPlan) {
        // Prevent multiple redirects
        if (this.isRedirecting) return;
        this.isRedirecting = true;

        setTimeout(() => {
          this.router.navigateByUrl('/pricing');
          setTimeout(() => {
            this.isRedirecting = false;
          }, 2000);
        }, 500);
        return;
      }

      // Check if we have cached data for this date
      const dateKey = this.formatDate(this.selectedDate);
      if (this.questCache[dateKey]) {
        // Use cached data
        this.quests = this.questCache[dateKey];

        // Initialize slider backgrounds immediately
        requestAnimationFrame(() => {
          this.initializeSliderBackgrounds();
        });

        // Load daily side quest if needed
        this.loadDailySideQuest();
      } else {
        // Load data with the current selected date
        this.loadData();
      }
    });

    // Make sure the URL reflects the selected date and week offset
    const route = this.router.url;
    const dateParam = this.formatDate(this.selectedDate);

    if (route === '/today') {
      // If we're on the base route, update to include the date and week_offset as query parameters
      this.router.navigate(['/today'], {
        queryParams: {
          date: dateParam,
          week_offset: this.weekOffset !== 0 ? this.weekOffset : null
        },
        replaceUrl: true
      });
    }
  }


  // Initialize all slider backgrounds
  initializeSliderBackgrounds() {
    // Use requestAnimationFrame for better performance
    requestAnimationFrame(() => {
      const sliders = document.querySelectorAll('.progress-slider');
      if (sliders.length === 0) {
        return;
      }

      sliders.forEach(slider => {
        if (slider instanceof HTMLInputElement) {
          // Get the slider's quest ID for debugging
          const sliderQuestId = slider.getAttribute('data-quest-id');
          if (!sliderQuestId) {
            return;
          }

          // Get the exact value from the slider (no rounding)
          const sliderValue = parseInt(slider.value);
          const minValue = parseInt(slider.min);
          const maxValue = parseInt(slider.max);

          // Calculate the percentage value
          const percentage = maxValue > minValue ?
            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;

          // Set the background directly with hardcoded colors
          slider.style.background =
            `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;

          // Add a data attribute to track the current value
          slider.setAttribute('data-current-value', slider.value);
        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {
          // Get the slider's quest ID for debugging
          const sliderQuestId = slider.getAttribute('data-quest-id');
          if (!sliderQuestId) {
            return;
          }

          // Get the value from the element's properties or attributes
          const valueAttr = slider.getAttribute('value') || '0';
          const minAttr = slider.getAttribute('min') || '0';
          const maxAttr = slider.getAttribute('max') || '100';

          const sliderValue = parseInt(valueAttr);
          const minValue = parseInt(minAttr);
          const maxValue = parseInt(maxAttr);

          // Calculate the percentage value
          const percentage = maxValue > minValue ?
            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;

          // Set the CSS variable for the progress
          slider.style.setProperty('--progress-value', `${percentage}%`);

          // Add a data attribute to track the current value
          slider.setAttribute('data-current-value', sliderValue.toString());
        }
      });
    });
  }

  ionViewWillLeave() {
    console.log('TodayPage: ionViewWillLeave called');
  }

  ngOnDestroy() {
    console.log('TodayPage: ngOnDestroy called');
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  async loadData() {
    if (!this.userId) {
      return;
    }

    // Update header text
    this.updateHeaderText();

    // Check if we have cached data for this date
    const dateKey = this.formatDate(this.selectedDate);
    if (this.questCache[dateKey]) {
      // Use cached data
      this.quests = this.questCache[dateKey];

      // Initialize slider backgrounds immediately
      requestAnimationFrame(() => {
        this.initializeSliderBackgrounds();
      });

      // Load daily side quest if needed
      this.loadDailySideQuest();

      return;
    }

    // Set up date variables
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(this.selectedDate);
    selectedDate.setHours(0, 0, 0, 0);
    const isTodaySelected = selectedDate.getTime() === today.getTime();

    console.log('TodayPage: Loading data for date:', this.formatDate(this.selectedDate));
    if (isTodaySelected) {
      // Check if we've already calculated streaks for today
      const todayDateString = this.formatDate(today);
      try {
        const { value: lastStreakCalculation } = await this.preferencesService.get('last_streak_calculation');

        if (lastStreakCalculation !== todayDateString) {
          console.log('TodayPage: First time loading today, calculating streaks');

          // Najprv spracujeme všetky questy pomocou checkMissedDays
          await firstValueFrom(this.questService.getQuests(this.userId!).pipe(
            take(1),
            switchMap(async quests => {
              // Check missed days for each quest
              for (const quest of quests) {
                if (quest.id) {
                  await this.questService.checkMissedDays(quest.id);
                }
              }

              // Potom vytvoríme progress záznamy pre quit questy
              await this.questService.createQuitQuestProgressForToday();

              // NEBUDEME tu nastavovať last_streak_calculation, aby sa mohli vypočítať streaky v ďalšej časti kódu
              return quests;
            })
          ));

          // Streaky sa vypočítajú v ďalšej časti kódu
        } else {
          console.log('TodayPage: Streaks already calculated for today');
        }
      } catch (error) {
        console.error('TodayPage: Error checking last streak calculation:', error);

        // Ak nastane chyba, nenastavujeme last_streak_calculation, aby sa mohli vypočítať streaky
      }

      // Recalculate streak for the daily side quest only for today
      if (this.showSidequests) {
        this.sideQuestService.recalculateSideQuestStreak(this.userId, this.selectedDate)
          .subscribe({
            error: (error) => {
              console.error('Error recalculating side quest streak:', error);
            }
          });
      }
    }
    // Load quests
    this.questService.getQuests(this.userId).pipe(
      take(1),
      switchMap(quests => {
        // Filter active quests for the selected date
        const filteredQuests = this.filterQuestsForDate(quests, this.selectedDate);

        if (filteredQuests.length === 0) {
          return of([]);
        }

        // Sort filtered quests by creation date (newest first) or ID
        const sortedFilteredQuests = [...filteredQuests].sort((a, b) => {
          if (a.created_at && b.created_at) {
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          }
          return a.id && b.id ? a.id.localeCompare(b.id) : 0;
        });

        // Get all progress for all quests at once
        return this.questService.getQuestProgressForDate(this.userId!, this.selectedDate).pipe(
          take(1),
          switchMap(allProgress => {
            // Create a lookup for quick access
            const progressLookup: { [questId: string]: QuestProgress } = {};
            allProgress.forEach(progress => {
              progressLookup[progress.quest_id] = progress;
            });

            // For today's view, calculate streaks once per day
            // For other days, just use the streak from the database
            if (isTodaySelected) {
              // Check if we've already calculated streaks for today
              const todayDateString = this.formatDate(today);
              return this.preferencesService.get('last_streak_calculation').then(({ value: lastStreakCalculation }) => {
                if (lastStreakCalculation !== todayDateString) {
                  console.log('TodayPage: First time loading today, calculating streaks');

                  // Calculate streaks using our streak calculator
                  return this.streakCalculator.calculateStreaks(this.userId!, sortedFilteredQuests).then(streaks => {
                    // Map quests with progress and calculated streaks
                    return sortedFilteredQuests.map(quest => {
                      const progress = progressLookup[quest.id!];
                      const calculatedStreak = streaks[quest.id!] || 0;

                      // Update the streak in the database
                      this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe();

                      return {
                        ...quest,
                        completed: progress?.completed || false,
                        value_achieved: progress?.value_achieved || 0,
                        streak: calculatedStreak
                      } as QuestDisplay;
                    });
                  }).then(result => {
                    // Po výpočte streakov nastavíme last_streak_calculation
                    this.preferencesService.set('last_streak_calculation', todayDateString);
                    return result;
                  });
                } else {
                  console.log('TodayPage: Streaks already calculated for today, using database values');

                  // Just use the streak from the database
                  return sortedFilteredQuests.map(quest => {
                    const progress = progressLookup[quest.id!];

                    return {
                      ...quest,
                      completed: progress?.completed || false,
                      value_achieved: progress?.value_achieved || 0,
                      streak: quest.streak || 0
                    } as QuestDisplay;
                  });
                }
              }).catch(error => {
                console.error('TodayPage: Error checking last streak calculation:', error);

                // If there's an error, just use the streak from the database
                return sortedFilteredQuests.map(quest => {
                  const progress = progressLookup[quest.id!];

                  return {
                    ...quest,
                    completed: progress?.completed || false,
                    value_achieved: progress?.value_achieved || 0,
                    streak: quest.streak || 0
                  } as QuestDisplay;
                });
              });
            } else {
              // For previous days, just use the streak from the database but set it to 0 for display
              return Promise.resolve(sortedFilteredQuests.map(quest => {
                const progress = progressLookup[quest.id!];

                return {
                  ...quest,
                  completed: progress?.completed || false,
                  value_achieved: progress?.value_achieved || 0,
                  streak: 0 // Don't show streak for previous days
                } as QuestDisplay;
              }));
            }
          })
        );


      })
    ).subscribe({
      next: (questsWithProgress) => {
        // Sort quests by creation date (newest first) or ID
        const sortedQuests = [...questsWithProgress].sort((a, b) => {
          if (a.created_at && b.created_at) {
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          }
          return a.id && b.id ? a.id.localeCompare(b.id) : 0;
        });

        // Check if all quests are completed for today
        this.checkAllQuestsCompleted(sortedQuests);

        // Update the quests array
        this.quests = sortedQuests;

        // Cache the quests for this date
        const dateKey = this.formatDate(this.selectedDate);
        this.questCache[dateKey] = sortedQuests;

        // Update the week date progress
        this.updateWeekDateProgress();

        // Initialize slider backgrounds
        requestAnimationFrame(() => {
          this.initializeSliderBackgrounds();
        });

        // Load daily side quest if needed
        this.loadDailySideQuest();
      },
      error: (error) => {
        console.error('Error loading quests:', error);
      }
    });

  }

  generateWeekDates() {
    const today = new Date();

    // Calculate the start of the week based on week offset
    // This starts on Monday (1) instead of Sunday (0)
    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday
    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // Convert to Monday-based (0 = Monday)
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - daysFromMonday + (7 * this.weekOffset));

    this.weekDates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);

      const dateString = this.formatDate(date);
      const isToday = this.isSameDay(date, today);
      const isSelected = this.isSameDay(date, this.selectedDate);
      const isFuture = date > today;

      // Check if we have cached progress for this date
      const dateKey = dateString;
      let totalQuests = 0;
      let completedQuests = 0;
      let completionPercentage = 0;

      if (this.weekProgressCache[dateKey]) {
        const cached = this.weekProgressCache[dateKey];
        totalQuests = cached.total;
        completedQuests = cached.completed;
        completionPercentage = totalQuests > 0
          ? Math.round((completedQuests / totalQuests) * 100)
          : 0;
      }

      this.weekDates.push({
        date: dateString,
        day: date.getDate(),
        is_today: isToday,
        is_selected: isSelected,
        is_future: isFuture,
        total_quests: totalQuests,
        completed_quests: completedQuests,
        completion_percentage: completionPercentage
      });
    }

    // Preload data for all days in the week
    if (this.userId) {
      // Use setTimeout to allow the UI to render first
      setTimeout(() => {
        this.preloadWeekData();
      }, 0);
    }
  }

  // Cache for week date progress
  private weekProgressCache: { [dateKey: string]: { total: number, completed: number } } = {};

  updateWeekDateProgress() {
    if (!this.userId) return;

    // For each date in the week, update the progress
    this.weekDates.forEach((weekDate, index) => {
      if (weekDate.is_future) return;

      const date = new Date(weekDate.date);
      const dateKey = this.formatDate(date);

      // Check if we have cached progress for this date
      if (this.weekProgressCache[dateKey]) {
        const cached = this.weekProgressCache[dateKey];
        this.weekDates[index].total_quests = cached.total;
        this.weekDates[index].completed_quests = cached.completed;
        this.weekDates[index].completion_percentage = cached.total > 0
          ? Math.round((cached.completed / cached.total) * 100)
          : 0;
        return;
      }

      // If we have cached quests for this date, use them to calculate progress
      if (this.questCache[dateKey]) {
        const cachedQuests = this.questCache[dateKey];
        const totalQuests = cachedQuests.length;
        const completedQuests = cachedQuests.filter(q => q.completed).length;

        // Cache the progress
        this.weekProgressCache[dateKey] = {
          total: totalQuests,
          completed: completedQuests
        };

        // Update the week date
        this.weekDates[index].total_quests = totalQuests;
        this.weekDates[index].completed_quests = completedQuests;
        this.weekDates[index].completion_percentage = totalQuests > 0
          ? Math.round((completedQuests / totalQuests) * 100)
          : 0;
        return;
      }
    });

    // Preload data for all days in the week
    this.preloadWeekData();
  }

  // Helper method to filter quests for a specific date
  private filterQuestsForDate(quests: Quest[], date: Date): Quest[] {
    const dateObj = new Date(date);
    const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.
    // Django uses Monday=0, Sunday=6 format, so we need to convert
    const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format
    const dayOfMonth = dateObj.getDate(); // 1-31

    console.log(`TodayPage: Filtering quests for date ${this.formatDate(date)}, day of week: ${dayOfWeek} (Django: ${djangoDayOfWeek}), day of month: ${dayOfMonth}`);

    const filteredQuests = quests.filter(quest => {
      console.log(`TodayPage: Checking quest ${quest.id} (${quest.name}), type: ${quest.quest_type}, period: ${quest.goal_period}, task_days_of_week: ${quest.task_days_of_week}, task_days_of_month: ${quest.task_days_of_month}`);

      if (!quest.active) {
        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is not active, filtering out`);
        return false;
      }

      // Only show quests from the date they were created
      if (quest.created_at) {
        const createdDate = new Date(quest.created_at);
        createdDate.setHours(0, 0, 0, 0);
        dateObj.setHours(0, 0, 0, 0);

        // If the selected date is before the quest was created, don't show it
        if (dateObj < createdDate) {
          return false;
        }
      }

      // Daily quests are always shown
      if (quest.goal_period === 'day') {
        return true;
      }

      // Weekly quests are shown on specific days
      if (quest.goal_period === 'week') {
        if (!quest.task_days_of_week) {
          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has no task_days_of_week specified, showing every day`);
          return true; // If no days specified, show every day
        }

        // Parse task_days_of_week
        let taskDays: any[] = [];
        if (typeof quest.task_days_of_week === 'string') {
          taskDays = quest.task_days_of_week.split(',').map(day => day.trim());
          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as string: ${quest.task_days_of_week}, parsed to:`, taskDays);
        } else if (Array.isArray(quest.task_days_of_week)) {
          taskDays = quest.task_days_of_week;
          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as array:`, taskDays);
        }

        // Check if current day is in task days
        // Convert current day to different formats for comparison
        const dayNameShort = this.getDayNameShort(djangoDayOfWeek);
        const dayNameFull = this.getDayNameFull(djangoDayOfWeek);

        console.log(`TodayPage: Checking if day ${dayNameFull} (${dayNameShort}, ${djangoDayOfWeek}) is in task days:`, taskDays);

        const isIncluded = taskDays.includes(djangoDayOfWeek) ||
          taskDays.includes(djangoDayOfWeek.toString()) ||
          taskDays.includes(dayNameShort) ||
          taskDays.includes(dayNameFull);

        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) should be shown on ${dayNameFull}? ${isIncluded}`);

        return isIncluded;
      }

      // Monthly quests are shown on specific days of month
      if (quest.goal_period === 'month') {
        if (!quest.task_days_of_month) return true; // If no days specified, show every day

        // Parse task_days_of_month
        let taskDays: any[] = [];
        if (typeof quest.task_days_of_month === 'string') {
          taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));
        } else if (Array.isArray(quest.task_days_of_month)) {
          taskDays = quest.task_days_of_month;
        }

        // Check if current day is in task days
        return taskDays.includes(dayOfMonth) ||
          taskDays.includes(dayOfMonth.toString());
      }

      return false;
    });

    console.log(`TodayPage: Filtered ${quests.length} quests to ${filteredQuests.length} for date ${this.formatDate(date)}`);

    return filteredQuests;
  }

  selectDate(dateObj: any) {
    if (this.isLoadingData) {
      return;
    }

    const dateData = dateObj.date

    this.isLoadingData = true;

    this.selectedDateData = dateObj;

    const date = new Date(dateData);
    this.selectedDate = date;

    this.weekDates.forEach(weekDate => {
      weekDate.is_selected = weekDate.date === dateData;
    });

    const formattedDate = this.formatDate(date);

    this.router.navigate(['/today'], {
      queryParams: {
        date: formattedDate,
        week_offset: this.weekOffset !== 0 ? this.weekOffset : null
      },
      replaceUrl: true
    });

    this.updateHeaderText();

    setTimeout(() => {
      this.loadData();
      this.isLoadingData = false;
    }, 10);
  }

  // Flag to track if we're currently changing weeks
  private isChangingWeek = false;

  changeWeek(direction: number) {
    // Prevent multiple rapid week changes
    if (this.isChangingWeek) {
      return;
    }

    this.isChangingWeek = true;

    // Update the week offset
    this.weekOffset += direction;

    // Generate new week dates with the updated offset
    this.generateWeekDates();

    // Preload quest data for all days in the week
    this.preloadWeekData();

    // Update the URL with the new week offset while preserving the selected date
    const dateParam = this.formatDate(this.selectedDate);
    this.router.navigate(['/today'], {
      queryParams: {
        date: dateParam,
        week_offset: this.weekOffset
      },
      replaceUrl: true
    });

    // Reset the flag after a short delay
    setTimeout(() => {
      this.isChangingWeek = false;
    }, 300);
  }

  // Preload data for all days in the current week
  private preloadWeekData() {
    if (!this.userId) return;

    // Get all quests once to avoid multiple API calls
    this.questService.getQuests(this.userId!).pipe(
      take(1)
    ).subscribe(allQuests => {
      // Create an array of observables for each date
      const dateObservables = this.weekDates
        .filter(weekDate => !weekDate.is_future)
        .map(weekDate => {
          const date = new Date(weekDate.date);
          const dateKey = this.formatDate(date);

          // Skip if we already have cached data
          if (this.weekProgressCache[dateKey]) {
            return of({
              date: weekDate.date,
              progress: this.weekProgressCache[dateKey]
            });
          }

          // Filter active quests for this date
          const activeQuests = this.filterQuestsForDate(allQuests, date);

          // If no active quests, return empty progress
          if (activeQuests.length === 0) {
            const emptyProgress = { total: 0, completed: 0 };
            this.weekProgressCache[dateKey] = emptyProgress;
            return of({
              date: weekDate.date,
              progress: emptyProgress
            });
          }

          // Get progress for this date
          return this.questService.getQuestProgressForDate(this.userId!, date).pipe(
            take(1),
            map(progressList => {
              // Count completed quests
              const questIds = activeQuests.map(q => q.id);
              const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));
              const completedQuests = relevantProgress.filter(p => p.completed).length;
              const totalQuests = activeQuests.length;

              // Create progress object
              const progress = {
                total: totalQuests,
                completed: completedQuests
              };

              // Cache the progress
              this.weekProgressCache[dateKey] = progress;

              return {
                date: weekDate.date,
                progress
              };
            })
          );
        });

      // Process all date observables in parallel
      forkJoin(dateObservables).subscribe(results => {
        // Update the week dates with the progress
        results.forEach(result => {
          const index = this.weekDates.findIndex(wd => wd.date === result.date);
          if (index >= 0) {
            this.weekDates[index].total_quests = result.progress.total;
            this.weekDates[index].completed_quests = result.progress.completed;
            this.weekDates[index].completion_percentage = result.progress.total > 0
              ? Math.round((result.progress.completed / result.progress.total) * 100)
              : 0;
          }
        });
      });
    });
  }

  updateHeaderText() {
    const today = new Date();
    if (this.isSameDay(this.selectedDate, today)) {
      this.headerText = 'Today';
    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() - 1)))) {
      this.headerText = 'Yesterday';
    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() + 2)))) {
      this.headerText = 'Tomorrow';
    } else {
      // Format as "Mon, 15 Jan"
      this.headerText = this.selectedDate.toLocaleDateString('en-US', {
        weekday: 'short',
        day: 'numeric',
        month: 'short'
      });
    }
  }

  // Map to track which quests are currently being toggled
  private togglingQuestIds: { [questId: string]: boolean } = {};

  async toggleQuest(quest: QuestDisplay) {
    if (!this.userId || !quest.id) return;

    // Check if this specific quest is already being toggled
    if (this.togglingQuestIds[quest.id]) {
      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is already being toggled, ignoring duplicate call`);
      return;
    }

    // Set flag for this specific quest
    this.togglingQuestIds[quest.id] = true;
    console.log(`TodayPage: Starting toggle for quest ${quest.id} (${quest.name})`);

    try {
      // For normal quests, we don't want to toggle the value when clicking on the quest
      // Instead, we want to keep the current value from the slider
      // This is different from the original behavior where clicking would toggle between 0 and goal_value

      // We'll just log that the quest was clicked but not change any values
      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) clicked, keeping current value: ${quest.value_achieved}`);

      // No need to update the database since we're not changing any values
      // Just release the flag and return
      delete this.togglingQuestIds[quest.id];
      return;
    } catch (error) {
      console.error(`TodayPage: Error in toggleQuest for ${quest.id} (${quest.name}):`, error);
    } finally {
      // Reset flag for this specific quest
      delete this.togglingQuestIds[quest.id];
      console.log(`TodayPage: Finished toggle for quest ${quest.id} (${quest.name})`);
    }
  }

  // Map to track which quests are currently being updated
  private updatingQuestIds: { [questId: string]: boolean } = {};

  async updateQuestProgress(quest: QuestDisplay, event?: any) {
    if (!this.userId || !quest.id) return;

    // Check if this specific quest is already being updated
    if (this.updatingQuestIds[quest.id]) {
      return;
    }

    // Set flag for this specific quest
    this.updatingQuestIds[quest.id] = true;

    try {
      // Store the original completed state before any changes
      const wasCompletedBefore = quest.completed;
      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) original completed state: ${wasCompletedBefore}`);

      // Update the slider background if an event is provided
      if (event) {
        // Handle both standard Event and Ionic's CustomEvent
        const slider = event.target || (event.detail ? event.detail.value : null);
        this.updateSliderBackground(slider);

        // Verify that the slider is for the correct quest
        const sliderQuestId = slider instanceof HTMLElement ? slider.getAttribute('data-quest-id') : null;
        if (sliderQuestId && sliderQuestId !== quest.id) {
          delete this.updatingQuestIds[quest.id];
          return;
        }

        // Get the value from the slider
        let sliderValue = 0;
        if (event.detail && event.detail.value !== undefined) {
          // Ionic range event
          sliderValue = event.detail.value;
        } else if (slider instanceof HTMLInputElement) {
          // Standard input event
          sliderValue = parseInt(slider.value);
        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {
          // Ionic range element
          const valueAttr = slider.getAttribute('value') || '0';
          sliderValue = parseInt(valueAttr);
        }

        // Update the quest's value_achieved with the slider value
        quest.value_achieved = sliderValue;

        // Update completed status based on quest type and value
        // This exactly matches the Django implementation in toggle_quest view
        if (quest.quest_type === 'build') {
          // For build quests, completed when value >= goal
          quest.completed = sliderValue >= quest.goal_value;
        } else { // 'quit' type
          // For quit quests, completed when value < goal (opposite of build)
          quest.completed = sliderValue < quest.goal_value;
        }

        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) new completed state: ${quest.completed}`);
      }

      // Make a deep copy of the quest to avoid reference issues
      const questCopy = { ...quest };

      // Call the service and get the updated values
      const result = await this.questService.toggleQuestCompletion(
        this.userId,
        quest.id,
        this.selectedDate,
        quest.value_achieved,
        questCopy
      );

      // Update the quest in the UI with the returned values
      quest.completed = result.completed;
      quest.value_achieved = result.value_achieved;

      // Get today's date for comparison
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(this.selectedDate);
      selectedDate.setHours(0, 0, 0, 0);
      const isTodaySelected = selectedDate.getTime() === today.getTime();

      // Handle streak calculation differently based on whether we're in today's view or a previous day
      if (isTodaySelected) {
        // For today's view, manually calculate the streak by going backward from today
        // until we find a non-completed progress entry

        // Use the streak from the result (from Supabase)
        let streak = result.streak;

        // Get the current completed state after the update
        const isCompletedNow = quest.completed;

        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status: was ${wasCompletedBefore}, now ${isCompletedNow}`);

        // Only update streak if the completion status has changed
        if (wasCompletedBefore !== isCompletedNow) {
          if (isCompletedNow) {
            // Changed from incomplete to complete
            streak++;
            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from incomplete to complete, streak increased to ${streak}`);
          } else {
            // Changed from complete to incomplete
            streak = Math.max(0, streak - 1);
            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from complete to incomplete, streak decreased to ${streak}`);
          }

          // Update the streak in the database
          this.questService.updateQuestStreak(quest.id!, streak).subscribe({
            next: () => {
              console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${streak}`);

              // Update the quest in the cache
              const dateKey = this.formatDate(this.selectedDate);
              if (this.questCache[dateKey]) {
                const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);
                if (cachedQuestIndex >= 0) {
                  this.questCache[dateKey][cachedQuestIndex].streak = streak;
                }
              }
            },
            error: (error: any) => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)
          });
        } else {
          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status did not change, keeping streak at ${streak}`);
        }
      } else {
        // For previous days, recalculate streak for today
        console.log(`TodayPage: Quest toggled in previous day (${this.formatDate(this.selectedDate)}), recalculating streak for today`);

        // Get the quest details
        this.questService.getQuest(quest.id!).subscribe(questDetails => {
          if (!questDetails) {
            console.error(`TodayPage: Could not get quest details for ${quest.id}`);
            return;
          }

          // Calculate the streak for today using our streak calculator
          this.streakCalculator.calculateStreak(this.userId!, quest.id!)
            .then(calculatedStreak => {
              console.log(`TodayPage: Recalculated streak for quest ${quest.id} for today: ${calculatedStreak}`);

              // Update the streak in the database
              this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe({
                next: () => {
                  console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${calculatedStreak}`);

                  // Clear today's cache for next time
                  const todayString = this.formatDate(today);
                  console.log('TodayPage: Clearing today\'s cache to force reload of updated streak next time today is viewed');
                  delete this.questCache[todayString];

                  // If we have today's date in the week view, update its progress
                  const todayIndex = this.weekDates.findIndex(wd => wd.date === todayString);
                  if (todayIndex >= 0) {
                    delete this.weekProgressCache[todayString];
                    this.updateProgressRingForDate(todayString);
                  }
                },
                error: (error: any) => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)
              });
            })
            .catch(error => {
              console.error(`TodayPage: Error calculating streak for quest ${quest.id}:`, error);
            });
        });
      }

      // Update the UI element for this quest
      this.updateQuestUI(quest);

      // Cache the updated quest data and update progress ring
      const dateKey = this.formatDate(this.selectedDate);
      if (this.questCache[dateKey]) {
        // Find and update the quest in the cache
        const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);
        if (cachedQuestIndex >= 0) {
          this.questCache[dateKey][cachedQuestIndex] = { ...quest };
        }
      }

      // Clear the cache for this date to force a refresh
      delete this.weekProgressCache[dateKey];

      // Update the progress ring for this date
      this.updateProgressRingForDate(dateKey);

      // Check if all quests are completed
      this.checkAllQuestsCompleted(this.quests);
    } catch (error) {
      console.error(`TodayPage: Error updating quest progress:`, error);
    } finally {
      // Reset flag for this specific quest
      delete this.updatingQuestIds[quest.id];
    }
  }

  // Helper method to update the progress ring for a specific date
  private updateProgressRingForDate(dateKey: string) {
    // Find the index of this date in weekDates
    const index = this.weekDates.findIndex(wd => wd.date === dateKey);
    if (index < 0) return;

    // If we have cached quests for this date, use them to calculate progress
    if (this.questCache[dateKey]) {
      const cachedQuests = this.questCache[dateKey];
      const totalQuests = cachedQuests.length;
      const completedQuests = cachedQuests.filter(q => q.completed).length;

      // Cache the progress
      this.weekProgressCache[dateKey] = {
        total: totalQuests,
        completed: completedQuests
      };

      // Update the week date
      this.weekDates[index].total_quests = totalQuests;
      this.weekDates[index].completed_quests = completedQuests;
      this.weekDates[index].completion_percentage = totalQuests > 0
        ? Math.round((completedQuests / totalQuests) * 100)
        : 0;

      return;
    }

    // If no cached quests, fetch from server
    if (this.userId) {
      const date = new Date(dateKey);

      this.questService.getQuestProgressForDate(this.userId, date).pipe(
        take(1)
      ).subscribe(progressList => {
        this.questService.getQuests(this.userId!).pipe(
          take(1)
        ).subscribe(quests => {
          // Filter active quests for this date
          const activeQuests = this.filterQuestsForDate(quests, date);

          // Count completed quests
          const questIds = activeQuests.map(q => q.id);
          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));
          const completedQuests = relevantProgress.filter(p => p.completed).length;
          const totalQuests = activeQuests.length;

          // Cache the progress
          this.weekProgressCache[dateKey] = {
            total: totalQuests,
            completed: completedQuests
          };

          // Update the week date
          this.weekDates[index].total_quests = totalQuests;
          this.weekDates[index].completed_quests = completedQuests;
          this.weekDates[index].completion_percentage = totalQuests > 0
            ? Math.round((completedQuests / totalQuests) * 100)
            : 0;
        });
      });
    }
  }

  // Helper method to update the UI for a specific quest
  private updateQuestUI(quest: QuestDisplay) {
    // Find the quest element in the DOM
    const questElement = document.querySelector(`[data-quest-id="${quest.id}"]`);
    if (!questElement) {
      console.error(`TodayPage: Could not find quest element for ID: ${quest.id}`);
      return;
    }

    // Update the completed class
    if (quest.completed) {
      questElement.classList.add('completed');
    } else {
      questElement.classList.remove('completed');
    }

    // Update the streak display - only show streak for today
    const streakElements = questElement.querySelectorAll('.quest-streak');
    if (streakElements && streakElements.length > 0) {
      // Get today's date for comparison
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(this.selectedDate);
      selectedDate.setHours(0, 0, 0, 0);
      const isTodaySelected = selectedDate.getTime() === today.getTime();

      // Only show streak for today's view
      if (isTodaySelected) {
        const streakValue = quest.streak || 0;
        console.log(`TodayPage: Quest ${quest.id}, completed: ${quest.completed}, streak: ${streakValue}`);

        // Update all streak elements (there might be multiple due to ngIf)
        streakElements.forEach(element => {
          if (element.parentElement && element.parentElement.contains(element)) {
            // Make sure the streak is visible
            (element as HTMLElement).style.display = 'block';
            element.textContent = `🔥${streakValue}d`;
          }
        });
      } else {
        // Hide streak for previous days
        streakElements.forEach(element => {
          if (element.parentElement && element.parentElement.contains(element)) {
            (element as HTMLElement).style.display = 'none';
            element.textContent = '';
          }
        });
      }
    }

    // Update the progress text
    const progressText = questElement.querySelector('.progress-text');
    if (progressText) {
      const isTimeUnit = progressText.parentElement?.classList.contains('progress-time');
      const unitSuffix = isTimeUnit ? 'm' : '';
      const goalUnitSuffix = quest.goal_unit !== 'count' && !isTimeUnit ? ` ${quest.goal_unit}` : '';

      progressText.textContent = `${quest.value_achieved}${unitSuffix}/${quest.goal_value}${unitSuffix}${goalUnitSuffix}`;
    }

    console.log(`TodayPage: Updated UI for quest ${quest.id}`);
  }

  // Update slider background based on value
  updateSliderBackground(slider: HTMLInputElement | EventTarget | null) {
    if (!slider) {
      return;
    }

    // Handle different types of slider elements
    let sliderElement: HTMLElement;
    let sliderValue = 0;
    let minValue = 0;
    let maxValue = 100;
    let sliderQuestId = '';

    if (slider instanceof HTMLInputElement) {
      // Standard HTML input range
      sliderElement = slider;
      sliderQuestId = slider.getAttribute('data-quest-id') || '';
      sliderValue = parseInt(slider.value);
      minValue = parseInt(slider.min);
      maxValue = parseInt(slider.max);
    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {
      // Ionic range element
      sliderElement = slider;
      sliderQuestId = slider.getAttribute('data-quest-id') || '';

      // Get the value from the element's properties or attributes
      const valueAttr = slider.getAttribute('value') || '0';
      const minAttr = slider.getAttribute('min') || '0';
      const maxAttr = slider.getAttribute('max') || '100';

      sliderValue = parseInt(valueAttr);
      minValue = parseInt(minAttr);
      maxValue = parseInt(maxAttr);
    } else {
      return;
    }

    if (!sliderQuestId) {
      return;
    }

    // Calculate the percentage value
    const percentage = maxValue > minValue ?
      ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;

    // For Ionic range, we need to set the CSS variable
    if (sliderElement.tagName === 'ION-RANGE') {
      sliderElement.style.setProperty('--progress-value', `${percentage}%`);
    } else {
      // Set the background directly with hardcoded colors for standard HTML input
      sliderElement.style.background =
        `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;
    }

    // Add a data attribute to track the current value
    sliderElement.setAttribute('data-current-value', sliderValue.toString());
  }

  // Map to track which side quests are currently being toggled
  private togglingSideQuestIds: { [questId: string]: boolean } = {};

  /**
   * Toggle side quest completion
   * This matches the Django implementation in toggle_daily_side_quest view
   * Side quests are always toggled between 0 and goal value
   */
  async toggleSideQuest(sideQuest: DailyQuest) {
    if (!this.userId || !sideQuest.id) return;

    // Check if this specific side quest is already being toggled
    if (this.togglingSideQuestIds[sideQuest.id]) {
      console.log(`TodayPage: Side quest ${sideQuest.id} is already being toggled, ignoring duplicate call`);
      return;
    }

    // Set flag for this specific side quest
    this.togglingSideQuestIds[sideQuest.id] = true;
    console.log(`TodayPage: Starting toggle for side quest ${sideQuest.id}`);

    try {
      // For side quests, we always toggle between 0 and goal value
      // This matches the Django implementation where side quests are either completed or not
      console.log(`TodayPage: Click event on side quest ${sideQuest.id}`);

      // Toggle the value immediately for better UI feedback
      const newValue = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;
      const newCompletedState = newValue === sideQuest.current_quest.goal_value;

      // Update local state first for immediate feedback
      sideQuest.value_achieved = newValue;
      sideQuest.completed = newCompletedState;

      console.log(`TodayPage: Updated side quest ${sideQuest.id} value to ${sideQuest.value_achieved}, completed: ${sideQuest.completed}`);

      // Get today's date for comparison
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(this.selectedDate);
      selectedDate.setHours(0, 0, 0, 0);
      const isToday = selectedDate.getTime() === today.getTime();

      // Only allow toggling side quests for today
      if (!isToday) {
        console.log(`TodayPage: Cannot toggle side quest for past date: ${this.formatDate(this.selectedDate)}`);
        delete this.togglingSideQuestIds[sideQuest.id];
        return;
      }

      // Update the UI element immediately for better feedback
      this.updateSideQuestUI(sideQuest);

      try {
        const result = await this.sideQuestService.toggleSideQuestCompletion(
          sideQuest.id,
          this.userId,
          this.selectedDate // Pass the selected date
        );

        console.log(`TodayPage: Successfully toggled side quest ${sideQuest.id}`);
        console.log(`TodayPage: Updated values:`, result);

        // Update the side quest in the UI with the returned values from the server
        sideQuest.completed = result.completed;
        sideQuest.value_achieved = result.value_achieved;
        sideQuest.streak = result.streak;

        // Update the UI element with the updated streak
        this.updateSideQuestUI(sideQuest);

        // Update the week date progress for the selected date
        // Clear the cache for this date to force a refresh
        const dateKey = this.formatDate(this.selectedDate);
        delete this.weekProgressCache[dateKey];

        // Update the progress ring for this date
        this.updateProgressRingForDate(dateKey);

        // Check if all quests are completed
        this.checkAllQuestsCompleted(this.quests);

        // Reset flag for this specific side quest
        delete this.togglingSideQuestIds[sideQuest.id];
        console.log(`TodayPage: Finished toggle for side quest ${sideQuest.id}`);
      } catch (error) {
        console.error(`TodayPage: Error toggling side quest ${sideQuest.id}:`, error);

        // Revert the local state if the server update failed
        sideQuest.value_achieved = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;
        sideQuest.completed = sideQuest.value_achieved === sideQuest.current_quest.goal_value;
        this.updateSideQuestUI(sideQuest);

        delete this.togglingSideQuestIds[sideQuest.id];
      }
    } catch (error) {
      console.error(`TodayPage: Error in toggleSideQuest for ${sideQuest.id}:`, error);
      delete this.togglingSideQuestIds[sideQuest.id];
    }
  }

  // Helper method to update the UI for a specific side quest
  private updateSideQuestUI(sideQuest: DailyQuest) {
    // Find the side quest element in the DOM
    const questElement = document.querySelector(`.daily-side-quest [data-quest-id="${sideQuest.id}"]`);
    if (!questElement) {
      console.error(`TodayPage: Could not find side quest element for ID: ${sideQuest.id}`);
      return;
    }

    // Update the completed class
    if (sideQuest.completed) {
      questElement.classList.add('completed');
    } else {
      questElement.classList.remove('completed');
    }

    // Update the streak display - only show streak for today
    const streakElements = questElement.querySelectorAll('.quest-streak');
    if (streakElements && streakElements.length > 0) {
      // Get today's date for comparison
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(this.selectedDate);
      selectedDate.setHours(0, 0, 0, 0);
      const isTodaySelected = selectedDate.getTime() === today.getTime();

      // Only show streak for today's view
      if (isTodaySelected) {
        const streakValue = sideQuest.streak || 0;
        console.log(`TodayPage: Side quest ${sideQuest.id}, completed: ${sideQuest.completed}, streak: ${streakValue}`);

        // Update all streak elements (there might be multiple due to ngIf)
        streakElements.forEach(element => {
          if (element.parentElement && element.parentElement.contains(element)) {
            // Make sure the streak is visible
            (element as HTMLElement).style.display = 'block';
            element.textContent = `🔥${streakValue}d`;
          }
        });
      } else {
        // Hide streak for previous days
        streakElements.forEach(element => {
          if (element.parentElement && element.parentElement.contains(element)) {
            (element as HTMLElement).style.display = 'none';
            element.textContent = '';
          }
        });
      }
    }

    // Update the progress text
    const progressText = questElement.querySelector('.progress-text');
    if (progressText) {
      const goalUnit = sideQuest.current_quest.goal_unit !== 'count' ? ` ${sideQuest.current_quest.goal_unit}` : '';
      progressText.textContent = `${sideQuest.value_achieved}/${sideQuest.current_quest.goal_value}${goalUnit}`;
    }

    // Force a repaint to ensure the UI updates
    setTimeout(() => {
      if (questElement.parentElement) {
        const display = questElement.parentElement.style.display;
        questElement.parentElement.style.display = 'none';
        // Force a reflow
        void questElement.parentElement.offsetHeight;
        questElement.parentElement.style.display = display;
      }
    }, 0);

    console.log(`TodayPage: Updated UI for side quest ${sideQuest.id}`);
  }

  openAddQuestModal(event: Event) {
    event.preventDefault();
    this.showAddQuestModal = true;
    this.newQuest = this.getEmptyQuest();
    this.selectedDaysOfWeek = [];
    this.selectedDaysOfMonth = [];

    // Reset hasHighPriorityQuest flag
    this.hasHighPriorityQuest = false;

    // Reset animation states
    this.resetAnimationStates();

    // Start quest type animation after modal opens
    setTimeout(() => {
      console.log('Setting questTypeAnimated to true');
      this.questTypeAnimated = true;
    }, 300);
  }

  resetAnimationStates() {
    this.questTypeAnimated = false;
    this.questTypeAnimating = false;
    this.selectedQuestType = '';
    this.categoryAnimated = false;
    this.categoryAnimating = false;
    this.categorySelected = false;
    this.selectedCategory = '';
    this.priorityAnimated = false;
    this.priorityAnimating = false;
    this.goalAnimated = false;
  }

  closeAddQuestModal() {
    this.showAddQuestModal = false;
    // Reset form state
    this.newQuest = this.getEmptyQuest();
    this.selectedDaysOfWeek = [];
    this.selectedDaysOfMonth = [];
    this.hasHighPriorityQuest = false;
    this.resetAnimationStates();
  }

  // Animation methods
  selectQuestType(type: 'build' | 'quit') {
    this.selectedQuestType = type;
    this.questTypeAnimating = true;

    // Start slide out animation
    setTimeout(() => {
      this.newQuest.quest_type = type as any;

      // After quest type is set, trigger category animation
      setTimeout(() => {
        this.categoryAnimated = true;
      }, 100);
    }, 300); // Half of the animation duration
  }

  selectCategory(category: string) {
    this.selectedCategory = category;
    this.categoryAnimating = true;

    // Start slide out animation based on category
    setTimeout(() => {
      this.newQuest.category = category as any;
      this.categorySelected = true;
      this.checkCategoryPriority({ detail: { value: category } });

      // After category is set, trigger priority animation
      setTimeout(() => {
        this.priorityAnimated = true;
      }, 100);
    }, 300); // Half of the animation duration
  }

  selectPriority(priority: 'basic' | 'high') {
    if (priority === 'high' && this.hasHighPriorityQuest) {
      return; // Don't allow high priority if already exists
    }

    this.priorityAnimating = true;

    setTimeout(() => {
      this.newQuest.priority = priority as any;
    }, 300);
  }

  nextStep() {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;

      // If moving to step 5 (goal section), trigger goal animation
      if (this.currentStep === 5) {
        setTimeout(() => {
          this.goalAnimated = true;
        }, 200);
      }
    }
  }

  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  moveCaretToEnd() {
    setTimeout(() => {
      this.emojiInput.getInputElement().then((input: HTMLInputElement) => {
        const pos = input.value.length;
        input.setSelectionRange(pos, pos);
        input.scrollLeft = input.scrollWidth;
      });
    }, 100);
  }

  get progress(): number {
    return (this.currentStep) / (this.totalSteps);
  }


  async createQuest() {
    if (!this.userId || !this.newQuest.name || !this.newQuest.emoji || !this.newQuest.quest_type ||
      !this.newQuest.category || !this.newQuest.goal_value || !this.newQuest.goal_unit || !this.newQuest.goal_period) {
      console.error('TodayPage: Cannot create quest - missing required fields');
      return;
    }

    try {
      if (this.newQuest.goal_period === 'week' && this.selectedDaysOfWeek.length > 0) {
        this.newQuest.task_days_of_week = this.selectedDaysOfWeek.join(',');
      } else if (this.newQuest.goal_period === 'month' && this.selectedDaysOfMonth.length > 0) {
        this.newQuest.task_days_of_month = this.selectedDaysOfMonth.join(',');
      }

      const { data: userProfile, error: userError } = await this.supabaseService.getClient()
        .from('profiles')
        .select('id')
        .eq('id', this.userId)
        .single();

      if (userError || !userProfile) {
        console.error('TodayPage: User profile not found:', userError || 'No profile found');
        throw new Error('User profile not found. Please ensure you are logged in.');
      }

      const questToCreate: Omit<Quest, 'id' | 'streak' | 'created_at'> = {
        name: this.newQuest.name || '',
        description: this.newQuest.description || '',
        quest_type: this.newQuest.quest_type || 'build',
        goal_value: this.newQuest.goal_value,
        goal_unit: this.newQuest.goal_unit || 'count',
        goal_period: this.newQuest.goal_period || 'day',
        priority: this.newQuest.priority || 'basic',
        category: this.newQuest.category || 'strength',
        emoji: this.newQuest.emoji || '🎯',
        task_days_of_week: this.newQuest.task_days_of_week || '',
        task_days_of_month: this.newQuest.task_days_of_month || '',
        user_id: this.userId,
        active: true
      };

      try {
        const questId = await this.questService.createQuest(questToCreate);

        if (this.newQuest.quest_type === 'quit') {

          await this.questService.toggleQuestCompletion(
            this.userId,
            questId,
            new Date(),
            0,
            { ...questToCreate, id: questId } as Quest
          );
        }

        const dateKey = this.formatDate(this.selectedDate);
        delete this.questCache[dateKey];
        delete this.weekProgressCache[dateKey];

        this.closeAddQuestModal();
        this.loadData();
      } catch (questError: any) {
        console.error('TodayPage: Error creating quest:', questError);

        if (questError.message && questError.message.includes('foreign key constraint')) {
          alert('Database configuration issue detected. Please run the fix_quest_constraints.sql script in the Supabase SQL Editor to fix the foreign key constraints.');
        } else if (questError.message && questError.message.includes('fix_quest_constraints.sql')) {
          alert(questError.message);
        } else {
          alert(`Error creating quest: ${questError.message}`);
        }
      }
    } catch (error: any) {
      console.error('TodayPage: Error in createQuest:', error);
      alert(`Error: ${error.message || 'Unknown error occurred'}`);
    }
  }

  updateDaysOfWeek(day: string) {
    const index = this.selectedDaysOfWeek.indexOf(day);

    if (index !== -1) {
      this.selectedDaysOfWeek.splice(index, 1);
    } else {
      this.selectedDaysOfWeek.push(day);
    }
  }

  updateDaysOfMonth(event: any, day: number) {
    // Handle both standard Event and Ionic's CustomEvent
    let isChecked = false;

    if (event.detail !== undefined) {
      // Ionic checkbox event
      isChecked = event.detail.checked;
    } else if (event.target instanceof HTMLInputElement) {
      // Standard checkbox event
      isChecked = event.target.checked;
    }

    if (isChecked) {
      this.selectedDaysOfMonth.push(day);
    } else {
      const index = this.selectedDaysOfMonth.indexOf(day);
      if (index !== -1) {
        this.selectedDaysOfMonth.splice(index, 1);
      }
    }

    console.log(`TodayPage: Updated days of month: ${this.selectedDaysOfMonth.join(', ')}`);
  }

  updatePeriodDisplay() {
    // Reset selections when period changes
    this.selectedDaysOfWeek = [];
    this.selectedDaysOfMonth = [];
    console.log(`TodayPage: Period changed to ${this.newQuest.goal_period}, reset selections`);
  }

  checkCategoryPriority(event?: any) {
    if (!this.userId || !this.newQuest.category) return;

    // If this is an Ionic event, make sure we have the latest category value
    if (event && event.detail) {
      this.newQuest.category = event.detail.value;
      console.log(`TodayPage: Category changed to ${this.newQuest.category} via Ionic event`);
    }

    // Check if user already has a high priority quest in this category
    this.questService.getQuests(this.userId).pipe(
      take(1),
      map(quests => {
        return quests.some(q =>
          q.category === this.newQuest.category &&
          q.priority === 'high' &&
          q.active
        );
      })
    ).subscribe({
      next: (hasHighPriority) => {
        this.hasHighPriorityQuest = hasHighPriority;

        // If user already has a high priority quest, set this one to basic
        if (hasHighPriority) {
          this.newQuest.priority = 'basic';
        }

        console.log(`TodayPage: Category ${this.newQuest.category} has high priority quest: ${hasHighPriority}`);
      }
    });
  }

  /**
   * Check if all quests are completed for today and show celebration if enabled
   */
  checkAllQuestsCompleted(quests: QuestDisplay[]) {
    // Only check for today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(this.selectedDate);
    selectedDate.setHours(0, 0, 0, 0);
    const isTodaySelected = selectedDate.getTime() === today.getTime();
    const todayStr = this.formatDate(today);

    if (!isTodaySelected || !this.currentUser) {
      return;
    }

    // Check if celebration has already been shown for today
    const celebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);
    if (celebrationShown) {
      console.log('TodayPage: Celebration already shown for today:', todayStr);
      return;
    }

    // Check if all quests are completed
    const allQuestsCompleted = quests.length > 0 && quests.every(quest => quest.completed);

    // Check if side quest is completed (if enabled)
    const sideQuestCompleted = !this.showSidequests || !this.dailyQuest || this.dailyQuest.completed;

    // Show celebration if all quests and side quests are completed and celebration is enabled

    if (allQuestsCompleted && sideQuestCompleted && this.currentUser.show_celebration) {
      // Make sure we have the latest user data
      this.userService.getUserById(this.userId!).subscribe(userData => {
        if (userData) {
          this.currentUser = userData;
        }

        // Show the celebration
        this.showCelebration = true;

        // Save today's date to localStorage
        localStorage.setItem(`celebration_shown_${todayStr}`, 'true');

        // Update our tracking array
        if (!this.celebrationShownDates.includes(todayStr)) {
          this.celebrationShownDates.push(todayStr);
        }
      });
    }
  }

  /**
   * Close the celebration modal
   */
  closeCelebration() {
    this.showCelebration = false;
  }



  // Helper methods
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate();
  }

  getToday(): Date {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  }

  // Convert Django day index (0=Monday, 6=Sunday) to short day name
  private getDayNameShort(djangoDayIndex: number): string {
    // Map Django day index to day name
    const dayMap = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];
    return dayMap[djangoDayIndex];
  }

  // Convert Django day index (0=Monday, 6=Sunday) to full day name
  private getDayNameFull(djangoDayIndex: number): string {
    // Map Django day index to full day name
    const dayMap = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return dayMap[djangoDayIndex];
  }

  private getEmptyQuest(): Partial<Quest> {
    return {
      name: '',
      description: '',
      quest_type: '' as any, // Empty by default, user must select
      goal_value: 1,
      goal_unit: 'count' as QuestGoalUnit,
      goal_period: 'day' as QuestPeriod,
      priority: 'basic' as QuestPriority, // Default to basic priority
      category: '' as QuestCategory,
      emoji: '🎯'
    };
  }

  // Helper methods for the template
  // Note: Progress slider background is now handled via CSS variables
}
