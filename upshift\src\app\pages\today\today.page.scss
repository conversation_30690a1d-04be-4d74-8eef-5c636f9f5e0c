ion-content {
  .date {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    position: relative;

    .date-progress {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      pointer-events: none;
      z-index: 0;
      transform: rotate(-90deg);
    }

    .date-progress circle {
      fill: transparent;
      stroke-width: 5;
      stroke-linecap: round;
      transform-origin: center;
      transition: stroke-dasharray 0.5s ease;
    }

    .date-progress .background-circle {
      stroke: rgba(255, 255, 255, 0.1);
      stroke-width: 5;
    }

    .date-progress .progress-circle {
      stroke: #4169E1;
      stroke-opacity: 1;
      stroke-width: 5;
    }

    .date-progress .progress-circle.low {
      stroke: #FF9500 !important;
    }
  }

  ion-header {
    .week-row {
      display: flex;
      justify-content: space-around;

      .day-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 40px;

        .day-name {
          padding: 5px;
          font-size: 12px;
          color: white;
          margin-bottom: 8px;
          font-weight: lighter;
          width: 22px;
          height: 22px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .day-name.active {
          background-color: var(--accent);
        }

        .day-name.selected {
          background-color: var(--text-muted);
        }

        .date-progress .progress-circle {
          stroke: #4169E1;
          stroke-opacity: 0.9;
        }

        .date.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .date.unselected {
          color: var(--text-muted);
        }
      }
    }
  }

  ion-grid {
    padding: 0;

    .heartbeat-circle {
      margin: 32px;
      width: 100px;
      height: 100px;
      background: linear-gradient(220deg,
          #4169e1 0%,
          #6b85e8 20%,
          #95a5ef 40%,
          #bfc5f6 60%,
          #e7e9fd 80%,
          #ffffff 100%);
      background-size: 300% 100%;
      border-radius: 50%;
      position: relative;
      animation:
        heartbeat 1.2s infinite,
        gradient 2s ease-in-out infinite alternate;
    }

    @keyframes heartbeat {
      0% {
        transform: scale(1);
        opacity: 1;
      }

      25% {
        transform: scale(1.03);
      }

      50% {
        transform: scale(1.05);
      }

      75% {
        transform: scale(1.03);
      }

      100% {
        transform: scale(1);
      }
    }

    @keyframes gradient {
      0% {
        background-position: 100% 0%;
      }

      100% {
        background-position: 0% 0%;
      }
    }

    .big-date {
      min-width: 200px;
      max-width: 450px;
      min-height: 200px;
      max-height: 450px;

      .date-progress circle {
        stroke-width: 3;
      }
    }

    .add-quest {
      display: flex;
      align-items: center;

      ion-col {
        h2 {
          margin: 0;
        }

        display: flex;
        align-items: center;
      }
    }

    .quests {
      .no-quest-card {
        color: var(--text);
        margin: 16px 0;
        border: 1px solid var(--error);

        ion-card-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 20px;

          ion-col {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: left;

            ion-icon {
              font-size: 5rem;
              color: var(--error);
            }

            ion-button {
              margin-top: 16px;
              width: 80%;
            }
          }
        }
      }
    }
  }
}

.quest-item {
  padding: 5px 0 5px 0;
  margin: 16px 0 16px 0;
  display: flex;
  flex-direction: column;

  ion-row {
    width: 100%;

    .quest-info {
      align-items: flex-start;

      h3 {
        font-size: 20px;
        color: var(--accent);
      }

      ion-text {
        font-size: 14px;
        margin-top: 5px;
        color: var(--text-secondary);
      }
    }

    ion-col {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .quest-icon {
        font-size: 2rem;
      }

      .quest-streak {
        font-size: 1rem;
        color: var(--text-secondary);
      }
    }
  }

  .progress-container {
    margin-top: 10px;
    width: 100%;
    display: flex;
    justify-content: center;

    .progress-time,
    .progress {
      width: 100%;
      display: flex;
      flex-direction: column;
    }

    .progress-text {
      margin: 5px auto;
      color: var(--text-secondary);
      text-align: right;
    }
  }
}

.add-quest-btn {
  --background: rgba(65, 105, 225, 0.1);
  --color: #4169E1;
  --border-radius: 6px;
  --padding-start: 12px;
  --padding-end: 12px;
  font-size: 14px;
  font-weight: 500;
  height: 32px;
  margin: 0;
}

.add-quest-btn:hover {
  --background: rgba(65, 105, 225, 0.2);
}


.quest-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quest-item {
  background-color: #1C1C1E;
  border: 1px solid #2C2C2E;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.quest-item:active {
  transform: scale(0.98);
}

.quest-item.completed {
  border-color: #4169E1;
}

.quest-item.completed .quest-title {
  color: #4169E1;
}

.quest-item.completed::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(65, 105, 225, 0.05);
  pointer-events: none;
}

.quest-icon {
  font-size: 20px;
  min-width: 24px;
  text-align: center;
}

.quest-info {
  flex-grow: 1;
}

.quest-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  color: #4169E1;
}

.quest-description {
  color: #8E8E93;
  font-size: 12px;
  margin-bottom: 4px;
}


.progress,
.progress-time {
  color: var(--secondary-text);
  font-size: 12px;
}


/* Add a purple line between main quests and side quests */
.side-quests {
  position: relative;
  padding-top: 32px;
}


.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.quests h2,
.daily-side-quest h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}


.progress-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 4px;
  background: #2C2C2E;
  /* Use hardcoded color */
  outline: none;
  position: relative;
  /* Remove top and transform properties that cause vertical alignment issues */
}


.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  position: relative;
  margin-top: -4px;
  /* Only use margin-top for vertical centering */
}


.progress-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  border: none;
  position: relative;
  margin-top: 0;
  /* Firefox handles vertical centering differently */
}


.progress-slider::-webkit-slider-runnable-track {
  height: 4px;
  border-radius: 2px;
}


.progress-slider::-moz-range-track {
  height: 4px;
  border-radius: 2px;
}


/* Custom progress bar fill */
.progress-slider {
  /* Remove the static background gradient */
  background: var(--inactive-date);
}

/* Add dynamic styling for the progress slider */
input[type="range"].progress-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  border-radius: 2px;
  outline: none;
  position: relative;
  z-index: 1;
  /* Background will be set inline via JavaScript */
}

input[type="range"].progress-slider::-webkit-slider-runnable-track {
  height: 4px;
  border-radius: 2px;
  background: transparent;
}

input[type="range"].progress-slider::-moz-range-track {
  height: 4px;
  border-radius: 2px;
  background: transparent;
}

/* Style the filled part of the slider */
input[type="range"].progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  /* Use hardcoded color */
  cursor: pointer;
  margin-top: -4px;
  /* Adjust to center the thumb */
  position: relative;
  z-index: 2;
}

input[type="range"].progress-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  /* Use hardcoded color */
  cursor: pointer;
  border: none;
  position: relative;
  margin-top: 0;
  /* Firefox handles vertical centering differently */
  z-index: 2;
}


.progress-text {
  font-size: 12px;
  color: var(--secondary-text);
  margin-top: 2px;
}



.container {
  width: 480px;
  margin: 0 auto;
  padding: 20px;
  overflow-y: auto;
  scrollbar-width: none;
  height: 100%;
  padding-bottom: 74px;
}

.container::-webkit-scrollbar {
  display: none;
  /* Chrome/Safari */
}


.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
}


.week-calendar {
  margin-bottom: 32px;
}


.days,
.dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  gap: 8px;
}


.day-name {
  color: var(--secondary-text);
  font-size: 14px;
}

.date-content {
  position: relative;
  z-index: 1;
}

h2 {
  font-size: 20px;
  margin-bottom: 16px;
}


.side-quests::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 1px;
  background: linear-gradient(to right, transparent, #4B0082, transparent);
}


.calendar {
  margin: 20px 0;
  padding: 10px;
  background: var(--bg-secondary);
  border-radius: 8px;
}


.calendar-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}


.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  text-align: center;
}


.day-name {
  color: #8E8E93;
  font-size: 12px;
  font-weight: 500;
}


.day-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  text-decoration: none;
  color: var(--text-primary);
  margin: 0 auto;
}


.day-number:hover {
  background: var(--bg-hover);
}


.day-number.selected {
  background: var(--primary-color);
  color: white;
}


.day-number.today {
  border: 2px solid var(--primary-color);
}


.nav-arrow {
  --background: transparent;
  --color: #FFFFFF;
  --border-radius: 50%;
  --padding-start: 0;
  --padding-end: 0;
  width: 32px;
  height: 32px;
  margin: 0;
  font-size: 18px;
  cursor: pointer;
}

.nav-arrow:hover {
  --background: rgba(255, 255, 255, 0.1);
}


.time-display {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-right: 16px;
}


/* Make number input spinners less prominent */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  opacity: 0.3;
}

/* Progress slider styling */
ion-range.progress-slider {
  --bar-height: 6px;
  --bar-border-radius: 3px;
  --knob-size: 16px;
  --bar-background: #2C2C2E;
  --bar-background-active: #4169E1;
  --knob-background: #4169E1;
  --pin-background: #4169E1;
  --pin-color: #FFFFFF;
  --step: 1;
  --tick-height: 0;
  --tick-width: 0;
  --tick-background: transparent;
  --tick-background-active: transparent;
  margin: 0;
  padding: 0;
}

/* For standard HTML sliders (fallback) */
.progress-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  outline: none;
  background: linear-gradient(to right, #4169E1 0%, #4169E1 var(--progress-value), #2C2C2E var(--progress-value), #2C2C2E 100%);
}

/* Hide tick marks completely */
ion-range.progress-slider::part(tick) {
  display: none !important;
}

ion-range.progress-slider::part(tick-active) {
  display: none !important;
}

.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  margin-top: -5px;
}

.progress-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  margin-top: 0;
  border: none;
}

.daily-side-quest h2 {
  padding-bottom: 20px;
}

.add-quest-modal {
  ion-content {
    --background: var(--bg);
    position: relative;
    overflow: hidden;

    .create-quest-row {
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }

  .modal-header {
    ion-progress-bar {
      --background: var(--progress-bg);
      height: 8px;
      border-radius: 4px;
    }

    ion-icon {
      position: absolute;
      right: 15px;
      top: 15px;
      font-size: 30px;
    }
  }

  form {
    h2 {
      font-size: 24px;
    }

    .choose-quest {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 90%;

      ion-card {
        height: 150px;

        ion-icon {
          color: var(--accent);
          font-size: 32px;
        }

        h2 {
          margin: 0px;
        }
      }
    }

    .first-step {
      text-align: center;



      .preview-emoji {
        ion-col {
          display: flex;
          justify-content: center;
        }

        ion-input {
          height: 100%;
          font-size: 8em;
          max-width: 60%;
        }
      }

      .emoji-row {
        ion-text {
          font-size: 1em;
        }
      }
    }

    // Step 3 - Designová pecka
    .step-three-container {
      .step-header {
        margin-bottom: 30px;

        .floating-emoji {
          font-size: 4em;
          display: inline-block;
          animation: float-emoji 3s ease-in-out infinite;
          filter: drop-shadow(0 0 20px rgba(65, 105, 225, 0.3));
        }
      }

      .input-section {
        margin-top: 32px;
      }

      .floating-input-container,
      .floating-textarea-container {
        .input-icon {
          position: absolute;
          left: 18px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 20px;
          z-index: 3;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
          opacity: 0.8;
        }

        .dark-input {
          --padding-start: 55px;
          --padding-end: 20px;
          --padding-top: 24px;
          --padding-bottom: 24px;
          border-radius: 16px;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(20px);
          position: relative;
          overflow: hidden;
          box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);

          &:focus-within {
            border-color: var(--accent);
            box-shadow:
              0 0 0 1px var(--accent),
              0 0 30px rgba(65, 105, 225, 0.4),
              0 12px 40px rgba(0, 0, 0, 0.4),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
          }
        }

        .floating-label {
          position: absolute;
          left: 20%;
          top: -10px;
          color: var(--text-secondary);
          font-size: 16px;
          font-weight: 600;
          padding: 4px 12px;
          border-radius: 8px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          z-index: 4;
        }

        .character-counter {
          position: absolute;
          right: 20px;
          font-size: 11px;
          font-weight: 600;
          color: var(--text-muted);
          background: rgba(22, 23, 28, 0.8);
          padding: 4px 8px;
          border-radius: 6px;
          border: 1px solid rgba(255, 255, 255, 0.05);
          transition: all 0.3s ease;

          span {
            &.warning {
              color: var(--warning);
              text-shadow: 0 0 8px rgba(250, 204, 21, 0.3);
            }
          }
        }

        .dark-input:focus+.floating-label,
        .dark-input:not(:placeholder-shown)+.floating-label {
          top: -12px;
          left: 50px;
          font-size: 12px;
          color: var(--accent);
          transform: translateY(0) scale(0.9);
          background: linear-gradient(135deg,
              rgba(65, 105, 225, 0.2),
              rgba(82, 119, 232, 0.15));
          border-color: rgba(65, 105, 225, 0.3);
          box-shadow:
            0 4px 12px rgba(65, 105, 225, 0.2),
            0 0 0 1px rgba(65, 105, 225, 0.1);
        }
      }
    }

    // Step 4 - Configuration pecka
    .step-four-container {
      margin-top: 10%;

      .step-header {
        margin-bottom: 30px;

        .step-icon-container {
          position: relative;
          margin-bottom: 20px;

          .floating-emoji {
            font-size: 4em;
            display: inline-block;
            animation: float-emoji 3s ease-in-out infinite;
            filter: drop-shadow(0 0 20px rgba(65, 105, 225, 0.3));
          }
        }

        .step-title {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 10px;
          color: var(--text);
        }

        .step-subtitle {
          font-size: 16px;
          color: var(--text-secondary);
          margin: 0;
          opacity: 0.8;
        }
      }

      .section-label {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        font-size: 18px;
        font-weight: 600;
        color: var(--text);

        ion-icon {
          margin-right: 8px;
          font-size: 20px;
          color: var(--accent);
        }
      }

      // Quest Type Toggle
      .quest-type-section {
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(100%);
        opacity: 0;

        &.slide-in-from-right {
          transform: translateX(0);
          opacity: 1;
        }

        &.slide-out-left {
          transform: translateX(-100%);
          opacity: 0;
        }

        .toggle-container {
          display: flex;
          gap: 15px;

          .toggle-option {
            flex: 1;
            background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));
            border: 2px solid var(--border);
            border-radius: 16px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg,
                  rgba(65, 105, 225, 0.1) 0%,
                  rgba(82, 119, 232, 0.05) 100%);
              opacity: 0;
              transition: opacity 0.4s ease;
              pointer-events: none;
            }

            .toggle-icon {
              font-size: 2.5em;
              margin-bottom: 10px;
              filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
            }

            .toggle-text {
              h4 {
                margin: 0 0 5px 0;
                font-size: 16px;
                font-weight: 600;
                color: var(--text);
              }

              p {
                margin: 0;
                font-size: 12px;
                color: var(--text-secondary);
                opacity: 0.8;
              }
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            }

            &.active {
              border-color: var(--accent);
              box-shadow:
                0 0 0 1px var(--accent),
                0 0 20px rgba(65, 105, 225, 0.3);

              &::before {
                opacity: 1;
              }

              .toggle-icon {
                transform: scale(1.1);
              }
            }
          }
        }
      }

      // Category Grid
      .category-section {
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(100%);
        opacity: 0;

        &.slide-in-from-right {
          transform: translateX(0);
          opacity: 1;
        }

        &.slide-out-left {
          transform: translateX(-100%);
          opacity: 0;
        }

        .category-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          .category-card {
            background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));
            border: 2px solid var(--border);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg,
                  rgba(65, 105, 225, 0.1) 0%,
                  rgba(82, 119, 232, 0.05) 100%);
              opacity: 0;
              transition: opacity 0.4s ease;
              pointer-events: none;
            }

            .category-icon {
              font-size: 2.5em;
              margin-bottom: 8px;
              filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
              transition: transform 0.3s ease;
            }

            span {
              font-size: 14px;
              font-weight: 600;
              color: var(--text);
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);

              .category-icon {
                transform: scale(1.1);
              }
            }

            &.selected {
              border-color: var(--accent);
              box-shadow:
                0 0 0 1px var(--accent),
                0 0 20px rgba(65, 105, 225, 0.3);

              &::before {
                opacity: 1;
              }

              .category-icon {
                transform: scale(1.1);
              }
            }
          }
        }
      }

      .priority-section {
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(100%);
        opacity: 0;

        &.slide-in-from-right {
          transform: translateX(0);
          opacity: 1;
        }

        &.slide-out-left {
          transform: translateX(-100%);
          opacity: 0;
        }

        .priority-container {
          display: flex;
          gap: 15px;

          .priority-option {
            flex: 1;
            background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));
            border: 2px solid var(--border);
            border-radius: 16px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            display: flex;
            align-items: center;
            gap: 12px;

            .priority-indicator {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              transition: all 0.3s ease;

              &.basic {
                background: linear-gradient(135deg, #4ade80, #22c55e);
                box-shadow: 0 0 10px rgba(74, 222, 128, 0.3);
              }

              &.high {
                background: linear-gradient(135deg, #f59e0b, #d97706);
                box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
              }
            }

            span {
              font-size: 14px;
              font-weight: 600;
              color: var(--text);
            }

            &:hover:not(.disabled) {
              transform: translateY(-1px);
              box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            }

            &.active {
              border-color: var(--accent);
              box-shadow:
                0 0 0 1px var(--accent),
                0 0 15px rgba(65, 105, 225, 0.3);

              .priority-indicator {
                transform: scale(1.2);
              }
            }

            &.disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }

        .priority-warning {
          margin-top: 10px;
          padding: 10px 15px;
          background: rgba(245, 158, 11, 0.1);
          border: 1px solid rgba(245, 158, 11, 0.3);
          border-radius: 8px;
          display: flex;
          align-items: center;
          gap: 8px;

          ion-icon {
            color: var(--warning);
            font-size: 16px;
          }

          span {
            font-size: 12px;
            color: var(--warning);
          }
        }
      }

      // Goal Section
      .goal-section {
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;

        .goal-container {
          transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateY(30px);
          opacity: 0;

          &.slide-in {
            transform: translateY(0);
            opacity: 1;
          }

          .goal-input-wrapper {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;

            .goal-number {
              flex: 0 0 100px;

              .goal-value-input {
                --background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));
                --color: var(--text);
                --border-color: var(--border);
                --border-radius: 12px;
                --padding-start: 15px;
                --padding-end: 15px;
                --padding-top: 15px;
                --padding-bottom: 15px;
                border: 2px solid var(--border);
                border-radius: 12px;
                text-align: center;
                font-size: 18px;
                font-weight: 600;
                transition: all 0.3s ease;

                &:focus-within {
                  border-color: var(--accent);
                  box-shadow: 0 0 15px rgba(65, 105, 225, 0.3);
                }
              }
            }

            .goal-unit {
              flex: 1;

              .goal-unit-select {
                --background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));
                --color: var(--text);
                --border-color: var(--border);
                --border-radius: 12px;
                --padding-start: 15px;
                --padding-end: 15px;
                --padding-top: 15px;
                --padding-bottom: 15px;
                border: 2px solid var(--border);
                border-radius: 12px;
                transition: all 0.3s ease;

                &:focus-within {
                  border-color: var(--accent);
                  box-shadow: 0 0 15px rgba(65, 105, 225, 0.3);
                }
              }
            }
          }

          .goal-preview {
            background: linear-gradient(135deg, rgba(65, 105, 225, 0.1), rgba(82, 119, 232, 0.05));
            border: 1px solid rgba(65, 105, 225, 0.2);
            border-radius: 12px;
            padding: 15px;
            text-align: center;

            .preview-text {
              font-size: 16px;
              color: var(--text-secondary);

              .highlight {
                color: var(--accent);
                font-weight: 700;
                font-size: 18px;
              }
            }
          }
        }
      }
    }

    .select-days {
      display: flex;
      justify-content: space-evenly;

      .day-checked {
        width: 100%;
        flex: 1;
        --background: var(--surface);
        --color: var(--text);
        --border-color: var(--border);
        --border-style: solid;
        --border-width: 1px;
      }
    }
  }
}

@keyframes aurora-curve {
  0% {
    transform: translateX(-10px) translateY(5px) scale(1);
    opacity: 0.8;
  }

  50% {
    transform: translateX(15px) translateY(-8px) scale(1.05);
    opacity: 1;
  }

  100% {
    transform: translateX(-5px) translateY(3px) scale(0.98);
    opacity: 0.9;
  }
}

@keyframes aurora-flow {
  0% {
    transform: translateX(-15px) translateY(-5px) skewX(1deg);
    opacity: 0.6;
  }

  50% {
    transform: translateX(20px) translateY(10px) skewX(-1.5deg);
    opacity: 0.8;
  }

  100% {
    transform: translateX(-8px) translateY(-3px) skewX(0.5deg);
    opacity: 0.7;
  }
}

@keyframes aurora-glow {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.4;
  }

  50% {
    transform: scale(1.1) rotate(1deg);
    opacity: 0.6;
  }

  100% {
    transform: scale(0.95) rotate(-0.5deg);
    opacity: 0.5;
  }
}

// Floating emoji animation
@keyframes float-emoji {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  33% {
    transform: translateY(-10px) rotate(2deg);
  }

  66% {
    transform: translateY(-5px) rotate(-2deg);
  }
}