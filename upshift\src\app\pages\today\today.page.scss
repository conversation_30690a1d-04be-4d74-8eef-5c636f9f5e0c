ion-content {
  .date {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    position: relative;

    .date-progress {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      pointer-events: none;
      z-index: 0;
      transform: rotate(-90deg);
    }

    .date-progress circle {
      fill: transparent;
      stroke-width: 5;
      stroke-linecap: round;
      transform-origin: center;
      transition: stroke-dasharray 0.5s ease;
    }

    .date-progress .background-circle {
      stroke: rgba(255, 255, 255, 0.1);
      stroke-width: 5;
    }

    .date-progress .progress-circle {
      stroke: #4169E1;
      stroke-opacity: 1;
      stroke-width: 5;
    }

    .date-progress .progress-circle.low {
      stroke: #FF9500 !important;
    }
  }

  ion-header {
    .week-row {
      display: flex;
      justify-content: space-around;

      .day-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 40px;

        .day-name {
          padding: 5px;
          font-size: 12px;
          color: white;
          margin-bottom: 8px;
          font-weight: lighter;
          width: 22px;
          height: 22px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .day-name.active {
          background-color: var(--accent);
        }

        .day-name.selected {
          background-color: var(--text-muted);
        }

        .date-progress .progress-circle {
          stroke: #4169E1;
          stroke-opacity: 0.9;
        }

        .date.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .date.unselected {
          color: var(--text-muted);
        }
      }
    }
  }

  ion-grid {
    padding: 0;

    .heartbeat-circle {
      margin: 32px;
      width: 100px;
      height: 100px;
      background: linear-gradient(220deg,
          #4169e1 0%,
          #6b85e8 20%,
          #95a5ef 40%,
          #bfc5f6 60%,
          #e7e9fd 80%,
          #ffffff 100%);
      background-size: 300% 100%;
      border-radius: 50%;
      position: relative;
      animation:
        heartbeat 1.2s infinite,
        gradient 2s ease-in-out infinite alternate;
    }

    @keyframes heartbeat {
      0% {
        transform: scale(1);
        opacity: 1;
      }

      25% {
        transform: scale(1.03);
      }

      50% {
        transform: scale(1.05);
      }

      75% {
        transform: scale(1.03);
      }

      100% {
        transform: scale(1);
      }
    }

    @keyframes gradient {
      0% {
        background-position: 100% 0%;
      }

      100% {
        background-position: 0% 0%;
      }
    }

    .big-date {
      min-width: 200px;
      max-width: 450px;
      min-height: 200px;
      max-height: 450px;

      .date-progress circle {
        stroke-width: 3;
      }
    }

    .add-quest {
      display: flex;
      align-items: center;

      ion-col {
        h2 {
          margin: 0;
        }

        display: flex;
        align-items: center;
      }
    }

    .quests {
      .no-quest-card {
        color: var(--text);
        margin: 16px 0;
        border: 1px solid var(--error);

        ion-card-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 20px;

          ion-col {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: left;

            ion-icon {
              font-size: 5rem;
              color: var(--error);
            }

            ion-button {
              margin-top: 16px;
              width: 80%;
            }
          }
        }
      }
    }
  }
}

.quest-item {
  padding: 5px 0 5px 0;
  margin: 16px 0 16px 0;
  display: flex;
  flex-direction: column;

  ion-row {
    width: 100%;

    .quest-info {
      align-items: flex-start;

      h3 {
        font-size: 20px;
        color: var(--accent);
      }

      ion-text {
        font-size: 14px;
        margin-top: 5px;
        color: var(--text-secondary);
      }
    }

    ion-col {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .quest-icon {
        font-size: 2rem;
      }

      .quest-streak {
        font-size: 1rem;
        color: var(--text-secondary);
      }
    }
  }

  .progress-container {
    margin-top: 10px;
    width: 100%;
    display: flex;
    justify-content: center;

    .progress-time,
    .progress {
      width: 100%;
      display: flex;
      flex-direction: column;
    }

    .progress-text {
      margin: 5px auto;
      color: var(--text-secondary);
      text-align: right;
    }
  }
}

.add-quest-btn {
  --background: rgba(65, 105, 225, 0.1);
  --color: #4169E1;
  --border-radius: 6px;
  --padding-start: 12px;
  --padding-end: 12px;
  font-size: 14px;
  font-weight: 500;
  height: 32px;
  margin: 0;
}

.add-quest-btn:hover {
  --background: rgba(65, 105, 225, 0.2);
}


.quest-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quest-item {
  background-color: #1C1C1E;
  border: 1px solid #2C2C2E;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.quest-item:active {
  transform: scale(0.98);
}

.quest-item.completed {
  border-color: #4169E1;
}

.quest-item.completed .quest-title {
  color: #4169E1;
}

.quest-item.completed::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(65, 105, 225, 0.05);
  pointer-events: none;
}

.quest-icon {
  font-size: 20px;
  min-width: 24px;
  text-align: center;
}

.quest-info {
  flex-grow: 1;
}

.quest-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  color: #4169E1;
}

.quest-description {
  color: #8E8E93;
  font-size: 12px;
  margin-bottom: 4px;
}


.progress,
.progress-time {
  color: var(--secondary-text);
  font-size: 12px;
}


/* Add a purple line between main quests and side quests */
.side-quests {
  position: relative;
  padding-top: 32px;
}


.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.quests h2,
.daily-side-quest h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}


.progress-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 4px;
  background: #2C2C2E;
  /* Use hardcoded color */
  outline: none;
  position: relative;
  /* Remove top and transform properties that cause vertical alignment issues */
}


.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  position: relative;
  margin-top: -4px;
  /* Only use margin-top for vertical centering */
}


.progress-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  border: none;
  position: relative;
  margin-top: 0;
  /* Firefox handles vertical centering differently */
}


.progress-slider::-webkit-slider-runnable-track {
  height: 4px;
  border-radius: 2px;
}


.progress-slider::-moz-range-track {
  height: 4px;
  border-radius: 2px;
}


/* Custom progress bar fill */
.progress-slider {
  /* Remove the static background gradient */
  background: var(--inactive-date);
}

/* Add dynamic styling for the progress slider */
input[type="range"].progress-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  border-radius: 2px;
  outline: none;
  position: relative;
  z-index: 1;
  /* Background will be set inline via JavaScript */
}

input[type="range"].progress-slider::-webkit-slider-runnable-track {
  height: 4px;
  border-radius: 2px;
  background: transparent;
}

input[type="range"].progress-slider::-moz-range-track {
  height: 4px;
  border-radius: 2px;
  background: transparent;
}

/* Style the filled part of the slider */
input[type="range"].progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  /* Use hardcoded color */
  cursor: pointer;
  margin-top: -4px;
  /* Adjust to center the thumb */
  position: relative;
  z-index: 2;
}

input[type="range"].progress-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4169E1;
  /* Use hardcoded color */
  cursor: pointer;
  border: none;
  position: relative;
  margin-top: 0;
  /* Firefox handles vertical centering differently */
  z-index: 2;
}


.progress-text {
  font-size: 12px;
  color: var(--secondary-text);
  margin-top: 2px;
}



.container {
  width: 480px;
  margin: 0 auto;
  padding: 20px;
  overflow-y: auto;
  scrollbar-width: none;
  height: 100%;
  padding-bottom: 74px;
}

.container::-webkit-scrollbar {
  display: none;
  /* Chrome/Safari */
}


.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
}


.week-calendar {
  margin-bottom: 32px;
}


.days,
.dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  gap: 8px;
}


.day-name {
  color: var(--secondary-text);
  font-size: 14px;
}

.date-content {
  position: relative;
  z-index: 1;
}

h2 {
  font-size: 20px;
  margin-bottom: 16px;
}


.side-quests::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 1px;
  background: linear-gradient(to right, transparent, #4B0082, transparent);
}


.calendar {
  margin: 20px 0;
  padding: 10px;
  background: var(--bg-secondary);
  border-radius: 8px;
}


.calendar-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}


.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  text-align: center;
}


.day-name {
  color: #8E8E93;
  font-size: 12px;
  font-weight: 500;
}


.day-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  text-decoration: none;
  color: var(--text-primary);
  margin: 0 auto;
}


.day-number:hover {
  background: var(--bg-hover);
}


.day-number.selected {
  background: var(--primary-color);
  color: white;
}


.day-number.today {
  border: 2px solid var(--primary-color);
}


.nav-arrow {
  --background: transparent;
  --color: #FFFFFF;
  --border-radius: 50%;
  --padding-start: 0;
  --padding-end: 0;
  width: 32px;
  height: 32px;
  margin: 0;
  font-size: 18px;
  cursor: pointer;
}

.nav-arrow:hover {
  --background: rgba(255, 255, 255, 0.1);
}


.time-display {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-right: 16px;
}


/* Make number input spinners less prominent */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  opacity: 0.3;
}

/* Progress slider styling */
ion-range.progress-slider {
  --bar-height: 6px;
  --bar-border-radius: 3px;
  --knob-size: 16px;
  --bar-background: #2C2C2E;
  --bar-background-active: #4169E1;
  --knob-background: #4169E1;
  --pin-background: #4169E1;
  --pin-color: #FFFFFF;
  --step: 1;
  --tick-height: 0;
  --tick-width: 0;
  --tick-background: transparent;
  --tick-background-active: transparent;
  margin: 0;
  padding: 0;
}

/* For standard HTML sliders (fallback) */
.progress-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  outline: none;
  background: linear-gradient(to right, #4169E1 0%, #4169E1 var(--progress-value), #2C2C2E var(--progress-value), #2C2C2E 100%);
}

/* Hide tick marks completely */
ion-range.progress-slider::part(tick) {
  display: none !important;
}

ion-range.progress-slider::part(tick-active) {
  display: none !important;
}

.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  margin-top: -5px;
}

.progress-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #4169E1;
  cursor: pointer;
  margin-top: 0;
  border: none;
}

.daily-side-quest h2 {
  padding-bottom: 20px;
}

.add-quest-modal {
  ion-content {
    --background: var(--bg);

    .create-quest-row {
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }

  .modal-header {
    ion-progress-bar {
      --background: var(--progress-bg);
      height: 8px;
      border-radius: 4px;
    }

    ion-icon {
      position: absolute;
      right: 15px;
      top: 15px;
      font-size: 30px;
    }
  }

  form {
    .choose-quest {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 90%;

      ion-card {
        height: 150px;

        ion-icon {
          color: var(--accent);
          font-size: 32px;
        }

        h2 {
          margin: 0px;
        }
      }
    }

    .first-step {
      margin-top: 15%;
      text-align: center;



      .preview-emoji {
        ion-col {
          display: flex;
          justify-content: center;
        }

        ion-input {
          border-bottom: 5px dashed var(--accent);
          --background: transparent;
          height: 100%;
          font-size: 8em;
          max-width: 60%;
        }
      }

      .emoji-row {
        ion-text {
          font-size: 3em;
        }
      }

      h2 {
        font-size: 24px;
      }
    }

    .select-days {
      display: flex;
      justify-content: space-evenly;

      .day-checked {
        width: 100%;
        flex: 1;
        --background: var(--surface);
        --color: var(--text);
        --border-color: var(--border);
        --border-style: solid;
        --border-width: 1px;
      }
    }
  }
}