// Create a test user with a valid plan in Supabase
// Run this script with Node.js

const { createClient } = require('@supabase/supabase-js');

// Replace with your Supabase config
const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
const supabaseServiceRoleKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; // Replace with your service role key

// Initialize Supabase with service role key (admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function createTestUser() {
  try {
    // 1. Create user in Supabase Auth
    const email = '<EMAIL>';
    const password = 'password123';
    
    console.log(`Creating user with email: ${email}`);
    
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true // Auto-confirm email
    });
    
    if (authError) {
      console.error('Error creating user in Auth:', authError);
      return;
    }
    
    console.log('User created in Auth:', authData.user.id);
    
    // 2. Create user profile with a valid plan
    const now = new Date();
    const oneMonthLater = new Date();
    oneMonthLater.setMonth(now.getMonth() + 1);
    
    const userData = {
      id: authData.user.id,
      email: email,
      username: 'testuser',
      name: 'Test User',
      profile_picture: null,
      registration_date: now,
      last_login: now,
      active: true,
      level: 1,
      strength_xp: 0,
      money_xp: 0,
      health_xp: 0,
      knowledge_xp: 0,
      title: '🥚 Beginner',
      bio: '',
      plan: 'monthly',
      start_of_current_plan: now,
      end_of_current_plan: oneMonthLater,
      auto_renew: true,
      sidequests_switch: true,
      show_celebration: true,
      celebration_name: 'Another Day, Another W',
      celebration_description: "You've completed all your quests for today. Keep up the great work!",
      celebration_emoji: '🎉',
      subscription_status: 'email marketing'
    };
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert(userData)
      .select();
    
    if (profileError) {
      console.error('Error creating user profile:', profileError);
      return;
    }
    
    console.log('User profile created:', profileData);
    console.log(`Test user created successfully with email: ${email} and password: ${password}`);
    console.log('Plan valid until:', oneMonthLater);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createTestUser();
