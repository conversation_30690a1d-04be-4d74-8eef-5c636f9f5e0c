// Skript na kontrolu a opravu problémov s prístupovými právami v Supabase
// Spustite tento skript pomocou Node.js

const { createClient } = require('@supabase/supabase-js');

// Nahraďte svojimi Supabase údajmi
const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
const supabaseKey = 'YOUR_SUPABASE_KEY'; // Použite svoj anon kľúč alebo service role kľúč

// Inicializácia Supabase klienta
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkAccess() {
  console.log('Kontrola prístupových práv v Supabase...');

  try {
    // 1. Kontrola prístupu k profiles tabuľke
    console.log('\nKontrola prístupu k profiles tabuľke:');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (profilesError) {
      console.error('❌ Chyba pri prístupe k profiles tabuľke:', profilesError.message);
      console.log('   Riešenie: Pridajte politiku "Users can view all profiles" pre profiles tabuľku');
    } else {
      console.log('✅ Prístup k profiles tabuľke je v poriadku');
    }

    // 2. Kontrola prístupu k friends tabuľke
    console.log('\nKontrola prístupu k friends tabuľke:');
    const { data: friends, error: friendsError } = await supabase
      .from('friends')
      .select('*')
      .limit(1);
    
    if (friendsError) {
      console.error('❌ Chyba pri prístupe k friends tabuľke:', friendsError.message);
      console.log('   Riešenie: Pridajte politiku "Users can view their own friends" pre friends tabuľku');
    } else {
      console.log('✅ Prístup k friends tabuľke je v poriadku');
    }

    // 3. Kontrola prístupu k groups tabuľke
    console.log('\nKontrola prístupu k groups tabuľke:');
    const { data: groups, error: groupsError } = await supabase
      .from('groups')
      .select('*')
      .limit(1);
    
    if (groupsError) {
      console.error('❌ Chyba pri prístupe k groups tabuľke:', groupsError.message);
      console.log('   Riešenie: Pridajte politiku "Users can view all groups" pre groups tabuľku');
    } else {
      console.log('✅ Prístup k groups tabuľke je v poriadku');
    }

    // 4. Kontrola prístupu k group_members tabuľke
    console.log('\nKontrola prístupu k group_members tabuľke:');
    const { data: groupMembers, error: groupMembersError } = await supabase
      .from('group_members')
      .select('*')
      .limit(1);
    
    if (groupMembersError) {
      console.error('❌ Chyba pri prístupe k group_members tabuľke:', groupMembersError.message);
      console.log('   Riešenie: Pridajte politiku "Users can view all group members" pre group_members tabuľku');
    } else {
      console.log('✅ Prístup k group_members tabuľke je v poriadku');
    }

    // 5. Kontrola prístupu k user_badges tabuľke
    console.log('\nKontrola prístupu k user_badges tabuľke:');
    const { data: userBadges, error: userBadgesError } = await supabase
      .from('user_badges')
      .select('*')
      .limit(1);
    
    if (userBadgesError) {
      console.error('❌ Chyba pri prístupe k user_badges tabuľke:', userBadgesError.message);
      console.log('   Riešenie: Pridajte politiku "Users can view all badges" pre user_badges tabuľku');
    } else {
      console.log('✅ Prístup k user_badges tabuľke je v poriadku');
    }

    // 6. Kontrola prístupu k activities tabuľke
    console.log('\nKontrola prístupu k activities tabuľke:');
    const { data: activities, error: activitiesError } = await supabase
      .from('activities')
      .select('*')
      .limit(1);
    
    if (activitiesError) {
      console.error('❌ Chyba pri prístupe k activities tabuľke:', activitiesError.message);
      console.log('   Riešenie: Pridajte politiku "Users can view their own activities" pre activities tabuľku');
    } else {
      console.log('✅ Prístup k activities tabuľke je v poriadku');
    }

    // 7. Kontrola prístupu k goals tabuľke
    console.log('\nKontrola prístupu k goals tabuľke:');
    const { data: goals, error: goalsError } = await supabase
      .from('goals')
      .select('*')
      .limit(1);
    
    if (goalsError) {
      console.error('❌ Chyba pri prístupe k goals tabuľke:', goalsError.message);
      console.log('   Riešenie: Pridajte politiku "Users can view their own goals" pre goals tabuľku');
    } else {
      console.log('✅ Prístup k goals tabuľke je v poriadku');
    }

    console.log('\n=== Súhrn ===');
    console.log('Ak ste našli chyby, postupujte podľa návodu v SUPABASE-ACCESS-GUIDE.md');
    console.log('Spustite SQL príkazy v supabase-rls-policies.sql pre nastavenie všetkých potrebných politík');

  } catch (error) {
    console.error('Neočakávaná chyba:', error);
  }
}

checkAccess();
