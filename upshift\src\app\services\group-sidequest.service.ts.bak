import { Injectable } from '@angular/core';
import { Observable, from, of, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { SupabaseService } from './supabase.service';
import { GroupSideQuest, GroupSideQuestPool, GroupSideQuestMemberStatus, GroupDailyQuest } from '../models/group-sidequest.model';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class GroupSideQuestService {

  constructor(
    private supabaseService: SupabaseService,
    private http: HttpClient
  ) { }

  /**
   * Get all group side quests for a group
   */
  getGroupSideQuests(groupId: string): Observable<GroupSideQuest[]> {
    console.log('GroupSideQuestService: Getting side quests for group:', groupId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*, group_sidequest_pool(*)')
        .eq('group_id', groupId)
        .order('date_assigned', { ascending: false })
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupSideQuestService: Error getting group side quests:', response.error);
          return [];
        }

        console.log(`GroupSideQuestService: Found ${response.data.length} side quests for group:`, groupId);

        // Transform the data to match our model
        return response.data.map(item => {
          const sideQuest: GroupSideQuest = {
            id: item.id,
            group_id: item.group_id,
            current_quest_id: item.current_quest_id,
            streak: item.streak,
            last_completed_date: item.last_completed_date,
            date_assigned: item.date_assigned,
            completed: item.completed,
            value_achieved: item.value_achieved,
            category: item.category,
            created_at: item.created_at,
            current_quest: item.group_sidequest_pool
          };
          return sideQuest;
        });
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting group side quests:', error);
        return of([]);
      })
    );
  }

  /**
   * Get a specific group side quest by ID
   */
  getGroupSideQuest(sideQuestId: string): Observable<GroupSideQuest | null> {
    console.log('GroupSideQuestService: Getting side quest with ID:', sideQuestId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*, group_sidequest_pool(*)')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupSideQuestService: Error getting side quest:', response.error);
          return null;
        }

        console.log('GroupSideQuestService: Found side quest:', response.data);

        // Transform the data to match our model
        const item = response.data;
        const sideQuest: GroupSideQuest = {
          id: item.id,
          group_id: item.group_id,
          current_quest_id: item.current_quest_id,
          streak: item.streak,
          last_completed_date: item.last_completed_date,
          date_assigned: item.date_assigned,
          completed: item.completed,
          value_achieved: item.value_achieved,
          category: item.category,
          created_at: item.created_at,
          current_quest: item.group_sidequest_pool
        };
        return sideQuest;
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting side quest:', error);
        return of(null);
      })
    );
  }

  /**
   * Get the latest group side quest for a group
   */
  getLatestGroupSideQuest(groupId: string): Observable<GroupSideQuest | null> {
    console.log('GroupSideQuestService: Getting latest side quest for group:', groupId);

    return this.getGroupSideQuests(groupId).pipe(
      map(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          console.log('GroupSideQuestService: No side quests found for group:', groupId);
          return null;
        }

        console.log('GroupSideQuestService: Found side quests for group:', sideQuests);
        return sideQuests[0];
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting latest side quest for group:', error);
        return of(null);
      })
    );
  }

  /**
   * Get member status for a group side quest
   */
  getMemberStatus(groupQuestId: string, memberId: string): Observable<GroupSideQuestMemberStatus | null> {
    console.log('GroupSideQuestService: Getting member status for quest:', groupQuestId, 'member:', memberId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*')
        .eq('group_quest_id', groupQuestId)
        .eq('member_id', memberId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupSideQuestService: Error getting member status:', response.error);
          return null;
        }

        console.log('GroupSideQuestService: Found member status:', response.data);
        return response.data as GroupSideQuestMemberStatus;
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting member status:', error);
        return of(null);
      })
    );
  }

  /**
   * Get all member statuses for a group side quest
   */
  getAllMemberStatuses(groupQuestId: string): Observable<GroupSideQuestMemberStatus[]> {
    console.log('GroupSideQuestService: Getting all member statuses for quest:', groupQuestId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*, profiles(id, username, profile_picture)')
        .eq('group_quest_id', groupQuestId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupSideQuestService: Error getting member statuses:', response.error);
          return [];
        }

        console.log(`GroupSideQuestService: Found ${response.data.length} member statuses for quest:`, groupQuestId);

        // Transform the data to match our model
        return response.data.map(item => {
          const status: GroupSideQuestMemberStatus = {
            id: item.id,
            group_quest_id: item.group_quest_id,
            member_id: item.member_id,
            completed: item.completed,
            value_achieved: item.value_achieved,
            last_updated: item.last_updated,
            created_at: item.created_at,
            member: item.profiles
          };
          return status;
        });
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting member statuses:', error);
        return of([]);
      })
    );
  }

  /**
   * Toggle member completion status
   * Implements the logic in TypeScript instead of using a Supabase function
   */
  toggleMemberCompletion(statusId: string, _groupId: string): Observable<GroupSideQuestMemberStatus> {
    console.log('GroupSideQuestService: Toggling member completion for status:', statusId);

    // First, get the current status
    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*, group_quest:group_quest_id(*)')
        .eq('id', statusId)
        .single()
    ).pipe(
      switchMap(statusResponse => {
        if (statusResponse.error) {
          console.error('GroupSideQuestService: Error getting member status:', statusResponse.error);
          return throwError(() => new Error(statusResponse.error.message));
        }

        const currentStatus = statusResponse.data as any;
        const groupQuestId = currentStatus.group_quest_id;
        const currentCompleted = currentStatus.completed;
        const today = new Date().toISOString().split('T')[0];

        // Toggle the completion status
        const newCompleted = !currentCompleted;

        // Get the goal value from the group quest
        return from(
          this.supabaseService.getClient()
            .from('group_sidequests')
            .select('*, group_sidequest_pool(*)')
            .eq('id', groupQuestId)
            .single()
        ).pipe(
          switchMap(questResponse => {
            if (questResponse.error) {
              console.error('GroupSideQuestService: Error getting quest details:', questResponse.error);
              return throwError(() => new Error(questResponse.error.message));
            }

            const quest = questResponse.data;
            const goalValue = quest.group_sidequest_pool?.goal_value || 60; // Default to 60 if not found
            const newValueAchieved = newCompleted ? goalValue : 0;

        // Update the member status
        return from(
          this.supabaseService.getClient()
            .from('group_sidequest_member_status')
            .update({
              completed: newCompleted,
              value_achieved: newValueAchieved,
              last_updated: today
            })
            .eq('id', statusId)
            .select()
            .single()
        ).pipe(
          switchMap(updateResponse => {
            if (updateResponse.error) {
              console.error('GroupSideQuestService: Error updating member status:', updateResponse.error);
              return throwError(() => new Error(updateResponse.error.message));
            }

            const updatedStatus = updateResponse.data as GroupSideQuestMemberStatus;
            console.log('GroupSideQuestService: Updated member status:', updatedStatus);

            // Now we need to check if all eligible members have completed the quest
            // First, get the group ID from the group quest
            return from(
              this.supabaseService.getClient()
                .from('group_sidequests')
                .select('group_id')
                .eq('id', groupQuestId)
                .single()
            ).pipe(
              switchMap(groupQuestResponse => {
                if (groupQuestResponse.error) {
                  console.error('GroupSideQuestService: Error getting group quest:', groupQuestResponse.error);
                  return throwError(() => new Error(groupQuestResponse.error.message));
                }

                const groupId = groupQuestResponse.data.group_id;

                // Get all eligible members (joined before today)
                return from(
                  this.supabaseService.getClient()
                    .from('group_members')
                    .select('user_id')
                    .eq('group_id', groupId)
                    .lt('joined_date', today)
                ).pipe(
                  switchMap(eligibleMembersResponse => {
                    if (eligibleMembersResponse.error) {
                      console.error('GroupSideQuestService: Error getting eligible members:', eligibleMembersResponse.error);
                      return throwError(() => new Error(eligibleMembersResponse.error.message));
                    }

                    const eligibleMembers = eligibleMembersResponse.data;
                    const eligibleMemberIds = eligibleMembers.map(m => m.user_id);
                    const eligibleMembersCount = eligibleMembers.length;

                    if (eligibleMembersCount === 0) {
                      // No eligible members, just return the updated status
                      return of(updatedStatus);
                    }

                    // Get all completed members
                    return from(
                      this.supabaseService.getClient()
                        .from('group_sidequest_member_status')
                        .select('member_id')
                        .eq('group_quest_id', groupQuestId)
                        .eq('completed', true)
                    ).pipe(
                      switchMap(completedMembersResponse => {
                        if (completedMembersResponse.error) {
                          console.error('GroupSideQuestService: Error getting completed members:', completedMembersResponse.error);
                          return throwError(() => new Error(completedMembersResponse.error.message));
                        }

                        const completedMembers = completedMembersResponse.data;
                        const completedMemberIds = completedMembers.map(m => m.member_id);

                        // Check if all eligible members have completed the quest
                        const allCompleted = eligibleMemberIds.every(id => completedMemberIds.includes(id)) && eligibleMembersCount > 0;

                        // Get the current group quest to check if it was previously all completed
                        return from(
                          this.supabaseService.getClient()
                            .from('group_sidequests')
                            .select('*')
                            .eq('id', groupQuestId)
                            .single()
                        ).pipe(
                          switchMap(groupQuestDetailResponse => {
                            if (groupQuestDetailResponse.error) {
                              console.error('GroupSideQuestService: Error getting group quest details:', groupQuestDetailResponse.error);
                              return throwError(() => new Error(groupQuestDetailResponse.error.message));
                            }

                            const groupQuest = groupQuestDetailResponse.data;
                            const wasAllCompleted = groupQuest.completed;
                            const category = groupQuest.category;

                            // Determine if we need to update the streak and XP
                            if (allCompleted && !wasAllCompleted) {
                              // All members have completed the quest, increase streak and add XP
                              const xpToAdd = eligibleMembersCount * 2; // 2 XP per eligible member

                              // Update the group quest
                              return from(
                                this.supabaseService.getClient()
                                  .from('group_sidequests')
                                  .update({
                                    streak: groupQuest.streak + 1,
                                    completed: true,
                                    last_completed_date: today
                                  })
                                  .eq('id', groupQuestId)
                              ).pipe(
                                switchMap(() => {
                                  // Update the group XP
                                  return from(
                                    this.supabaseService.getClient()
                                      .from('groups')
                                      .select('*')
                                      .eq('id', groupId)
                                      .single()
                                  ).pipe(
                                    switchMap(groupXpResponse => {
                                      if (groupXpResponse.error) {
                                        console.error('GroupSideQuestService: Error getting group XP:', groupXpResponse.error);
                                        return throwError(() => new Error(groupXpResponse.error.message));
                                      }

                                      // Get the current XP value based on category
                                      let currentXp = 0;
                                      if (category === 'strength') {
                                        currentXp = groupXpResponse.data.strength_xp || 0;
                                      } else if (category === 'money') {
                                        currentXp = groupXpResponse.data.money_xp || 0;
                                      } else if (category === 'health') {
                                        currentXp = groupXpResponse.data.health_xp || 0;
                                      } else if (category === 'knowledge') {
                                        currentXp = groupXpResponse.data.knowledge_xp || 0;
                                      }
                                      const newXp = currentXp + xpToAdd;

                                      // Use the dedicated method to update XP
                                      return this.updateGroupXp(groupId, category, newXp).pipe(
                                        switchMap(() => {
                                          // Verify the update by fetching the updated group data
                                          return from(
                                            this.supabaseService.getClient()
                                              .from('groups')
                                              .select('*')
                                              .eq('id', groupId)
                                              .single()
                                          ).pipe(
                                            map(verifyResponse => {
                                              if (verifyResponse.error) {
                                                console.error('GroupSideQuestService: Error verifying group XP update:', verifyResponse.error);
                                              } else {
                                                // Log the actual updated value
                                                let updatedXp = 0;
                                                if (category === 'strength') {
                                                  updatedXp = verifyResponse.data.strength_xp || 0;
                                                } else if (category === 'money') {
                                                  updatedXp = verifyResponse.data.money_xp || 0;
                                                } else if (category === 'health') {
                                                  updatedXp = verifyResponse.data.health_xp || 0;
                                                } else if (category === 'knowledge') {
                                                  updatedXp = verifyResponse.data.knowledge_xp || 0;
                                                }
                                                console.log(`GroupSideQuestService: Added ${xpToAdd} XP to group ${groupId} ${category}_xp from ${currentXp} to ${newXp}, verified value: ${updatedXp}`);
                                              }
                                              return updatedStatus;
                                            })
                                          );
                                        })
                                      );
                                    })
                                  );
                                })
                              );
                            } else if (!allCompleted && wasAllCompleted) {
                              // Quest was previously completed but now it's not, decrease streak and subtract XP
                              const xpToSubtract = -1 * eligibleMembersCount * 2; // -2 XP per eligible member

                              // Update the group quest
                              return from(
                                this.supabaseService.getClient()
                                  .from('group_sidequests')
                                  .update({
                                    streak: Math.max(0, groupQuest.streak - 1),
                                    completed: false
                                  })
                                  .eq('id', groupQuestId)
                              ).pipe(
                                switchMap(() => {
                                  // Update the group XP
                                  return from(
                                    this.supabaseService.getClient()
                                      .from('groups')
                                      .select('*')
                                      .eq('id', groupId)
                                      .single()
                                  ).pipe(
                                    switchMap(groupXpResponse => {
                                      if (groupXpResponse.error) {
                                        console.error('GroupSideQuestService: Error getting group XP:', groupXpResponse.error);
                                        return throwError(() => new Error(groupXpResponse.error.message));
                                      }

                                      // Get the current XP value based on category
                                      let currentXp = 0;
                                      if (category === 'strength') {
                                        currentXp = groupXpResponse.data.strength_xp || 0;
                                      } else if (category === 'money') {
                                        currentXp = groupXpResponse.data.money_xp || 0;
                                      } else if (category === 'health') {
                                        currentXp = groupXpResponse.data.health_xp || 0;
                                      } else if (category === 'knowledge') {
                                        currentXp = groupXpResponse.data.knowledge_xp || 0;
                                      }
                                      const newXp = Math.max(0, currentXp + xpToSubtract);

                                      // Use the dedicated method to update XP
                                      return this.updateGroupXp(groupId, category, newXp).pipe(
                                        switchMap(() => {
                                          // Verify the update by fetching the updated group data
                                          return from(
                                            this.supabaseService.getClient()
                                              .from('groups')
                                              .select('*')
                                              .eq('id', groupId)
                                              .single()
                                          ).pipe(
                                            map(verifyResponse => {
                                              if (verifyResponse.error) {
                                                console.error('GroupSideQuestService: Error verifying group XP update:', verifyResponse.error);
                                              } else {
                                                // Log the actual updated value
                                                let updatedXp = 0;
                                                if (category === 'strength') {
                                                  updatedXp = verifyResponse.data.strength_xp || 0;
                                                } else if (category === 'money') {
                                                  updatedXp = verifyResponse.data.money_xp || 0;
                                                } else if (category === 'health') {
                                                  updatedXp = verifyResponse.data.health_xp || 0;
                                                } else if (category === 'knowledge') {
                                                  updatedXp = verifyResponse.data.knowledge_xp || 0;
                                                }
                                                console.log(`GroupSideQuestService: Subtracted ${Math.abs(xpToSubtract)} XP from group ${groupId} ${category}_xp from ${currentXp} to ${newXp}, verified value: ${updatedXp}`);
                                              }
                                              return updatedStatus;
                                            })
                                          );
                                        })
                                      );
                                    })
                                  );
                                })
                              );
                            } else {
                              // No change in completion status, just return the updated status
                              return of(updatedStatus);
                            }
                          })
                        );
                      })
                    );
                  })
                );
              })
            );
          })
        );
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error toggling member completion:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Ensure group has a daily side quest
   * This matches the Django implementation where we check if the group has any side quests
   * and create one if they don't
   */
  ensureGroupHasDailySideQuest(groupId: string): Observable<GroupDailyQuest | null> {
    console.log('GroupSideQuestService: Ensuring group has a daily side quest:', groupId);

    // First, check if the group already has a side quest
    return this.getLatestGroupSideQuest(groupId).pipe(
      switchMap(existingSideQuest => {
        if (existingSideQuest) {
          console.log('GroupSideQuestService: Group already has a side quest:', existingSideQuest);

          // Check if we need to reset/assign new quest
          // Get the current date
          const today = new Date();
          const dateString = today.toISOString().split('T')[0];

          // Check if the quest was assigned today
          if (existingSideQuest.date_assigned === dateString) {
            console.log('GroupSideQuestService: Side quest was assigned today, no need to reset');

            // Get all member statuses
            return this.getAllMemberStatuses(existingSideQuest.id).pipe(
              switchMap(statuses => {
                // Get all eligible members (joined before today)
                return from(
                  this.supabaseService.getClient()
                    .from('group_members')
                    .select('user_id, joined_date')
                    .eq('group_id', groupId)
                ).pipe(
                  map(membersResponse => {
                    if (membersResponse.error) {
                      console.error('GroupSideQuestService: Error getting group members:', membersResponse.error);
                      return null;
                    }

                    const members = membersResponse.data;

                    // Count eligible members (joined before today)
                    const eligibleMembers = members.filter(m => {
                      const joinedDate = new Date(m.joined_date);
                      const today = new Date();
                      today.setHours(0, 0, 0, 0);
                      return joinedDate < today;
                    });
                    const eligibleMembersCount = eligibleMembers.length;
                    const eligibleMemberIds = eligibleMembers.map(m => m.user_id);

                    // Count completed members
                    const completedMembers = statuses.filter(s =>
                      s.completed && eligibleMemberIds.includes(s.member_id)
                    );
                    const completedMembersCount = completedMembers.length;

                    // Create the GroupDailyQuest object
                    const dailyQuest: GroupDailyQuest = {
                      id: existingSideQuest.id,
                      group_id: existingSideQuest.group_id,
                      streak: existingSideQuest.streak,
                      completed: existingSideQuest.completed,
                      value_achieved: existingSideQuest.value_achieved,
                      date_assigned: existingSideQuest.date_assigned,
                      last_completed_date: existingSideQuest.last_completed_date,
                      category: existingSideQuest.category,
                      current_quest: {
                        id: existingSideQuest.current_quest!.id,
                        name: existingSideQuest.current_quest!.name,
                        description: existingSideQuest.current_quest!.description,
                        goal_value: existingSideQuest.current_quest!.goal_value,
                        goal_unit: existingSideQuest.current_quest!.goal_unit,
                        emoji: existingSideQuest.current_quest!.emoji
                      },
                      eligible_members_count: eligibleMembersCount,
                      completed_members_count: completedMembersCount
                    };

                    return dailyQuest;
                  })
                );
              })
            );
          } else {
            console.log('GroupSideQuestService: Side quest was assigned on a different day, need to reset');

            // Get a random active quest from the pool
            return from(
              this.supabaseService.getClient()
                .from('group_sidequest_pool')
                .select('*')
                .eq('active', true)
            ).pipe(
              switchMap(response => {
                if (response.error) {
                  console.error('GroupSideQuestService: Error getting side quest pool:', response.error);
                  return throwError(() => new Error(response.error.message));
                }

                if (!response.data || response.data.length === 0) {
                  console.error('GroupSideQuestService: No active side quests available');
                  return throwError(() => new Error('No active side quests available'));
                }

                // Select a random side quest from the pool
                const randomIndex = Math.floor(Math.random() * response.data.length);
                const selectedQuest = response.data[randomIndex] as GroupSideQuestPool;

                // Update the existing side quest
                return from(
                  this.supabaseService.getClient()
                    .from('group_sidequests')
                    .update({
                      current_quest_id: selectedQuest.id,
                      date_assigned: dateString,
                      completed: false,
                      value_achieved: 0,
                      category: selectedQuest.category
                    })
                    .eq('id', existingSideQuest.id)
                    .select()
                    .single()
                ).pipe(
                  switchMap(updateResponse => {
                    if (updateResponse.error) {
                      console.error('GroupSideQuestService: Error updating group side quest:', updateResponse.error);
                      return throwError(() => new Error(updateResponse.error.message));
                    }

                    const updatedSideQuest = updateResponse.data;

                    // Reset all member statuses
                    return from(
                      this.supabaseService.getClient()
                        .from('group_sidequest_member_status')
                        .update({
                          completed: false,
                          value_achieved: 0,
                          last_updated: dateString
                        })
                        .eq('group_quest_id', existingSideQuest.id)
                    ).pipe(
                      switchMap(() => {
                        // Get all eligible members
                        return from(
                          this.supabaseService.getClient()
                            .from('group_members')
                            .select('user_id, joined_date')
                            .eq('group_id', groupId)
                        ).pipe(
                          map(membersResponse => {
                            if (membersResponse.error) {
                              console.error('GroupSideQuestService: Error getting group members:', membersResponse.error);
                              return null;
                            }

                            const members = membersResponse.data;

                            // Count eligible members (joined before today)
                            const eligibleMembers = members.filter(m => {
                              const joinedDate = new Date(m.joined_date);
                              const today = new Date();
                              today.setHours(0, 0, 0, 0);
                              return joinedDate < today;
                            });
                            const eligibleMembersCount = eligibleMembers.length;

                            // Create the GroupDailyQuest object
                            const dailyQuest: GroupDailyQuest = {
                              id: updatedSideQuest.id,
                              group_id: updatedSideQuest.group_id,
                              streak: updatedSideQuest.streak,
                              completed: updatedSideQuest.completed,
                              value_achieved: updatedSideQuest.value_achieved,
                              date_assigned: updatedSideQuest.date_assigned,
                              last_completed_date: updatedSideQuest.last_completed_date,
                              category: updatedSideQuest.category,
                              current_quest: {
                                id: selectedQuest.id,
                                name: selectedQuest.name,
                                description: selectedQuest.description,
                                goal_value: selectedQuest.goal_value,
                                goal_unit: selectedQuest.goal_unit,
                                emoji: selectedQuest.emoji
                              },
                              eligible_members_count: eligibleMembersCount,
                              completed_members_count: 0
                            };

                            return dailyQuest;
                          })
                        );
                      })
                    );
                  })
                );
              })
            );
          }
        }

        console.log('GroupSideQuestService: No side quest found, creating new side quest for group');

        // Create a new side quest for the group
        return this.createGroupSideQuest(groupId);
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error ensuring group has a daily side quest:', error);
        return of(null);
      })
    );
  }

  /**
   * Create a group side quest
   */
  private createGroupSideQuest(groupId: string): Observable<GroupDailyQuest | null> {
    console.log('GroupSideQuestService: Creating side quest for group:', groupId);

    // Get a random active quest from the pool
    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .eq('active', true)
    ).pipe(
      switchMap(response => {
        if (response.error) {
          console.error('GroupSideQuestService: Error getting side quest pool:', response.error);
          return throwError(() => new Error(response.error.message));
        }

        if (!response.data || response.data.length === 0) {
          console.error('GroupSideQuestService: No active side quests available');
          return throwError(() => new Error('No active side quests available'));
        }

        console.log('GroupSideQuestService: Found side quest pool:', response.data);

        // Select a random side quest from the pool
        const randomIndex = Math.floor(Math.random() * response.data.length);
        const selectedQuest = response.data[randomIndex] as GroupSideQuestPool;

        console.log('GroupSideQuestService: Selected random side quest:', selectedQuest);

        // Create a new group side quest
        const today = new Date();
        const dateString = today.toISOString().split('T')[0];

        const newSideQuest = {
          group_id: groupId,
          current_quest_id: selectedQuest.id,
          streak: 0,
          date_assigned: dateString,
          completed: false,
          value_achieved: 0,
          category: selectedQuest.category
        };

        return from(
          this.supabaseService.getClient()
            .from('group_sidequests')
            .insert(newSideQuest)
            .select()
            .single()
        ).pipe(
          switchMap(insertResponse => {
            if (insertResponse.error) {
              console.error('GroupSideQuestService: Error creating group side quest:', insertResponse.error);
              return throwError(() => new Error(insertResponse.error.message));
            }

            console.log('GroupSideQuestService: Created group side quest with ID:', insertResponse.data.id);
            const newGroupQuestId = insertResponse.data.id;

            // Get all group members to create member statuses
            return from(
              this.supabaseService.getClient()
                .from('group_members')
                .select('user_id, joined_date')
                .eq('group_id', groupId)
            ).pipe(
              switchMap(membersResponse => {
                if (membersResponse.error) {
                  console.error('GroupSideQuestService: Error getting group members:', membersResponse.error);
                  return throwError(() => new Error(membersResponse.error.message));
                }

                const members = membersResponse.data;
                console.log('GroupSideQuestService: Found group members:', members);

                // Create member statuses for all members
                const memberStatuses = members.map(member => ({
                  group_quest_id: newGroupQuestId,
                  member_id: member.user_id,
                  completed: false,
                  value_achieved: 0,
                  last_updated: dateString
                }));

                if (memberStatuses.length === 0) {
                  // No members, just return the daily quest
                  const dailyQuest: GroupDailyQuest = {
                    id: insertResponse.data.id,
                    group_id: insertResponse.data.group_id,
                    streak: insertResponse.data.streak,
                    completed: insertResponse.data.completed,
                    value_achieved: insertResponse.data.value_achieved,
                    date_assigned: insertResponse.data.date_assigned,
                    last_completed_date: insertResponse.data.last_completed_date,
                    category: insertResponse.data.category,
                    current_quest: {
                      id: selectedQuest.id,
                      name: selectedQuest.name,
                      description: selectedQuest.description,
                      goal_value: selectedQuest.goal_value,
                      goal_unit: selectedQuest.goal_unit,
                      emoji: selectedQuest.emoji
                    },
                    eligible_members_count: 0,
                    completed_members_count: 0
                  };
                  return of(dailyQuest);
                }

                // Insert member statuses
                return from(
                  this.supabaseService.getClient()
                    .from('group_sidequest_member_status')
                    .insert(memberStatuses)
                ).pipe(
                  map(() => {
                    console.log('GroupSideQuestService: Created member statuses for group quest');

                    // Count eligible members (joined before today)
                    const eligibleMembers = members.filter(m => {
                      const joinedDate = new Date(m.joined_date);
                      const today = new Date();
                      today.setHours(0, 0, 0, 0);
                      return joinedDate < today;
                    });
                    const eligibleMembersCount = eligibleMembers.length;

                    // Create the GroupDailyQuest object
                    const dailyQuest: GroupDailyQuest = {
                      id: insertResponse.data.id,
                      group_id: insertResponse.data.group_id,
                      streak: insertResponse.data.streak,
                      completed: insertResponse.data.completed,
                      value_achieved: insertResponse.data.value_achieved,
                      date_assigned: insertResponse.data.date_assigned,
                      last_completed_date: insertResponse.data.last_completed_date,
                      category: insertResponse.data.category,
                      current_quest: {
                        id: selectedQuest.id,
                        name: selectedQuest.name,
                        description: selectedQuest.description,
                        goal_value: selectedQuest.goal_value,
                        goal_unit: selectedQuest.goal_unit,
                        emoji: selectedQuest.emoji
                      },
                      eligible_members_count: eligibleMembersCount,
                      completed_members_count: 0
                    };

                    return dailyQuest;
                  })
                );
              })
            );
          })
        );
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error creating group side quest:', error);
        return of(null);
      })
    );
  }

  /**
   * Update group XP using a Supabase RPC function to bypass RLS
   *
   * Note: This requires creating the following function in Supabase SQL editor:
   *
   * CREATE OR REPLACE FUNCTION update_group_xp(p_group_id UUID, p_category TEXT, p_value INTEGER)
   * RETURNS VOID AS $$
   * BEGIN
   *   IF p_category = 'strength' THEN
   *     UPDATE groups SET strength_xp = p_value WHERE id = p_group_id;
   *   ELSIF p_category = 'money' THEN
   *     UPDATE groups SET money_xp = p_value WHERE id = p_group_id;
   *   ELSIF p_category = 'health' THEN
   *     UPDATE groups SET health_xp = p_value WHERE id = p_group_id;
   *   ELSIF p_category = 'knowledge' THEN
   *     UPDATE groups SET knowledge_xp = p_value WHERE id = p_group_id;
   *   END IF;
   * END;
   * $$ LANGUAGE plpgsql SECURITY DEFINER;
   */
  updateGroupXp(groupId: string, category: string, newXpValue: number): Observable<any> {
    console.log(`GroupSideQuestService: Updating ${category}_xp to ${newXpValue} for group ${groupId} via RPC`);

    // First try to get the current group data to log the current values
    return from(
      this.supabaseService.getClient()
        .from('groups')
        .select('*')
        .eq('id', groupId)
        .single()
    ).pipe(
      switchMap(groupResponse => {
        if (groupResponse.error) {
          console.error(`GroupSideQuestService: Error getting group:`, groupResponse.error);
          return throwError(() => new Error(groupResponse.error.message));
        }

        const currentGroup = groupResponse.data;

        // Log the current XP values
        console.log(`GroupSideQuestService: Current XP values:`, {
          strength_xp: currentGroup.strength_xp,
          money_xp: currentGroup.money_xp,
          health_xp: currentGroup.health_xp,
          knowledge_xp: currentGroup.knowledge_xp
        });

        // Use the RPC function to update the XP value
        return from(
          this.supabaseService.getClient()
            .rpc('update_group_xp', {
              p_group_id: groupId,
              p_category: category,
              p_value: newXpValue
            })
        ).pipe(
          switchMap(rpcResponse => {
            if (rpcResponse.error) {
              console.error(`GroupSideQuestService: Error updating ${category}_xp via RPC:`, rpcResponse.error);
              console.log(`GroupSideQuestService: RPC function may not exist. Please create it in Supabase SQL editor.`);

              // Fallback to direct update (will likely fail due to RLS)
              let updateData: any = {};
              updateData[`${category}_xp`] = newXpValue;

              return from(
                this.supabaseService.getClient()
                  .from('groups')
                  .update(updateData)
                  .eq('id', groupId)
                  .select()
              ).pipe(
                map(updateResponse => {
                  if (updateResponse.error) {
                    console.error(`GroupSideQuestService: Fallback update also failed:`, updateResponse.error);
                    return null;
                  }

                  console.log(`GroupSideQuestService: Fallback update succeeded:`, updateResponse.data);
                  return updateResponse.data;
                })
              );
            }

            console.log(`GroupSideQuestService: Successfully updated ${category}_xp to ${newXpValue} via RPC`);

            // Verify the update by fetching the updated group data
            return from(
              this.supabaseService.getClient()
                .from('groups')
                .select('*')
                .eq('id', groupId)
                .single()
            ).pipe(
              map(verifyResponse => {
                if (verifyResponse.error) {
                  console.error(`GroupSideQuestService: Error verifying update:`, verifyResponse.error);
                  return null;
                }

                const updatedGroup = verifyResponse.data;

                // Log the updated XP values
                console.log(`GroupSideQuestService: Updated XP values:`, {
                  strength_xp: updatedGroup.strength_xp,
                  money_xp: updatedGroup.money_xp,
                  health_xp: updatedGroup.health_xp,
                  knowledge_xp: updatedGroup.knowledge_xp
                });

                return updatedGroup;
              })
            );
          }),
          catchError(error => {
            console.error(`GroupSideQuestService: Error updating ${category}_xp:`, error);
            return of(null);
          })
        );
      })
    );
  }

  /**
   * Import side quests from JSON file to Supabase
   */
  importSideQuestsFromJson(): Observable<boolean> {
    console.log('GroupSideQuestService: Importing side quests from JSON');

    return this.http.get<any[]>('assets/data/group-sidequest-pool.json').pipe(
      switchMap(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          console.error('GroupSideQuestService: No side quests found in JSON file');
          return of(false);
        }

        console.log(`GroupSideQuestService: Importing ${sideQuests.length} side quests from JSON`);

        // Convert to Supabase model
        const sideQuestsToImport = sideQuests.map(quest => ({
          name: quest.name,
          description: quest.description || null,
          goal_value: quest.goal_value || 1,
          category: quest.category,
          goal_unit: quest.goal_unit || 'count',
          active: true,
          emoji: quest.emoji || '🎯'
        }));

        // Insert into Supabase
        return from(
          this.supabaseService.getClient()
            .from('group_sidequest_pool')
            .insert(sideQuestsToImport)
        ).pipe(
          map(response => {
            if (response.error) {
              console.error('GroupSideQuestService: Error importing side quests:', response.error);
              return false;
            }

            console.log('GroupSideQuestService: Successfully imported side quests');
            return true;
          }),
          catchError(error => {
            console.error('GroupSideQuestService: Error importing side quests:', error);
            return of(false);
          })
        );
      })
    );
  }
}
