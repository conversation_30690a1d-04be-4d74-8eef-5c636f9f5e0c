import { Injectable } from '@angular/core';
import { Observable, from, of, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { SupabaseService } from './supabase.service';
import { GroupSideQuest, GroupSideQuestMemberStatus, GroupDailyQuest } from '../models/group-sidequest.model';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class GroupSideQuestService {

  constructor(
    private supabaseService: SupabaseService,
    private http: HttpClient
  ) { }

  /**
   * Get all group side quests for a group
   */
  getGroupSideQuests(groupId: string): Observable<GroupSideQuest[]> {
    console.log('GroupSideQuestService: Getting side quests for group:', groupId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*, group_sidequest_pool(*)')
        .eq('group_id', groupId)
        .order('date_assigned', { ascending: false })
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupSideQuestService: Error getting group side quests:', response.error);
          return [];
        }

        console.log(`GroupSideQuestService: Found ${response.data.length} side quests for group:`, groupId);

        // Transform the data to match our model
        return response.data.map(item => {
          const sideQuest: GroupSideQuest = {
            id: item.id,
            group_id: item.group_id,
            current_quest_id: item.current_quest_id,
            streak: item.streak,
            last_completed_date: item.last_completed_date,
            date_assigned: item.date_assigned,
            completed: item.completed,
            value_achieved: item.value_achieved,
            category: item.category,
            created_at: item.created_at,
            current_quest: item.group_sidequest_pool
          };
          return sideQuest;
        });
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting group side quests:', error);
        return of([]);
      })
    );
  }

  /**
   * Get a specific group side quest by ID
   */
  getGroupSideQuest(sideQuestId: string): Observable<GroupSideQuest | null> {
    console.log('GroupSideQuestService: Getting side quest with ID:', sideQuestId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*, group_sidequest_pool(*)')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupSideQuestService: Error getting side quest:', response.error);
          return null;
        }

        console.log('GroupSideQuestService: Found side quest:', response.data);

        // Transform the data to match our model
        const item = response.data;
        const sideQuest: GroupSideQuest = {
          id: item.id,
          group_id: item.group_id,
          current_quest_id: item.current_quest_id,
          streak: item.streak,
          last_completed_date: item.last_completed_date,
          date_assigned: item.date_assigned,
          completed: item.completed,
          value_achieved: item.value_achieved,
          category: item.category,
          created_at: item.created_at,
          current_quest: item.group_sidequest_pool
        };
        return sideQuest;
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting side quest:', error);
        return of(null);
      })
    );
  }

  /**
   * Get the latest group side quest for a group
   */
  getLatestGroupSideQuest(groupId: string): Observable<GroupSideQuest | null> {
    console.log('GroupSideQuestService: Getting latest side quest for group:', groupId);

    return this.getGroupSideQuests(groupId).pipe(
      map(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          console.log('GroupSideQuestService: No side quests found for group:', groupId);
          return null;
        }

        console.log('GroupSideQuestService: Found side quests for group:', sideQuests);
        return sideQuests[0];
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting latest side quest for group:', error);
        return of(null);
      })
    );
  }

  /**
   * Get member status for a group side quest
   */
  getMemberStatus(groupQuestId: string, memberId: string): Observable<GroupSideQuestMemberStatus | null> {
    console.log('GroupSideQuestService: Getting member status for quest:', groupQuestId, 'member:', memberId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*')
        .eq('group_quest_id', groupQuestId)
        .eq('member_id', memberId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupSideQuestService: Error getting member status:', response.error);
          return null;
        }

        console.log('GroupSideQuestService: Found member status:', response.data);
        return response.data as GroupSideQuestMemberStatus;
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting member status:', error);
        return of(null);
      })
    );
  }

  /**
   * Get all member statuses for a group side quest
   */
  getAllMemberStatuses(groupQuestId: string): Observable<GroupSideQuestMemberStatus[]> {
    console.log('GroupSideQuestService: Getting all member statuses for quest:', groupQuestId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*, profiles(id, username, profile_picture)')
        .eq('group_quest_id', groupQuestId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupSideQuestService: Error getting member statuses:', response.error);
          return [];
        }

        console.log(`GroupSideQuestService: Found ${response.data.length} member statuses for quest:`, groupQuestId);

        // Transform the data to match our model
        return response.data.map(item => {
          const status: GroupSideQuestMemberStatus = {
            id: item.id,
            group_quest_id: item.group_quest_id,
            member_id: item.member_id,
            completed: item.completed,
            value_achieved: item.value_achieved,
            last_updated: item.last_updated,
            created_at: item.created_at,
            member: item.profiles
          };
          return status;
        });
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error getting member statuses:', error);
        return of([]);
      })
    );
  }

  /**
   * Toggle member completion status
   * Implements the logic in TypeScript instead of using a Supabase function
   */
  toggleMemberCompletion(statusId: string, groupId: string): Observable<GroupSideQuestMemberStatus> {
    console.log('GroupSideQuestService: Toggling member completion for status:', statusId);

    // First, get the current status
    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*, group_quest:group_quest_id(*)')
        .eq('id', statusId)
        .single()
    ).pipe(
      switchMap(statusResponse => {
        if (statusResponse.error) {
          console.error('GroupSideQuestService: Error getting member status:', statusResponse.error);
          return throwError(() => new Error(statusResponse.error.message));
        }

        const currentStatus = statusResponse.data as any;
        const groupQuestId = currentStatus.group_quest_id;
        const currentCompleted = currentStatus.completed;
        const today = new Date().toISOString().split('T')[0];

        // Toggle the completion status
        const newCompleted = !currentCompleted;

        // Get the goal value from the group quest
        return from(
          this.supabaseService.getClient()
            .from('group_sidequests')
            .select('*, group_sidequest_pool(*)')
            .eq('id', groupQuestId)
            .single()
        ).pipe(
          switchMap(questResponse => {
            if (questResponse.error) {
              console.error('GroupSideQuestService: Error getting quest details:', questResponse.error);
              return throwError(() => new Error(questResponse.error.message));
            }

            const quest = questResponse.data;
            const goalValue = quest.group_sidequest_pool?.goal_value || 60; // Default to 60 if not found
            const newValueAchieved = newCompleted ? goalValue : 0;

            // Update the member status
            return from(
              this.supabaseService.getClient()
                .from('group_sidequest_member_status')
                .update({
                  completed: newCompleted,
                  value_achieved: newValueAchieved,
                  last_updated: today
                })
                .eq('id', statusId)
                .select()
                .single()
            ).pipe(
              switchMap(updateResponse => {
                if (updateResponse.error) {
                  console.error('GroupSideQuestService: Error updating member status:', updateResponse.error);
                  return throwError(() => new Error(updateResponse.error.message));
                }

                const updatedStatus = updateResponse.data as GroupSideQuestMemberStatus;
                console.log('GroupSideQuestService: Updated member status:', updatedStatus);

                // Now we need to check if all eligible members have completed the quest
                // First, get the group ID from the group quest
                return from(
                  this.supabaseService.getClient()
                    .from('group_sidequests')
                    .select('group_id')
                    .eq('id', groupQuestId)
                    .single()
                ).pipe(
                  switchMap(groupQuestResponse => {
                    if (groupQuestResponse.error) {
                      console.error('GroupSideQuestService: Error getting group quest:', groupQuestResponse.error);
                      return throwError(() => new Error(groupQuestResponse.error.message));
                    }

                    const groupId = groupQuestResponse.data.group_id;

                    // Get all eligible members (joined before today)
                    return from(
                      this.supabaseService.getClient()
                        .from('group_members')
                        .select('user_id')
                        .eq('group_id', groupId)
                        .lt('joined_date', today)
                    ).pipe(
                      switchMap(eligibleMembersResponse => {
                        if (eligibleMembersResponse.error) {
                          console.error('GroupSideQuestService: Error getting eligible members:', eligibleMembersResponse.error);
                          return throwError(() => new Error(eligibleMembersResponse.error.message));
                        }

                        const eligibleMembers = eligibleMembersResponse.data;
                        const eligibleMemberIds = eligibleMembers.map(m => m.user_id);
                        const eligibleMembersCount = eligibleMembers.length;

                        if (eligibleMembersCount === 0) {
                          // No eligible members, just return the updated status
                          return of(updatedStatus);
                        }

                        // Get all completed members
                        return from(
                          this.supabaseService.getClient()
                            .from('group_sidequest_member_status')
                            .select('member_id')
                            .eq('group_quest_id', groupQuestId)
                            .eq('completed', true)
                        ).pipe(
                          switchMap(completedMembersResponse => {
                            if (completedMembersResponse.error) {
                              console.error('GroupSideQuestService: Error getting completed members:', completedMembersResponse.error);
                              return throwError(() => new Error(completedMembersResponse.error.message));
                            }

                            const completedMembers = completedMembersResponse.data;
                            const completedMemberIds = completedMembers.map(m => m.member_id);

                            // Check if all eligible members have completed the quest
                            // Count how many eligible members have completed the quest
                            const completedEligibleCount = eligibleMemberIds.filter(id => completedMemberIds.includes(id)).length;

                            console.log(`GroupSideQuestService: Completed eligible members: ${completedEligibleCount}/${eligibleMembersCount}`);

                            // All eligible members must complete the quest for it to be considered completed
                            const allCompleted = completedEligibleCount === eligibleMembersCount && eligibleMembersCount > 0;

                            // Get the current group quest to check if it was previously all completed
                            return from(
                              this.supabaseService.getClient()
                                .from('group_sidequests')
                                .select('*')
                                .eq('id', groupQuestId)
                                .single()
                            ).pipe(
                              switchMap(groupQuestDetailResponse => {
                                if (groupQuestDetailResponse.error) {
                                  console.error('GroupSideQuestService: Error getting group quest details:', groupQuestDetailResponse.error);
                                  return throwError(() => new Error(groupQuestDetailResponse.error.message));
                                }

                                const groupQuest = groupQuestDetailResponse.data;
                                const wasAllCompleted = groupQuest.completed;
                                const category = groupQuest.category;

                                console.log(`GroupSideQuestService: Current quest state - ID: ${groupQuestId}, Streak: ${groupQuest.streak}, Was completed: ${wasAllCompleted}, All completed now: ${allCompleted}`);

                                // Determine if we need to update the streak and XP
                                if (allCompleted && !wasAllCompleted) {
                                  // All members have completed the quest, increase streak and add XP
                                  const xpToAdd = eligibleMembersCount * 2; // 2 XP per eligible member

                                  // Update the group quest
                                  console.log(`GroupSideQuestService: Increasing streak from ${groupQuest.streak} to ${groupQuest.streak + 1}`);
                                  return from(
                                    this.supabaseService.getClient()
                                      .from('group_sidequests')
                                      .update({
                                        streak: groupQuest.streak + 1,
                                        completed: true,
                                        last_completed_date: today
                                      })
                                      .eq('id', groupQuestId)
                                  ).pipe(
                                    switchMap(() => {
                                      // Update the group XP
                                      return from(
                                        this.supabaseService.getClient()
                                          .from('groups')
                                          .select('*')
                                          .eq('id', groupId)
                                          .single()
                                      ).pipe(
                                        switchMap(groupXpResponse => {
                                          if (groupXpResponse.error) {
                                            console.error('GroupSideQuestService: Error getting group XP:', groupXpResponse.error);
                                            return throwError(() => new Error(groupXpResponse.error.message));
                                          }

                                          // Get the current XP value based on category
                                          let currentXp = 0;
                                          if (category === 'strength') {
                                            currentXp = groupXpResponse.data.strength_xp || 0;
                                          } else if (category === 'money') {
                                            currentXp = groupXpResponse.data.money_xp || 0;
                                          } else if (category === 'health') {
                                            currentXp = groupXpResponse.data.health_xp || 0;
                                          } else if (category === 'knowledge') {
                                            currentXp = groupXpResponse.data.knowledge_xp || 0;
                                          }
                                          const newXp = currentXp + xpToAdd;

                                          // Update the group XP directly in the database
                                          return from(
                                            this.supabaseService.getClient()
                                              .from('groups')
                                              .update({
                                                [`${category}_xp`]: newXp
                                              })
                                              .eq('id', groupId)
                                          ).pipe(
                                            switchMap(() => {
                                              // Verify the update by fetching the updated group data
                                              return from(
                                                this.supabaseService.getClient()
                                                  .from('groups')
                                                  .select('*')
                                                  .eq('id', groupId)
                                                  .single()
                                              ).pipe(
                                                map(verifyResponse => {
                                                  if (verifyResponse.error) {
                                                    console.error('GroupSideQuestService: Error verifying group XP update:', verifyResponse.error);
                                                  } else {
                                                    // Log the actual updated value
                                                    let updatedXp = 0;
                                                    if (category === 'strength') {
                                                      updatedXp = verifyResponse.data.strength_xp || 0;
                                                    } else if (category === 'money') {
                                                      updatedXp = verifyResponse.data.money_xp || 0;
                                                    } else if (category === 'health') {
                                                      updatedXp = verifyResponse.data.health_xp || 0;
                                                    } else if (category === 'knowledge') {
                                                      updatedXp = verifyResponse.data.knowledge_xp || 0;
                                                    }
                                                    console.log(`GroupSideQuestService: Added ${xpToAdd} XP to group ${groupId} ${category}_xp from ${currentXp} to ${newXp}, verified value: ${updatedXp}`);
                                                  }
                                                  return updatedStatus;
                                                })
                                              );
                                            })
                                          );
                                        })
                                      );
                                    })
                                  );
                                } else if (!allCompleted && wasAllCompleted) {
                                  // Quest was previously completed but now it's not, decrease streak and subtract XP
                                  const xpToSubtract = -1 * eligibleMembersCount * 2; // -2 XP per eligible member

                                  // Update the group quest
                                  const newStreak = Math.max(0, groupQuest.streak - 1);
                                  console.log(`GroupSideQuestService: Decreasing streak from ${groupQuest.streak} to ${newStreak}`);
                                  return from(
                                    this.supabaseService.getClient()
                                      .from('group_sidequests')
                                      .update({
                                        streak: newStreak,
                                        completed: false
                                      })
                                      .eq('id', groupQuestId)
                                  ).pipe(
                                    switchMap(() => {
                                      // Update the group XP
                                      return from(
                                        this.supabaseService.getClient()
                                          .from('groups')
                                          .select('*')
                                          .eq('id', groupId)
                                          .single()
                                      ).pipe(
                                        switchMap(groupXpResponse => {
                                          if (groupXpResponse.error) {
                                            console.error('GroupSideQuestService: Error getting group XP:', groupXpResponse.error);
                                            return throwError(() => new Error(groupXpResponse.error.message));
                                          }

                                          // Get the current XP value based on category
                                          let currentXp = 0;
                                          if (category === 'strength') {
                                            currentXp = groupXpResponse.data.strength_xp || 0;
                                          } else if (category === 'money') {
                                            currentXp = groupXpResponse.data.money_xp || 0;
                                          } else if (category === 'health') {
                                            currentXp = groupXpResponse.data.health_xp || 0;
                                          } else if (category === 'knowledge') {
                                            currentXp = groupXpResponse.data.knowledge_xp || 0;
                                          }
                                          const newXp = Math.max(0, currentXp + xpToSubtract);

                                          // Update the group XP directly in the database
                                          return from(
                                            this.supabaseService.getClient()
                                              .from('groups')
                                              .update({
                                                [`${category}_xp`]: newXp
                                              })
                                              .eq('id', groupId)
                                          ).pipe(
                                            switchMap(() => {
                                              // Verify the update by fetching the updated group data
                                              return from(
                                                this.supabaseService.getClient()
                                                  .from('groups')
                                                  .select('*')
                                                  .eq('id', groupId)
                                                  .single()
                                              ).pipe(
                                                map(verifyResponse => {
                                                  if (verifyResponse.error) {
                                                    console.error('GroupSideQuestService: Error verifying group XP update:', verifyResponse.error);
                                                  } else {
                                                    // Log the actual updated value
                                                    let updatedXp = 0;
                                                    if (category === 'strength') {
                                                      updatedXp = verifyResponse.data.strength_xp || 0;
                                                    } else if (category === 'money') {
                                                      updatedXp = verifyResponse.data.money_xp || 0;
                                                    } else if (category === 'health') {
                                                      updatedXp = verifyResponse.data.health_xp || 0;
                                                    } else if (category === 'knowledge') {
                                                      updatedXp = verifyResponse.data.knowledge_xp || 0;
                                                    }
                                                    console.log(`GroupSideQuestService: Subtracted ${Math.abs(xpToSubtract)} XP from group ${groupId} ${category}_xp from ${currentXp} to ${newXp}, verified value: ${updatedXp}`);
                                                  }
                                                  return updatedStatus;
                                                })
                                              );
                                            })
                                          );
                                        })
                                      );
                                    })
                                  );
                                } else {
                                  // No change in completion status, just return the updated status
                                  return of(updatedStatus);
                                }
                              })
                            );
                          })
                        );
                      })
                    );
                  })
                );
              })
            );
          })
        );
      })
    );
  }

  /**
   * Ensure a group has a daily side quest
   * If one doesn't exist, create it
   */
  ensureGroupHasDailySideQuest(groupId: string): Observable<GroupDailyQuest | null> {
    console.log('GroupSideQuestService: Ensuring group has a daily side quest:', groupId);

    // First, check if the group already has a side quest
    return this.getLatestGroupSideQuest(groupId).pipe(
      switchMap(existingSideQuest => {
        if (existingSideQuest) {
          console.log('GroupSideQuestService: Group already has a side quest:', existingSideQuest);

          // Check if it's from today
          const today = new Date().toISOString().split('T')[0];
          const sideQuestDate = existingSideQuest.date_assigned?.split('T')[0];

          if (sideQuestDate === today) {
            // It's today's side quest, convert it to GroupDailyQuest
            // Create a proper GroupDailyQuest object
            const dailyQuest: GroupDailyQuest = {
              id: existingSideQuest.id,
              group_id: existingSideQuest.group_id,
              streak: existingSideQuest.streak,
              completed: existingSideQuest.completed,
              value_achieved: existingSideQuest.value_achieved,
              date_assigned: existingSideQuest.date_assigned,
              last_completed_date: existingSideQuest.last_completed_date,
              category: existingSideQuest.category,
              current_quest: {
                id: existingSideQuest.current_quest?.id || '',
                name: existingSideQuest.current_quest?.name || '',
                description: existingSideQuest.current_quest?.description,
                goal_value: existingSideQuest.current_quest?.goal_value || 0,
                goal_unit: existingSideQuest.current_quest?.goal_unit || 'count',
                emoji: existingSideQuest.current_quest?.emoji || '🎯'
              },
              eligible_members_count: 0,
              completed_members_count: 0
            };

            // Get eligible and completed members counts
            return from(
              this.supabaseService.getClient()
                .from('group_members')
                .select('user_id')
                .eq('group_id', groupId)
                .lt('joined_date', today)
            ).pipe(
              switchMap(eligibleMembersResponse => {
                if (eligibleMembersResponse.error) {
                  console.error('GroupSideQuestService: Error getting eligible members:', eligibleMembersResponse.error);
                  return of(dailyQuest);
                }

                const eligibleMembers = eligibleMembersResponse.data;
                dailyQuest.eligible_members_count = eligibleMembers.length;

                return from(
                  this.supabaseService.getClient()
                    .from('group_sidequest_member_status')
                    .select('member_id')
                    .eq('group_quest_id', existingSideQuest.id)
                    .eq('completed', true)
                ).pipe(
                  map(completedMembersResponse => {
                    if (completedMembersResponse.error) {
                      console.error('GroupSideQuestService: Error getting completed members:', completedMembersResponse.error);
                      return dailyQuest;
                    }

                    const completedMembers = completedMembersResponse.data;
                    dailyQuest.completed_members_count = completedMembers.length;

                    return dailyQuest;
                  })
                );
              })
            );
          } else {
            // It's an old side quest, create a new one
            console.log('GroupSideQuestService: Side quest is old, creating a new one');
            return this.createGroupSideQuest(groupId);
          }
        } else {
          // No side quest exists, create one
          console.log('GroupSideQuestService: No side quest found, creating one');
          return this.createGroupSideQuest(groupId);
        }
      })
    );
  }

  /**
   * Create or update a group side quest
   */
  private createGroupSideQuest(groupId: string): Observable<GroupDailyQuest | null> {
    console.log('GroupSideQuestService: Creating/updating side quest for group:', groupId);

    // Get a random side quest from the pool
    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .order('id', { ascending: false })
    ).pipe(
      switchMap(poolResponse => {
        if (poolResponse.error) {
          console.error('GroupSideQuestService: Error getting side quest pool:', poolResponse.error);
          return of(null);
        }

        if (!poolResponse.data || poolResponse.data.length === 0) {
          console.error('GroupSideQuestService: No side quests in pool');
          return of(null);
        }

        // Pick a random side quest from the pool
        const randomIndex = Math.floor(Math.random() * poolResponse.data.length);
        const selectedQuest = poolResponse.data[randomIndex];

        console.log('GroupSideQuestService: Selected random side quest:', selectedQuest);

        // Create the side quest data
        const today = new Date().toISOString().split('T')[0];
        const sideQuestData = {
          current_quest_id: selectedQuest.id,
          streak: 0,
          date_assigned: today,
          completed: false,
          value_achieved: 0,
          category: selectedQuest.category
        };

        // First check if a side quest already exists for this group
        return from(
          this.supabaseService.getClient()
            .from('group_sidequests')
            .select('id')
            .eq('group_id', groupId)
            .maybeSingle()
        ).pipe(
          switchMap(existingQuestResponse => {
            if (existingQuestResponse.error) {
              console.error('GroupSideQuestService: Error checking for existing side quest:', existingQuestResponse.error);
              return of(null);
            }

            if (existingQuestResponse.data) {
              // Side quest exists, update it
              console.log('GroupSideQuestService: Updating existing side quest for group:', groupId);
              return from(
                this.supabaseService.getClient()
                  .from('group_sidequests')
                  .update(sideQuestData)
                  .eq('group_id', groupId)
                  .select()
                  .single()
              );
            } else {
              // No side quest exists, create a new one
              console.log('GroupSideQuestService: Creating new side quest for group:', groupId);
              return from(
                this.supabaseService.getClient()
                  .from('group_sidequests')
                  .insert({
                    group_id: groupId,
                    ...sideQuestData
                  })
                  .select()
                  .single()
              );
            }
          })
        ).pipe(
          switchMap(createResponse => {
            if (!createResponse || createResponse.error) {
              console.error('GroupSideQuestService: Error creating side quest:', createResponse?.error);
              return of(null);
            }

            console.log('GroupSideQuestService: Created side quest:', createResponse.data);

            // Get all members of the group
            return from(
              this.supabaseService.getClient()
                .from('group_members')
                .select('user_id')
                .eq('group_id', groupId)
            ).pipe(
              switchMap(membersResponse => {
                if (membersResponse.error) {
                  console.error('GroupSideQuestService: Error getting group members:', membersResponse.error);
                  return of(null);
                }

                const members = membersResponse.data;
                console.log(`GroupSideQuestService: Found ${members.length} members for group:`, groupId);

                if (members.length === 0) {
                  // No members, just return the created quest with proper conversion
                  const createdSideQuest: GroupSideQuest = {
                    ...createResponse.data,
                    current_quest: selectedQuest
                  };

                  // Create a proper GroupDailyQuest object
                  const dailyQuest: GroupDailyQuest = {
                    id: createdSideQuest.id,
                    group_id: createdSideQuest.group_id,
                    streak: createdSideQuest.streak,
                    completed: createdSideQuest.completed,
                    value_achieved: createdSideQuest.value_achieved,
                    date_assigned: createdSideQuest.date_assigned,
                    last_completed_date: createdSideQuest.last_completed_date,
                    category: createdSideQuest.category,
                    current_quest: {
                      id: selectedQuest.id || '',
                      name: selectedQuest.name || '',
                      description: selectedQuest.description,
                      goal_value: selectedQuest.goal_value || 0,
                      goal_unit: selectedQuest.goal_unit || 'count',
                      emoji: selectedQuest.emoji || '🎯'
                    },
                    eligible_members_count: 0,
                    completed_members_count: 0
                  };

                  return of(dailyQuest);
                }

                // Create member statuses for all members
                const memberStatusPromises = members.map(member => {
                  return this.supabaseService.getClient()
                    .from('group_sidequest_member_status')
                    .insert({
                      group_quest_id: createResponse.data.id,
                      member_id: member.user_id,
                      completed: false,
                      value_achieved: 0
                    })
                    .select();
                });

                // Wait for all member statuses to be created
                return from(Promise.all(memberStatusPromises)).pipe(
                  map(() => {
                    console.log('GroupSideQuestService: Created member statuses for all members');

                    // Create a proper GroupDailyQuest object
                    const createdSideQuest: GroupSideQuest = {
                      ...createResponse.data,
                      current_quest: selectedQuest
                    };

                    // Create a proper GroupDailyQuest object
                    const dailyQuest: GroupDailyQuest = {
                      id: createdSideQuest.id,
                      group_id: createdSideQuest.group_id,
                      streak: createdSideQuest.streak,
                      completed: createdSideQuest.completed,
                      value_achieved: createdSideQuest.value_achieved,
                      date_assigned: createdSideQuest.date_assigned,
                      last_completed_date: createdSideQuest.last_completed_date,
                      category: createdSideQuest.category,
                      current_quest: {
                        id: selectedQuest.id || '',
                        name: selectedQuest.name || '',
                        description: selectedQuest.description,
                        goal_value: selectedQuest.goal_value || 0,
                        goal_unit: selectedQuest.goal_unit || 'count',
                        emoji: selectedQuest.emoji || '🎯'
                      },
                      eligible_members_count: members.length,
                      completed_members_count: 0
                    };

                    return dailyQuest;
                  })
                );
              })
            );
          })
        );
      })
    );
  }



  /**
   * Update group XP
   */
  updateGroupXp(groupId: string, category: string, newXpValue: number): Observable<any> {
    console.log(`GroupSideQuestService: Updating ${category}_xp to ${newXpValue} for group ${groupId} via direct update`);

    return from(
      this.supabaseService.getClient()
        .from('groups')
        .update({
          [`${category}_xp`]: newXpValue
        })
        .eq('id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error(`GroupSideQuestService: Error updating ${category}_xp:`, response.error);
          throw new Error(response.error.message);
        }

        console.log(`GroupSideQuestService: Successfully updated ${category}_xp to ${newXpValue}`);
        return response.data;
      }),
      catchError(error => {
        console.error(`GroupSideQuestService: Error updating ${category}_xp:`, error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Import side quests from JSON
   */
  importSideQuestsFromJson(): Observable<boolean> {
    console.log('GroupSideQuestService: Importing side quests from JSON');

    return this.http.get<any[]>('assets/data/group-sidequest-pool.json').pipe(
      switchMap(sideQuests => {
        console.log(`GroupSideQuestService: Found ${sideQuests.length} side quests in JSON`);

        // Insert each side quest into the database
        const insertPromises = sideQuests.map(sideQuest => {
          return this.supabaseService.getClient()
            .from('group_sidequest_pool')
            .insert({
              name: sideQuest.name,
              description: sideQuest.description,
              emoji: sideQuest.emoji,
              goal_value: sideQuest.goal_value,
              category: sideQuest.category
            });
        });

        return from(Promise.all(insertPromises)).pipe(
          map(() => {
            console.log('GroupSideQuestService: Successfully imported all side quests');
            return true;
          }),
          catchError(error => {
            console.error('GroupSideQuestService: Error importing side quests:', error);
            return of(false);
          })
        );
      }),
      catchError(error => {
        console.error('GroupSideQuestService: Error loading JSON file:', error);
        return of(false);
      })
    );
  }
}