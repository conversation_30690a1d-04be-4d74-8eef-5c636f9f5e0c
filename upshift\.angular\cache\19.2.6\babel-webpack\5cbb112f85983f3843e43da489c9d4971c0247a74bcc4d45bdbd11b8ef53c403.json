{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement, c as createEvent, d as readTask, i as forceUpdate, w as writeTask } from './index-527b9e34.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-864101a3.js';\nimport { p as printIonWarning } from './index-738d7504.js';\nimport { b as getIonMode, c as config, a as isPlatform } from './ionic-global-ca86cf32.js';\nimport { i as inheritAriaAttributes, k as hasLazyBuild, c as componentOnReady, j as clamp, s as shallowEqualStringMap } from './helpers-78efeec3.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { a as findIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-e919e353.js';\nimport { c as createKeyboardController } from './keyboard-controller-ec5c2bfa.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { a as attachComponent, d as detachComponent } from './framework-delegate-2eea1763.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { t as transition } from './index-ecb55b8d.js';\nimport './index-a5d50daf.js';\nimport './keyboard-73175e24.js';\nimport './capacitor-59395cbd.js';\nconst appCss = \"html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}\";\nconst IonAppStyle0 = appCss;\nconst App = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  componentDidLoad() {\n    var _this = this;\n    {\n      rIC(/*#__PURE__*/_asyncToGenerator(function* () {\n        const isHybrid = isPlatform(window, 'hybrid');\n        if (!config.getBoolean('_testing')) {\n          import('./index-40894f4b.js').then(module => module.startTapClick(config));\n        }\n        if (config.getBoolean('statusTap', isHybrid)) {\n          import('./status-tap-f6d08e9e.js').then(module => module.startStatusTap());\n        }\n        if (config.getBoolean('inputShims', needInputShims())) {\n          /**\n           * needInputShims() ensures that only iOS and Android\n           * platforms proceed into this block.\n           */\n          const platform = isPlatform(window, 'ios') ? 'ios' : 'android';\n          import('./input-shims-7dc1f6dc.js').then(module => module.startInputShims(config, platform));\n        }\n        const hardwareBackButtonModule = yield import('./hardware-back-button-864101a3.js');\n        const supportsHardwareBackButtonEvents = isHybrid || shouldUseCloseWatcher();\n        if (config.getBoolean('hardwareBackButton', supportsHardwareBackButtonEvents)) {\n          hardwareBackButtonModule.startHardwareBackButton();\n        } else {\n          /**\n           * If an app sets hardwareBackButton: false and experimentalCloseWatcher: true\n           * then the close watcher will not be used.\n           */\n          if (shouldUseCloseWatcher()) {\n            printIonWarning('experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used.');\n          }\n          hardwareBackButtonModule.blockHardwareBackButton();\n        }\n        if (typeof window !== 'undefined') {\n          import('./keyboard-52278bd7.js').then(module => module.startKeyboardAssist(window));\n        }\n        import('./focus-visible-dd40d69f.js').then(module => _this.focusVisible = module.startFocusVisible());\n      }));\n    }\n  }\n  /**\n   * Used to set focus on an element that uses `ion-focusable`.\n   * Do not use this if focusing the element as a result of a keyboard\n   * event as the focus utility should handle this for us. This method\n   * should be used when we want to programmatically focus an element as\n   * a result of another user action. (Ex: We focus the first element\n   * inside of a popover when the user presents it, but the popover is not always\n   * presented as a result of keyboard action.)\n   */\n  setFocus(elements) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.focusVisible) {\n        _this2.focusVisible.setFocus(elements);\n      }\n    })();\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '96715520fd05d6f0e6fa26a8ba78792cfccd4c0a',\n      class: {\n        [mode]: true,\n        'ion-page': true,\n        'force-statusbar-padding': config.getBoolean('_forceStatusbarPadding')\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst needInputShims = () => {\n  /**\n   * iOS always needs input shims\n   */\n  const needsShimsIOS = isPlatform(window, 'ios') && isPlatform(window, 'mobile');\n  if (needsShimsIOS) {\n    return true;\n  }\n  /**\n   * Android only needs input shims when running\n   * in the browser and only if the browser is using the\n   * new Chrome 108+ resize behavior: https://developer.chrome.com/blog/viewport-resize-behavior/\n   */\n  const isAndroidMobileWeb = isPlatform(window, 'android') && isPlatform(window, 'mobileweb');\n  if (isAndroidMobileWeb) {\n    return true;\n  }\n  return false;\n};\nconst rIC = callback => {\n  if ('requestIdleCallback' in window) {\n    window.requestIdleCallback(callback);\n  } else {\n    setTimeout(callback, 32);\n  }\n};\nApp.style = IonAppStyle0;\nconst buttonsIosCss = \".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}\";\nconst IonButtonsIosStyle0 = buttonsIosCss;\nconst buttonsMdCss = \".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}\";\nconst IonButtonsMdStyle0 = buttonsMdCss;\nconst Buttons = /*#__PURE__*/(() => {\n  let Buttons = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.collapse = false;\n    }\n    render() {\n      const mode = getIonMode(this);\n      return h(Host, {\n        key: '58c1fc5eb867d0731c63549b1ccb3ec3bbbe6e1b',\n        class: {\n          [mode]: true,\n          ['buttons-collapse']: this.collapse\n        }\n      }, h(\"slot\", {\n        key: '0c8f95b9840c8fa0c4e50be84c5159620a3eb5c8'\n      }));\n    }\n  };\n  Buttons.style = {\n    ios: IonButtonsIosStyle0,\n    md: IonButtonsMdStyle0\n  };\n  return Buttons;\n})();\nconst contentCss = \":host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:\\\"\\\"}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}\";\nconst IonContentStyle0 = contentCss;\nconst Content = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionScrollStart = createEvent(this, \"ionScrollStart\", 7);\n    this.ionScroll = createEvent(this, \"ionScroll\", 7);\n    this.ionScrollEnd = createEvent(this, \"ionScrollEnd\", 7);\n    this.watchDog = null;\n    this.isScrolling = false;\n    this.lastScroll = 0;\n    this.queued = false;\n    this.cTop = -1;\n    this.cBottom = -1;\n    this.isMainContent = true;\n    this.resizeTimeout = null;\n    this.inheritedAttributes = {};\n    this.tabsElement = null;\n    // Detail is used in a hot loop in the scroll event, by allocating it here\n    // V8 will be able to inline any read/write to it since it's a monomorphic class.\n    // https://mrale.ph/blog/2015/01/11/whats-up-with-monomorphism.html\n    this.detail = {\n      scrollTop: 0,\n      scrollLeft: 0,\n      type: 'scroll',\n      event: undefined,\n      startX: 0,\n      startY: 0,\n      startTime: 0,\n      currentX: 0,\n      currentY: 0,\n      velocityX: 0,\n      velocityY: 0,\n      deltaX: 0,\n      deltaY: 0,\n      currentTime: 0,\n      data: undefined,\n      isScrolling: true\n    };\n    this.color = undefined;\n    this.fullscreen = false;\n    this.fixedSlotPlacement = 'after';\n    this.forceOverscroll = undefined;\n    this.scrollX = false;\n    this.scrollY = true;\n    this.scrollEvents = false;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  connectedCallback() {\n    this.isMainContent = this.el.closest('ion-menu, ion-popover, ion-modal') === null;\n    /**\n     * The fullscreen content offsets need to be\n     * computed after the tab bar has loaded. Since\n     * lazy evaluation means components are not hydrated\n     * at the same time, we need to wait for the ionTabBarLoaded\n     * event to fire. This does not impact dist-custom-elements\n     * because there is no hydration there.\n     */\n    if (hasLazyBuild(this.el)) {\n      /**\n       * We need to cache the reference to the tabs.\n       * If just the content is unmounted then we won't\n       * be able to query for the closest tabs on disconnectedCallback\n       * since the content has been removed from the DOM tree.\n       */\n      const closestTabs = this.tabsElement = this.el.closest('ion-tabs');\n      if (closestTabs !== null) {\n        /**\n         * When adding and removing the event listener\n         * we need to make sure we pass the same function reference\n         * otherwise the event listener will not be removed properly.\n         * We can't only pass `this.resize` because \"this\" in the function\n         * context becomes a reference to IonTabs instead of IonContent.\n         *\n         * Additionally, we listen for ionTabBarLoaded on the IonTabs\n         * instance rather than the IonTabBar instance. It's possible for\n         * a tab bar to be conditionally rendered/mounted. Since ionTabBarLoaded\n         * bubbles, we can catch any instances of child tab bars loading by listening\n         * on IonTabs.\n         */\n        this.tabsLoadCallback = () => this.resize();\n        closestTabs.addEventListener('ionTabBarLoaded', this.tabsLoadCallback);\n      }\n    }\n  }\n  disconnectedCallback() {\n    this.onScrollEnd();\n    if (hasLazyBuild(this.el)) {\n      /**\n       * The event listener and tabs caches need to\n       * be cleared otherwise this will create a memory\n       * leak where the IonTabs instance can never be\n       * garbage collected.\n       */\n      const {\n        tabsElement,\n        tabsLoadCallback\n      } = this;\n      if (tabsElement !== null && tabsLoadCallback !== undefined) {\n        tabsElement.removeEventListener('ionTabBarLoaded', tabsLoadCallback);\n      }\n      this.tabsElement = null;\n      this.tabsLoadCallback = undefined;\n    }\n  }\n  /**\n   * Rotating certain devices can update\n   * the safe area insets. As a result,\n   * the fullscreen feature on ion-content\n   * needs to be recalculated.\n   *\n   * We listen for \"resize\" because we\n   * do not care what the orientation of\n   * the device is. Other APIs\n   * such as ScreenOrientation or\n   * the deviceorientation event must have\n   * permission from the user first whereas\n   * the \"resize\" event does not.\n   *\n   * We also throttle the callback to minimize\n   * thrashing when quickly resizing a window.\n   */\n  onResize() {\n    if (this.resizeTimeout) {\n      clearTimeout(this.resizeTimeout);\n      this.resizeTimeout = null;\n    }\n    this.resizeTimeout = setTimeout(() => {\n      /**\n       * Resize should only happen\n       * if the content is visible.\n       * When the content is hidden\n       * then offsetParent will be null.\n       */\n      if (this.el.offsetParent === null) {\n        return;\n      }\n      this.resize();\n    }, 100);\n  }\n  shouldForceOverscroll() {\n    const {\n      forceOverscroll\n    } = this;\n    const mode = getIonMode(this);\n    return forceOverscroll === undefined ? mode === 'ios' && isPlatform('ios') : forceOverscroll;\n  }\n  resize() {\n    /**\n     * Only force update if the component is rendered in a browser context.\n     * Using `forceUpdate` in a server context with pre-rendering can lead to an infinite loop.\n     * The `hydrateDocument` function in `@stencil/core` will render the `ion-content`, but\n     * `forceUpdate` will trigger another render, locking up the server.\n     *\n     * TODO: Remove if STENCIL-834 determines Stencil will account for this.\n     */\n    {\n      if (this.fullscreen) {\n        readTask(() => this.readDimensions());\n      } else if (this.cTop !== 0 || this.cBottom !== 0) {\n        this.cTop = this.cBottom = 0;\n        forceUpdate(this);\n      }\n    }\n  }\n  readDimensions() {\n    const page = getPageElement(this.el);\n    const top = Math.max(this.el.offsetTop, 0);\n    const bottom = Math.max(page.offsetHeight - top - this.el.offsetHeight, 0);\n    const dirty = top !== this.cTop || bottom !== this.cBottom;\n    if (dirty) {\n      this.cTop = top;\n      this.cBottom = bottom;\n      forceUpdate(this);\n    }\n  }\n  onScroll(ev) {\n    const timeStamp = Date.now();\n    const shouldStart = !this.isScrolling;\n    this.lastScroll = timeStamp;\n    if (shouldStart) {\n      this.onScrollStart();\n    }\n    if (!this.queued && this.scrollEvents) {\n      this.queued = true;\n      readTask(ts => {\n        this.queued = false;\n        this.detail.event = ev;\n        updateScrollDetail(this.detail, this.scrollEl, ts, shouldStart);\n        this.ionScroll.emit(this.detail);\n      });\n    }\n  }\n  /**\n   * Get the element where the actual scrolling takes place.\n   * This element can be used to subscribe to `scroll` events or manually modify\n   * `scrollTop`. However, it's recommended to use the API provided by `ion-content`:\n   *\n   * i.e. Using `ionScroll`, `ionScrollStart`, `ionScrollEnd` for scrolling events\n   * and `scrollToPoint()` to scroll the content into a certain point.\n   */\n  getScrollElement() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n       * scrollEl won't be defined yet with the custom elements build, so wait for it to load in.\n       */\n      if (!_this3.scrollEl) {\n        yield new Promise(resolve => componentOnReady(_this3.el, resolve));\n      }\n      return Promise.resolve(_this3.scrollEl);\n    })();\n  }\n  /**\n   * Returns the background content element.\n   * @internal\n   */\n  getBackgroundElement() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.backgroundContentEl) {\n        yield new Promise(resolve => componentOnReady(_this4.el, resolve));\n      }\n      return Promise.resolve(_this4.backgroundContentEl);\n    })();\n  }\n  /**\n   * Scroll to the top of the component.\n   *\n   * @param duration The amount of time to take scrolling to the top. Defaults to `0`.\n   */\n  scrollToTop(duration = 0) {\n    return this.scrollToPoint(undefined, 0, duration);\n  }\n  /**\n   * Scroll to the bottom of the component.\n   *\n   * @param duration The amount of time to take scrolling to the bottom. Defaults to `0`.\n   */\n  scrollToBottom(duration = 0) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const scrollEl = yield _this5.getScrollElement();\n      const y = scrollEl.scrollHeight - scrollEl.clientHeight;\n      return _this5.scrollToPoint(undefined, y, duration);\n    })();\n  }\n  /**\n   * Scroll by a specified X/Y distance in the component.\n   *\n   * @param x The amount to scroll by on the horizontal axis.\n   * @param y The amount to scroll by on the vertical axis.\n   * @param duration The amount of time to take scrolling by that amount.\n   */\n  scrollByPoint(x, y, duration) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const scrollEl = yield _this6.getScrollElement();\n      return _this6.scrollToPoint(x + scrollEl.scrollLeft, y + scrollEl.scrollTop, duration);\n    })();\n  }\n  /**\n   * Scroll to a specified X/Y location in the component.\n   *\n   * @param x The point to scroll to on the horizontal axis.\n   * @param y The point to scroll to on the vertical axis.\n   * @param duration The amount of time to take scrolling to that point. Defaults to `0`.\n   */\n  scrollToPoint(x, y, duration = 0) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const el = yield _this7.getScrollElement();\n      if (duration < 32) {\n        if (y != null) {\n          el.scrollTop = y;\n        }\n        if (x != null) {\n          el.scrollLeft = x;\n        }\n        return;\n      }\n      let resolve;\n      let startTime = 0;\n      const promise = new Promise(r => resolve = r);\n      const fromY = el.scrollTop;\n      const fromX = el.scrollLeft;\n      const deltaY = y != null ? y - fromY : 0;\n      const deltaX = x != null ? x - fromX : 0;\n      // scroll loop\n      const step = timeStamp => {\n        const linearTime = Math.min(1, (timeStamp - startTime) / duration) - 1;\n        const easedT = Math.pow(linearTime, 3) + 1;\n        if (deltaY !== 0) {\n          el.scrollTop = Math.floor(easedT * deltaY + fromY);\n        }\n        if (deltaX !== 0) {\n          el.scrollLeft = Math.floor(easedT * deltaX + fromX);\n        }\n        if (easedT < 1) {\n          // do not use DomController here\n          // must use nativeRaf in order to fire in the next frame\n          requestAnimationFrame(step);\n        } else {\n          resolve();\n        }\n      };\n      // chill out for a frame first\n      requestAnimationFrame(ts => {\n        startTime = ts;\n        step(ts);\n      });\n      return promise;\n    })();\n  }\n  onScrollStart() {\n    this.isScrolling = true;\n    this.ionScrollStart.emit({\n      isScrolling: true\n    });\n    if (this.watchDog) {\n      clearInterval(this.watchDog);\n    }\n    // watchdog\n    this.watchDog = setInterval(() => {\n      if (this.lastScroll < Date.now() - 120) {\n        this.onScrollEnd();\n      }\n    }, 100);\n  }\n  onScrollEnd() {\n    if (this.watchDog) clearInterval(this.watchDog);\n    this.watchDog = null;\n    if (this.isScrolling) {\n      this.isScrolling = false;\n      this.ionScrollEnd.emit({\n        isScrolling: false\n      });\n    }\n  }\n  render() {\n    const {\n      fixedSlotPlacement,\n      inheritedAttributes,\n      isMainContent,\n      scrollX,\n      scrollY,\n      el\n    } = this;\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    const mode = getIonMode(this);\n    const forceOverscroll = this.shouldForceOverscroll();\n    const transitionShadow = mode === 'ios';\n    this.resize();\n    return h(Host, Object.assign({\n      key: 'f2a24aa66dbf5c76f9d4b06f708eb73cadc239df',\n      role: isMainContent ? 'main' : undefined,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'content-sizing': hostContext('ion-popover', this.el),\n        overscroll: forceOverscroll,\n        [`content-${rtl}`]: true\n      }),\n      style: {\n        '--offset-top': `${this.cTop}px`,\n        '--offset-bottom': `${this.cBottom}px`\n      }\n    }, inheritedAttributes), h(\"div\", {\n      key: '6480ca7648b278abb36477b3838bccbcd4995e2a',\n      ref: el => this.backgroundContentEl = el,\n      id: \"background-content\",\n      part: \"background\"\n    }), fixedSlotPlacement === 'before' ? h(\"slot\", {\n      name: \"fixed\"\n    }) : null, h(\"div\", {\n      key: '29a23b663f5f0215bb000820c01e1814c0d55985',\n      class: {\n        'inner-scroll': true,\n        'scroll-x': scrollX,\n        'scroll-y': scrollY,\n        overscroll: (scrollX || scrollY) && forceOverscroll\n      },\n      ref: scrollEl => this.scrollEl = scrollEl,\n      onScroll: this.scrollEvents ? ev => this.onScroll(ev) : undefined,\n      part: \"scroll\"\n    }, h(\"slot\", {\n      key: '0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0'\n    })), transitionShadow ? h(\"div\", {\n      class: \"transition-effect\"\n    }, h(\"div\", {\n      class: \"transition-cover\"\n    }), h(\"div\", {\n      class: \"transition-shadow\"\n    })) : null, fixedSlotPlacement === 'after' ? h(\"slot\", {\n      name: \"fixed\"\n    }) : null);\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst getParentElement = el => {\n  var _a;\n  if (el.parentElement) {\n    // normal element with a parent element\n    return el.parentElement;\n  }\n  if ((_a = el.parentNode) === null || _a === void 0 ? void 0 : _a.host) {\n    // shadow dom's document fragment\n    return el.parentNode.host;\n  }\n  return null;\n};\nconst getPageElement = el => {\n  const tabs = el.closest('ion-tabs');\n  if (tabs) {\n    return tabs;\n  }\n  /**\n   * If we're in a popover, we need to use its wrapper so we can account for space\n   * between the popover and the edges of the screen. But if the popover contains\n   * its own page element, we should use that instead.\n   */\n  const page = el.closest('ion-app, ion-page, .ion-page, page-inner, .popover-content');\n  if (page) {\n    return page;\n  }\n  return getParentElement(el);\n};\n// ******** DOM READ ****************\nconst updateScrollDetail = (detail, el, timestamp, shouldStart) => {\n  const prevX = detail.currentX;\n  const prevY = detail.currentY;\n  const prevT = detail.currentTime;\n  const currentX = el.scrollLeft;\n  const currentY = el.scrollTop;\n  const timeDelta = timestamp - prevT;\n  if (shouldStart) {\n    // remember the start positions\n    detail.startTime = timestamp;\n    detail.startX = currentX;\n    detail.startY = currentY;\n    detail.velocityX = detail.velocityY = 0;\n  }\n  detail.currentTime = timestamp;\n  detail.currentX = detail.scrollLeft = currentX;\n  detail.currentY = detail.scrollTop = currentY;\n  detail.deltaX = currentX - detail.startX;\n  detail.deltaY = currentY - detail.startY;\n  if (timeDelta > 0 && timeDelta < 100) {\n    const velocityX = (currentX - prevX) / timeDelta;\n    const velocityY = (currentY - prevY) / timeDelta;\n    detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n    detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n  }\n};\nContent.style = IonContentStyle0;\nconst handleFooterFade = (scrollEl, baseEl) => {\n  readTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    const maxScroll = scrollEl.scrollHeight - scrollEl.clientHeight;\n    /**\n     * Toolbar background will fade\n     * out over fadeDuration in pixels.\n     */\n    const fadeDuration = 10;\n    /**\n     * Begin fading out maxScroll - 30px\n     * from the bottom of the content.\n     * Also determine how close we are\n     * to starting the fade. If we are\n     * before the starting point, the\n     * scale value will get clamped to 0.\n     * If we are after the maxScroll (rubber\n     * band scrolling), the scale value will\n     * get clamped to 1.\n     */\n    const fadeStart = maxScroll - fadeDuration;\n    const distanceToStart = scrollTop - fadeStart;\n    const scale = clamp(0, 1 - distanceToStart / fadeDuration, 1);\n    writeTask(() => {\n      baseEl.style.setProperty('--opacity-scale', scale.toString());\n    });\n  });\n};\nconst footerIosCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}\";\nconst IonFooterIosStyle0 = footerIosCss;\nconst footerMdCss = \"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst IonFooterMdStyle0 = footerMdCss;\nconst Footer = /*#__PURE__*/(() => {\n  let Footer = class {\n    constructor(hostRef) {\n      var _this8 = this;\n      registerInstance(this, hostRef);\n      this.keyboardCtrl = null;\n      this.checkCollapsibleFooter = () => {\n        const mode = getIonMode(this);\n        if (mode !== 'ios') {\n          return;\n        }\n        const {\n          collapse\n        } = this;\n        const hasFade = collapse === 'fade';\n        this.destroyCollapsibleFooter();\n        if (hasFade) {\n          const pageEl = this.el.closest('ion-app,ion-page,.ion-page,page-inner');\n          const contentEl = pageEl ? findIonContent(pageEl) : null;\n          if (!contentEl) {\n            printIonContentErrorMsg(this.el);\n            return;\n          }\n          this.setupFadeFooter(contentEl);\n        }\n      };\n      this.setupFadeFooter = /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (contentEl) {\n          const scrollEl = _this8.scrollEl = yield getScrollElement(contentEl);\n          /**\n           * Handle fading of toolbars on scroll\n           */\n          _this8.contentScrollCallback = () => {\n            handleFooterFade(scrollEl, _this8.el);\n          };\n          scrollEl.addEventListener('scroll', _this8.contentScrollCallback);\n          handleFooterFade(scrollEl, _this8.el);\n        });\n        return function (_x) {\n          return _ref2.apply(this, arguments);\n        };\n      }();\n      this.keyboardVisible = false;\n      this.collapse = undefined;\n      this.translucent = false;\n    }\n    componentDidLoad() {\n      this.checkCollapsibleFooter();\n    }\n    componentDidUpdate() {\n      this.checkCollapsibleFooter();\n    }\n    connectedCallback() {\n      var _this9 = this;\n      return _asyncToGenerator(function* () {\n        _this9.keyboardCtrl = yield createKeyboardController(/*#__PURE__*/function () {\n          var _ref3 = _asyncToGenerator(function* (keyboardOpen, waitForResize) {\n            /**\n             * If the keyboard is hiding, then we need to wait\n             * for the webview to resize. Otherwise, the footer\n             * will flicker before the webview resizes.\n             */\n            if (keyboardOpen === false && waitForResize !== undefined) {\n              yield waitForResize;\n            }\n            _this9.keyboardVisible = keyboardOpen; // trigger re-render by updating state\n          });\n          return function (_x2, _x3) {\n            return _ref3.apply(this, arguments);\n          };\n        }());\n      })();\n    }\n    disconnectedCallback() {\n      if (this.keyboardCtrl) {\n        this.keyboardCtrl.destroy();\n      }\n    }\n    destroyCollapsibleFooter() {\n      if (this.scrollEl && this.contentScrollCallback) {\n        this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n        this.contentScrollCallback = undefined;\n      }\n    }\n    render() {\n      const {\n        translucent,\n        collapse\n      } = this;\n      const mode = getIonMode(this);\n      const tabs = this.el.closest('ion-tabs');\n      const tabBar = tabs === null || tabs === void 0 ? void 0 : tabs.querySelector(':scope > ion-tab-bar');\n      return h(Host, {\n        key: 'ddc228f1a1e7fa4f707dccf74db2490ca3241137',\n        role: \"contentinfo\",\n        class: {\n          [mode]: true,\n          // Used internally for styling\n          [`footer-${mode}`]: true,\n          [`footer-translucent`]: translucent,\n          [`footer-translucent-${mode}`]: translucent,\n          ['footer-toolbar-padding']: !this.keyboardVisible && (!tabBar || tabBar.slot !== 'bottom'),\n          [`footer-collapse-${collapse}`]: collapse !== undefined\n        }\n      }, mode === 'ios' && translucent && h(\"div\", {\n        key: 'e16ed4963ff94e06de77eb8038201820af73937c',\n        class: \"footer-background\"\n      }), h(\"slot\", {\n        key: 'f186934febf85d37133d9351a96c1a64b0a4b203'\n      }));\n    }\n    get el() {\n      return getElement(this);\n    }\n  };\n  Footer.style = {\n    ios: IonFooterIosStyle0,\n    md: IonFooterMdStyle0\n  };\n  return Footer;\n})();\nconst TRANSITION = 'all 0.2s ease-in-out';\nconst cloneElement = tagName => {\n  const getCachedEl = document.querySelector(`${tagName}.ion-cloned-element`);\n  if (getCachedEl !== null) {\n    return getCachedEl;\n  }\n  const clonedEl = document.createElement(tagName);\n  clonedEl.classList.add('ion-cloned-element');\n  clonedEl.style.setProperty('display', 'none');\n  document.body.appendChild(clonedEl);\n  return clonedEl;\n};\nconst createHeaderIndex = headerEl => {\n  if (!headerEl) {\n    return;\n  }\n  const toolbars = headerEl.querySelectorAll('ion-toolbar');\n  return {\n    el: headerEl,\n    toolbars: Array.from(toolbars).map(toolbar => {\n      const ionTitleEl = toolbar.querySelector('ion-title');\n      return {\n        el: toolbar,\n        background: toolbar.shadowRoot.querySelector('.toolbar-background'),\n        ionTitleEl,\n        innerTitleEl: ionTitleEl ? ionTitleEl.shadowRoot.querySelector('.toolbar-title') : null,\n        ionButtonsEl: Array.from(toolbar.querySelectorAll('ion-buttons'))\n      };\n    })\n  };\n};\nconst handleContentScroll = (scrollEl, scrollHeaderIndex, contentEl) => {\n  readTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    const scale = clamp(1, 1 + -scrollTop / 500, 1.1);\n    // Native refresher should not cause titles to scale\n    const nativeRefresher = contentEl.querySelector('ion-refresher.refresher-native');\n    if (nativeRefresher === null) {\n      writeTask(() => {\n        scaleLargeTitles(scrollHeaderIndex.toolbars, scale);\n      });\n    }\n  });\n};\nconst setToolbarBackgroundOpacity = (headerEl, opacity) => {\n  /**\n   * Fading in the backdrop opacity\n   * should happen after the large title\n   * has collapsed, so it is handled\n   * by handleHeaderFade()\n   */\n  if (headerEl.collapse === 'fade') {\n    return;\n  }\n  if (opacity === undefined) {\n    headerEl.style.removeProperty('--opacity-scale');\n  } else {\n    headerEl.style.setProperty('--opacity-scale', opacity.toString());\n  }\n};\nconst handleToolbarBorderIntersection = (ev, mainHeaderIndex, scrollTop) => {\n  if (!ev[0].isIntersecting) {\n    return;\n  }\n  /**\n   * There is a bug in Safari where overflow scrolling on a non-body element\n   * does not always reset the scrollTop position to 0 when letting go. It will\n   * set to 1 once the rubber band effect has ended. This causes the background to\n   * appear slightly on certain app setups.\n   *\n   * Additionally, we check if user is rubber banding (scrolling is negative)\n   * as this can mean they are using pull to refresh. Once the refresher starts,\n   * the content is transformed which can cause the intersection observer to erroneously\n   * fire here as well.\n   */\n  const scale = ev[0].intersectionRatio > 0.9 || scrollTop <= 0 ? 0 : (1 - ev[0].intersectionRatio) * 100 / 75;\n  setToolbarBackgroundOpacity(mainHeaderIndex.el, scale === 1 ? undefined : scale);\n};\n/**\n * If toolbars are intersecting, hide the scrollable toolbar content\n * and show the primary toolbar content. If the toolbars are not intersecting,\n * hide the primary toolbar content and show the scrollable toolbar content\n */\nconst handleToolbarIntersection = (ev,\n// TODO(FW-2832): type (IntersectionObserverEntry[] triggers errors which should be sorted)\nmainHeaderIndex, scrollHeaderIndex, scrollEl) => {\n  writeTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    handleToolbarBorderIntersection(ev, mainHeaderIndex, scrollTop);\n    const event = ev[0];\n    const intersection = event.intersectionRect;\n    const intersectionArea = intersection.width * intersection.height;\n    const rootArea = event.rootBounds.width * event.rootBounds.height;\n    const isPageHidden = intersectionArea === 0 && rootArea === 0;\n    const leftDiff = Math.abs(intersection.left - event.boundingClientRect.left);\n    const rightDiff = Math.abs(intersection.right - event.boundingClientRect.right);\n    const isPageTransitioning = intersectionArea > 0 && (leftDiff >= 5 || rightDiff >= 5);\n    if (isPageHidden || isPageTransitioning) {\n      return;\n    }\n    if (event.isIntersecting) {\n      setHeaderActive(mainHeaderIndex, false);\n      setHeaderActive(scrollHeaderIndex);\n    } else {\n      /**\n       * There is a bug with IntersectionObserver on Safari\n       * where `event.isIntersecting === false` when cancelling\n       * a swipe to go back gesture. Checking the intersection\n       * x, y, width, and height provides a workaround. This bug\n       * does not happen when using Safari + Web Animations,\n       * only Safari + CSS Animations.\n       */\n      const hasValidIntersection = intersection.x === 0 && intersection.y === 0 || intersection.width !== 0 && intersection.height !== 0;\n      if (hasValidIntersection && scrollTop > 0) {\n        setHeaderActive(mainHeaderIndex);\n        setHeaderActive(scrollHeaderIndex, false);\n        setToolbarBackgroundOpacity(mainHeaderIndex.el);\n      }\n    }\n  });\n};\nconst setHeaderActive = (headerIndex, active = true) => {\n  const headerEl = headerIndex.el;\n  const toolbars = headerIndex.toolbars;\n  const ionTitles = toolbars.map(toolbar => toolbar.ionTitleEl);\n  if (active) {\n    headerEl.classList.remove('header-collapse-condense-inactive');\n    ionTitles.forEach(ionTitle => {\n      if (ionTitle) {\n        ionTitle.removeAttribute('aria-hidden');\n      }\n    });\n  } else {\n    headerEl.classList.add('header-collapse-condense-inactive');\n    /**\n     * The small title should only be accessed by screen readers\n     * when the large title collapses into the small title due\n     * to scrolling.\n     *\n     * Originally, the header was given `aria-hidden=\"true\"`\n     * but this caused issues with screen readers not being\n     * able to access any focusable elements within the header.\n     */\n    ionTitles.forEach(ionTitle => {\n      if (ionTitle) {\n        ionTitle.setAttribute('aria-hidden', 'true');\n      }\n    });\n  }\n};\nconst scaleLargeTitles = (toolbars = [], scale = 1, transition = false) => {\n  toolbars.forEach(toolbar => {\n    const ionTitle = toolbar.ionTitleEl;\n    const titleDiv = toolbar.innerTitleEl;\n    if (!ionTitle || ionTitle.size !== 'large') {\n      return;\n    }\n    titleDiv.style.transition = transition ? TRANSITION : '';\n    titleDiv.style.transform = `scale3d(${scale}, ${scale}, 1)`;\n  });\n};\nconst handleHeaderFade = (scrollEl, baseEl, condenseHeader) => {\n  readTask(() => {\n    const scrollTop = scrollEl.scrollTop;\n    const baseElHeight = baseEl.clientHeight;\n    const fadeStart = condenseHeader ? condenseHeader.clientHeight : 0;\n    /**\n     * If we are using fade header with a condense\n     * header, then the toolbar backgrounds should\n     * not begin to fade in until the condense\n     * header has fully collapsed.\n     *\n     * Additionally, the main content should not\n     * overflow out of the container until the\n     * condense header has fully collapsed. When\n     * using just the condense header the content\n     * should overflow out of the container.\n     */\n    if (condenseHeader !== null && scrollTop < fadeStart) {\n      baseEl.style.setProperty('--opacity-scale', '0');\n      scrollEl.style.setProperty('clip-path', `inset(${baseElHeight}px 0px 0px 0px)`);\n      return;\n    }\n    const distanceToStart = scrollTop - fadeStart;\n    const fadeDuration = 10;\n    const scale = clamp(0, distanceToStart / fadeDuration, 1);\n    writeTask(() => {\n      scrollEl.style.removeProperty('clip-path');\n      baseEl.style.setProperty('--opacity-scale', scale.toString());\n    });\n  });\n};\nconst headerIosCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}\";\nconst IonHeaderIosStyle0 = headerIosCss;\nconst headerMdCss = \"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}\";\nconst IonHeaderMdStyle0 = headerMdCss;\nconst Header = /*#__PURE__*/(() => {\n  let Header = class {\n    constructor(hostRef) {\n      var _this10 = this;\n      registerInstance(this, hostRef);\n      this.inheritedAttributes = {};\n      this.setupFadeHeader = /*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator(function* (contentEl, condenseHeader) {\n          const scrollEl = _this10.scrollEl = yield getScrollElement(contentEl);\n          /**\n           * Handle fading of toolbars on scroll\n           */\n          _this10.contentScrollCallback = () => {\n            handleHeaderFade(_this10.scrollEl, _this10.el, condenseHeader);\n          };\n          scrollEl.addEventListener('scroll', _this10.contentScrollCallback);\n          handleHeaderFade(_this10.scrollEl, _this10.el, condenseHeader);\n        });\n        return function (_x4, _x5) {\n          return _ref4.apply(this, arguments);\n        };\n      }();\n      this.collapse = undefined;\n      this.translucent = false;\n    }\n    componentWillLoad() {\n      this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n      this.checkCollapsibleHeader();\n    }\n    componentDidUpdate() {\n      this.checkCollapsibleHeader();\n    }\n    disconnectedCallback() {\n      this.destroyCollapsibleHeader();\n    }\n    checkCollapsibleHeader() {\n      var _this11 = this;\n      return _asyncToGenerator(function* () {\n        const mode = getIonMode(_this11);\n        if (mode !== 'ios') {\n          return;\n        }\n        const {\n          collapse\n        } = _this11;\n        const hasCondense = collapse === 'condense';\n        const hasFade = collapse === 'fade';\n        _this11.destroyCollapsibleHeader();\n        if (hasCondense) {\n          const pageEl = _this11.el.closest('ion-app,ion-page,.ion-page,page-inner');\n          const contentEl = pageEl ? findIonContent(pageEl) : null;\n          // Cloned elements are always needed in iOS transition\n          writeTask(() => {\n            const title = cloneElement('ion-title');\n            title.size = 'large';\n            cloneElement('ion-back-button');\n          });\n          yield _this11.setupCondenseHeader(contentEl, pageEl);\n        } else if (hasFade) {\n          const pageEl = _this11.el.closest('ion-app,ion-page,.ion-page,page-inner');\n          const contentEl = pageEl ? findIonContent(pageEl) : null;\n          if (!contentEl) {\n            printIonContentErrorMsg(_this11.el);\n            return;\n          }\n          const condenseHeader = contentEl.querySelector('ion-header[collapse=\"condense\"]');\n          yield _this11.setupFadeHeader(contentEl, condenseHeader);\n        }\n      })();\n    }\n    destroyCollapsibleHeader() {\n      if (this.intersectionObserver) {\n        this.intersectionObserver.disconnect();\n        this.intersectionObserver = undefined;\n      }\n      if (this.scrollEl && this.contentScrollCallback) {\n        this.scrollEl.removeEventListener('scroll', this.contentScrollCallback);\n        this.contentScrollCallback = undefined;\n      }\n      if (this.collapsibleMainHeader) {\n        this.collapsibleMainHeader.classList.remove('header-collapse-main');\n        this.collapsibleMainHeader = undefined;\n      }\n    }\n    setupCondenseHeader(contentEl, pageEl) {\n      var _this12 = this;\n      return _asyncToGenerator(function* () {\n        if (!contentEl || !pageEl) {\n          printIonContentErrorMsg(_this12.el);\n          return;\n        }\n        if (typeof IntersectionObserver === 'undefined') {\n          return;\n        }\n        _this12.scrollEl = yield getScrollElement(contentEl);\n        const headers = pageEl.querySelectorAll('ion-header');\n        _this12.collapsibleMainHeader = Array.from(headers).find(header => header.collapse !== 'condense');\n        if (!_this12.collapsibleMainHeader) {\n          return;\n        }\n        const mainHeaderIndex = createHeaderIndex(_this12.collapsibleMainHeader);\n        const scrollHeaderIndex = createHeaderIndex(_this12.el);\n        if (!mainHeaderIndex || !scrollHeaderIndex) {\n          return;\n        }\n        setHeaderActive(mainHeaderIndex, false);\n        setToolbarBackgroundOpacity(mainHeaderIndex.el, 0);\n        /**\n         * Handle interaction between toolbar collapse and\n         * showing/hiding content in the primary ion-header\n         * as well as progressively showing/hiding the main header\n         * border as the top-most toolbar collapses or expands.\n         */\n        const toolbarIntersection = ev => {\n          handleToolbarIntersection(ev, mainHeaderIndex, scrollHeaderIndex, _this12.scrollEl);\n        };\n        _this12.intersectionObserver = new IntersectionObserver(toolbarIntersection, {\n          root: contentEl,\n          threshold: [0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1]\n        });\n        _this12.intersectionObserver.observe(scrollHeaderIndex.toolbars[scrollHeaderIndex.toolbars.length - 1].el);\n        /**\n         * Handle scaling of large iOS titles and\n         * showing/hiding border on last toolbar\n         * in primary header\n         */\n        _this12.contentScrollCallback = () => {\n          handleContentScroll(_this12.scrollEl, scrollHeaderIndex, contentEl);\n        };\n        _this12.scrollEl.addEventListener('scroll', _this12.contentScrollCallback);\n        writeTask(() => {\n          if (_this12.collapsibleMainHeader !== undefined) {\n            _this12.collapsibleMainHeader.classList.add('header-collapse-main');\n          }\n        });\n      })();\n    }\n    render() {\n      const {\n        translucent,\n        inheritedAttributes\n      } = this;\n      const mode = getIonMode(this);\n      const collapse = this.collapse || 'none';\n      // banner role must be at top level, so remove role if inside a menu\n      const roleType = hostContext('ion-menu', this.el) ? 'none' : 'banner';\n      return h(Host, Object.assign({\n        key: 'b6cc27f0b08afc9fcc889683525da765d80ba672',\n        role: roleType,\n        class: {\n          [mode]: true,\n          // Used internally for styling\n          [`header-${mode}`]: true,\n          [`header-translucent`]: this.translucent,\n          [`header-collapse-${collapse}`]: true,\n          [`header-translucent-${mode}`]: this.translucent\n        }\n      }, inheritedAttributes), mode === 'ios' && translucent && h(\"div\", {\n        key: '395766d4dcee3398bc91960db21f922095292f14',\n        class: \"header-background\"\n      }), h(\"slot\", {\n        key: '09a67ece27b258ff1248805d43d92a49b2c6859a'\n      }));\n    }\n    get el() {\n      return getElement(this);\n    }\n  };\n  Header.style = {\n    ios: IonHeaderIosStyle0,\n    md: IonHeaderMdStyle0\n  };\n  return Header;\n})();\nconst routerOutletCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\nconst IonRouterOutletStyle0 = routerOutletCss;\nconst RouterOutlet = /*#__PURE__*/(() => {\n  let RouterOutlet = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n      this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n      this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n      this.lockController = createLockController();\n      this.gestureOrAnimationInProgress = false;\n      this.mode = getIonMode(this);\n      this.delegate = undefined;\n      this.animated = true;\n      this.animation = undefined;\n      this.swipeHandler = undefined;\n    }\n    swipeHandlerChanged() {\n      if (this.gesture) {\n        this.gesture.enable(this.swipeHandler !== undefined);\n      }\n    }\n    connectedCallback() {\n      var _this13 = this;\n      return _asyncToGenerator(function* () {\n        const onStart = () => {\n          _this13.gestureOrAnimationInProgress = true;\n          if (_this13.swipeHandler) {\n            _this13.swipeHandler.onStart();\n          }\n        };\n        _this13.gesture = (yield import('./swipe-back-07df2095.js')).createSwipeBackGesture(_this13.el, () => !_this13.gestureOrAnimationInProgress && !!_this13.swipeHandler && _this13.swipeHandler.canStart(), () => onStart(), step => {\n          var _a;\n          return (_a = _this13.ani) === null || _a === void 0 ? void 0 : _a.progressStep(step);\n        }, (shouldComplete, step, dur) => {\n          if (_this13.ani) {\n            _this13.ani.onFinish(() => {\n              _this13.gestureOrAnimationInProgress = false;\n              if (_this13.swipeHandler) {\n                _this13.swipeHandler.onEnd(shouldComplete);\n              }\n            }, {\n              oneTimeCallback: true\n            });\n            // Account for rounding errors in JS\n            let newStepValue = shouldComplete ? -0.001 : 0.001;\n            /**\n             * Animation will be reversed here, so need to\n             * reverse the easing curve as well\n             *\n             * Additionally, we need to account for the time relative\n             * to the new easing curve, as `stepValue` is going to be given\n             * in terms of a linear curve.\n             */\n            if (!shouldComplete) {\n              _this13.ani.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n              newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], step)[0];\n            } else {\n              newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], step)[0];\n            }\n            _this13.ani.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n          } else {\n            _this13.gestureOrAnimationInProgress = false;\n          }\n        });\n        _this13.swipeHandlerChanged();\n      })();\n    }\n    componentWillLoad() {\n      this.ionNavWillLoad.emit();\n    }\n    disconnectedCallback() {\n      if (this.gesture) {\n        this.gesture.destroy();\n        this.gesture = undefined;\n      }\n    }\n    /** @internal */\n    commit(enteringEl, leavingEl, opts) {\n      var _this14 = this;\n      return _asyncToGenerator(function* () {\n        const unlock = yield _this14.lockController.lock();\n        let changed = false;\n        try {\n          changed = yield _this14.transition(enteringEl, leavingEl, opts);\n        } catch (e) {\n          console.error(e);\n        }\n        unlock();\n        return changed;\n      })();\n    }\n    /** @internal */\n    setRouteId(id, params, direction, animation) {\n      var _this15 = this;\n      return _asyncToGenerator(function* () {\n        const changed = yield _this15.setRoot(id, params, {\n          duration: direction === 'root' ? 0 : undefined,\n          direction: direction === 'back' ? 'back' : 'forward',\n          animationBuilder: animation\n        });\n        return {\n          changed,\n          element: _this15.activeEl\n        };\n      })();\n    }\n    /** @internal */\n    getRouteId() {\n      var _this16 = this;\n      return _asyncToGenerator(function* () {\n        const active = _this16.activeEl;\n        return active ? {\n          id: active.tagName,\n          element: active,\n          params: _this16.activeParams\n        } : undefined;\n      })();\n    }\n    setRoot(component, params, opts) {\n      var _this17 = this;\n      return _asyncToGenerator(function* () {\n        if (_this17.activeComponent === component && shallowEqualStringMap(params, _this17.activeParams)) {\n          return false;\n        }\n        // attach entering view to DOM\n        const leavingEl = _this17.activeEl;\n        const enteringEl = yield attachComponent(_this17.delegate, _this17.el, component, ['ion-page', 'ion-page-invisible'], params);\n        _this17.activeComponent = component;\n        _this17.activeEl = enteringEl;\n        _this17.activeParams = params;\n        // commit animation\n        yield _this17.commit(enteringEl, leavingEl, opts);\n        yield detachComponent(_this17.delegate, leavingEl);\n        return true;\n      })();\n    }\n    transition(enteringEl, leavingEl, opts = {}) {\n      var _this18 = this;\n      return _asyncToGenerator(function* () {\n        if (leavingEl === enteringEl) {\n          return false;\n        }\n        // emit nav will change event\n        _this18.ionNavWillChange.emit();\n        const {\n          el,\n          mode\n        } = _this18;\n        const animated = _this18.animated && config.getBoolean('animated', true);\n        const animationBuilder = opts.animationBuilder || _this18.animation || config.get('navAnimation');\n        yield transition(Object.assign(Object.assign({\n          mode,\n          animated,\n          enteringEl,\n          leavingEl,\n          baseEl: el,\n          /**\n           * We need to wait for all Stencil components\n           * to be ready only when using the lazy\n           * loaded bundle.\n           */\n          deepWait: hasLazyBuild(el),\n          progressCallback: opts.progressAnimation ? ani => {\n            /**\n             * Because this progress callback is called asynchronously\n             * it is possible for the gesture to start and end before\n             * the animation is ever set. In that scenario, we should\n             * immediately call progressEnd so that the transition promise\n             * resolves and the gesture does not get locked up.\n             */\n            if (ani !== undefined && !_this18.gestureOrAnimationInProgress) {\n              _this18.gestureOrAnimationInProgress = true;\n              ani.onFinish(() => {\n                _this18.gestureOrAnimationInProgress = false;\n                if (_this18.swipeHandler) {\n                  _this18.swipeHandler.onEnd(false);\n                }\n              }, {\n                oneTimeCallback: true\n              });\n              /**\n               * Playing animation to beginning\n               * with a duration of 0 prevents\n               * any flickering when the animation\n               * is later cleaned up.\n               */\n              ani.progressEnd(0, 0, 0);\n            } else {\n              _this18.ani = ani;\n            }\n          } : undefined\n        }, opts), {\n          animationBuilder\n        }));\n        // emit nav changed event\n        _this18.ionNavDidChange.emit();\n        return true;\n      })();\n    }\n    render() {\n      return h(\"slot\", {\n        key: '8d0c163c5f63158e16ef2db7cc3c756cf597461d'\n      });\n    }\n    get el() {\n      return getElement(this);\n    }\n    static get watchers() {\n      return {\n        \"swipeHandler\": [\"swipeHandlerChanged\"]\n      };\n    }\n  };\n  RouterOutlet.style = IonRouterOutletStyle0;\n  return RouterOutlet;\n})();\nconst titleIosCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}\";\nconst IonTitleIosStyle0 = titleIosCss;\nconst titleMdCss = \":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}\";\nconst IonTitleMdStyle0 = titleMdCss;\nconst ToolbarTitle = /*#__PURE__*/(() => {\n  let ToolbarTitle = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.ionStyle = createEvent(this, \"ionStyle\", 7);\n      this.color = undefined;\n      this.size = undefined;\n    }\n    sizeChanged() {\n      this.emitStyle();\n    }\n    connectedCallback() {\n      this.emitStyle();\n    }\n    emitStyle() {\n      const size = this.getSize();\n      this.ionStyle.emit({\n        [`title-${size}`]: true\n      });\n    }\n    getSize() {\n      return this.size !== undefined ? this.size : 'default';\n    }\n    render() {\n      const mode = getIonMode(this);\n      const size = this.getSize();\n      return h(Host, {\n        key: '3f7b19c99961dbb86c0a925218332528b22e6880',\n        class: createColorClasses(this.color, {\n          [mode]: true,\n          [`title-${size}`]: true,\n          'title-rtl': document.dir === 'rtl'\n        })\n      }, h(\"div\", {\n        key: '12054fbdd60e40a15875e442c20143766fc34fc3',\n        class: \"toolbar-title\"\n      }, h(\"slot\", {\n        key: '9f14fb14a67d4bd1e4536a4d64a637fbe5a151c7'\n      })));\n    }\n    get el() {\n      return getElement(this);\n    }\n    static get watchers() {\n      return {\n        \"size\": [\"sizeChanged\"]\n      };\n    }\n  };\n  ToolbarTitle.style = {\n    ios: IonTitleIosStyle0,\n    md: IonTitleMdStyle0\n  };\n  return ToolbarTitle;\n})();\nconst toolbarIosCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}\";\nconst IonToolbarIosStyle0 = toolbarIosCss;\nconst toolbarMdCss = \":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}\";\nconst IonToolbarMdStyle0 = toolbarMdCss;\nconst Toolbar = /*#__PURE__*/(() => {\n  let Toolbar = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.childrenStyles = new Map();\n      this.color = undefined;\n    }\n    componentWillLoad() {\n      const buttons = Array.from(this.el.querySelectorAll('ion-buttons'));\n      const firstButtons = buttons.find(button => {\n        return button.slot === 'start';\n      });\n      if (firstButtons) {\n        firstButtons.classList.add('buttons-first-slot');\n      }\n      const buttonsReversed = buttons.reverse();\n      const lastButtons = buttonsReversed.find(button => button.slot === 'end') || buttonsReversed.find(button => button.slot === 'primary') || buttonsReversed.find(button => button.slot === 'secondary');\n      if (lastButtons) {\n        lastButtons.classList.add('buttons-last-slot');\n      }\n    }\n    childrenStyle(ev) {\n      ev.stopPropagation();\n      const tagName = ev.target.tagName;\n      const updatedStyles = ev.detail;\n      const newStyles = {};\n      const childStyles = this.childrenStyles.get(tagName) || {};\n      let hasStyleChange = false;\n      Object.keys(updatedStyles).forEach(key => {\n        const childKey = `toolbar-${key}`;\n        const newValue = updatedStyles[key];\n        if (newValue !== childStyles[childKey]) {\n          hasStyleChange = true;\n        }\n        if (newValue) {\n          newStyles[childKey] = true;\n        }\n      });\n      if (hasStyleChange) {\n        this.childrenStyles.set(tagName, newStyles);\n        forceUpdate(this);\n      }\n    }\n    render() {\n      const mode = getIonMode(this);\n      const childStyles = {};\n      this.childrenStyles.forEach(value => {\n        Object.assign(childStyles, value);\n      });\n      return h(Host, {\n        key: '402afe7ce0c97883cedd0e48a5a0492a9bfe76ae',\n        class: Object.assign(Object.assign({}, childStyles), createColorClasses(this.color, {\n          [mode]: true,\n          'in-toolbar': hostContext('ion-toolbar', this.el)\n        }))\n      }, h(\"div\", {\n        key: '2465a6dc8d507ec650538378d1be2abd399c58ad',\n        class: \"toolbar-background\",\n        part: \"background\"\n      }), h(\"div\", {\n        key: '6075096afd12303b961e4fe9ad345ef2887573af',\n        class: \"toolbar-container\",\n        part: \"container\"\n      }, h(\"slot\", {\n        key: '8b7eec1148cfeb339d87cdf9273f2104703e7601',\n        name: \"start\"\n      }), h(\"slot\", {\n        key: 'b102d3926cade24faf78b7af78ad5e192c4c0308',\n        name: \"secondary\"\n      }), h(\"div\", {\n        key: 'c6ab2e978328324c6f9e7892024cbcd8b8987067',\n        class: \"toolbar-content\",\n        part: \"content\"\n      }, h(\"slot\", {\n        key: '86f8952c4355a9df5b4bbb95e9d0cafefd272d5b'\n      })), h(\"slot\", {\n        key: '501e43431da6b9dd35b47b79222f948d445f7a78',\n        name: \"primary\"\n      }), h(\"slot\", {\n        key: '84bf1a15a5e52e8e94df9f479c4ce18004f5ab57',\n        name: \"end\"\n      })));\n    }\n    get el() {\n      return getElement(this);\n    }\n  };\n  Toolbar.style = {\n    ios: IonToolbarIosStyle0,\n    md: IonToolbarMdStyle0\n  };\n  return Toolbar;\n})();\nexport { App as ion_app, Buttons as ion_buttons, Content as ion_content, Footer as ion_footer, Header as ion_header, RouterOutlet as ion_router_outlet, ToolbarTitle as ion_title, Toolbar as ion_toolbar };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}