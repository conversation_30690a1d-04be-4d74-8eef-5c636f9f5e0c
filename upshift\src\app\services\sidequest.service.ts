import { Injectable } from '@angular/core';
import { DailySideQuestPool, UserDailySideQuest } from '../models/sidequest.model';
import { Observable, map, of, from, switchMap, catchError, take } from 'rxjs';
import { UserService } from './user.service';
import { HttpClient } from '@angular/common/http';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class SideQuestService {
  constructor(
    private supabaseService: SupabaseService,
    private userService: UserService,
    private http: HttpClient
  ) {}

  // DailySideQuestPool operations
  getSideQuestPools(): Observable<DailySideQuestPool[]> {


    return from(
      this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .select('*')
        .eq('active', true)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('SideQuestService: Error getting side quest pools:', response.error);
          return [];
        }


        return response.data as DailySideQuestPool[];
      }),
      catchError(error => {
        console.error('SideQuestService: Error getting side quest pools:', error);
        return of([]);
      })
    );
  }

  // UserDailySideQuest operations
  getUserDailySideQuest(userId: string): Observable<UserDailySideQuest | null> {


    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('user_id', userId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('SideQuestService: Error getting user daily side quests:', response.error);
          return null;
        }


        return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;
      }),
      catchError(error => {
        console.error('SideQuestService: Error getting user daily side quests:', error);
        return of(null);
      })
    );
  }

  async createUserDailySideQuest(userId: string): Promise<string> {


    try {
      // Get a random active quest from the pool
      const { data: poolQuests, error: poolError } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .select('*')
        .eq('active', true);

      if (poolError) {

        throw new Error('Error getting side quest pool: ' + poolError.message);
      }

      if (!poolQuests || poolQuests.length === 0) {
        console.error('SideQuestService: No active side quests available');
        throw new Error('No active side quests available');
      }



      // Select a random quest from the pool
      const randomIndex = Math.floor(Math.random() * poolQuests.length);
      const randomQuest = poolQuests[randomIndex];


      // Create new user daily side quest
      const today = new Date();
      const dateString = today.toISOString().split('T')[0];

      const newSideQuest: Omit<UserDailySideQuest, 'id'> = {
        user_id: userId,
        current_quest_id: randomQuest.id,
        streak: 0,
        date_assigned: dateString,
        completed: false,
        value_achieved: 0,
        category: randomQuest.category,
        emoji: randomQuest.emoji,
        last_completed_date: null
      };



      const { data: insertData, error: insertError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .insert(newSideQuest)
        .select('id')
        .single();

      if (insertError) {
        console.error('SideQuestService: Error creating side quest:', insertError);
        throw new Error('Error creating side quest: ' + insertError.message);
      }


      return insertData.id;
    } catch (error) {
      console.error('SideQuestService: Error creating user daily side quest:', error);
      throw error;
    }
  }

  async toggleSideQuestCompletion(sideQuestId: string, userId: string, selectedDate?: Date): Promise<{
    completed: boolean;
    value_achieved: number;
    streak: number;
    goal_value: number;
    goal_unit: string;
  }> {


    try {
      // Get the current side quest
      const { data: sideQuest, error: questError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single();

      if (questError || !sideQuest) {
        console.error('SideQuestService: Error getting side quest:', questError);
        throw new Error('Side quest not found');
      }

      const wasCompleted = sideQuest.completed;

      // Get the quest details from the pool
      const { data: questPool, error: poolError } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .select('*')
        .eq('id', sideQuest.current_quest_id)
        .single();

      if (poolError || !questPool) {
        console.error('SideQuestService: Error getting quest pool item:', poolError);
        throw new Error('Side quest pool item not found');
      }



      // Toggle completion
      const isCompleted = !wasCompleted;
      const today = selectedDate || new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      let newStreak = sideQuest.streak;
      let newLastCompletedDate = sideQuest.last_completed_date;
      let newValueAchieved = sideQuest.value_achieved;

      if (isCompleted) {
        // Completing today's quest
        newValueAchieved = questPool.goal_value;

        // Check if yesterday was completed to update streak
        if (sideQuest.last_completed_date) {
          const lastCompletedDate = new Date(sideQuest.last_completed_date);
          const lastCompletedDay = lastCompletedDate.getDate();
          const yesterdayDay = yesterday.getDate();

          if (lastCompletedDay === yesterdayDay) {
            // Yesterday was completed, increment streak
            newStreak += 1;
          } else {
            // Start new streak
            newStreak = 1;
          }
        } else {
          // Start new streak
          newStreak = 1;
        }

        newLastCompletedDate = today;
      } else {
        // Uncompleting today's quest
        newValueAchieved = 0;

        if (sideQuest.last_completed_date && new Date(sideQuest.last_completed_date).getDate() === today.getDate()) {
          // If uncompleting today's quest, revert to previous day's state
          if (newStreak > 1) {
            newStreak -= 1;
            newLastCompletedDate = yesterday;
          } else {
            newStreak = 0;
            newLastCompletedDate = yesterday;  // Set to yesterday instead of null
          }
        }
      }

      // Update the side quest
      const { error: updateError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .update({
          completed: isCompleted,
          value_achieved: newValueAchieved,
          streak: newStreak,
          last_completed_date: newLastCompletedDate
        })
        .eq('id', sideQuestId);

      if (updateError) {
        console.error('SideQuestService: Error updating side quest:', updateError);
        throw new Error('Error updating side quest: ' + updateError.message);
      }


      // Update user XP based on completion status change
      if (isCompleted !== wasCompleted) {
        await this.updateUserXP(userId, sideQuest.category, isCompleted, wasCompleted);
      }

      // Update badges if streak is significant
      if (newStreak >= 7) {
        await this.updateSideQuestBadges(userId, newStreak);
      }

      // If we're updating a past date, also update today's streak
      const actualToday = new Date();
      actualToday.setHours(0, 0, 0, 0);

      if (selectedDate && selectedDate < actualToday) {

        // Get the current side quest for today
        const { data: todaySideQuest, error: todaySideQuestError } = await this.supabaseService.getClient()
          .from('user_daily_sidequests')
          .select('*')
          .eq('user_id', userId)
          .single();

        if (todaySideQuestError || !todaySideQuest) {
          console.error('SideQuestService: Error getting today\'s side quest:', todaySideQuestError);
        } else {
          // Check if today's side quest is different from the one we just updated
          if (todaySideQuest.id !== sideQuestId) {

            // Recalculate streak for today's side quest
            await this.checkMissedDays(todaySideQuest.id);
          }
        }
      }

      // Return the updated values to update the UI without a full page refresh
      return {
        completed: isCompleted,
        value_achieved: newValueAchieved,
        streak: newStreak,
        goal_value: questPool.goal_value,
        goal_unit: questPool.goal_unit
      };
    } catch (error) {
      console.error('SideQuestService: Error toggling side quest completion:', error);
      throw error;
    }
  }

  async checkMissedDays(sideQuestId: string | undefined): Promise<void> {

    if (!sideQuestId) {
      console.error('SideQuestService: Cannot check missed days - sideQuestId is undefined');
      return;
    }

    try {
      // Get the current side quest
      const { data: sideQuest, error: questError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single();

      if (questError || !sideQuest) {
        console.error('SideQuestService: Error getting side quest:', questError);
        return;
      }

      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      // Only reset streak if we actually missed a day
      // Convert dates to proper Date objects for comparison
      const lastCompletedDate = sideQuest.last_completed_date ? new Date(sideQuest.last_completed_date) : null;

      // Set hours to 0 for proper date comparison
      if (lastCompletedDate) {
        lastCompletedDate.setHours(0, 0, 0, 0);
      }
      yesterday.setHours(0, 0, 0, 0);

      // Check if last completed date is null or before yesterday (and not equal to yesterday)
      const missedDay = !lastCompletedDate ||
                       (lastCompletedDate < yesterday &&
                        lastCompletedDate.toISOString().split('T')[0] !== yesterday.toISOString().split('T')[0]);


      if (missedDay) {


        const { error: updateError } = await this.supabaseService.getClient()
          .from('user_daily_sidequests')
          .update({
            streak: 0,
            last_completed_date: yesterday.toISOString().split('T')[0]  // Set to yesterday instead of null
          })
          .eq('id', sideQuestId);

        if (updateError) {
          console.error('SideQuestService: Error resetting streak:', updateError);
          return;
        }

      } else {
      }
    } catch (error) {
      console.error('SideQuestService: Error checking missed days:', error);
    }
  }

  async assignNewQuest(sideQuestId: string | undefined): Promise<void> {

    if (!sideQuestId) {
      console.error('SideQuestService: Cannot assign new quest - sideQuestId is undefined');
      return;
    }

    try {
      // Get the current side quest
      const { data: sideQuest, error: questError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single();

      if (questError || !sideQuest) {
        console.error('SideQuestService: Error getting side quest:', questError);
        return;
      }

      const today = new Date();

      // Only assign a new quest if it's a new day
      if (new Date(sideQuest.date_assigned).getDate() === today.getDate()) {
        return;
      }

      // Check for missed days first
      await this.checkMissedDays(sideQuestId);

      // Get a random active quest from the pool that's different from the current one
      const { data: poolQuests, error: poolError } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .select('*')
        .eq('active', true)
        .neq('id', sideQuest.current_quest_id);

      if (poolError) {
        console.error('SideQuestService: Error getting side quest pool:', poolError);
        return;
      }

      if (!poolQuests || poolQuests.length === 0) {
        console.error('SideQuestService: No active side quests available');
        return;
      }


      // Select a random quest from the pool
      const randomIndex = Math.floor(Math.random() * poolQuests.length);
      const randomQuest = poolQuests[randomIndex];

      // Update the side quest
      const { error: updateError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .update({
          current_quest_id: randomQuest.id,
          date_assigned: today,
          completed: false,
          value_achieved: 0,
          category: randomQuest.category,
          emoji: randomQuest.emoji
        })
        .eq('id', sideQuestId);

      if (updateError) {
        console.error('SideQuestService: Error updating side quest:', updateError);
        return;
      }

    } catch (error) {
      console.error('SideQuestService: Error assigning new quest:', error);
    }
  }

  private async updateUserXP(
    userId: string,
    category: string,
    isCompleted: boolean,
    wasCompleted: boolean
  ): Promise<void> {

    try {
      // Only update XP if completion status changed
      if (isCompleted === wasCompleted) {
        return;
      }

      console.log('SIDEQUEST XP UPDATE: Starting XP update for user', userId);

      // Get the field name for the category XP
      const xpField = `${category}_xp`;

      // Side quests always give 2 XP
      const xpValue = 2;
      console.log('SIDEQUEST XP UPDATE: XP value for side quest:', xpValue);

      // Use UserService to update the user
      try {
        // Create update data object with the XP change
        let updateData = {};

        if (isCompleted && !wasCompleted) {
          // Add XP when completing a side quest
          console.log(`SIDEQUEST XP UPDATE: Adding ${xpValue} XP to ${category}`);

          // Get current user to get current XP value
          const { data: userData, error: userError } = await this.supabaseService.getClient()
            .from('profiles')
            .select(`${xpField}`)
            .eq('id', userId)
            .single();

          if (userError) {
            console.error('SIDEQUEST XP UPDATE: Error getting user:', userError);
            return;
          }
          console.log("xxxxxxxxxxxxxxxxxxxxxxxxxx", userData, xpField);
          const currentXP = userData[xpField] || 0;
          const newXP = currentXP + xpValue;

          console.log(`SIDEQUEST XP UPDATE: Current ${xpField}: ${currentXP}, New: ${newXP}`);
          updateData = { [xpField]: newXP };

        } else if (!isCompleted && wasCompleted) {
          // Remove XP when uncompleting a side quest
          console.log(`SIDEQUEST XP UPDATE: Removing ${xpValue} XP from ${category}`);

          // Get current user to get current XP value
          const { data: userData, error: userError } = await this.supabaseService.getClient()
            .from('profiles')
            .select(`${xpField}`)
            .eq('id', userId)
            .single();

          if (userError) {
            console.error('SIDEQUEST XP UPDATE: Error getting user:', userError);
            return;
          }

          const currentXP = userData[xpField] || 0;
          const newXP = Math.max(0, currentXP - xpValue);

          console.log(`SIDEQUEST XP UPDATE: Current ${xpField}: ${currentXP}, New: ${newXP}`);
          updateData = { [xpField]: newXP };
        }

        // Only update if we have data to update
        if (Object.keys(updateData).length > 0) {
          // Update the user's XP
          const { error: updateError } = await this.supabaseService.getClient()
            .from('profiles')
            .update(updateData)
            .eq('id', userId);

          if (updateError) {
            console.error('SIDEQUEST XP UPDATE: Error updating XP:', updateError);
            return;
          }

          console.log('SIDEQUEST XP UPDATE: Successfully updated XP');

          // Check if user can level up
          await this.userService.checkAndLevelUp(userId);

          // Refresh the current user profile to update UI
          // await this.userService.refreshCurrentUserProfile();
        }
      } catch (error) {
        console.error('SIDEQUEST XP UPDATE: Error updating user XP:', error);
      }
    } catch (error) {
      console.error('SIDEQUEST XP UPDATE: Error in updateUserXP:', error);
    }
  }

  private async updateSideQuestBadges(userId: string, streak: number): Promise<void> {

    try {
      // Get user badges
      const { data: badges, error: badgesError } = await this.supabaseService.getClient()
        .from('user_badges')
        .select('*')
        .eq('user_id', userId);

      if (badgesError) {
        console.error('SideQuestService: Error getting user badges:', badgesError);
        return;
      }

      if (!badges || badges.length === 0) {
        return;
      }

      const badgeDoc = badges[0];

      // Update badges based on streak
      const updates: any = {};

      if (streak >= 7) {
        updates.badge_sidequest_streak_7_days = true;
      }
      if (streak >= 30) {
        updates.badge_sidequest_streak_30_days = true;
      }
      if (streak >= 100) {
        updates.badge_sidequest_streak_100_days = true;
      }
      if (streak >= 365) {
        updates.badge_sidequest_streak_365_days = true;
      }

      // Only update if there are changes
      if (Object.keys(updates).length > 0) {

        const { error: updateError } = await this.supabaseService.getClient()
          .from('user_badges')
          .update(updates)
          .eq('id', badgeDoc.id);

        if (updateError) {
          console.error('SideQuestService: Error updating badges:', updateError);
          return;
        }

      } else {
      }
    } catch (error) {
      console.error('SideQuestService: Error updating side quest badges:', error);
    }
  }

  /**
   * Helper method to check if two dates are the same day
   */
  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  /**
   * Gets or creates a daily side quest for the user
   * This follows the Django approach where we get or create a side quest for today
   */
  getOrCreateDailySideQuest(userId: string): Observable<UserDailySideQuest | null> {
    const today = new Date();

    // First check if the user already has a side quest
    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('user_id', userId)
    ).pipe(
      take(1),
      map(response => {
        if (response.error) {
          console.error('SideQuestService: Error getting user daily side quests:', response.error);
          return null;
        }

        return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;
      }),
      switchMap(existingSideQuest => {
        if (existingSideQuest) {

          // Check if we need to reset/assign new quest for a new day
          const assignedDate = new Date(existingSideQuest.date_assigned);
          if (assignedDate.getDate() !== today.getDate() ||
              assignedDate.getMonth() !== today.getMonth() ||
              assignedDate.getFullYear() !== today.getFullYear()) {

            // It's a new day, assign a new quest
            return from(this.assignNewQuest(existingSideQuest.id)).pipe(
              switchMap(() => from(
                this.supabaseService.getClient()
                  .from('user_daily_sidequests')
                  .select('*')
                  .eq('user_id', userId)
              ).pipe(
                take(1),
                map(response => {
                  if (response.error) {
                    console.error('SideQuestService: Error getting updated side quest:', response.error);
                    return null;
                  }

                  return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;
                })
              ))
            );
          }


          // Check for missed days first
          return from(this.checkMissedDays(existingSideQuest.id)).pipe(
            switchMap(() => of(existingSideQuest))
          );
        } else {

          // No existing side quest, create a new one
          return from(this.createUserDailySideQuest(userId)).pipe(
            switchMap(() => from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .select('*')
                .eq('user_id', userId)
            ).pipe(
              take(1),
              map(response => {
                if (response.error) {
                  console.error('SideQuestService: Error getting new side quest:', response.error);
                  return null;
                }

                return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;
              })
            ))
          );
        }
      }),
      catchError(error => {
        console.error('SideQuestService: Error in getOrCreateDailySideQuest:', error);
        return of(null);
      })
    );
  }

  /**
   * Recalculate streak for the daily side quest
   * This matches the Django implementation in today_view
   */
  recalculateSideQuestStreak(userId: string, _selectedDate?: Date): Observable<void> {

    // Get the latest side quest for the user
    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('user_id', userId)
        .order('date_assigned', { ascending: false })
        .limit(1)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('SideQuestService: Error getting side quest:', response.error);
          return null;
        }
        return response.data.length > 0 ? response.data[0] : null;
      }),
      switchMap(sideQuest => {
        if (!sideQuest) {
          return of(undefined);
        }

        return from(this.checkMissedDays(sideQuest.id)).pipe(
          map(() => undefined)
        );
      }),
      catchError(error => {
        console.error('SideQuestService: Error recalculating side quest streak:', error);
        return of(undefined);
      })
    );
  }

  /**
   * Ensure user has daily side quests for today
   * This matches the Django implementation where we check if the user has any side quests
   * and create one if they don't
   * It also checks for missed days and resets the streak if needed
   */
  ensureUserHasDailySideQuests(userId: string): Observable<UserDailySideQuest[]> {

    const today = new Date();

    // First, check if the user already has a side quest
    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*, daily_sidequest_pool(*)')
        .eq('user_id', userId)
        .order('date_assigned', { ascending: false })
        .limit(1)
    ).pipe(
      switchMap(response => {
        if (response.error) {
          console.error('SideQuestService: Error getting side quests:', response.error);
          return of([]);
        }

        if (response.data && response.data.length > 0) {

          // Check for missed days first, just like in Django
          const sideQuest = response.data[0];

          return from(this.checkMissedDays(sideQuest.id)).pipe(
            switchMap(() => {
              // Check if we need to assign a new quest
              // In Django, this is done in check_and_reset

              // Check if the side quest is from today
              const assignedDate = new Date(sideQuest.date_assigned);
              if (assignedDate.getDate() !== today.getDate() ||
                  assignedDate.getMonth() !== today.getMonth() ||
                  assignedDate.getFullYear() !== today.getFullYear()) {

                // It's a new day, assign a new quest
                return from(this.assignNewQuest(sideQuest.id)).pipe(
                  switchMap(() => from(
                    this.supabaseService.getClient()
                      .from('user_daily_sidequests')
                      .select('*, daily_sidequest_pool(*)')
                      .eq('user_id', userId)
                      .order('date_assigned', { ascending: false })
                      .limit(1)
                  ).pipe(
                    map(updatedResponse => {
                      if (updatedResponse.error) {
                        console.error('SideQuestService: Error getting updated side quests:', updatedResponse.error);
                        return [];
                      }
                      return updatedResponse.data as UserDailySideQuest[];
                    })
                  ))
                );
              }

              // Side quest is from today, just return it
              return of([sideQuest] as UserDailySideQuest[]);
            })
          );
        }


        // Create 1 side quest for the user
        return from(this.createUserDailySideQuest(userId)).pipe(
          switchMap(newSideQuestId => {
            return from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .select('*, daily_sidequest_pool(*)')
                .eq('user_id', userId)
                .order('date_assigned', { ascending: false })
                .limit(1)
            ).pipe(
              map(newResponse => {
                if (newResponse.error) {
                  console.error('SideQuestService: Error getting new side quests:', newResponse.error);
                  return [];
                }
                return newResponse.data as UserDailySideQuest[];
              })
            );
          }),
          catchError(error => {
            console.error('SideQuestService: Error creating side quests:', error);
            return of([]);
          })
        );
      })
    );
  }

  /**
   * Imports side quests from the JSON file to Supabase
   * This should be called once to initialize the side quest pool
   */
  importSideQuestsFromJson(): Observable<boolean> {

    return this.http.get<DailySideQuestPool[]>('assets/data/sidequest-pool.json').pipe(
      switchMap(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          console.error('SideQuestService: No side quests found in JSON file');
          return of(false);
        }


        // Check if side quests already exist in Supabase
        return from(
          this.supabaseService.getClient()
            .from('daily_sidequest_pool')
            .select('id', { count: 'exact' })
        ).pipe(
          switchMap(response => {
            if (response.error) {
              console.error('SideQuestService: Error checking existing side quests:', response.error);
              return of(false);
            }

            if (response.data && response.data.length > 0) {
              return of(false);
            }

            // Add active flag to each quest
            const questsWithActive = sideQuests.map(quest => ({
              ...quest,
              active: true
            }));

            // Import side quests to Supabase
            return from(
              this.supabaseService.getClient()
                .from('daily_sidequest_pool')
                .insert(questsWithActive)
            ).pipe(
              map(insertResponse => {
                if (insertResponse.error) {
                  console.error('SideQuestService: Error importing side quests:', insertResponse.error);
                  return false;
                }

                return true;
              }),
              catchError(error => {
                console.error('SideQuestService: Error importing side quests:', error);
                return of(false);
              })
            );
          })
        );
      }),
      catchError(error => {
        console.error('SideQuestService: Error fetching side quests JSON:', error);
        return of(false);
      })
    );
  }
}
