
.quest-message {
  color: #FF9500;
  font-weight: 500;
  font-size: small;
}

/* Add Quest Button */
.add-quest-link {
  display: flex;
  align-items: center;
  color: var(--accent-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 6px;
  background-color: rgba(77, 123, 255, 0.1);
  transition: background-color 0.2s;
}

.add-quest-link:hover {
  background-color: rgba(77, 123, 255, 0.2);
}

.add-quest-icon {
  margin-right: 4px;
  font-size: 16px;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
  background-color: transparent;
  margin: 5% auto;
  padding: 20px;
  width: 90%;
  max-width: 500px;
  position: relative;
  color: white;
  /* max-height: 80vh; */
  overflow-y: auto;
}

.close-modal {
  color: #666;
  position: absolute;
  top: 5px;
  right: 10px;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
}

.close-modal:hover {
  color: white;
}

.modal h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.form-group {
  margin-bottom: 18px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #CCC;
  font-size: 14px;
  font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  background-color: #1C1C1E;
  border: 1px solid #3C3C3E;
  border-radius: 8px;
  color: white;
  font-size: 14px;
}

.form-group textarea {
  height: 80px;
  resize: vertical;
}

#emoji {
  width: 60px;
  text-align: center;
  background-color: #1C1C1E;
  border-radius: 8px;
  padding: 10px;
}

.goal-inputs {
  display: flex;
  gap: 8px;
}

.goal-inputs input {
  width: 80px;
}

.goal-inputs select {
  flex-grow: 1;
}

.submit-btn {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  width: 100%;
  margin-top: 20px;
  transition: background 0.2s;
}

.submit-btn:hover {
  background-color: #3A57C2;
}

/* Day selectors */
.schedule-container {
  margin-top: 15px;
}

.days-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.day-checkbox {
  display: flex;
  align-items: center;
  gap: 4px;
}

.day-checkbox label {
  font-size: 13px;
  cursor: pointer;
  margin-bottom: 0;
}

.month-days-selector {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.month-days-selector .day-checkbox {
  justify-content: center;
}

.month-days-selector .day-checkbox input[type="checkbox"] {
  margin-right: 2px;
}

.month-days-selector .day-checkbox label {
  min-width: 20px;
  text-align: center;
}

.quests, .side-quests {
  margin-bottom: 32px;
}


.quest-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}


.quest-item {
  background-color: var(--quest-bg);
  border: 1px solid var(--quest-border);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}


.quest-item:active {
  transform: scale(0.98);
}


.quest-item.completed {
  border-color: var(--accent-color);
}


.quest-item.completed .quest-info h3 {
  color: var(--accent-color);
}


.quest-icon {
  font-size: 20px;
  min-width: 24px;
  text-align: center;
}


.quest-info {
  flex-grow: 1;
}


.quest-info h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
}


.quest-info p {
  color: var(--secondary-text);
  font-size: 12px;
  margin-bottom: 4px;
}


.progress, .progress-time {
  color: var(--secondary-text);
  font-size: 12px;
}


.quest-streak {
  font-size: 12px;
  white-space: nowrap;
}


/* Add a purple line between main quests and side quests */
.side-quests {
  position: relative;
  padding-top: 32px;
}


.quests h2 {
  display: flex;
  align-items: center;
  gap: 10px;
}


.progress-container {
  width: 100%;
  margin: 4px 0;
}


.progress-slider {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 4px;
  background: var(--inactive-date);
  outline: none;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}


.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}


.progress-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  border: none;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}


.progress-slider::-webkit-slider-runnable-track {
  height: 4px;
  border-radius: 2px;
}


.progress-slider::-moz-range-track {
  height: 4px;
  border-radius: 2px;
}


/* Custom progress bar fill */
.progress-slider {
  background: linear-gradient(to right, var(--accent-color) 0%, var(--accent-color) 50%, var(--bg-tertiary) 50%, var(--bg-tertiary) 100%);
}


.progress-text {
  font-size: 12px;
  color: var(--secondary-text);
  margin-top: 2px;
}
:host {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --quest-bg: #1C1C1E;
  --quest-border: #2C2C2E;
  --active-date: #4169E1;
  --inactive-date: #2C2C2E;
}


* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}


body.dark-theme {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
}


.container {
  width: 480px;
  margin: 0 auto;
  padding: 20px;
  scrollbar-width: none;
  overflow-y: auto;
}
.container::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

.scroll-y {
  display: none;    
}
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}


.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}


.logo img {
  height: 24px;
}


.logo span {
  font-size: 20px;
  font-weight: 600;
}


h1 {
  font-size: 20px;
  font-weight: 600;
}


.week-calendar {
  margin-bottom: 32px;
}


.days, .dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  gap: 8px;
}


.days {
  margin-bottom: 8px;
}


.day-name {
  color: var(--secondary-text);
  font-size: 14px;
}


.date {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 auto;
  font-size: 14px;
  background-color: var(--inactive-date);
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.date-progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  pointer-events: none;
  z-index: 0;
}

.date-progress circle {
  fill: transparent;
  stroke-width: 2.5;
  stroke-linecap: round;
  transform-origin: center;
  transform: rotate(-90deg);
  transition: stroke-dasharray 0.3s ease;
}

/* Different colors for different completion levels */
.date-progress .progress-circle {
  stroke: var(--accent-color);
  stroke-opacity: 0.9;
}

.date-progress .progress-circle.low {
  stroke: var(--accent-color);
}

.date-progress circle {
  stroke-width: 3;
}

/* Special handling for selected days */
.date.selected .date-progress .progress-circle {
  stroke: #78a8f3;
  stroke-opacity: 0.7;
}

.date.active .date-progress .progress-circle {
  stroke: #78a8f3;
  /* #4287f5; */
  /* 6da5fe */
  stroke-opacity: 0.7;
}

.date-content {
  position: relative;
  z-index: 1;
}


.date.active {
  background-color: var(--active-date);
  color: white;
}


.date.selected {
  background-color: var(--accent-color);
  color: white;
}


.date.selected::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: var(--accent-color);
  border-radius: 50%;
}


.date:hover:not(.disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}


.date.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}


h2 {
  font-size: 20px;
  margin-bottom: 16px;
}


.side-quests::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 1px;
  background: linear-gradient(to right, transparent, #4B0082, transparent);
}


.calendar {
  margin: 20px 0;
  padding: 10px;
  background: var(--bg-secondary);
  border-radius: 8px;
}


.calendar-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}


.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  text-align: center;
}


.day-name {
  color: var(--secondary-text);
  font-size: 14px;
}


.day-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  text-decoration: none;
  color: var(--text-primary);
  margin: 0 auto;
}


.day-number:hover {
  background: var(--bg-hover);
}


.day-number.selected {
  background: var(--primary-color);
  color: white;
}


.day-number.today {
  border: 2px solid var(--primary-color);
}


.nav-arrow {
  padding: 8px 12px;
  border: none;
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
}


.nav-arrow:hover {
  background: var(--bg-hover);
}


.time-display {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-right: 16px;
}


/* Make number input spinners less prominent */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  opacity: 0.3;
}

.profile-container {
  padding: 20px;
  margin-bottom: 80px;
}

.profile-header {
  display: flex;
  align-items: center;
 
}

.profile-picture {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin-right: 20px;
  overflow: hidden;
}

.profile-picture img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.profile-username {
  font-size: 16px;
  color: var(--secondary-text);
  margin-bottom: 5px;
}

.profile-bio {
  font-size: 14px;
  color: var(--secondary-text);
  margin-bottom: 10px;
  font-style: italic;
  max-width: 300px;
}

.profile-level {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.level-badge {
  background-color: var(--accent-color);
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 14px;
  font-weight: 600;
  margin-right: 10px;
}

.profile-title {
  font-size: 16px;
  color: var(--accent-color);
}

.xp-section {
  margin-top: 30px;
}

.xp-section h2 {
  font-size: 20px;
  margin-bottom: 20px;
}

.category-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.category-icon {
  font-size: 24px;
  margin-right: 10px;
}

.category-name {
  font-size: 18px;
  font-weight: 600;
}

.progress-container {
  height: 10px;
  background-color: var(--bg-color);
  border-radius: 5px;
  margin-bottom: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 5px;
}

.xp-text {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: var(--secondary-text);
}

.next-level-info {
  text-align: center;
  margin-top: 30px;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 12px;
}

.next-level-text {
  font-size: 16px;
  margin-bottom: 10px;
}

.next-level-requirements {
  font-size: 14px;
  color: var(--secondary-text);
}

.back-button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 15px;
  display: inline-block;
  text-decoration: none;
  text-align: center;
}

.back-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 10px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.action-button {
  background-color: var(--card-bg);
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
  border-radius: 20px;
  padding: 5px 12px;
  font-size: 12px;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
}

.action-button.danger {
  color: var(--danger-color);
  border-color: var(--danger-color);
}
.settings-button {
  display: inline-block;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 16px;
  text-decoration: none;
  text-align: center;
}
.badges-button {
  display: inline-block; 
  margin-right: 10px;
   padding: 8px 16px; 
   background-color: #1c1c1e; 
  color: white; 
  border: 1px solid #4d7bff; 
  border-radius: 20px;
   text-decoration: none; 
   font-size: 14px; 
   font-weight: 600; 
  transition: all 0.3s ease;
}
.button-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}
.back-link {
  display: inline-block;
  color: var(--secondary-text);
  text-decoration: none;
  margin-bottom: 20px;
  font-size: 14px;
}

.back-link:hover {
  color: var(--text-color);
}

.view-badges {
  margin-top: 15px;
  display: inline-block;
  margin-right: 10px;
  padding: 8px 16px;
  background-color: #1c1c1e;
  color: white;
  border: 1px solid #4d7bff;
  border-radius: 20px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;

}