ion-content {
  --background: var(--bg);
}
ion-content ion-header ion-toolbar {
  --background: transparent;
}
ion-content ion-header ion-toolbar .head {
  padding: 16px;
}
ion-content ion-header ion-toolbar .head ion-icon {
  font-size: 24px;
  color: var(--text);
  cursor: pointer;
}
ion-content .rating-container {
  padding: 0 24px 0 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
ion-content .rating-container h1 {
  color: var(--text);
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 32px;
  text-align: center;
}
ion-content .rating-container .stars-container {
  background: var(--surface);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 16px;
}
ion-content .rating-container .stars-container .star-icon {
  font-size: 32px;
  color: #FFD700;
  cursor: pointer;
}
ion-content .rating-container .rating-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}
ion-content .rating-container .rating-popup.show {
  opacity: 1;
  pointer-events: auto;
}
ion-content .rating-container .rating-popup .popup-content {
  background: white;
  border-radius: 16px;
  padding: 24px;
  width: 90%;
  max-width: 320px;
  text-align: center;
}
ion-content .rating-container .rating-popup .popup-content .app-icon {
  width: 64px;
  height: 64px;
  background: #000;
  border-radius: 12px;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
ion-content .rating-container .rating-popup .popup-content .app-icon ion-icon {
  font-size: 40px;
  color: white;
}
ion-content .rating-container .rating-popup .popup-content h2 {
  color: #000;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}
ion-content .rating-container .rating-popup .popup-content p {
  color: #666;
  font-size: 16px;
  margin-bottom: 24px;
}
ion-content .rating-container .rating-popup .popup-content .popup-stars {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 24px;
}
ion-content .rating-container .rating-popup .popup-content .popup-stars .popup-star {
  font-size: 28px;
  color: #007AFF;
  cursor: pointer;
}
ion-content .rating-container .rating-popup .popup-content .not-now {
  background: none;
  border: none;
  color: #007AFF;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 16px;
}
ion-content .rating-container .testimonial {
  background: var(--surface);
  border-radius: 16px;
  padding: 24px;
  width: 100%;
  margin-bottom: 24px;
}
ion-content .rating-container .testimonial .user {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}
ion-content .rating-container .testimonial .user .avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
}
ion-content .rating-container .testimonial .user .user-info h3 {
  color: var(--text);
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px;
}
ion-content .rating-container .testimonial .user .user-info .star-rating {
  display: flex;
  gap: 4px;
}
ion-content .rating-container .testimonial .user .user-info .star-rating ion-icon {
  color: #FFD700;
  font-size: 16px;
}
ion-content .rating-container .testimonial .testimonial-text {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
}
ion-content .rating-container .continue-btn {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 0;
  width: calc(100% - 48px);
  max-width: 400px;
  --background: var(--accent);
  --background-hover: var(--accent-hover);
  --color: var(--text);
  --border-radius: 12px;
  --padding-top: 20px;
  --padding-bottom: 20px;
  font-weight: 600;
  font-size: 18px;
}

:host ::ng-deep .rating-alert {
  --background: white;
  --min-width: 320px;
}
:host ::ng-deep .rating-alert .alert-wrapper {
  border-radius: 16px;
}
:host ::ng-deep .rating-alert .alert-head {
  padding: 24px 24px 0;
  text-align: center;
}
:host ::ng-deep .rating-alert .alert-head .alert-title {
  font-size: 20px;
  font-weight: 600;
  color: #000;
}
:host ::ng-deep .rating-alert .alert-head .alert-sub-title {
  font-size: 16px;
  color: #666;
  margin-top: 8px;
}
:host ::ng-deep .rating-alert .alert-radio-group {
  padding: 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
}
:host ::ng-deep .rating-alert .alert-radio-group .alert-radio-button {
  margin: 0;
  padding: 0;
}
:host ::ng-deep .rating-alert .alert-radio-group .alert-radio-button .alert-button-inner {
  justify-content: center;
}
:host ::ng-deep .rating-alert .alert-radio-group .alert-radio-button .alert-radio-label {
  font-size: 24px;
  padding: 0;
}
:host ::ng-deep .rating-alert .alert-button-group {
  padding: 0 24px 24px;
}
:host ::ng-deep .rating-alert .alert-button-group .not-now-button {
  margin: 0;
  color: #007AFF;
  font-weight: 500;
  font-size: 16px;
}/*# sourceMappingURL=rating.page.css.map */