:host {
  --bg: #0c0c0f;
  --card: #121217;
  --pill: #1c1c1e;
  --text: #fff;
  --text-muted: #8e8e93;
  --accent: #4d7bff;
  --pill-padding: 10px 14px;
  --radius: 14px;
  /* Calendar variables */
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --quest-bg: #1C1C1E;
  --quest-border: #2C2C2E;
  --active-date: #4169E1;
  --inactive-date: #2C2C2E;
  --bg-tertiary: #2C2C2E;
  --bg-secondary: #1C1C1E;
  --bg-hover: rgba(255, 255, 255, 0.1);
  --primary-color: #4169E1;
  --text-primary: #FFFFFF;
}

body {
  background-color: var(--bg);
  color: var(--text);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

/* Calendar styles */
.week-calendar {
  margin-bottom: 32px;
}

.days, .dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  gap: 8px;
}

.days {
  margin-bottom: 8px;
}

.day-name {
  color: var(--secondary-text);
  font-size: 14px;
}

.date {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 auto;
  font-size: 14px;
  background-color: var(--inactive-date);
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.date-progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  pointer-events: none;
  z-index: 0;
}

.date-progress circle {
  fill: transparent;
  stroke-width: 2.5;
  stroke-linecap: round;
  transform-origin: center;
  transform: rotate(-90deg);
  transition: stroke-dasharray 0.3s ease;
}

/* Different colors for different completion levels */
.date-progress .progress-circle {
  stroke: var(--accent-color);
  stroke-opacity: 0.9;
}

.date-progress .progress-circle.low {
  stroke: var(--accent-color);
}

.date-progress circle {
  stroke-width: 3;
}

/* Special handling for selected days */
.date.selected .date-progress .progress-circle {
  stroke: #78a8f3;
  stroke-opacity: 0.7;
}

.date.active .date-progress .progress-circle {
  stroke: #78a8f3;
  stroke-opacity: 0.7;
}

.date-content {
  position: relative;
  z-index: 1;
}

.date.active {
  background-color: var(--active-date);
  color: white;
}

.date.selected {
  background-color: var(--accent-color);
  color: white;
}

.date.selected::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: var(--accent-color);
  border-radius: 50%;
}

.date:hover:not(.disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}

.date.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.calendar-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.nav-arrow {
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: var(--text-primary);
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
}

.nav-arrow:hover {
  background: var(--bg-hover);
}

/* === SCREEN CARD === */
.screen-summary {
  margin-top: 24px;
  background-color: var(--card);
  border-radius: 22px;
  padding: 20px;
  box-shadow: 0 0 0 1px #1e1e1e;
}

.summary-text {
  text-align: center;
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 24px;
  line-height: 1.5;
}

/* === TIME NUMBERS === */
.screen-time-box {
  display: flex;
  justify-content: space-evenly;
  align-items: baseline;
  margin-bottom: 24px;
}

.big-time {
  font-size: 36px;
  font-weight: 700;
  text-align: center;
  display: block;
  color: white;
}

.time-label {
  font-size: 13px;
  color: var(--text-muted);
  text-align: center;
  margin-top: 2px;
  display: block;
}

/* === HORIZONTAL APP ROW === */
.app-times {
  display: flex;
  gap: 10px;
  margin-bottom: 24px;
  overflow-x: auto;
  scrollbar-width: none;
  padding-bottom: 2px;
}

.app-times::-webkit-scrollbar {
  display: none;
}

.app-time {
  flex: 0 0 auto;
  background-color: var(--pill);
  border-radius: 12px;
  padding: var(--pill-padding);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  gap: 8px;
  color: white;
  white-space: nowrap;
}

/* === BLOCKERS === */
.blockers {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.blocker {
  background-color: var(--pill);
  border-radius: 14px;
  padding: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 15px;
  font-weight: 500;
}

.blocker span:first-child {
  display: flex;
  align-items: center;
  gap: 10px;
}

.blocker svg {
  width: 18px;
  height: 18px;
  color: var(--accent);
}

.cancel-link {
  font-size: 13px;
  color: var(--accent);
  text-decoration: none;
  font-weight: 500;
}

.cancel-link:hover {
  text-decoration: underline;
}

.scheduled-blocks {
  margin-top: 40px;
}

.scheduled-blocks h2 {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-muted);
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* --- CARD --- */
.block-card {
  background-color: var(--pill);
  border-radius: 16px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.block-card-text {
  max-width: 70%;
}

.block-card-title {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.block-card-sub {
  font-size: 14px;
  color: var(--text-muted);
  margin-bottom: 8px;
}

.block-card-detail {
  font-size: 13px;
  color: var(--text-muted);
  line-height: 1.4;
}

/* --- Icon right --- */
.block-card-icon {
  text-align: center;
  color: var(--text-muted);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.block-card-icon svg {
  width: 28px;
  height: 28px;
  margin-bottom: 4px;
}

.usage-text {
  font-size: 12px;
  text-decoration: underline;
}

/* --- Button row --- */
.block-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.block-btn {
  flex: 1;
  background-color: var(--pill);
  color: var(--accent);
  border: none;
  padding: 14px 10px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.block-btn svg {
  width: 20px;
  height: 20px;
  fill: var(--accent);
}/*# sourceMappingURL=focus.page.css.map */