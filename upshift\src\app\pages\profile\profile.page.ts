import { Component, OnInit, inject } from '@angular/core';
import { IonRouterOutlet } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { UserService } from '../../services/user.service';
import { User } from '../../models/user.model';
import { Subscription, of, switchMap } from 'rxjs';
import { NavigationComponent } from '../../components/navigation/navigation.component';
import { SupabaseService } from '../../services/supabase.service';
import { XpService, EntityType } from '../../services/xp.service';

interface CategoryDisplay {
  name: string;
  icon: string;
  color: string;
  current_xp: number;
  required_xp: number;
  progress: number;
}

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]
})
export class ProfilePage implements OnInit {
  // User data
  user: User | null = null;
  userSubscription: Subscription | null = null;

  // XP categories
  categories: CategoryDisplay[] = [];
  nextLevel = 0;

  // Bio form
  showBioForm = false;
  editedBio = '';

  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private xpService = inject(XpService);
  private routerOutlet = inject(IonRouterOutlet);

  constructor() {}

  ngOnInit() {
    // Subscribe to the current user profile from UserService
    this.userSubscription = this.userService.currentUserProfile$.subscribe(userProfile => {
      console.log('Profile: Received user profile data:', userProfile);

      if (userProfile) {
        // Convert UserProfile to User
        const user = userProfile as unknown as User;

        // Store the original user data for reference
        console.log('Profile: Original user data from UserService:', JSON.stringify(user));

        // Ensure all required fields have default values
        this.user = {
          ...user,
          level: user.level ?? 1,  // Use nullish coalescing to handle 0 values correctly
          strength_xp: user.strength_xp ?? 0,
          money_xp: user.money_xp ?? 0,
          health_xp: user.health_xp ?? 0,
          knowledge_xp: user.knowledge_xp ?? 0,
          bio: user.bio || '',
          title: user.title || '🥚 Beginner'
        };

        this.editedBio = this.user.bio || '';

        console.log('Profile: User with defaults:', this.user);
        console.log('Profile: User level:', this.user.level);
        console.log('Profile: User XP fields:', {
          strength_xp: this.user.strength_xp,
          money_xp: this.user.money_xp,
          health_xp: this.user.health_xp,
          knowledge_xp: this.user.knowledge_xp
        });

        this.calculateXpProgress();

        // Ensure edit button visibility is correct on page load
        setTimeout(() => {
          const editBioBtn = document.getElementById('edit-bio-btn');
          if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {
            editBioBtn.style.display = 'none';
          }
        }, 100);
      } else {
        console.error('Profile: No user profile data received');

        // If no profile data, try to get the auth user and ensure it exists
        this.supabaseService.currentUser$.pipe(
          switchMap(authUser => {
            if (!authUser) {
              console.log('Profile: No authenticated user found');
              return of(null);
            }

            console.log('Profile: Auth user:', authUser);
            console.log('Profile: Auth user ID:', authUser.id);

            // Use the ensureUserExists method to get or create the user
            return this.userService.ensureUserExists(authUser);
          })
        ).subscribe();
      }
    });
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  ionViewWillEnter() {
    console.log('Profile: ionViewWillEnter');

    // Check if we're coming from another page (not initial load)
    if (this.routerOutlet.canGoBack()) {
      console.log('Profile: Coming from another page, refreshing profile');

      // Refresh the user profile
      this.userService.refreshCurrentUserProfile().then(() => {
        console.log('Profile: Profile refreshed on page enter');
      }).catch(error => {
        console.error('Profile: Error refreshing profile:', error);
      });
    } else {
      console.log('Profile: Initial page load, no refresh needed');
    }
  }

  async calculateXpProgress() {
    if (!this.user) {
      console.error('Cannot calculate XP progress: user is null');
      return;
    }

    console.log('Calculating XP progress for user:', this.user);

    // Make sure user.level is defined and is a number
    if (typeof this.user.level !== 'number') {
      console.error('User level is not a number:', this.user.level);
      this.user.level = 1; // Default to level 1 if not set
    }

    // Use the XP service to calculate the progress
    this.xpService.calculateXpProgress(this.user, EntityType.USER).subscribe(result => {
      if (result) {
        this.categories = result.categories;
        this.nextLevel = result.next_level;
        console.log('XP progress calculated:', this.categories);
        console.log('Next level:', this.nextLevel);
      }
    });
  }

  toggleBioForm() {
    this.showBioForm = !this.showBioForm;

    // Reset the edited bio to the current bio when opening the form
    if (this.showBioForm && this.user) {
      this.editedBio = this.user.bio || '';
    }

    // When closing the form, ensure edit button visibility is correct
    if (!this.showBioForm) {
      setTimeout(() => {
        const editBioBtn = document.getElementById('edit-bio-btn');
        if (editBioBtn && (!this.user?.bio || this.user?.bio.trim() === '')) {
          editBioBtn.style.display = 'none';
        }
      }, 0);
    }
  }

  updateBio() {
    if (!this.user || !this.user.id) return;

    // Trim and validate bio
    const bio = this.editedBio.trim();
    if (bio.length > 100) {
      // Show error message
      return;
    }

    // Update user bio (will be set to empty string if only whitespace)
    this.userService.updateUserBio(this.user.id, bio).then(() => {
      if (this.user) {
        // If bio is empty, set it to empty string
        this.user.bio = bio === '' ? '' : bio;
      }
      this.showBioForm = false;

      // Force DOM update to ensure edit button visibility is correct
      setTimeout(() => {
        const editBioBtn = document.getElementById('edit-bio-btn');
        if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {
          editBioBtn.style.display = 'none';
        }
      }, 0);
    });
  }
}
