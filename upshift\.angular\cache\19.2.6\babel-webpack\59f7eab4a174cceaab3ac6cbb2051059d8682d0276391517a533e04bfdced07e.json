{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { d as doc } from './index-a5d50daf.js';\nimport { r as raf, g as getElementRoot } from './helpers-78efeec3.js';\nimport { a as hapticSelectionStart, b as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-ac164e4c.js';\nimport { a as isPlatform, b as getIonMode } from './ionic-global-ca86cf32.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport './capacitor-59395cbd.js';\nconst pickerColumnCss = \":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;max-width:100%;height:200px;font-size:22px;text-align:center}.assistive-focusable{left:0;right:0;top:0;bottom:0;position:absolute;z-index:1;pointer-events:none}.assistive-focusable:focus{outline:none}.picker-opts{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-width:26px;max-height:200px;outline:none;text-align:inherit;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none}.picker-item-empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.picker-opts::-webkit-scrollbar{display:none}::slotted(ion-picker-column-option){display:block;scroll-snap-align:center}.picker-item-empty,:host(:not([disabled])) ::slotted(ion-picker-column-option.option-disabled){scroll-snap-align:none}::slotted([slot=prefix]),::slotted([slot=suffix]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}::slotted([slot=prefix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:end;justify-content:end}::slotted([slot=suffix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:start;justify-content:start}:host(.picker-column-disabled) .picker-opts{overflow-y:hidden}:host(.picker-column-disabled) ::slotted(ion-picker-column-option){cursor:default;opacity:0.4;pointer-events:none}@media (any-hover: hover){:host(:focus) .picker-opts{outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}\";\nconst IonPickerColumnStyle0 = pickerColumnCss;\nconst PickerColumn = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.isScrolling = false;\n    this.isColumnVisible = false;\n    this.canExitInputMode = true;\n    this.updateValueTextOnScroll = false;\n    this.centerPickerItemInView = (target, smooth = true, canExitInputMode = true) => {\n      const {\n        isColumnVisible,\n        scrollEl\n      } = this;\n      if (isColumnVisible && scrollEl) {\n        // (Vertical offset from parent) - (three empty picker rows) + (half the height of the target to ensure the scroll triggers)\n        const top = target.offsetTop - 3 * target.clientHeight + target.clientHeight / 2;\n        if (scrollEl.scrollTop !== top) {\n          /**\n           * Setting this flag prevents input\n           * mode from exiting in the picker column's\n           * scroll callback. This is useful when the user manually\n           * taps an item or types on the keyboard as both\n           * of these can cause a scroll to occur.\n           */\n          this.canExitInputMode = canExitInputMode;\n          this.updateValueTextOnScroll = false;\n          scrollEl.scroll({\n            top,\n            left: 0,\n            behavior: smooth ? 'smooth' : undefined\n          });\n        }\n      }\n    };\n    this.setPickerItemActiveState = (item, isActive) => {\n      if (isActive) {\n        item.classList.add(PICKER_ITEM_ACTIVE_CLASS);\n      } else {\n        item.classList.remove(PICKER_ITEM_ACTIVE_CLASS);\n      }\n    };\n    /**\n     * When ionInputModeChange is emitted, each column\n     * needs to check if it is the one being made available\n     * for text entry.\n     */\n    this.inputModeChange = ev => {\n      if (!this.numericInput) {\n        return;\n      }\n      const {\n        useInputMode,\n        inputModeColumn\n      } = ev.detail;\n      /**\n       * If inputModeColumn is undefined then this means\n       * all numericInput columns are being selected.\n       */\n      const isColumnActive = inputModeColumn === undefined || inputModeColumn === this.el;\n      if (!useInputMode || !isColumnActive) {\n        this.setInputModeActive(false);\n        return;\n      }\n      this.setInputModeActive(true);\n    };\n    /**\n     * Setting isActive will cause a re-render.\n     * As a result, we do not want to cause the\n     * re-render mid scroll as this will cause\n     * the picker column to jump back to\n     * whatever value was selected at the\n     * start of the scroll interaction.\n     */\n    this.setInputModeActive = state => {\n      if (this.isScrolling) {\n        this.scrollEndCallback = () => {\n          this.isActive = state;\n        };\n        return;\n      }\n      this.isActive = state;\n    };\n    /**\n     * When the column scrolls, the component\n     * needs to determine which item is centered\n     * in the view and will emit an ionChange with\n     * the item object.\n     */\n    this.initializeScrollListener = () => {\n      /**\n       * The haptics for the wheel picker are\n       * an iOS-only feature. As a result, they should\n       * be disabled on Android.\n       */\n      const enableHaptics = isPlatform('ios');\n      const {\n        el,\n        scrollEl\n      } = this;\n      let timeout;\n      let activeEl = this.activeItem;\n      const scrollCallback = () => {\n        raf(() => {\n          var _a;\n          if (!scrollEl) return;\n          if (timeout) {\n            clearTimeout(timeout);\n            timeout = undefined;\n          }\n          if (!this.isScrolling) {\n            enableHaptics && hapticSelectionStart();\n            this.isScrolling = true;\n          }\n          /**\n           * Select item in the center of the column\n           * which is the month/year that we want to select\n           */\n          const bbox = scrollEl.getBoundingClientRect();\n          const centerX = bbox.x + bbox.width / 2;\n          const centerY = bbox.y + bbox.height / 2;\n          /**\n           * elementFromPoint returns the top-most element.\n           * This means that if an ion-backdrop is overlaying the\n           * picker then the appropriate picker column option will\n           * not be selected. To account for this, we use elementsFromPoint\n           * and use an Array.find to find the appropriate column option\n           * at that point.\n           *\n           * Additionally, the picker column could be used in the\n           * Shadow DOM (i.e. in ion-datetime) so we need to make\n           * sure we are choosing the correct host otherwise\n           * the elements returns by elementsFromPoint will be\n           * retargeted. To account for this, we check to see\n           * if the picker column has a parent shadow root. If\n           * so, we use that shadow root when doing elementsFromPoint.\n           * Otherwise, we just use the document.\n           */\n          const rootNode = el.getRootNode();\n          const hasParentShadow = rootNode instanceof ShadowRoot;\n          const referenceNode = hasParentShadow ? rootNode : doc;\n          /**\n           * If the reference node is undefined\n           * then it's likely that doc is undefined\n           * due to being in an SSR environment.\n           */\n          if (referenceNode === undefined) {\n            return;\n          }\n          const elementsAtPoint = referenceNode.elementsFromPoint(centerX, centerY);\n          /**\n           * elementsFromPoint can returns multiple elements\n           * so find the relevant picker column option if one exists.\n           */\n          const newActiveElement = elementsAtPoint.find(el => el.tagName === 'ION-PICKER-COLUMN-OPTION');\n          if (activeEl !== undefined) {\n            this.setPickerItemActiveState(activeEl, false);\n          }\n          if (newActiveElement === undefined || newActiveElement.disabled) {\n            return;\n          }\n          /**\n           * If we are selecting a new value,\n           * we need to run haptics again.\n           */\n          if (newActiveElement !== activeEl) {\n            enableHaptics && hapticSelectionChanged();\n            if (this.canExitInputMode) {\n              /**\n               * The native iOS wheel picker\n               * only dismisses the keyboard\n               * once the selected item has changed\n               * as a result of a swipe\n               * from the user. If `canExitInputMode` is\n               * `false` then this means that the\n               * scroll is happening as a result of\n               * the `value` property programmatically changing\n               * either by an application or by the user via the keyboard.\n               */\n              this.exitInputMode();\n            }\n          }\n          activeEl = newActiveElement;\n          this.setPickerItemActiveState(newActiveElement, true);\n          /**\n           * Set the aria-valuetext even though the value prop has not been updated yet.\n           * This enables some screen readers to announce the value as the users drag\n           * as opposed to when their release their pointer from the screen.\n           *\n           * When the value is programmatically updated, we will smoothly scroll\n           * to the new option. However, we do not want to update aria-valuetext mid-scroll\n           * as that can cause the old value to be briefly set before being set to the\n           * correct option. This will cause some screen readers to announce the old value\n           * again before announcing the new value. The correct valuetext will be set on render.\n           */\n          if (this.updateValueTextOnScroll) {\n            (_a = this.assistiveFocusable) === null || _a === void 0 ? void 0 : _a.setAttribute('aria-valuetext', this.getOptionValueText(newActiveElement));\n          }\n          timeout = setTimeout(() => {\n            this.isScrolling = false;\n            this.updateValueTextOnScroll = true;\n            enableHaptics && hapticSelectionEnd();\n            /**\n             * Certain tasks (such as those that\n             * cause re-renders) should only be done\n             * once scrolling has finished, otherwise\n             * flickering may occur.\n             */\n            const {\n              scrollEndCallback\n            } = this;\n            if (scrollEndCallback) {\n              scrollEndCallback();\n              this.scrollEndCallback = undefined;\n            }\n            /**\n             * Reset this flag as the\n             * next scroll interaction could\n             * be a scroll from the user. In this\n             * case, we should exit input mode.\n             */\n            this.canExitInputMode = true;\n            this.setValue(newActiveElement.value);\n          }, 250);\n        });\n      };\n      /**\n       * Wrap this in an raf so that the scroll callback\n       * does not fire when component is initially shown.\n       */\n      raf(() => {\n        if (!scrollEl) return;\n        scrollEl.addEventListener('scroll', scrollCallback);\n        this.destroyScrollListener = () => {\n          scrollEl.removeEventListener('scroll', scrollCallback);\n        };\n      });\n    };\n    /**\n     * Tells the parent picker to\n     * exit text entry mode. This is only called\n     * when the selected item changes during scroll, so\n     * we know that the user likely wants to scroll\n     * instead of type.\n     */\n    this.exitInputMode = () => {\n      const {\n        parentEl\n      } = this;\n      if (parentEl == null) return;\n      parentEl.exitInputMode();\n      /**\n       * setInputModeActive only takes\n       * effect once scrolling stops to avoid\n       * a component re-render while scrolling.\n       * However, we want the visual active\n       * indicator to go away immediately, so\n       * we call classList.remove here.\n       */\n      this.el.classList.remove('picker-column-active');\n    };\n    /**\n     * Find the next enabled option after the active option.\n     * @param stride - How many options to \"jump\" over in order to select the next option.\n     * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n     * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n     * the enabled option 5 options after the active one. Note that the actual option selected\n     * may be past the stride if the option at the stride is disabled.\n     */\n    this.findNextOption = (stride = 1) => {\n      const {\n        activeItem\n      } = this;\n      if (!activeItem) return null;\n      let prevNode = activeItem;\n      let node = activeItem.nextElementSibling;\n      while (node != null) {\n        if (stride > 0) {\n          stride--;\n        }\n        if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n          return node;\n        }\n        prevNode = node;\n        // Use nextElementSibling instead of nextSibling to avoid text/comment nodes\n        node = node.nextElementSibling;\n      }\n      return prevNode;\n    };\n    /**\n     * Find the next enabled option after the active option.\n     * @param stride - How many options to \"jump\" over in order to select the next option.\n     * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n     * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n     * the enabled option 5 options before the active one. Note that the actual option selected\n     *  may be past the stride if the option at the stride is disabled.\n     */\n    this.findPreviousOption = (stride = 1) => {\n      const {\n        activeItem\n      } = this;\n      if (!activeItem) return null;\n      let nextNode = activeItem;\n      let node = activeItem.previousElementSibling;\n      while (node != null) {\n        if (stride > 0) {\n          stride--;\n        }\n        if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n          return node;\n        }\n        nextNode = node;\n        // Use previousElementSibling instead of previousSibling to avoid text/comment nodes\n        node = node.previousElementSibling;\n      }\n      return nextNode;\n    };\n    this.onKeyDown = ev => {\n      /**\n       * The below operations should be inverted when running on a mobile device.\n       * For example, swiping up will dispatch an \"ArrowUp\" event. On desktop,\n       * this should cause the previous option to be selected. On mobile, swiping\n       * up causes a view to scroll down. As a result, swiping up on mobile should\n       * cause the next option to be selected. The Home/End operations remain\n       * unchanged because those always represent the first/last options, respectively.\n       */\n      const mobile = isPlatform('mobile');\n      let newOption = null;\n      switch (ev.key) {\n        case 'ArrowDown':\n          newOption = mobile ? this.findPreviousOption() : this.findNextOption();\n          break;\n        case 'ArrowUp':\n          newOption = mobile ? this.findNextOption() : this.findPreviousOption();\n          break;\n        case 'PageUp':\n          newOption = mobile ? this.findNextOption(5) : this.findPreviousOption(5);\n          break;\n        case 'PageDown':\n          newOption = mobile ? this.findPreviousOption(5) : this.findNextOption(5);\n          break;\n        case 'Home':\n          /**\n           * There is no guarantee that the first child will be an ion-picker-column-option,\n           * so we do not use firstElementChild.\n           */\n          newOption = this.el.querySelector('ion-picker-column-option:first-of-type');\n          break;\n        case 'End':\n          /**\n           * There is no guarantee that the last child will be an ion-picker-column-option,\n           * so we do not use lastElementChild.\n           */\n          newOption = this.el.querySelector('ion-picker-column-option:last-of-type');\n          break;\n      }\n      if (newOption !== null) {\n        this.setValue(newOption.value);\n        // This stops any default browser behavior such as scrolling\n        ev.preventDefault();\n      }\n    };\n    /**\n     * Utility to generate the correct text for aria-valuetext.\n     */\n    this.getOptionValueText = el => {\n      var _a;\n      return el ? (_a = el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : el.innerText : '';\n    };\n    /**\n     * Render an element that overlays the column. This element is for assistive\n     * tech to allow users to navigate the column up/down. This element should receive\n     * focus as it listens for synthesized keyboard events as required by the\n     * slider role: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/slider_role\n     */\n    this.renderAssistiveFocusable = () => {\n      const {\n        activeItem\n      } = this;\n      const valueText = this.getOptionValueText(activeItem);\n      /**\n       * When using the picker, the valuetext provides important context that valuenow\n       * does not. Additionally, using non-zero valuemin/valuemax values can cause\n       * WebKit to incorrectly announce numeric valuetext values (such as a year\n       * like \"2024\") as percentages: https://bugs.webkit.org/show_bug.cgi?id=273126\n       */\n      return h(\"div\", {\n        ref: el => this.assistiveFocusable = el,\n        class: \"assistive-focusable\",\n        role: \"slider\",\n        tabindex: this.disabled ? undefined : 0,\n        \"aria-label\": this.ariaLabel,\n        \"aria-valuemin\": 0,\n        \"aria-valuemax\": 0,\n        \"aria-valuenow\": 0,\n        \"aria-valuetext\": valueText,\n        \"aria-orientation\": \"vertical\",\n        onKeyDown: ev => this.onKeyDown(ev)\n      });\n    };\n    this.ariaLabel = null;\n    this.isActive = false;\n    this.disabled = false;\n    this.value = undefined;\n    this.color = 'primary';\n    this.numericInput = false;\n  }\n  ariaLabelChanged(newValue) {\n    this.ariaLabel = newValue;\n  }\n  valueChange() {\n    if (this.isColumnVisible) {\n      /**\n       * Only scroll the active item into view when the picker column\n       * is actively visible to the user.\n       */\n      this.scrollActiveItemIntoView(true);\n    }\n  }\n  /**\n   * Only setup scroll listeners\n   * when the picker is visible, otherwise\n   * the container will have a scroll\n   * height of 0px.\n   */\n  componentWillLoad() {\n    /**\n     * We cache parentEl in a local variable\n     * so we don't need to keep accessing\n     * the class variable (which comes with\n     * a small performance hit)\n     */\n    const parentEl = this.parentEl = this.el.closest('ion-picker');\n    const visibleCallback = entries => {\n      /**\n       * Browsers will sometimes group multiple IO events into a single callback.\n       * As a result, we want to grab the last/most recent event in case there are multiple events.\n       */\n      const ev = entries[entries.length - 1];\n      if (ev.isIntersecting) {\n        const {\n          activeItem,\n          el\n        } = this;\n        this.isColumnVisible = true;\n        /**\n         * Because this initial call to scrollActiveItemIntoView has to fire before\n         * the scroll listener is set up, we need to manage the active class manually.\n         */\n        const oldActive = getElementRoot(el).querySelector(`.${PICKER_ITEM_ACTIVE_CLASS}`);\n        if (oldActive) {\n          this.setPickerItemActiveState(oldActive, false);\n        }\n        this.scrollActiveItemIntoView();\n        if (activeItem) {\n          this.setPickerItemActiveState(activeItem, true);\n        }\n        this.initializeScrollListener();\n      } else {\n        this.isColumnVisible = false;\n        if (this.destroyScrollListener) {\n          this.destroyScrollListener();\n          this.destroyScrollListener = undefined;\n        }\n      }\n    };\n    /**\n     * Set the root to be the parent picker element\n     * This causes the IO callback\n     * to be fired in WebKit as soon as the element\n     * is visible. If we used the default root value\n     * then WebKit would only fire the IO callback\n     * after any animations (such as a modal transition)\n     * finished, and there would potentially be a flicker.\n     */\n    new IntersectionObserver(visibleCallback, {\n      threshold: 0.001,\n      root: this.parentEl\n    }).observe(this.el);\n    if (parentEl !== null) {\n      // TODO(FW-2832): type\n      parentEl.addEventListener('ionInputModeChange', ev => this.inputModeChange(ev));\n    }\n  }\n  componentDidRender() {\n    const {\n      el,\n      activeItem,\n      isColumnVisible,\n      value\n    } = this;\n    if (isColumnVisible && !activeItem) {\n      const firstOption = el.querySelector('ion-picker-column-option');\n      /**\n       * If the picker column does not have an active item and the current value\n       * does not match the first item in the picker column, that means\n       * the value is out of bounds. In this case, we assign the value to the\n       * first item to match the scroll position of the column.\n       *\n       */\n      if (firstOption !== null && firstOption.value !== value) {\n        this.setValue(firstOption.value);\n      }\n    }\n  }\n  /** @internal  */\n  scrollActiveItemIntoView(smooth = false) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const activeEl = _this.activeItem;\n      if (activeEl) {\n        _this.centerPickerItemInView(activeEl, smooth, false);\n      }\n    })();\n  }\n  /**\n   * Sets the value prop and fires the ionChange event.\n   * This is used when we need to fire ionChange from\n   * user-generated events that cannot be caught with normal\n   * input/change event listeners.\n   * @internal\n   */\n  setValue(value) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.disabled === true || _this2.value === value) {\n        return;\n      }\n      _this2.value = value;\n      _this2.ionChange.emit({\n        value\n      });\n    })();\n  }\n  /**\n   * Sets focus on the scrollable container within the picker column.\n   * Use this method instead of the global `pickerColumn.focus()`.\n   */\n  setFocus() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (_this3.assistiveFocusable) {\n        _this3.assistiveFocusable.focus();\n      }\n    })();\n  }\n  connectedCallback() {\n    var _a;\n    this.ariaLabel = (_a = this.el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : 'Select a value';\n  }\n  get activeItem() {\n    const {\n      value\n    } = this;\n    const options = Array.from(this.el.querySelectorAll('ion-picker-column-option'));\n    return options.find(option => {\n      /**\n       * If the whole picker column is disabled, the current value should appear active\n       * If the current value item is specifically disabled, it should not appear active\n       */\n      if (!this.disabled && option.disabled) {\n        return false;\n      }\n      return option.value === value;\n    });\n  }\n  render() {\n    const {\n      color,\n      disabled,\n      isActive,\n      numericInput\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'a221dc10f1eb7c41637a16d2c7167c16939822fd',\n      class: createColorClasses(color, {\n        [mode]: true,\n        ['picker-column-active']: isActive,\n        ['picker-column-numeric-input']: numericInput,\n        ['picker-column-disabled']: disabled\n      })\n    }, this.renderAssistiveFocusable(), h(\"slot\", {\n      key: '81b0656f606856f3dc0a657bf167d81a5011405e',\n      name: \"prefix\"\n    }), h(\"div\", {\n      key: '71b9de67c04150255dd66592601c9d926db0c31c',\n      \"aria-hidden\": \"true\",\n      class: \"picker-opts\",\n      ref: el => {\n        this.scrollEl = el;\n      },\n      /**\n       * When an element has an overlay scroll style and\n       * a fixed height, Firefox will focus the scrollable\n       * container if the content exceeds the container's\n       * dimensions.\n       *\n       * This causes keyboard navigation to focus to this\n       * element instead of going to the next element in\n       * the tab order.\n       *\n       * The desired behavior is for the user to be able to\n       * focus the assistive focusable element and tab to\n       * the next element in the tab order. Instead of tabbing\n       * to this element.\n       *\n       * To prevent this, we set the tabIndex to -1. This\n       * will match the behavior of the other browsers.\n       */\n      tabIndex: -1\n    }, h(\"div\", {\n      key: 'ebdc2f08c83db0cf17b4be29f28fcb00f529601e',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '04ab56fcb8e6a7d6af00204c4560feb99ff34a56',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '6cf8f538903faf0fe1e4130f3eaf7b4e2e17cb52',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"slot\", {\n      key: '1cc392307b70c576be5b81b5226ceba735957f0f'\n    }), h(\"div\", {\n      key: '23e3f28e2a99b9aa8b7c8f68ad9583e3ca63e9e2',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '8a0563f09780c3116af0caebe4f40587ec1f041f',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '13207e248fc0009f37e0c90a3ee2bac2f130b856',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\")), h(\"slot\", {\n      key: '55ecf2ab5f214f936c2468cbdb7952daf89416b8',\n      name: \"suffix\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"aria-label\": [\"ariaLabelChanged\"],\n      \"value\": [\"valueChange\"]\n    };\n  }\n};\nconst PICKER_ITEM_ACTIVE_CLASS = 'option-active';\nPickerColumn.style = IonPickerColumnStyle0;\nexport { PickerColumn as ion_picker_column };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}