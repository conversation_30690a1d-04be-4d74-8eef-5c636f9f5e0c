{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, i as forceUpdate, e as Host, f as getElement } from './index-527b9e34.js';\nimport { b as getIonMode } from './ionic-global-ca86cf32.js';\nimport { s as safeCall } from './overlays-41a5d51b.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport './index-a5d50daf.js';\nimport './helpers-78efeec3.js';\nimport './hardware-back-button-864101a3.js';\nimport './framework-delegate-2eea1763.js';\nimport './gesture-controller-314a54f6.js';\nimport './index-738d7504.js';\nconst ionicSelectModalMdCss = \".sc-ion-select-modal-ionic-h{height:100%}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(container){display:none}ion-list.sc-ion-select-modal-ionic ion-radio.sc-ion-select-modal-ionic::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-ionic{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-ionic{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-ionic{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst IonSelectModalIonicStyle0 = ionicSelectModalMdCss;\nconst selectModalIosCss = \".sc-ion-select-modal-ios-h{height:100%}ion-item.sc-ion-select-modal-ios{--inner-padding-end:0}ion-radio.sc-ion-select-modal-ios::after{bottom:0;position:absolute;width:calc(100% - 0.9375rem - 16px);border-width:0px 0px 0.55px 0px;border-style:solid;border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));content:\\\"\\\"}ion-radio.sc-ion-select-modal-ios::after{inset-inline-start:calc(0.9375rem + 16px)}\";\nconst IonSelectModalIosStyle0 = selectModalIosCss;\nconst selectModalMdCss = \".sc-ion-select-modal-md-h{height:100%}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(container){display:none}ion-list.sc-ion-select-modal-md ion-radio.sc-ion-select-modal-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-modal-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-modal-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-modal-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst IonSelectModalMdStyle0 = selectModalMdCss;\nconst SelectModal = /*#__PURE__*/(() => {\n  let SelectModal = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.header = undefined;\n      this.multiple = undefined;\n      this.options = [];\n    }\n    closeModal() {\n      const modal = this.el.closest('ion-modal');\n      if (modal) {\n        modal.dismiss();\n      }\n    }\n    findOptionFromEvent(ev) {\n      const {\n        options\n      } = this;\n      return options.find(o => o.value === ev.target.value);\n    }\n    getValues(ev) {\n      const {\n        multiple,\n        options\n      } = this;\n      if (multiple) {\n        // this is a modal with checkboxes (multiple value select)\n        // return an array of all the checked values\n        return options.filter(o => o.checked).map(o => o.value);\n      }\n      // this is a modal with radio buttons (single value select)\n      // return the value that was clicked, otherwise undefined\n      const option = ev ? this.findOptionFromEvent(ev) : null;\n      return option ? option.value : undefined;\n    }\n    callOptionHandler(ev) {\n      const option = this.findOptionFromEvent(ev);\n      const values = this.getValues(ev);\n      if (option === null || option === void 0 ? void 0 : option.handler) {\n        safeCall(option.handler, values);\n      }\n    }\n    setChecked(ev) {\n      const {\n        multiple\n      } = this;\n      const option = this.findOptionFromEvent(ev);\n      // this is a modal with checkboxes (multiple value select)\n      // we need to set the checked value for this option\n      if (multiple && option) {\n        option.checked = ev.detail.checked;\n      }\n    }\n    renderRadioOptions() {\n      const checked = this.options.filter(o => o.checked).map(o => o.value)[0];\n      return h(\"ion-radio-group\", {\n        value: checked,\n        onIonChange: ev => this.callOptionHandler(ev)\n      }, this.options.map(option => h(\"ion-item\", {\n        lines: \"none\",\n        class: Object.assign({\n          // TODO FW-4784\n          'item-radio-checked': option.value === checked\n        }, getClassMap(option.cssClass))\n      }, h(\"ion-radio\", {\n        value: option.value,\n        disabled: option.disabled,\n        justify: \"start\",\n        labelPlacement: \"end\",\n        onClick: () => this.closeModal(),\n        onKeyUp: ev => {\n          if (ev.key === ' ') {\n            /**\n             * Selecting a radio option with keyboard navigation,\n             * either through the Enter or Space keys, should\n             * dismiss the modal.\n             */\n            this.closeModal();\n          }\n        }\n      }, option.text))));\n    }\n    renderCheckboxOptions() {\n      return this.options.map(option => h(\"ion-item\", {\n        class: Object.assign({\n          // TODO FW-4784\n          'item-checkbox-checked': option.checked\n        }, getClassMap(option.cssClass))\n      }, h(\"ion-checkbox\", {\n        value: option.value,\n        disabled: option.disabled,\n        checked: option.checked,\n        justify: \"start\",\n        labelPlacement: \"end\",\n        onIonChange: ev => {\n          this.setChecked(ev);\n          this.callOptionHandler(ev);\n          // TODO FW-4784\n          forceUpdate(this);\n        }\n      }, option.text)));\n    }\n    render() {\n      return h(Host, {\n        key: '885198a9f21884e3bfb9bf0af53e0ee3ae37b231',\n        class: getIonMode(this)\n      }, h(\"ion-header\", {\n        key: 'd8b63726869747ac711e4fda78a50ce46f72970c'\n      }, h(\"ion-toolbar\", {\n        key: '9ab2a4c1480dd74eeae38d7b580a2e87fb71270e'\n      }, this.header !== undefined && h(\"ion-title\", {\n        key: '87a7034385ef57f55cefdd0371dbb66a64827290'\n      }, this.header), h(\"ion-buttons\", {\n        key: '0a35424ea13ca002abc9a43b6138730254f187d0',\n        slot: \"end\"\n      }, h(\"ion-button\", {\n        key: '238bf40b47128d9aa995d14d9ff9ebcae4f79492',\n        onClick: () => this.closeModal()\n      }, \"Close\")))), h(\"ion-content\", {\n        key: '4a256f3381f8cabbc7194337b8ae4aa1c3ab1066'\n      }, h(\"ion-list\", {\n        key: 'acd38fc52024632176467ed6a84106a454021544'\n      }, this.multiple === true ? this.renderCheckboxOptions() : this.renderRadioOptions())));\n    }\n    get el() {\n      return getElement(this);\n    }\n  };\n  SelectModal.style = {\n    ionic: IonSelectModalIonicStyle0,\n    ios: IonSelectModalIosStyle0,\n    md: IonSelectModalMdStyle0\n  };\n  return SelectModal;\n})();\nexport { SelectModal as ion_select_modal };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}