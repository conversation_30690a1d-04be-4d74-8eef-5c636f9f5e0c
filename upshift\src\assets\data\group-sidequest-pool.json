[{"name": "Group Meditation", "description": "Meditate together as a group for at least 10 minutes", "goal_value": 10, "category": "health", "goal_unit": "min", "emoji": "🧘"}, {"name": "Group Workout", "description": "Complete a workout together as a group", "goal_value": 30, "category": "strength", "goal_unit": "min", "emoji": "💪"}, {"name": "Group Study Session", "description": "Study together as a group for at least 45 minutes", "goal_value": 45, "category": "knowledge", "goal_unit": "min", "emoji": "📚"}, {"name": "Group Budget Planning", "description": "Plan your group budget together", "goal_value": 1, "category": "money", "goal_unit": "count", "emoji": "💰"}, {"name": "Group Walk", "description": "Go for a walk together as a group", "goal_value": 3000, "category": "health", "goal_unit": "steps", "emoji": "🚶"}, {"name": "Group Hydration", "description": "Each member drinks at least 2 glasses of water", "goal_value": 2, "category": "health", "goal_unit": "drink", "emoji": "💧"}, {"name": "Group Stretching", "description": "<PERSON><PERSON>ch together as a group for at least 5 minutes", "goal_value": 5, "category": "strength", "goal_unit": "min", "emoji": "🤸"}, {"name": "Group Reading", "description": "Read a book or article together as a group", "goal_value": 20, "category": "knowledge", "goal_unit": "min", "emoji": "📖"}, {"name": "Group Savings Challenge", "description": "Each member saves a small amount today", "goal_value": 1, "category": "money", "goal_unit": "count", "emoji": "💵"}, {"name": "Group Healthy Meal", "description": "Prepare and eat a healthy meal together", "goal_value": 1, "category": "health", "goal_unit": "count", "emoji": "🥗"}, {"name": "Group Pushups", "description": "Each member does at least 10 pushups", "goal_value": 10, "category": "strength", "goal_unit": "count", "emoji": "💪"}, {"name": "Group Learning", "description": "Learn something new together as a group", "goal_value": 1, "category": "knowledge", "goal_unit": "count", "emoji": "🧠"}, {"name": "Group Financial Review", "description": "Review your financial goals together", "goal_value": 1, "category": "money", "goal_unit": "count", "emoji": "📊"}, {"name": "Group Yoga", "description": "Do yoga together as a group for at least 15 minutes", "goal_value": 15, "category": "health", "goal_unit": "min", "emoji": "🧘"}, {"name": "Group Squats", "description": "Each member does at least 15 squats", "goal_value": 15, "category": "strength", "goal_unit": "count", "emoji": "🏋️"}]