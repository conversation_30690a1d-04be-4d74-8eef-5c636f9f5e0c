{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n  constructor(context) {\n    super(context.message);\n    this.name = 'PostgrestError';\n    this.details = context.details;\n    this.hint = context.hint;\n    this.code = context.code;\n  }\n}\nexports.default = PostgrestError;\n//# sourceMappingURL=PostgrestError.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}