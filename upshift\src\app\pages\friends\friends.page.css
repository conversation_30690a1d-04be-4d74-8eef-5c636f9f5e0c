/* Exact CSS from Django template */
:root {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --quest-bg: #1C1C1E;
  --quest-border: #2C2C2E;
  --active-date: #4169E1;
  --inactive-date: #2C2C2E;
  --card-bg: #1C1C1E;
  --border-color: #2C2C2E;
  --bg-color: #0C0C0F;
  --danger-color: #FF3B30;
  --accent-color-rgb: 65, 105, 225;
}

:host {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
  display: block;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.friends-container {
  padding: 20px;
  margin-bottom: 80px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.section-icon {
  font-size: 24px;
  margin-right: 10px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
}

.friend-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.friend-info {
  display: flex;
  align-items: center;
}

.friend-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 15px;
  overflow: hidden;
}

.friend-avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.friend-name {
  font-size: 16px;
  font-weight: 500;
}

.friend-actions a {
  color: var(--danger-color);
  text-decoration: none;
}

.code-section {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
}

.code-header {
  margin-bottom: 15px;
}

.code-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.code-display {
  background-color: var(--bg-color);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 2px;
  margin-bottom: 15px;
}

.code-info {
  font-size: 14px;
  color: var(--secondary-text);
  text-align: center;
}

.add-code-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.add-code-form input {
  padding: 12px;
  border-radius: 8px;
  border: none;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 16px;
}

.add-code-form button {
  padding: 12px;
  border-radius: 8px;
  border: none;
  background-color: var(--accent-color);
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.generate-code-btn {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  border: none;
  background-color: var(--accent-color);
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.no-friends {
  text-align: center;
  padding: 20px;
  color: var(--secondary-text);
}

.friend-requests {
  margin-bottom: 30px;
}

.request-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
}

.request-info {
  margin-bottom: 10px;
}

.request-actions {
  display: flex;
  gap: 10px;
}

.request-actions button {
  flex: 1;
  padding: 8px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.accept-btn {
  background-color: var(--accent-color);
  color: white;
}

.reject-btn {
  background-color: var(--danger-color);
  color: white;
}

/* Leaderboard styles */
.leaderboard-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.leaderboard-row {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  position: relative;
}

.current-user {
  background-color: rgba(var(--accent-color-rgb), 0.2);
  border: 2px solid var(--accent-color);
}

.rank-badge {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--bg-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
  flex-shrink: 0;
}

.leaderboard-row:nth-child(1) .rank-badge {
  background-color: gold;
  color: black;
}

.leaderboard-row:nth-child(2) .rank-badge {
  background-color: silver;
  color: black;
}

.leaderboard-row:nth-child(3) .rank-badge {
  background-color: #cd7f32; /* bronze */
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 15px;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
}

.user-level {
  font-size: 14px;
  color: var(--secondary-text);
}

.user-info-link {
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-grow: 1;
}

.message {
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.message.success {
  background-color: rgba(0, 128, 0, 0.1);
  border: 1px solid green;
  color: green;
}

.message.error {
  background-color: rgba(255, 0, 0, 0.1);
  border: 1px solid red;
  color: red;
}

/* Navigation Styles */
.main-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #121212;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  z-index: 1000;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #888;
  padding: 5px 0;
  transition: color 0.2s ease;
  width: 20%;
}

.nav-item:hover {
  color: #fff;
}

.nav-item.active {
  color: #4D7BFF;
  position: relative;
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #4D7BFF;
  border-radius: 50%;
}

.nav-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
}

/* Adjust container padding to account for navigation bar */
.container {
  padding-bottom: 120px !important;
}/*# sourceMappingURL=friends.page.css.map */