<div class="container">
  <header>
    <div class="logo">
      <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
      <span>Upshift</span>
    </div>
    <h1>Side Quests Management</h1>
  </header>

  <section class="status-section">
    <p>Status: {{ importStatus || 'Ready' }}</p>
  </section>

  <!-- Tab Navigation -->
  <div class="tab-navigation">
    <button [class.active]="activeTab === 'user'" (click)="switchTab('user')">User Side Quests</button>
    <button [class.active]="activeTab === 'group'" (click)="switchTab('group')">Group Side Quests</button>
  </div>

  <!-- User Side Quests Tab -->
  <div *ngIf="activeTab === 'user'">
    <!-- Existing Side Quests Section -->
    <section class="existing-quests-section">
      <div class="section-header">
        <h2>Existing User Side Quests ({{ existingSideQuests.length }})</h2>
      </div>

      <div class="side-quests-list" *ngIf="existingSideQuests.length > 0">
        <div class="side-quest-item" *ngFor="let quest of existingSideQuests">
          <div class="quest-emoji">{{ quest.emoji }}</div>
          <div class="quest-details">
            <h4>{{ quest.name }}</h4>
            <p>{{ quest.description }}</p>
            <div class="quest-meta">
              <span>Category: {{ quest.category }}</span>
              <span>Goal: {{ quest.goal_value }} {{ quest.goal_unit }}</span>
              <span [class.active-status]="quest.active" [class.inactive-status]="!quest.active">
                Status: {{ quest.active ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
          <div class="quest-actions">
            <button class="action-btn toggle-btn" (click)="toggleSideQuestActive(quest)">
              {{ quest.active ? 'Deactivate' : 'Activate' }}
            </button>
            <button class="action-btn delete-btn" (click)="deleteSideQuest(quest.id!)" *ngIf="quest.id">Delete</button>
          </div>
        </div>
      </div>
      <div *ngIf="existingSideQuests.length === 0" class="empty-state">
        <p>No user side quests found in the database.</p>
      </div>
    </section>

    <!-- Add New Side Quest Section -->
    <section class="add-quest-section">
      <div class="section-header">
        <h2>Add New User Side Quest</h2>
        <button class="toggle-btn" (click)="toggleAddForm()">
          {{ showAddForm ? 'Hide Form' : 'Show Form' }}
        </button>
      </div>

      <div class="add-form" *ngIf="showAddForm">
        <form [formGroup]="sideQuestForm" (ngSubmit)="addSideQuest()">
          <div class="form-row">
            <div class="form-group emoji-input">
              <label for="emoji">Emoji</label>
              <input type="text" id="emoji" formControlName="emoji" maxlength="5">
            </div>
            <div class="form-group name-input">
              <label for="name">Name</label>
              <input type="text" id="name" formControlName="name" placeholder="Enter quest name">
              <div class="error-message" *ngIf="sideQuestForm.get('name')?.invalid && sideQuestForm.get('name')?.touched">
                Name is required
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" formControlName="description" placeholder="Enter quest description"></textarea>
            <div class="error-message" *ngIf="sideQuestForm.get('description')?.invalid && sideQuestForm.get('description')?.touched">
              Description is required
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="category">Category</label>
              <select id="category" formControlName="category">
                <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
              </select>
            </div>

            <div class="form-group goal-input">
              <label for="goal_value">Goal Value</label>
              <input type="number" id="goal_value" formControlName="goal_value" min="1">
            </div>

            <div class="form-group">
              <label for="goal_unit">Goal Unit</label>
              <select id="goal_unit" formControlName="goal_unit">
                <option *ngFor="let unit of goalUnits" [value]="unit">{{ unit }}</option>
              </select>
            </div>
          </div>

          <button type="submit" class="submit-btn" [disabled]="sideQuestForm.invalid || isImporting">
            {{ isImporting ? 'Adding...' : 'Add Side Quest' }}
          </button>
        </form>
      </div>
    </section>

    <!-- Import from JSON Section -->
    <section class="import-section">
      <div class="section-header">
        <h2>Import from JSON</h2>
        <button class="toggle-btn" (click)="toggleJsonImport()">
          {{ showJsonImport ? 'Hide JSON Import' : 'Show JSON Import' }}
        </button>
      </div>

      <div *ngIf="showJsonImport">
        <p>Import side quests from the JSON file to Supabase.</p>

        <div class="button-group">
          <button class="import-btn" (click)="importJsonSideQuests()" [disabled]="isImporting || jsonSideQuests.length === 0">
            {{ isImporting ? 'Importing...' : 'Import Side Quests' }}
          </button>
          <button class="supabase-btn" (click)="openSupabaseConsole()">
            Open Supabase Console
          </button>
        </div>

        <div class="side-quests-list" *ngIf="jsonSideQuests.length > 0">
          <h3>Side Quests to Import ({{ jsonSideQuests.length }})</h3>
          <div class="side-quest-item" *ngFor="let quest of jsonSideQuests.slice(0, 5)">
            <div class="quest-emoji">{{ quest.emoji }}</div>
            <div class="quest-details">
              <h4>{{ quest.name }}</h4>
              <p>{{ quest.description }}</p>
              <div class="quest-meta">
                <span>Category: {{ quest.category }}</span>
                <span>Goal: {{ quest.goal_value }} {{ quest.goal_unit }}</span>
              </div>
            </div>
          </div>
          <p *ngIf="jsonSideQuests.length > 5">...and {{ jsonSideQuests.length - 5 }} more</p>
        </div>
      </div>
    </section>
  </div>

  <!-- Group Side Quests Tab -->
  <div *ngIf="activeTab === 'group'">
    <!-- Existing Group Side Quests Section -->
    <section class="existing-quests-section">
      <div class="section-header">
        <h2>Existing Group Side Quests ({{ existingGroupSideQuests.length }})</h2>
      </div>

      <div class="side-quests-list" *ngIf="existingGroupSideQuests.length > 0">
        <div class="side-quest-item" *ngFor="let quest of existingGroupSideQuests">
          <div class="quest-emoji">{{ quest.emoji }}</div>
          <div class="quest-details">
            <h4>{{ quest.name }}</h4>
            <p>{{ quest.description }}</p>
            <div class="quest-meta">
              <span>Category: {{ quest.category }}</span>
              <span>Goal: {{ quest.goal_value }} {{ quest.goal_unit }}</span>
              <span [class.active-status]="quest.active" [class.inactive-status]="!quest.active">
                Status: {{ quest.active ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
          <div class="quest-actions">
            <button class="action-btn toggle-btn" (click)="toggleGroupSideQuestActive(quest)">
              {{ quest.active ? 'Deactivate' : 'Activate' }}
            </button>
            <button class="action-btn delete-btn" (click)="deleteGroupSideQuest(quest.id)" *ngIf="quest.id">Delete</button>
          </div>
        </div>
      </div>
      <div *ngIf="existingGroupSideQuests.length === 0" class="empty-state">
        <p>No group side quests found in the database.</p>
      </div>
    </section>

    <!-- Add New Group Side Quest Section -->
    <section class="add-quest-section">
      <div class="section-header">
        <h2>Add New Group Side Quest</h2>
        <button class="toggle-btn" (click)="toggleGroupAddForm()">
          {{ showGroupAddForm ? 'Hide Form' : 'Show Form' }}
        </button>
      </div>

      <div class="add-form" *ngIf="showGroupAddForm">
        <form [formGroup]="groupSideQuestForm" (ngSubmit)="addGroupSideQuest()">
          <div class="form-row">
            <div class="form-group emoji-input">
              <label for="group-emoji">Emoji</label>
              <input type="text" id="group-emoji" formControlName="emoji" maxlength="5">
            </div>
            <div class="form-group name-input">
              <label for="group-name">Name</label>
              <input type="text" id="group-name" formControlName="name" placeholder="Enter quest name">
              <div class="error-message" *ngIf="groupSideQuestForm.get('name')?.invalid && groupSideQuestForm.get('name')?.touched">
                Name is required
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="group-description">Description</label>
            <textarea id="group-description" formControlName="description" placeholder="Enter quest description"></textarea>
            <div class="error-message" *ngIf="groupSideQuestForm.get('description')?.invalid && groupSideQuestForm.get('description')?.touched">
              Description is required
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="group-category">Category</label>
              <select id="group-category" formControlName="category">
                <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
              </select>
            </div>

            <div class="form-group goal-input">
              <label for="group-goal_value">Goal Value</label>
              <input type="number" id="group-goal_value" formControlName="goal_value" min="1">
            </div>

            <div class="form-group">
              <label for="group-goal_unit">Goal Unit</label>
              <select id="group-goal_unit" formControlName="goal_unit">
                <option *ngFor="let unit of goalUnits" [value]="unit">{{ unit }}</option>
              </select>
            </div>
          </div>

          <button type="submit" class="submit-btn" [disabled]="groupSideQuestForm.invalid || isImporting">
            {{ isImporting ? 'Adding...' : 'Add Group Side Quest' }}
          </button>
        </form>
      </div>
    </section>

    <!-- Import Group Side Quests Section -->
    <section class="import-section">
      <div class="section-header">
        <h2>Import Group Side Quests</h2>
        <button class="toggle-btn" (click)="toggleGroupJsonImport()">
          {{ showGroupJsonImport ? 'Hide JSON Import' : 'Show JSON Import' }}
        </button>
      </div>

      <div *ngIf="showGroupJsonImport">
        <p>Import group side quests from the JSON file to Supabase.</p>

        <div class="button-group">
          <button class="import-btn" (click)="importJsonGroupSideQuests()" [disabled]="isImporting || jsonGroupSideQuests.length === 0">
            {{ isImporting ? 'Importing...' : 'Import Group Side Quests' }}
          </button>
          <button class="supabase-btn" (click)="openSupabaseConsole()">
            Open Supabase Console
          </button>
        </div>

        <div class="side-quests-list" *ngIf="jsonGroupSideQuests.length > 0">
          <h3>Group Side Quests to Import ({{ jsonGroupSideQuests.length }})</h3>
          <div class="side-quest-item" *ngFor="let quest of jsonGroupSideQuests.slice(0, 5)">
            <div class="quest-emoji">{{ quest.emoji }}</div>
            <div class="quest-details">
              <h4>{{ quest.name }}</h4>
              <p>{{ quest.description }}</p>
              <div class="quest-meta">
                <span>Category: {{ quest.category }}</span>
                <span>Goal: {{ quest.goal_value }} {{ quest.goal_unit }}</span>
              </div>
            </div>
          </div>
          <p *ngIf="jsonGroupSideQuests.length > 5">...and {{ jsonGroupSideQuests.length - 5 }} more</p>
        </div>
      </div>
    </section>
  </div>
</div>

<!-- Navigation -->
<app-navigation></app-navigation>
