/* Exact CSS from Django template */
:root {
  --bg: #0c0c0f;
  --card: #121217;
  --pill: #1c1c1e;
  --text: #fff;
  --text-muted: #8e8e93;
  --accent: #4d7bff;
  --radius: 16px;
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --quest-bg: #1C1C1E;
  --quest-border: #2C2C2E;
}

:host {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
  display: block;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.container {
  max-width: 480px;
  margin: auto;
  padding: 20px;
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 22px;
}

.logo span {
  font-size: 18px;
  font-weight: bold;
}

.goal-card {
  background: var(--card);
  padding: 20px;
  border-radius: var(--radius);
  margin-bottom: 20px;
  box-shadow: 0 0 0 1px #1e1e1e;
  margin-top: 10px;
}

.description-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.goal-desc {
  font-size: 14px;
  color: var(--text-muted);
  margin-bottom: 10px;
  flex: 1;
}

.edit-bio-btn {
  background: none;
  border: none;
  color: var(--accent);
  font-size: 12px;
  cursor: pointer;
  padding: 2px 5px;
  margin-left: 10px;
}

.description-form {
  margin-bottom: 15px;
}

.description-form textarea {
  width: 100%;
  padding: 10px;
  background: var(--pill);
  color: white;
  border: 1px solid #2c2c2e;
  border-radius: var(--radius);
  margin-bottom: 10px;
  min-height: 80px;
  resize: vertical;
}

.save-btn, .cancel-btn {
  padding: 8px 12px;
  border-radius: var(--radius);
  font-size: 14px;
  cursor: pointer;
  margin-right: 10px;
}

.save-btn {
  background: var(--accent);
  color: white;
  border: none;
}

.cancel-btn {
  background: #2c2c2e;
  color: white;
  border: none;
}

.delete-goal-container {
  margin-top: 30px;
  text-align: center;
}

.delete-goal-btn {
  background: rgba(255, 59, 48, 0.2);
  color: #FF3B30;
  border: 1px solid #FF3B30;
  padding: 10px 16px;
  border-radius: var(--radius);
  font-size: 14px;
  cursor: pointer;
}

.progress-container {
  margin-bottom: 20px;
}

.progress-bar {
  height: 10px;
  background: #2c2c2e;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress-fill {
  height: 100%;
  background: var(--accent);
  transition: width 0.3s ease-in-out;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.progress-update input {
  width: 100%;
  padding: 10px;
  background: var(--pill);
  color: white;
  border: 1px solid #2c2c2e;
  border-radius: var(--radius);
  margin-top: 10px;
}

button {
  background: var(--accent);
  color: white;
  font-weight: 600;
  border: none;
  padding: 10px 16px;
  border-radius: var(--radius);
  cursor: pointer;
  margin-top: 10px;
}

.microgoals-section {
  margin-bottom: 24px;
}

.microgoals-list {
  list-style: none;
  padding-left: 0;
  margin-bottom: 12px;
}

.microgoal-form {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 6px;
}

.microgoal-add input {
  width: 100%;
  padding: 10px;
  background: var(--pill);
  color: white;
  border: 1px solid #2c2c2e;
  border-radius: var(--radius);
  margin-bottom: 8px;
}

.microgoal-add button {
  width: 100%;
}

.journal-section {
  background: var(--pill);
  padding: 16px;
  border-radius: var(--radius);
}

.journal-entry {
  border-top: 1px solid #2c2c2e;
  padding-top: 10px;
  margin-top: 10px;
}

.journal-entry:first-child {
  border-top: none;
  padding-top: 0;
  margin-top: 0;
}

.milestone {
  font-weight: 600;
  color: var(--accent);
}

.journal-modal {
  margin-top: 20px;
  background: var(--card);
  padding: 14px;
  border-radius: var(--radius);
  text-align: center;
}

.journal-link {
  display: inline-block;
  margin-top: 10px;
  color: var(--accent);
  text-decoration: underline;
}

.top-date {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-muted);
}

.page-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 20px;
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.goal-header h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.goal-percent {
  font-size: 14px;
  font-weight: 500;
  color: var(--accent);
}

.goal-value {
  font-size: 14px;
  color: var(--text-muted);
  margin-bottom: 12px;
}

.goal-link {
  font-weight: 500;
  color: var(--accent);
  text-decoration: none;
  display: inline-block;
}

.goal-link:hover {
  text-decoration: underline;
}

.no-goals {
  text-align: center;
  color: var(--text-muted);
  margin-top: 40px;
}

.delete-btn {
  background: none;
  border: none;
  color: #ff4d4d;
  cursor: pointer;
  margin-left: 8px;
  font-size: 16px;
  vertical-align: middle;
}

.delete-btn:hover {
  color: red;
}

.microgoal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--pill);
  padding: 10px 14px;
  margin-bottom: 8px;
  border-radius: 12px;
}

.microgoal-form {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.micro-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: var(--accent);
}

.checkmark {
  font-size: 20px;
  color: var(--text-muted);
}

.checkmark.checked {
  color: #4cd964;
}

.microgoal-title {
  font-size: 15px;
  font-weight: 500;
  color: var(--text);
}

.delete-form {
  margin-left: auto;
}

.delete-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #ff4d6d;
  cursor: pointer;
}

.journal-modal.fancy {
  background-color: var(--pill);
  border-radius: 16px;
  padding: 20px;
  margin-top: 24px;
  text-align: center;
  box-shadow: 0 0 0 1px #1e1e1e;
}

.journal-modal.fancy p {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 12px;
}

.journal-link {
  font-weight: 600;
  font-size: 14px;
  text-decoration: underline;
  color: var(--accent);
}

.milestone-highlight {
  color: var(--accent);
  font-weight: 600;
}

.micro-btn, .delete-btn {
  margin: 0;
}

.inline-journal-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 12px;
}

.inline-journal-form textarea {
  background-color: var(--pill);
  border: 1px solid var(--quest-border);
  border-radius: 10px;
  padding: 10px;
  color: var(--text);
  resize: vertical;
  font-size: 14px;
}

.inline-journal-form button {
  align-self: flex-start;
  background-color: var(--accent);
  border: none;
  padding: 10px 16px;
  color: white;
  font-weight: bold;
  border-radius: 10px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.inline-journal-form button:hover {
  opacity: 0.9;
}

.save-bar {
  display: flex;
}

.save-bar button {
  border-radius: 0 16px 16px 0;
}

.save-bar input {
  border-radius: 16px 0 0 16px;
}

.goal-card-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.goal-card-link:hover .goal-card {
  box-shadow: 0 0 0 1px var(--accent-color);
  transition: box-shadow 0.2s;
}

.goal-value {
  display: flex;
  justify-content: space-between;
}

.logo-wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.back-link {
  font-size: 14px;
  color: var(--text-muted);
  text-decoration: none;
  margin-left: 2px;
  transition: color 0.2s ease;
  margin-bottom: 10px;
}

.back-link:hover {
  color: var(--accent);
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.goal-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.goal-emoji {
  font-size: 20px;
  line-height: 1;
}

.goal-name {
  font-weight: 600;
  font-size: 16px;
}

/* Navigation Styles */
.main-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #121212;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  z-index: 1000;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #888;
  padding: 5px 0;
  transition: color 0.2s ease;
  width: 20%;
}

.nav-item:hover {
  color: #fff;
}

.nav-item.active {
  color: #4D7BFF;
  position: relative;
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #4D7BFF;
  border-radius: 50%;
}

.nav-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
}

/* Adjust container padding to account for navigation bar */
.container {
  padding-bottom: 120px !important;
}/*# sourceMappingURL=goal-detail.page.css.map */