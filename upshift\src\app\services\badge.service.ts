import { Injectable, inject } from '@angular/core';
import { UserBadges } from '../models/friend.model';
import { Observable, from, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class BadgeService {
  private supabaseService = inject(SupabaseService);

  constructor() {}

  getUserBadges(userId: string): Observable<UserBadges | undefined> {
    console.log('BadgeService: Getting badges for user:', userId);

    return from(
      this.supabaseService.supabase
        .from('user_badges')
        .select('*')
        .eq('user_id', userId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('BadgeService: Error getting user badges:', response.error);

          // Check if this is a permissions error
          if (response.error.code === '42501' ||
              response.error.message.includes('permission') ||
              response.error.message.includes('policy')) {
            console.error('BadgeService: This appears to be a permissions error. Please check your Supabase RLS policies.');
            throw new Error(`Permission denied: ${response.error.message}. Please check Supabase RLS policies for user_badges table.`);
          }

          return undefined;
        }

        if (response.data && response.data.length > 0) {
          console.log('BadgeService: Found badges for user:', response.data[0]);
          return response.data[0] as UserBadges;
        } else {
          console.log('BadgeService: No badges found for user');
          return undefined;
        }
      }),
      catchError(error => {
        console.error('BadgeService: Error getting user badges:', error);
        // Return the error message to the component
        if (error.message && error.message.includes('Permission denied')) {
          throw error;
        }
        return of(undefined);
      })
    );
  }

  async createUserBadges(userId: string): Promise<string> {
    console.log('BadgeService: Creating badges for user:', userId);

    try {
      // Check if badges already exist
      const { data: existingBadges, error: checkError } = await this.supabaseService.supabase
        .from('user_badges')
        .select('id')
        .eq('user_id', userId);

      if (checkError) {
        console.error('BadgeService: Error checking existing badges:', checkError);

        // Check if this is a permissions error
        if (checkError.code === '42501' || checkError.message.includes('permission') || checkError.message.includes('policy')) {
          console.error('BadgeService: This appears to be a permissions error. Please check your Supabase RLS policies.');
          throw new Error(`Permission denied: ${checkError.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(checkError.message);
      }

      if (existingBadges && existingBadges.length > 0) {
        console.log('BadgeService: Badges already exist for user:', existingBadges[0].id);
        return existingBadges[0].id;
      }

      // Create new badges
      const newBadges: UserBadges = {
        user_id: userId,
        badge_newbie: false,
        badge_warrior: false,
        badge_hardcore: false,
        badge_peak_performer: false,
        badge_indestructible: false,
        badge_professional: false,
        badge_streak_7_days: false,
        badge_streak_30_days: false,
        badge_streak_100_days: false,
        badge_streak_365_days: false,
        badge_sidequest_streak_7_days: false,
        badge_sidequest_streak_30_days: false,
        badge_sidequest_streak_100_days: false,
        badge_sidequest_streak_365_days: false,
        badge_friends_5: false,
        badge_friends_10: false,
        badge_strength_master: false,
        badge_money_master: false,
        badge_health_master: false,
        badge_knowledge_master: false,
        created_at: new Date(),
        updated_at: new Date()
      };

      console.log('BadgeService: Attempting to insert new badges with user_id:', userId);

      const { data, error } = await this.supabaseService.supabase
        .from('user_badges')
        .insert(newBadges)
        .select('id')
        .single();

      if (error) {
        console.error('BadgeService: Error creating badges:', error);

        // Check if this is a permissions error
        if (error.code === '42501' || error.message.includes('permission') || error.message.includes('policy')) {
          console.error('BadgeService: This appears to be a permissions error. Please check your Supabase RLS policies.');
          throw new Error(`Permission denied: ${error.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(error.message);
      }

      console.log('BadgeService: Created badges with ID:', data.id);
      return data.id;
    } catch (error) {
      console.error('BadgeService: Unexpected error creating badges:', error);
      throw error;
    }
  }

  async updateUserBadges(badgeId: string, data: Partial<UserBadges>): Promise<void> {
    console.log('BadgeService: Updating badges with ID:', badgeId, data);

    try {
      // Add updated_at timestamp
      const updates = {
        ...data,
        updated_at: new Date()
      };

      const { error } = await this.supabaseService.supabase
        .from('user_badges')
        .update(updates)
        .eq('id', badgeId);

      if (error) {
        console.error('BadgeService: Error updating badges:', error);

        // Check if this is a permissions error
        if (error.code === '42501' || error.message.includes('permission') || error.message.includes('policy')) {
          console.error('BadgeService: This appears to be a permissions error. Please check your Supabase RLS policies.');
          throw new Error(`Permission denied: ${error.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(error.message);
      }

      console.log('BadgeService: Updated badges successfully');
    } catch (error) {
      console.error('BadgeService: Unexpected error updating badges:', error);
      throw error;
    }
  }

  async updateCategoryBadges(userId: string, category: string): Promise<void> {
    console.log('BadgeService: Updating category badges for user:', userId, 'category:', category);

    try {
      // Get user badges
      const { data: badges, error: findError } = await this.supabaseService.supabase
        .from('user_badges')
        .select('id')
        .eq('user_id', userId);

      if (findError) {
        console.error('BadgeService: Error finding user badges:', findError);

        // Check if this is a permissions error
        if (findError.code === '42501' || findError.message.includes('permission') || findError.message.includes('policy')) {
          console.error('BadgeService: This appears to be a permissions error. Please check your Supabase RLS policies.');
          throw new Error(`Permission denied: ${findError.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(findError.message);
      }

      if (!badges || badges.length === 0) {
        console.log('BadgeService: No badges found for user, creating new badges');
        await this.createUserBadges(userId);
        return this.updateCategoryBadges(userId, category); // Retry after creating
      }

      const badgeId = badges[0].id;
      const badgeField = `badge_${category}_master`;

      console.log(`BadgeService: Updating badge field ${badgeField} for badge ID ${badgeId}`);

      // Update the badge
      const { error: updateError } = await this.supabaseService.supabase
        .from('user_badges')
        .update({
          [badgeField]: true,
          updated_at: new Date()
        })
        .eq('id', badgeId);

      if (updateError) {
        console.error('BadgeService: Error updating category badge:', updateError);

        // Check if this is a permissions error
        if (updateError.code === '42501' || updateError.message.includes('permission') || updateError.message.includes('policy')) {
          console.error('BadgeService: This appears to be a permissions error. Please check your Supabase RLS policies.');
          throw new Error(`Permission denied: ${updateError.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(updateError.message);
      }

      console.log('BadgeService: Updated category badge successfully');
    } catch (error) {
      console.error('BadgeService: Unexpected error updating category badges:', error);
      throw error;
    }
  }
}

// Import is now at the top of the file
