// @ts-nocheck - Disable TypeScript checking for this file
import { Component, inject, EnvironmentInjector, runInInjectionContext } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Preferences } from '@capacitor/preferences';
import { SupabaseService } from '../../services/supabase.service';
import { UserService } from '../../services/user.service';

@Component({
  selector: 'app-login',
  standalone: true,
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  imports: [CommonModule, IonicModule, ReactiveFormsModule, RouterModule],
})
export class LoginComponent {
  form: FormGroup;
  segment: 'login';
  private fb = inject(FormBuilder);
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);
  private injector = inject(EnvironmentInjector);

  constructor() {
    this.form = this.fb.group({
      email: [''],
      password: [''],
    });
  }


  async login() {
    console.log('Register: Login method called');
    const { email, password } = this.form.value;
    console.log('Register: Email:', email, 'Password:', password ? '******' : 'empty');

    if (!email || !password) {
      alert('Please enter both email and password');
      return;
    }

    try {
      console.log('Register: Attempting to sign in with email and password');
      const { data, error } = await this.supabaseService.getClient().auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        console.error('Register: Login error:', error);
        alert('Login error: ' + error.message);
        return;
      }

      console.log('Register: Sign in successful, user ID:', data.user.id);
      await this.handlePostLogin(data.user.id);
    } catch (error: any) {
      console.error('Register: Login error:', error);
      alert('Login error: ' + error.message);
    }
  }

  async signInWithGoogle() {
    console.log('Register: Attempting to sign in with Google');
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: '/'
        }
      });

      if (error) {
        console.error('Register: Google sign in error:', error);
        alert('Error signing in with Google: ' + error.message);
        return;
      }

      console.log('Register: Google sign in initiated:', data);
    } catch (error: any) {
      console.error('Register: Google sign in exception:', error);
      alert('Error signing in with Google');
    }
  }

  async signInWithApple() {
    console.log('Register: Attempting to sign in with Apple');
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: '/'
        }
      });

      if (error) {
        console.error('Register: Apple sign in error:', error);
        alert('Error signing in with Apple: ' + error.message);
        return;
      }

      console.log('Register: Apple sign in initiated:', data);
    } catch (error: any) {
      console.error('Register: Apple sign in exception:', error);
      alert('Error signing in with Apple');
    }
  }

  private async handlePostLogin(uid: string) {
    console.log('Register: handlePostLogin called with uid:', uid);
    try {
      await Preferences.set({ key: 'uid', value: uid });
      console.log('Register: Preferences set, checking user data');

      // Check if user has a record in the profiles table
      const { data: userData, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('*')
        .eq('id', uid)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Register: Error checking user data:', error);
      }

      if (!userData) {
        console.log('Register: No user record found, creating one');
        const authUser = this.supabaseService._currentUser.value;
        if (authUser) {
          await this.createUserRecord(uid, authUser);
        }
      } else {
        console.log('Register: User record found, updating last login');
        const { error: updateError } = await this.supabaseService.getClient()
          .from('profiles')
          .update({ last_login: new Date() })
          .eq('id', uid);

        if (updateError) {
          console.error('Register: Error updating last login:', updateError);
        }
      }

      console.log('Register: Navigating to root path for redirection');
      this.router.navigateByUrl('/');
      console.log('Register: Navigation completed');
    } catch (error) {
      console.error('Register: Error in handlePostLogin:', error);
    }
  }
}