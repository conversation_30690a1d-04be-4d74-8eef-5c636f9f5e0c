{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host } from './index-527b9e34.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-ca86cf32.js';\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\nconst IonTextStyle0 = textCss;\nconst Text = /*#__PURE__*/(() => {\n  let Text = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.color = undefined;\n    }\n    render() {\n      const mode = getIonMode(this);\n      return h(Host, {\n        key: '0c2546ea3f24b0a6bfd606199441d0a4edfa4ca1',\n        class: createColorClasses(this.color, {\n          [mode]: true\n        })\n      }, h(\"slot\", {\n        key: 'b7623ccb06f9461090a1f33e9f85886c7a4d5eff'\n      }));\n    }\n  };\n  Text.style = IonTextStyle0;\n  return Text;\n})();\nexport { Text as ion_text };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}