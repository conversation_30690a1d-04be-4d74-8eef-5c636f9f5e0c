# Upshift Supabase Migration

This document provides information about the migration from Firebase to Supabase in the Upshift application.

## Migration Overview

The Upshift application has been migrated from Firebase to Supabase for authentication, database, and cloud functions. The migration includes:

1. Authentication: Firebase Auth → Supabase Auth
2. Database: Firestore → Supabase PostgreSQL
3. Cloud Functions: Firebase Cloud Functions → Supabase Edge Functions

## Supabase Edge Functions

The Supabase Edge Functions are located in the `supabase/functions` directory. See the README in that directory for more details.

## Admin Scripts

### Set Admin User

To set a user as an admin in Supabase:

1. Open `src/app/pages/admin/set-admin-supabase.ts`
2. Replace `YOUR_SUPABASE_SERVICE_ROLE_KEY` with your actual Supabase service role key
3. Replace `USER_ID` with the ID of the user you want to set as admin
4. Run the script using Node.js

### Create Test User

To create a test user with a plan that expires in one month:

1. Open `create-test-user-supabase.js`
2. Replace `YOUR_SUPABASE_SERVICE_ROLE_KEY` with your actual Supabase service role key
3. Modify the user details if needed
4. Run the script using Node.js:

```bash
node create-test-user-supabase.js
```

## Environment Configuration

The Supabase configuration is stored in the environment files:

- `src/environments/environment.ts` (development)
- `src/environments/environment.prod.ts` (production)

Make sure to update these files with your Supabase project details.

## Admin Interface

The admin interface is available to users with the username 'admin' in the profiles table. Admin users can:

1. Create new users
2. Manage subscriptions
3. Access other administrative features

## Stripe Integration

The Stripe integration has been updated to work with Supabase:

1. Checkout sessions are created using the `createCheckoutSession` Edge Function
2. Stripe webhooks should be configured to point to your Supabase project

## Important Notes

- The Firebase configuration and dependencies have been removed from the project
- The application now uses Supabase for all backend functionality
- The migration maintains the same functionality as the Firebase implementation
