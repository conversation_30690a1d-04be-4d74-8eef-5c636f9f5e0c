{"ast": null, "code": "export class StorageError extends Error {\n  constructor(message) {\n    super(message);\n    this.__isStorageError = true;\n    this.name = 'StorageError';\n  }\n}\nexport function isStorageError(error) {\n  return typeof error === 'object' && error !== null && '__isStorageError' in error;\n}\nexport class StorageApiError extends StorageError {\n  constructor(message, status) {\n    super(message);\n    this.name = 'StorageApiError';\n    this.status = status;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status\n    };\n  }\n}\nexport class StorageUnknownError extends StorageError {\n  constructor(message, originalError) {\n    super(message);\n    this.name = 'StorageUnknownError';\n    this.originalError = originalError;\n  }\n}\n//# sourceMappingURL=errors.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}