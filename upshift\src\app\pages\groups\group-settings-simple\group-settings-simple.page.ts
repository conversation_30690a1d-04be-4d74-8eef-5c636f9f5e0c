import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { NavigationComponent } from '../../../components/navigation/navigation.component';

@Component({
  selector: 'app-group-settings-simple',
  templateUrl: './group-settings-simple.page.html',
  styleUrls: ['./group-settings-simple.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]
})
export class GroupSettingsSimplePage implements OnInit {
  groupId: string | null = null;
  private route = inject(ActivatedRoute);

  constructor() {}

  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      this.groupId = params.get('id');
      console.log('Group Settings Simple - Group ID:', this.groupId);
    });
  }
}
