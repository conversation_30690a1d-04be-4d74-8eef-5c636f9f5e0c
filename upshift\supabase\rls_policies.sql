-- This file contains all Row Level Security (RLS) policies for Supabase tables
-- Run this in the Supabase SQL Editor to set up proper permissions

-- Create a function to check if a user is an admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (SELECT username FROM profiles WHERE id = auth.uid()) = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable Row Level Security on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE quests ENABLE ROW LEVEL SECURITY;
ALTER TABLE quest_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE side_quests ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE friends ENABLE ROW LEVEL SECURITY;
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_quests ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_quest_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_join_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE goal_journal_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE microgoals ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_sidequest_pool ENABLE ROW LEVEL SECURITY;
ALTER TABLE day_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_daily_sidequests ENABLE ROW LEVEL SECURITY;

-- ==========================================
-- USER BADGES POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own badges" ON user_badges;
DROP POLICY IF EXISTS "Users can create their own badges" ON user_badges;
DROP POLICY IF EXISTS "Users can update their own badges" ON user_badges;
DROP POLICY IF EXISTS "Admins can do anything with badges" ON user_badges;

-- Enable Row Level Security
ALTER TABLE user_badges ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own badges
CREATE POLICY "Users can view their own badges"
ON user_badges
FOR SELECT
USING (auth.uid() = user_id);

-- Create policy to allow users to create their own badges
CREATE POLICY "Users can create their own badges"
ON user_badges
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own badges
CREATE POLICY "Users can update their own badges"
ON user_badges
FOR UPDATE
USING (auth.uid() = user_id);

-- Allow admins to access all badges
CREATE POLICY "Admins can do anything with badges"
ON user_badges
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.username = 'admin'
  )
);

-- ==========================================
-- QUESTS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own quests" ON quests;
DROP POLICY IF EXISTS "Users can create their own quests" ON quests;
DROP POLICY IF EXISTS "Users can update their own quests" ON quests;
DROP POLICY IF EXISTS "Users can delete their own quests" ON quests;
DROP POLICY IF EXISTS "Admins can do anything with quests" ON quests;

-- Enable Row Level Security
ALTER TABLE quests ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own quests
-- Note: user_id in quests references profiles.id
CREATE POLICY "Users can view their own quests"
ON quests
FOR SELECT
USING (auth.uid() = user_id);

-- Create policy to allow users to create their own quests
-- Note: user_id in quests references profiles.id
CREATE POLICY "Users can create their own quests"
ON quests
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own quests
-- Note: user_id in quests references profiles.id
CREATE POLICY "Users can update their own quests"
ON quests
FOR UPDATE
USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own quests
-- Note: user_id in quests references profiles.id
CREATE POLICY "Users can delete their own quests"
ON quests
FOR DELETE
USING (auth.uid() = user_id);

-- Allow admins to access all quests
CREATE POLICY "Admins can do anything with quests"
ON quests
USING (is_admin());

-- ==========================================
-- SIDE QUESTS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own side quests" ON side_quests;
DROP POLICY IF EXISTS "Users can create their own side quests" ON side_quests;
DROP POLICY IF EXISTS "Users can update their own side quests" ON side_quests;
DROP POLICY IF EXISTS "Users can delete their own side quests" ON side_quests;
DROP POLICY IF EXISTS "Admins can do anything with side quests" ON side_quests;

-- Enable Row Level Security
ALTER TABLE side_quests ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own side quests
CREATE POLICY "Users can view their own side quests"
ON side_quests
FOR SELECT
USING (auth.uid() = user_id);

-- Create policy to allow users to create their own side quests
CREATE POLICY "Users can create their own side quests"
ON side_quests
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own side quests
CREATE POLICY "Users can update their own side quests"
ON side_quests
FOR UPDATE
USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own side quests
CREATE POLICY "Users can delete their own side quests"
ON side_quests
FOR DELETE
USING (auth.uid() = user_id);

-- Allow admins to access all side quests
CREATE POLICY "Admins can do anything with side quests"
ON side_quests
USING (is_admin());

-- ==========================================
-- GROUPS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view groups" ON groups;
DROP POLICY IF EXISTS "Users can create groups" ON groups;
DROP POLICY IF EXISTS "Group owners can update their groups" ON groups;
DROP POLICY IF EXISTS "Group owners can delete their groups" ON groups;
DROP POLICY IF EXISTS "Admins can do anything with groups" ON groups;

-- Enable Row Level Security
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all users to view all groups
CREATE POLICY "Users can view groups"
ON groups
FOR SELECT
USING (true);

-- Create policy to allow users to create groups
CREATE POLICY "Users can create groups"
ON groups
FOR INSERT
WITH CHECK (auth.uid() = admin_id);

-- Create policy to allow group owners to update their groups
CREATE POLICY "Group owners can update their groups"
ON groups
FOR UPDATE
USING (auth.uid() = admin_id);

-- Create policy to allow group owners to delete their groups
CREATE POLICY "Group owners can delete their groups"
ON groups
FOR DELETE
USING (auth.uid() = admin_id);

-- Allow admins to access all groups
CREATE POLICY "Admins can do anything with groups"
ON groups
USING (is_admin());

-- ==========================================
-- GROUP MEMBERS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view group members" ON group_members;
DROP POLICY IF EXISTS "Users can join groups" ON group_members;
DROP POLICY IF EXISTS "Users can leave groups" ON group_members;
DROP POLICY IF EXISTS "Group owners can manage members" ON group_members;
DROP POLICY IF EXISTS "Admins can do anything with group members" ON group_members;

-- Enable Row Level Security
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all users to view all group members
CREATE POLICY "Users can view group members"
ON group_members
FOR SELECT
USING (true);

-- Create policy to allow users to join groups (add themselves as members)
CREATE POLICY "Users can join groups"
ON group_members
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to leave groups (delete their own membership)
CREATE POLICY "Users can leave groups"
ON group_members
FOR DELETE
USING (auth.uid() = user_id);

-- Create policy to allow group owners to manage members
CREATE POLICY "Group owners can manage members"
ON group_members
USING (
  EXISTS (
    SELECT 1 FROM groups
    WHERE groups.id = group_members.group_id AND groups.admin_id = auth.uid()
  )
);

-- Allow admins to access all group members
CREATE POLICY "Admins can do anything with group members"
ON group_members
USING (is_admin());

-- ==========================================
-- FRIENDS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their friends" ON friends;
DROP POLICY IF EXISTS "Users can add friends" ON friends;
DROP POLICY IF EXISTS "Users can remove friends" ON friends;
DROP POLICY IF EXISTS "Admins can do anything with friends" ON friends;

-- Enable Row Level Security
ALTER TABLE friends ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own friends
CREATE POLICY "Users can view their friends"
ON friends
FOR SELECT
USING (auth.uid() = user_id OR auth.uid() = friend_id);

-- Create policy to allow users to add friends
CREATE POLICY "Users can add friends"
ON friends
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to remove friends
CREATE POLICY "Users can remove friends"
ON friends
FOR DELETE
USING (auth.uid() = user_id OR auth.uid() = friend_id);

-- Allow admins to access all friends
CREATE POLICY "Admins can do anything with friends"
ON friends
USING (is_admin());

-- ==========================================
-- PROFILES POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can do anything with profiles" ON profiles;
DROP POLICY IF EXISTS "Admin access to profiles" ON profiles;

-- No need to enable RLS again, already done at the top

-- Create policy to allow all users to view all profiles
CREATE POLICY "Users can view all profiles"
ON profiles
FOR SELECT
USING (true);

-- Create policy to allow users to update their own profile
CREATE POLICY "Users can update their own profile"
ON profiles
FOR UPDATE
USING (auth.uid() = id);

-- Create policy to allow users to insert their own profile
CREATE POLICY "Users can insert their own profile"
ON profiles
FOR INSERT
WITH CHECK (auth.uid() = id);

-- Create policy to allow admin access to all profiles
-- This uses our is_admin() function to avoid recursion
CREATE POLICY "Admin access to profiles"
ON profiles
FOR ALL
USING (is_admin());

-- ==========================================
-- NOTIFICATIONS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can create notifications for others" ON notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can delete their own notifications" ON notifications;
DROP POLICY IF EXISTS "Admins can do anything with notifications" ON notifications;

-- Enable Row Level Security
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own notifications
CREATE POLICY "Users can view their own notifications"
ON notifications
FOR SELECT
USING (auth.uid() = user_id);

-- Create policy to allow users to create notifications for others
CREATE POLICY "Users can create notifications for others"
ON notifications
FOR INSERT
WITH CHECK (true);  -- Anyone can create notifications, but they'll only be visible to the recipient

-- Create policy to allow users to update their own notifications
CREATE POLICY "Users can update their own notifications"
ON notifications
FOR UPDATE
USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own notifications
CREATE POLICY "Users can delete their own notifications"
ON notifications
FOR DELETE
USING (auth.uid() = user_id);

-- Allow admins to access all notifications
CREATE POLICY "Admins can do anything with notifications"
ON notifications
USING (is_admin());

-- ==========================================
-- ACTIVITIES POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own activities" ON activities;
DROP POLICY IF EXISTS "Users can create their own activities" ON activities;
DROP POLICY IF EXISTS "Users can update their own activities" ON activities;
DROP POLICY IF EXISTS "Users can delete their own activities" ON activities;
DROP POLICY IF EXISTS "Admins can do anything with activities" ON activities;

-- Create policy to allow users to view their own activities
CREATE POLICY "Users can view their own activities"
ON activities
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM day_tracking
    WHERE day_tracking.id = activities.day_tracking_id AND day_tracking.user_id = auth.uid()
  )
);

-- Create policy to allow users to create their own activities
CREATE POLICY "Users can create their own activities"
ON activities
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM day_tracking
    WHERE day_tracking.id = activities.day_tracking_id AND day_tracking.user_id = auth.uid()
  )
);

-- Create policy to allow users to update their own activities
CREATE POLICY "Users can update their own activities"
ON activities
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM day_tracking
    WHERE day_tracking.id = activities.day_tracking_id AND day_tracking.user_id = auth.uid()
  )
);

-- Create policy to allow users to delete their own activities
CREATE POLICY "Users can delete their own activities"
ON activities
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM day_tracking
    WHERE day_tracking.id = activities.day_tracking_id AND day_tracking.user_id = auth.uid()
  )
);

-- Allow admins to access all activities
CREATE POLICY "Admins can do anything with activities"
ON activities
USING (is_admin());

-- ==========================================
-- ACTIVITY TYPES POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view activity types" ON activity_types;
DROP POLICY IF EXISTS "Admins can manage activity types" ON activity_types;

-- Create policy to allow all users to view activity types
CREATE POLICY "Users can view activity types"
ON activity_types
FOR SELECT
USING (true);

-- Allow admins to manage activity types
CREATE POLICY "Admins can manage activity types"
ON activity_types
USING (is_admin());

-- ==========================================
-- DAILY SIDEQUEST POOL POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view daily sidequest pool" ON daily_sidequest_pool;
DROP POLICY IF EXISTS "Admins can manage daily sidequest pool" ON daily_sidequest_pool;

-- Create policy to allow all users to view daily sidequest pool
CREATE POLICY "Users can view daily sidequest pool"
ON daily_sidequest_pool
FOR SELECT
USING (true);

-- Allow admins to manage daily sidequest pool
CREATE POLICY "Admins can manage daily sidequest pool"
ON daily_sidequest_pool
USING (is_admin());

-- ==========================================
-- DAY TRACKING POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own day tracking" ON day_tracking;
DROP POLICY IF EXISTS "Users can create their own day tracking" ON day_tracking;
DROP POLICY IF EXISTS "Users can update their own day tracking" ON day_tracking;
DROP POLICY IF EXISTS "Users can delete their own day tracking" ON day_tracking;
DROP POLICY IF EXISTS "Admins can do anything with day tracking" ON day_tracking;

-- Create policy to allow users to view their own day tracking
CREATE POLICY "Users can view their own day tracking"
ON day_tracking
FOR SELECT
USING (auth.uid() = user_id);

-- Create policy to allow users to create their own day tracking
CREATE POLICY "Users can create their own day tracking"
ON day_tracking
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own day tracking
CREATE POLICY "Users can update their own day tracking"
ON day_tracking
FOR UPDATE
USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own day tracking
CREATE POLICY "Users can delete their own day tracking"
ON day_tracking
FOR DELETE
USING (auth.uid() = user_id);

-- Allow admins to access all day tracking
CREATE POLICY "Admins can do anything with day tracking"
ON day_tracking
USING (is_admin());

-- ==========================================
-- GOALS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own goals" ON goals;
DROP POLICY IF EXISTS "Users can create their own goals" ON goals;
DROP POLICY IF EXISTS "Users can update their own goals" ON goals;
DROP POLICY IF EXISTS "Users can delete their own goals" ON goals;
DROP POLICY IF EXISTS "Admins can do anything with goals" ON goals;

-- Create policy to allow users to view their own goals
CREATE POLICY "Users can view their own goals"
ON goals
FOR SELECT
USING (auth.uid() = user_id);

-- Create policy to allow users to create their own goals
CREATE POLICY "Users can create their own goals"
ON goals
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own goals
CREATE POLICY "Users can update their own goals"
ON goals
FOR UPDATE
USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own goals
CREATE POLICY "Users can delete their own goals"
ON goals
FOR DELETE
USING (auth.uid() = user_id);

-- Allow admins to access all goals
CREATE POLICY "Admins can do anything with goals"
ON goals
USING (is_admin());

-- ==========================================
-- GOAL JOURNAL ENTRIES POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own goal journal entries" ON goal_journal_entries;
DROP POLICY IF EXISTS "Users can create their own goal journal entries" ON goal_journal_entries;
DROP POLICY IF EXISTS "Users can update their own goal journal entries" ON goal_journal_entries;
DROP POLICY IF EXISTS "Users can delete their own goal journal entries" ON goal_journal_entries;
DROP POLICY IF EXISTS "Admins can do anything with goal journal entries" ON goal_journal_entries;

-- Create policy to allow users to view their own goal journal entries
CREATE POLICY "Users can view their own goal journal entries"
ON goal_journal_entries
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM goals
    WHERE goals.id = goal_journal_entries.goal_id AND goals.user_id = auth.uid()
  )
);

-- Create policy to allow users to create their own goal journal entries
CREATE POLICY "Users can create their own goal journal entries"
ON goal_journal_entries
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM goals
    WHERE goals.id = goal_journal_entries.goal_id AND goals.user_id = auth.uid()
  )
);

-- Create policy to allow users to update their own goal journal entries
CREATE POLICY "Users can update their own goal journal entries"
ON goal_journal_entries
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM goals
    WHERE goals.id = goal_journal_entries.goal_id AND goals.user_id = auth.uid()
  )
);

-- Create policy to allow users to delete their own goal journal entries
CREATE POLICY "Users can delete their own goal journal entries"
ON goal_journal_entries
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM goals
    WHERE goals.id = goal_journal_entries.goal_id AND goals.user_id = auth.uid()
  )
);

-- Allow admins to access all goal journal entries
CREATE POLICY "Admins can do anything with goal journal entries"
ON goal_journal_entries
USING (is_admin());

-- ==========================================
-- MICROGOALS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own microgoals" ON microgoals;
DROP POLICY IF EXISTS "Users can create their own microgoals" ON microgoals;
DROP POLICY IF EXISTS "Users can update their own microgoals" ON microgoals;
DROP POLICY IF EXISTS "Users can delete their own microgoals" ON microgoals;
DROP POLICY IF EXISTS "Admins can do anything with microgoals" ON microgoals;

-- Create policy to allow users to view their own microgoals
CREATE POLICY "Users can view their own microgoals"
ON microgoals
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM goals
    WHERE goals.id = microgoals.goal_id AND goals.user_id = auth.uid()
  )
);

-- Create policy to allow users to create their own microgoals
CREATE POLICY "Users can create their own microgoals"
ON microgoals
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM goals
    WHERE goals.id = microgoals.goal_id AND goals.user_id = auth.uid()
  )
);

-- Create policy to allow users to update their own microgoals
CREATE POLICY "Users can update their own microgoals"
ON microgoals
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM goals
    WHERE goals.id = microgoals.goal_id AND goals.user_id = auth.uid()
  )
);

-- Create policy to allow users to delete their own microgoals
CREATE POLICY "Users can delete their own microgoals"
ON microgoals
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM goals
    WHERE goals.id = microgoals.goal_id AND goals.user_id = auth.uid()
  )
);

-- Allow admins to access all microgoals
CREATE POLICY "Admins can do anything with microgoals"
ON microgoals
USING (is_admin());

-- ==========================================
-- GROUP JOIN REQUESTS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view group join requests" ON group_join_requests;
DROP POLICY IF EXISTS "Users can create group join requests" ON group_join_requests;
DROP POLICY IF EXISTS "Group admins can update group join requests" ON group_join_requests;
DROP POLICY IF EXISTS "Group admins can delete group join requests" ON group_join_requests;
DROP POLICY IF EXISTS "Admins can do anything with group join requests" ON group_join_requests;

-- Create policy to allow users to view group join requests for their groups
CREATE POLICY "Users can view group join requests"
ON group_join_requests
FOR SELECT
USING (
  auth.uid() = invited_by OR
  EXISTS (
    SELECT 1 FROM groups
    WHERE groups.id = group_join_requests.group_id AND groups.admin_id = auth.uid()
  )
);

-- Create policy to allow users to create group join requests
CREATE POLICY "Users can create group join requests"
ON group_join_requests
FOR INSERT
WITH CHECK (auth.uid() = invited_by);

-- Create policy to allow group admins to update group join requests
CREATE POLICY "Group admins can update group join requests"
ON group_join_requests
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM groups
    WHERE groups.id = group_join_requests.group_id AND groups.admin_id = auth.uid()
  )
);

-- Create policy to allow group admins to delete group join requests
CREATE POLICY "Group admins can delete group join requests"
ON group_join_requests
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM groups
    WHERE groups.id = group_join_requests.group_id AND groups.admin_id = auth.uid()
  ) OR auth.uid() = invited_by
);

-- Allow admins to access all group join requests
CREATE POLICY "Admins can do anything with group join requests"
ON group_join_requests
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.username = 'admin'
  )
);

-- ==========================================
-- GROUP QUESTS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view group quests" ON group_quests;
DROP POLICY IF EXISTS "Group admins can create group quests" ON group_quests;
DROP POLICY IF EXISTS "Group admins can update group quests" ON group_quests;
DROP POLICY IF EXISTS "Group admins can delete group quests" ON group_quests;
DROP POLICY IF EXISTS "Admins can do anything with group quests" ON group_quests;

-- Create policy to allow users to view group quests for groups they are members of
CREATE POLICY "Users can view group quests"
ON group_quests
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = group_quests.group_id AND group_members.user_id = auth.uid()
  )
);

-- Create policy to allow group admins to create group quests
CREATE POLICY "Group admins can create group quests"
ON group_quests
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM groups
    WHERE groups.id = group_quests.group_id AND groups.admin_id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = group_quests.group_id AND group_members.user_id = auth.uid() AND group_members.is_admin = true
  )
);

-- Create policy to allow group admins to update group quests
CREATE POLICY "Group admins can update group quests"
ON group_quests
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM groups
    WHERE groups.id = group_quests.group_id AND groups.admin_id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = group_quests.group_id AND group_members.user_id = auth.uid() AND group_members.is_admin = true
  )
);

-- Create policy to allow group admins to delete group quests
CREATE POLICY "Group admins can delete group quests"
ON group_quests
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM groups
    WHERE groups.id = group_quests.group_id AND groups.admin_id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM group_members
    WHERE group_members.group_id = group_quests.group_id AND group_members.user_id = auth.uid() AND group_members.is_admin = true
  )
);

-- Allow admins to access all group quests
CREATE POLICY "Admins can do anything with group quests"
ON group_quests
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.username = 'admin'
  )
);

-- ==========================================
-- GROUP QUEST PROGRESS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view group quest progress" ON group_quest_progress;
DROP POLICY IF EXISTS "Users can create their own group quest progress" ON group_quest_progress;
DROP POLICY IF EXISTS "Users can update their own group quest progress" ON group_quest_progress;
DROP POLICY IF EXISTS "Group admins can update any group quest progress" ON group_quest_progress;
DROP POLICY IF EXISTS "Admins can do anything with group quest progress" ON group_quest_progress;

-- Create policy to allow users to view group quest progress for groups they are members of
CREATE POLICY "Users can view group quest progress"
ON group_quest_progress
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM group_quests
    JOIN group_members ON group_quests.group_id = group_members.group_id
    WHERE group_quests.id = group_quest_progress.quest_id AND group_members.user_id = auth.uid()
  )
);

-- Create policy to allow users to create their own group quest progress
CREATE POLICY "Users can create their own group quest progress"
ON group_quest_progress
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own group quest progress
CREATE POLICY "Users can update their own group quest progress"
ON group_quest_progress
FOR UPDATE
USING (auth.uid() = user_id);

-- Create policy to allow group admins to update any group quest progress
CREATE POLICY "Group admins can update any group quest progress"
ON group_quest_progress
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM group_quests
    JOIN groups ON group_quests.group_id = groups.id
    WHERE group_quests.id = group_quest_progress.quest_id AND groups.admin_id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM group_quests
    JOIN group_members ON group_quests.group_id = group_members.group_id
    WHERE group_quests.id = group_quest_progress.quest_id AND group_members.user_id = auth.uid() AND group_members.is_admin = true
  )
);

-- Allow admins to access all group quest progress
CREATE POLICY "Admins can do anything with group quest progress"
ON group_quest_progress
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.username = 'admin'
  )
);

-- ==========================================
-- QUEST PROGRESS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own quest progress" ON quest_progress;
DROP POLICY IF EXISTS "Users can create their own quest progress" ON quest_progress;
DROP POLICY IF EXISTS "Users can update their own quest progress" ON quest_progress;
DROP POLICY IF EXISTS "Users can delete their own quest progress" ON quest_progress;
DROP POLICY IF EXISTS "Admins can do anything with quest progress" ON quest_progress;

-- Create policy to allow users to view their own quest progress
CREATE POLICY "Users can view their own quest progress"
ON quest_progress
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM quests
    WHERE quests.id = quest_progress.quest_id AND quests.user_id = auth.uid()
  )
);

-- Create policy to allow users to create their own quest progress
CREATE POLICY "Users can create their own quest progress"
ON quest_progress
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM quests
    WHERE quests.id = quest_progress.quest_id AND quests.user_id = auth.uid()
  )
);

-- Create policy to allow users to update their own quest progress
CREATE POLICY "Users can update their own quest progress"
ON quest_progress
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM quests
    WHERE quests.id = quest_progress.quest_id AND quests.user_id = auth.uid()
  )
);

-- Create policy to allow users to delete their own quest progress
CREATE POLICY "Users can delete their own quest progress"
ON quest_progress
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM quests
    WHERE quests.id = quest_progress.quest_id AND quests.user_id = auth.uid()
  )
);

-- Allow admins to access all quest progress
CREATE POLICY "Admins can do anything with quest progress"
ON quest_progress
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.username = 'admin'
  )
);

-- ==========================================
-- SUBSCRIPTION HISTORY POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own subscription history" ON subscription_history;
DROP POLICY IF EXISTS "Users can create their own subscription history" ON subscription_history;
DROP POLICY IF EXISTS "Admins can do anything with subscription history" ON subscription_history;

-- Create policy to allow users to view their own subscription history
CREATE POLICY "Users can view their own subscription history"
ON subscription_history
FOR SELECT
USING (auth.uid() = user_id);

-- Create policy to allow users to create their own subscription history
CREATE POLICY "Users can create their own subscription history"
ON subscription_history
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Allow admins to access all subscription history
CREATE POLICY "Admins can do anything with subscription history"
ON subscription_history
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.username = 'admin'
  )
);

-- ==========================================
-- USER DAILY SIDEQUESTS POLICIES
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own daily sidequests" ON user_daily_sidequests;
DROP POLICY IF EXISTS "Users can create their own daily sidequests" ON user_daily_sidequests;
DROP POLICY IF EXISTS "Users can update their own daily sidequests" ON user_daily_sidequests;
DROP POLICY IF EXISTS "Users can delete their own daily sidequests" ON user_daily_sidequests;
DROP POLICY IF EXISTS "Admins can do anything with daily sidequests" ON user_daily_sidequests;

-- Create policy to allow users to view their own daily sidequests
CREATE POLICY "Users can view their own daily sidequests"
ON user_daily_sidequests
FOR SELECT
USING (auth.uid() = user_id);

-- Create policy to allow users to create their own daily sidequests
CREATE POLICY "Users can create their own daily sidequests"
ON user_daily_sidequests
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own daily sidequests
CREATE POLICY "Users can update their own daily sidequests"
ON user_daily_sidequests
FOR UPDATE
USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own daily sidequests
CREATE POLICY "Users can delete their own daily sidequests"
ON user_daily_sidequests
FOR DELETE
USING (auth.uid() = user_id);

-- Allow admins to access all daily sidequests
CREATE POLICY "Admins can do anything with daily sidequests"
ON user_daily_sidequests
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.username = 'admin'
  )
);
