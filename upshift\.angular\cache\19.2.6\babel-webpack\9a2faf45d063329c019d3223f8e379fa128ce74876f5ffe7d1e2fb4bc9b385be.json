{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { resolveFetch } from './helper';\nimport { FunctionsFetchError, FunctionsHttpError, FunctionsRelayError, FunctionRegion } from './types';\nexport class FunctionsClient {\n  constructor(url, {\n    headers = {},\n    customFetch,\n    region = FunctionRegion.Any\n  } = {}) {\n    this.url = url;\n    this.headers = headers;\n    this.region = region;\n    this.fetch = resolveFetch(customFetch);\n  }\n  /**\n   * Updates the authorization header\n   * @param token - the new jwt token sent in the authorisation header\n   */\n  setAuth(token) {\n    this.headers.Authorization = `Bearer ${token}`;\n  }\n  /**\n   * Invokes a function\n   * @param functionName - The name of the Function to invoke.\n   * @param options - Options for invoking the Function.\n   */\n  invoke(functionName, options = {}) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const {\n          headers,\n          method,\n          body: functionArgs\n        } = options;\n        let _headers = {};\n        let {\n          region\n        } = options;\n        if (!region) {\n          region = this.region;\n        }\n        if (region && region !== 'any') {\n          _headers['x-region'] = region;\n        }\n        let body;\n        if (functionArgs && (headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type') || !headers)) {\n          if (typeof Blob !== 'undefined' && functionArgs instanceof Blob || functionArgs instanceof ArrayBuffer) {\n            // will work for File as File inherits Blob\n            // also works for ArrayBuffer as it is the same underlying structure as a Blob\n            _headers['Content-Type'] = 'application/octet-stream';\n            body = functionArgs;\n          } else if (typeof functionArgs === 'string') {\n            // plain string\n            _headers['Content-Type'] = 'text/plain';\n            body = functionArgs;\n          } else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n            // don't set content-type headers\n            // Request will automatically add the right boundary value\n            body = functionArgs;\n          } else {\n            // default, assume this is JSON\n            _headers['Content-Type'] = 'application/json';\n            body = JSON.stringify(functionArgs);\n          }\n        }\n        const response = yield this.fetch(`${this.url}/${functionName}`, {\n          method: method || 'POST',\n          // headers priority is (high to low):\n          // 1. invoke-level headers\n          // 2. client-level headers\n          // 3. default Content-Type header\n          headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n          body\n        }).catch(fetchError => {\n          throw new FunctionsFetchError(fetchError);\n        });\n        const isRelayError = response.headers.get('x-relay-error');\n        if (isRelayError && isRelayError === 'true') {\n          throw new FunctionsRelayError(response);\n        }\n        if (!response.ok) {\n          throw new FunctionsHttpError(response);\n        }\n        let responseType = ((_a = response.headers.get('Content-Type')) !== null && _a !== void 0 ? _a : 'text/plain').split(';')[0].trim();\n        let data;\n        if (responseType === 'application/json') {\n          data = yield response.json();\n        } else if (responseType === 'application/octet-stream') {\n          data = yield response.blob();\n        } else if (responseType === 'text/event-stream') {\n          data = response;\n        } else if (responseType === 'multipart/form-data') {\n          data = yield response.formData();\n        } else {\n          // default to text\n          data = yield response.text();\n        }\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        return {\n          data: null,\n          error\n        };\n      }\n    });\n  }\n}\n//# sourceMappingURL=FunctionsClient.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}