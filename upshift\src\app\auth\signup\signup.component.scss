ion-content {
  --background: transparent;
  position: relative;
  &::part(scroll) {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
  }

    ion-grid {
      width: 100%;

      ion-row {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        h1 {
          color: var(--text);
          font-size: 36px;
          font-weight: 700;
          margin-bottom: 24px;
        }

        .join-text {
          margin-bottom: 48px;
        }

        ion-button {
          width: 100%;
          --color: var(--text);

          &.skip-button {
            --background: var(--accent);
            --background-hover: var(--accent-hover);
            margin-top: 24px;

            .button-content {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;

              .button-text {
                font-size: 15px;
                font-weight: 500;
                letter-spacing: 0.3px;
              }

              ion-icon {
                font-size: 18px;
                margin: 0;
              }
            }
          }
        }

        .skip-text {
          color: var(--text-secondary);
          font-size: 16px;
          margin-top: 32px;

          a {
            color: var(--accent);
            text-decoration: none;
            font-weight: 600;
            margin-left: 8px;
          }
        }
      }
    }
}