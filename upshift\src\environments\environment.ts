// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  supabase: {
    url: 'https://tobifepmbrrrvshpvrqa.supabase.co',
    key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvYmlmZXBtYnJycnZzaHB2cnFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2ODY0NDgsImV4cCI6MjA2MTI2MjQ0OH0.afZPEEdrS9SF755RMeZCFzi5vi1nZFnlO86qKGUHeVw',
    functionsUrl: 'https://tobifepmbrrrvshpvrqa.supabase.co/functions/v1'
  },
  stripe: {
    publishableKey: 'pk_test_51LlETOEB7UFpNfHGFTFA8OLyCMuwT9TBOjUF0Tcw9zIbdFQCV7IenK3rfXlSvXE3CNJebHJ9tLwHxEHtF4XfAJfL00tLB9aiHN'
  }
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
