.container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  color: var(--text-color);
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo img {
  height: 30px;
}

.import-section {
  background-color: var(--quest-bg);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.import-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
}

.import-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.supabase-btn {
  background-color: #3ecf8e;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
}

.side-quests-list {
  margin-top: 30px;
}

.side-quests-list h3 {
  margin-bottom: 15px;
  font-size: 18px;
  color: var(--text-color);
}

.side-quest-item {
  display: flex;
  background-color: var(--quest-bg);
  border: 1px solid var(--quest-border);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.quest-emoji {
  font-size: 24px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
}

.quest-details {
  flex: 1;
}

.quest-details h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: var(--text-color);
}

.quest-details p {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: var(--secondary-text);
}

.quest-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  font-size: 12px;
  color: var(--secondary-text);
}/*# sourceMappingURL=import-sidequests.page.css.map */