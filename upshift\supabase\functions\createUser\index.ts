import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get the request body
    const { email, password, userData } = await req.json();

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({
          error: 'No authorization header',
        }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Extract the JWT token
    const token = authHeader.replace('Bearer ', '');

    // Create a Supabase client with the admin key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') || '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    );

    // Verify the token and get the user
    const { data: { user: adminUser }, error: verifyError } = await supabaseAdmin.auth.getUser(token);

    if (verifyError || !adminUser) {
      return new Response(
        JSON.stringify({
          error: 'Unauthorized',
          details: verifyError?.message || 'Invalid token',
        }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Check if the user is an admin
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from('profiles')
      .select('username')
      .eq('id', adminUser.id)
      .single();

    if (adminError || !adminData) {
      return new Response(
        JSON.stringify({
          error: 'User document not found',
          details: adminError?.message,
        }),
        {
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    if (adminData.username !== 'admin') {
      return new Response(
        JSON.stringify({
          error: 'Only admin users can create new users',
        }),
        {
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate required parameters
    if (!email || !password) {
      return new Response(
        JSON.stringify({
          error: 'Email and password are required',
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Create the user in Supabase Auth
    const { data: authData, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
    });

    if (createError) {
      return new Response(
        JSON.stringify({
          error: 'Error creating user',
          details: createError.message,
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    const uid = authData.user.id;

    // Create a document in the 'profiles' table with the same UID
    if (userData) {
      const now = new Date();

      const userDocData = {
        ...userData,
        id: uid,
        email,
        registration_date: now,
        last_login: now,
      };

      // Create the user profile in Supabase
      const { error: insertError } = await supabaseAdmin
        .from('profiles')
        .insert(userDocData);

      if (insertError) {
        // If there's an error creating the user document, delete the auth user
        await supabaseAdmin.auth.admin.deleteUser(uid);

        return new Response(
          JSON.stringify({
            error: 'Error creating user profile',
            details: insertError.message,
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        );
      }
    }

    // Return the UID of the created user
    return new Response(
      JSON.stringify({ uid }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Error creating user:', error);

    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
