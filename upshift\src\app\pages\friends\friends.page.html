<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
    <header>
        <div class="logo">
            <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
            <span>Upshift</span>
        </div>
        <h1>Friends</h1>
    </header>

    <div class="friends-container">
        <div *ngIf="successMessage" class="message success">
            {{ successMessage }}
        </div>
        <div *ngIf="errorMessage" class="message error">
            {{ errorMessage }}
        </div>

        <div class="code-section">
            <div class="code-header">
                <div class="section-header">
                    <div class="section-icon">🔑</div>
                    <div class="section-title">Friend Code</div>
                </div>
                <p>Share your code with friends or enter a friend's code to connect.</p>
            </div>

            <div class="code-actions">
                <div *ngIf="friendCode && codeIsValid" class="code-display">{{ friendCode }}</div>
                <div *ngIf="friendCode && codeIsValid" class="code-info">This code is valid for 24 hours. Share it with friends to connect.</div>

                <button *ngIf="!friendCode || !codeIsValid" (click)="generateFriendCode()" class="generate-code-btn">
                    {{ friendCode && !codeIsValid ? 'Generate New Code' : 'Generate Friend Code' }}
                </button>

                <form (ngSubmit)="addFriendByCode()" class="add-code-form">
                    <input
                        type="text"
                        [(ngModel)]="enteredCode"
                        name="friend_code"
                        placeholder="Enter friend code"
                        pattern="^[\w.@+-]+$"
                        title="Friend code should only contain letters, numbers, and @/./+/-/_ characters"
                        required>
                    <button type="submit">Add Friend</button>
                </form>
            </div>
        </div>

        

        <div class="friends-list">
            <div class="section-header">
                <div class="section-icon">👥</div>
                <div class="section-title">Friends Leaderboard</div>
            </div>

            <div *ngIf="leaderboard && leaderboard.length > 0; else noFriends" class="leaderboard-container">
                <div *ngFor="let entry of leaderboard" class="leaderboard-row" [class.current-user]="entry.isCurrentUser">
                    <div class="rank-badge">{{ entry.rank }}</div>
                    <a *ngIf="!entry.isCurrentUser" [routerLink]="['/friends', entry.user.id]" class="user-info-link">
                        <div class="user-info">
                            <div class="user-avatar">
                                <img *ngIf="entry.user.profile_picture" [src]="entry.user.profile_picture" [alt]="entry.user.username">
                                <ng-container *ngIf="!entry.user.profile_picture">
                                    {{ entry.user.username.charAt(0).toUpperCase() }}
                                </ng-container>
                            </div>
                            <div class="user-details">
                                <div class="user-name">{{ entry.user.username }}</div>
                                <div class="user-level">Level {{ entry.user.level }} - {{ entry.user.title }}</div>
                            </div>
                        </div>
                    </a>
                    <div *ngIf="entry.isCurrentUser" class="user-info">
                        <div class="user-avatar">
                            <img *ngIf="entry.user.profile_picture" [src]="entry.user.profile_picture" [alt]="entry.user.username">
                            <ng-container *ngIf="!entry.user.profile_picture">
                                {{ entry.user.username.charAt(0).toUpperCase() }}
                            </ng-container>
                        </div>
                        <div class="user-details">
                            <div class="user-name">{{ entry.user.username }}</div>
                            <div class="user-level">Level {{ entry.user.level }} - {{ entry.user.title }}</div>
                        </div>
                    </div>
                    <div *ngIf="!entry.isCurrentUser" class="friend-actions">
                        <a href="#" (click)="removeFriend(entry.user.id || '', $event)">Remove</a>
                    </div>
                </div>
            </div>

            <ng-template #noFriends>
                <div class="no-friends">
                    <p>You don't have any friends yet. Generate a code and share it with your friends to connect!</p>
                </div>
            </ng-template>
        </div>

        <div class="affiliate-rewards-section">
            <div class="section-header">
                <div class="section-icon">🎁</div>
                <div class="section-title">Affiliate Rewards</div>
            </div>
            <p>Invite friends and earn exclusive rewards based on your affiliate count.</p>
            <div class="affiliate-info">
                <div class="affiliate-count">
                    <span class="count-label">Your Affiliates:</span>
                    <span class="count-value">{{ userAffiliateCount }}</span>
                </div>
                <button (click)="goToAffiliateRewards()" class="affiliate-rewards-btn">
                    View Rewards
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Navigation -->
<app-navigation></app-navigation>
