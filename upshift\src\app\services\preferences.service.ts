import { Injectable } from '@angular/core';
import { Preferences as CapacitorPreferences } from '@capacitor/preferences';

/**
 * A service that provides a unified interface for storing preferences
 * with a fallback to localStorage when Capacitor Preferences fails to load
 */
@Injectable({
  providedIn: 'root'
})
export class PreferencesService {
  private useLocalStorage = false;

  constructor() {
    // Check if we can use Capacitor Preferences
    this.checkCapacitorAvailability();
  }

  private async checkCapacitorAvailability(): Promise<void> {
    try {
      // Try to use Capacitor Preferences
      await CapacitorPreferences.get({ key: 'test_key' });
      this.useLocalStorage = false;
      console.log('Using Capacitor Preferences');
    } catch (error) {
      // If it fails, use localStorage as fallback
      this.useLocalStorage = true;
      console.log('Capacitor Preferences not available, using localStorage fallback');
    }
  }

  /**
   * Get a value from preferences
   * @param key The key to get
   * @returns An object with the value or null if not found
   */
  async get(key: string): Promise<{ value: string | null }> {
    try {
      if (this.useLocalStorage) {
        const value = localStorage.getItem(key);
        return { value };
      } else {
        return await CapacitorPreferences.get({ key });
      }
    } catch (error) {
      console.error('Error getting preference:', error);
      // Fallback to localStorage if Capacitor fails
      const value = localStorage.getItem(key);
      return { value };
    }
  }

  /**
   * Set a value in preferences
   * @param key The key to set
   * @param value The value to set
   */
  async set(key: string, value: string): Promise<void> {
    try {
      if (this.useLocalStorage) {
        localStorage.setItem(key, value);
      } else {
        await CapacitorPreferences.set({ key, value });
      }
    } catch (error) {
      console.error('Error setting preference:', error);
      // Fallback to localStorage if Capacitor fails
      localStorage.setItem(key, value);
    }
  }

  /**
   * Remove a value from preferences
   * @param key The key to remove
   */
  async remove(key: string): Promise<void> {
    try {
      if (this.useLocalStorage) {
        localStorage.removeItem(key);
      } else {
        await CapacitorPreferences.remove({ key });
      }
    } catch (error) {
      console.error('Error removing preference:', error);
      // Fallback to localStorage if Capacitor fails
      localStorage.removeItem(key);
    }
  }

  /**
   * Clear all preferences
   */
  async clear(): Promise<void> {
    try {
      if (this.useLocalStorage) {
        localStorage.clear();
      } else {
        await CapacitorPreferences.clear();
      }
    } catch (error) {
      console.error('Error clearing preferences:', error);
      // Fallback to localStorage if Capacitor fails
      localStorage.clear();
    }
  }
}
