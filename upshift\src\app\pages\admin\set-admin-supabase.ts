// This is a script to set a user as admin in Supabase
// Run this script using Node.js to set a user as admin

import { createClient } from '@supabase/supabase-js';

// Replace with your Supabase config
const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
const supabaseServiceRoleKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; // Replace with your service role key

// Initialize Supabase with service role key (admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Function to set a user as admin
async function setUserAsAdmin(userId: string) {
  try {
    // First, check if the user exists
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error finding user:', userError.message);
      return;
    }

    if (!user) {
      console.error('User not found');
      return;
    }

    console.log('Found user:', user);

    // Update the user's username to 'admin'
    const { data, error } = await supabase
      .from('profiles')
      .update({ username: 'admin' })
      .eq('id', userId);

    if (error) {
      console.error('Error setting user as admin:', error.message);
      return;
    }

    console.log('Successfully set user as admin');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Usage: Call the function with the user's ID
// Replace 'USER_ID' with the actual user ID
setUserAsAdmin('USER_ID');
