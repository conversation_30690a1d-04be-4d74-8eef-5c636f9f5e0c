ion-content {
  .users-circle-container {
    display: flex;
    justify-content: center;
    align-items: center;
    .user-col {
      position: relative;
      margin: 0 -10px;
      ion-avatar {
        width: 60px;
        height: 60px;
        border: 2px solid var(--accent);
      }
    }
    .user-col.middle {
      z-index: 2;
      ion-avatar {
        width: 80px;
        height: 80px;
        border: 4px solid var(--accent);
      }
    }
  }
  .testimonial {
    .header-content {
      display: flex;
      align-items: center;
    }
    ion-avatar {
      width: 40px;
      height: 40px;
      border: 2px solid var(--accent);
      margin-right: 10px;
    }
  }
}

ion-footer {
  display: flex;
  justify-content: center;
  padding: 16px;
}