import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AdminService } from '../../services/admin.service';
import { catchError, of } from 'rxjs';

@Component({
  selector: 'app-navigation',
  templateUrl: './navigation.component.html',
  styleUrls: ['./navigation.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule]
})
export class NavigationComponent implements OnInit {
  isAdmin = false;

  constructor(private adminService: AdminService) {}

  ngOnInit() {
    console.log('NavigationComponent: Initializing');
    this.adminService.isAdmin()
      .pipe(
        catchError(error => {
          console.error('NavigationComponent: Error checking admin status:', error);
          return of(false);
        })
      )
      .subscribe({
        next: (isAdmin) => {
          console.log('NavigationComponent: User is admin:', isAdmin);
          this.isAdmin = isAdmin;
        }
      });
  }
}

