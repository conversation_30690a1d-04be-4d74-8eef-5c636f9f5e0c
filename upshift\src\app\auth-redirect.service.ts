import { Injectable, inject } from '@angular/core';
import { Router, Resolve, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { SupabaseService } from './services/supabase.service';
import { UserService } from './services/user.service';

import { Observable, Subscription, of, from }  from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AuthRedirectService implements Resolve<boolean> {
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);
  private authSubscription: Subscription | undefined;

  // This is the method that will be called by the router
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    console.log('AuthRedirect: Resolver called for path:', state.url);
    // Return an observable that completes when the redirection is done
    return from(this.handleRedirect());
  }

  // This method handles the actual redirection logic
  async handleRedirect(): Promise<boolean> {
    console.log('AuthRedirect: handleRedirect called');
    // Get current user from Supabase (using the same approach as in signup.component.ts)
    const user = this.supabaseService._currentUser.value;
    console.log('AuthRedirect: Current user:', user);

    if (!user) {
      console.log('AuthRedirect: No user found, redirecting to signup');
      this.router.navigateByUrl('/signup', { replaceUrl: true });
      return true;
    }

    try {
      const onboardingSeen = await Preferences.get({ key: 'onboarding_complete' });
      if (!onboardingSeen.value) {
        console.log('AuthRedirect: Onboarding not seen, redirecting to onboarding');
        this.router.navigateByUrl('/onboarding', { replaceUrl: true });
        return true;
      }
    } catch (error) {
      console.error('Error getting preferences:', error);
      this.router.navigateByUrl('/onboarding', { replaceUrl: true });
      return true;
    }

    // Check if user has an ID
    if (!user.id) {
      console.error('AuthRedirect: User ID is missing, cannot fetch user document');
      this.router.navigateByUrl('/signup', { replaceUrl: true });
      return true;
    }

    console.log('AuthRedirect: Looking up user document for ID:', user.id);

    try {
      // Get user data from Supabase
      const { data: userData, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      // If there's an error other than "no rows returned", handle it
      if (error && error.code !== 'PGRST116') {
        console.error('AuthRedirect: Error fetching user data:', error);
        this.router.navigateByUrl('/signup', { replaceUrl: true });
        return true;
      }

      // If user doesn't exist in the database, redirect to signup
      if (!userData) {
        // Create new user record if it doesn't exist
        console.log('AuthRedirect: No user data found, redirecting to signup');
        this.router.navigateByUrl('/signup', { replaceUrl: true });
        return true;
      }

      console.log('AuthRedirect: User data:', userData);

      // Check if user has a valid plan
      const endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
      const username = userData.username;
      console.log('AuthRedirect: End date:', endDate);

      if (endDate && endDate >= new Date()) {
        if (username) {
          console.log('AuthRedirect: User has valid plan and username, redirecting to today');
          this.router.navigateByUrl('/today', { replaceUrl: true });
        } else {
          console.log('AuthRedirect: User has valid plan but no username, redirecting to signup-step3');
          this.router.navigateByUrl('/signup-step3', { replaceUrl: true });
        }
      } else {
        console.log('AuthRedirect: User has no valid plan or plan has expired, redirecting to pricing');
        this.router.navigateByUrl('/pricing', { replaceUrl: true });
      }
      return true;
    } catch (error) {
      console.error('AuthRedirect: Error processing user data:', error);
      this.router.navigateByUrl('/signup', { replaceUrl: true });
      return true;
    }
  }

  // Initialize auth subscription and handle redirects
  initAuthSubscription() {
    // Subscribe to auth state changes and handle redirects
    this.authSubscription = this.supabaseService.currentUser$.subscribe(async (user) => {
      console.log('AuthRedirect: Auth state changed, user:', user);

      // Call handleRedirect to respond to auth changes in real-time
      this.handleRedirect().then(() => {
        console.log('AuthRedirect: Redirect handled');
      }).catch(error => {
        console.error('AuthRedirect: Error handling redirect:', error);
      });
    });
  }

  // Clean up subscriptions
  cleanup() {
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }
}
