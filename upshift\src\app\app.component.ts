import { Component, OnInit } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { AuthGuard } from './guards/auth.guard';

@Component({
  selector: 'app-root',
  standalone: true,
  templateUrl: './app.component.html',
  imports: [IonicModule],
})
export class AppComponent implements OnInit {
  constructor(private router: Router) {
    // Redirection is now handled by the RedirectComponent
  }

  ngOnInit() {
    // Initialize from localStorage if needed
    try {
      const storedUrl = localStorage.getItem('lastAttemptedUrl');
      if (storedUrl && !AuthGuard.lastAttemptedUrl) {
        console.log('AppComponent: Restored lastAttemptedUrl from localStorage:', storedUrl);
        AuthGuard.lastAttemptedUrl = storedUrl;
      }
    } catch (error) {
      console.error('AppComponent: Failed to restore URL from localStorage:', error);
    }

    // Subscribe to router events to handle navigation
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      console.log('AppComponent: Navigation event to:', event.url);

      // If we're navigating to a page that's not the root or a public path,
      // store the URL in case we need to redirect back to it
      if (event.url !== '/' && !AuthGuard.isPublicPath(event.url)) {
        console.log('AppComponent: Storing non-public URL:', event.url);
        AuthGuard.storeCurrentUrl(event.url);
      }

      // If we're navigating to a public path (except root, redirect, and auth-redirect), clear any stored URL
      if (event.url !== '/' &&
          event.url !== '/redirect' &&
          !event.url.startsWith('/auth-redirect') &&
          AuthGuard.isPublicPath(event.url)) {
        console.log('AppComponent: Navigating to public path, clearing stored URL');
        AuthGuard.clearLastAttemptedUrl();
      }
    });
  }
}
