ion-content {
  --background: var(--bg);
}
ion-content ion-header ion-toolbar {
  --background: transparent;
}
ion-content ion-header ion-toolbar .head {
  padding: 16px;
}
ion-content ion-header ion-toolbar .head ion-icon {
  font-size: 24px;
  color: var(--text);
  cursor: pointer;
}
ion-content ion-header ion-toolbar .head ion-icon:hover {
  color: var(--accent);
}
ion-content ion-grid h1 {
  margin: 0;
  color: var(--text);
  font-size: 32px;
  font-weight: 700;
  text-align: center;
}
ion-content ion-grid .icon-col {
  position: relative;
  display: flex;
  justify-content: center;
}
ion-content ion-grid .icon-col .vertical-bar {
  position: absolute;
  width: 2px;
  background: var(--accent);
  top: 48px;
  bottom: -24px;
  opacity: 0.5;
}
ion-content ion-grid .icon-col .vertical-bar.last-bar {
  display: none;
}
ion-content ion-grid .icon-col .icon-badge {
  background: var(--surface);
  border: 2px solid var(--accent);
  padding: 12px;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
ion-content ion-grid .icon-col .icon-badge ion-icon {
  font-size: 24px;
  color: var(--accent);
}
ion-content ion-grid .text-col h2 {
  color: var(--text);
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}
ion-content ion-grid .text-col ion-text {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.5;
}
ion-content ion-grid .price-row {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 36px;
}
ion-content ion-grid .price-row .price {
  position: relative;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 16px;
  transition: transform 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}
ion-content ion-grid .price-row .price:hover {
  transform: translateY(-4px);
  border-color: var(--accent);
  box-shadow: 0 8px 24px var(--accent-glow);
}
ion-content ion-grid .price-row .price ion-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--accent);
  color: var(--text);
  padding: 4px 12px;
  border-radius: 12px;
  font-weight: 500;
}
ion-content ion-grid .price-row .price h3 {
  text-align: center;
  color: var(--text);
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}
ion-content ion-grid .price-row .price ion-text {
  color: var(--accent);
  font-size: 20px;
  font-weight: 700;
  display: block;
  margin-bottom: 16px;
}
ion-content ion-grid .confirm-pay {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 0;
  width: calc(100% - 48px);
  max-width: 400px;
  --background: var(--accent);
  --background-hover: var(--accent-hover);
  --color: var(--text);
  --border-radius: 12px;
  --padding-top: 20px;
  --padding-bottom: 20px;
  font-weight: 600;
  font-size: 18px;
  z-index: 1000;
}
ion-content ion-grid .confirm-pay:hover {
  --box-shadow: 0 0 20px var(--accent-glow);
}/*# sourceMappingURL=pricing.page.css.map */