import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';

import { GroupDetailPage } from './group-detail.page';
import { NavigationComponent } from '../../../components/navigation/navigation.component';
import { ComponentsModule } from '../../../components/components.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild([
      {
        path: '',
        component: GroupDetailPage
      }
    ]),
    NavigationComponent,
    ComponentsModule
  ],
  declarations: [GroupDetailPage]
})
export class GroupDetailPageModule {}
