import { Injectable, inject } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, map, switchMap, of } from 'rxjs';
import { SupabaseService } from '../services/supabase.service';
import { UserService } from '../services/user.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);

  canActivate(): Observable<boolean> {
    console.log('AdminGuard: Checking if user can access admin page');

    return this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (!authUser) {
          console.log('AdminGuard: No authenticated user found, redirecting to register');
          this.router.navigate(['/register']);
          return of(false);
        }

        console.log('AdminGuard: User authenticated:', authUser.id);

        // Check if user is admin by username
        return this.userService.getUserById(authUser.id).pipe(
          map(userData => {
            if (!userData) {
              console.log('AdminGuard: User data not found, redirecting to register');
              this.router.navigate(['/register']);
              return false;
            }

            const isAdmin = userData.username === 'admin';

            if (!isAdmin) {
              console.log('AdminGuard: User is not an admin, redirecting to today page');
              this.router.navigate(['/today']);
            } else {
              console.log('AdminGuard: User is an admin, allowing access');
            }

            return isAdmin;
          })
        );
      })
    );
  }
}


