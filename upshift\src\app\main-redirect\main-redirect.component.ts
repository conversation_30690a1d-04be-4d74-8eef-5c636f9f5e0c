import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { SupabaseService } from '../services/supabase.service';
import { AuthGuard } from '../guards/auth.guard';

@Component({
  selector: 'app-main-redirect',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Upshift Yourself</p>
    </div>
  `,
  styles: [`
    .loading-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
    }
  `]
})
export class MainRedirectComponent implements OnInit {
  private router = inject(Router);
  private supabaseService = inject(SupabaseService);

  constructor() {
    console.log('MainRedirectComponent: Constructor called');
  }

  ngOnInit() {
    console.log('MainRedirectComponent: ngOnInit called');
    this.ionViewWillEnter();
  }

  // This is an EXACT copy of the ionViewWillEnter method from signup.component.ts
  async ionViewWillEnter() {
    const { value: onboarding } = await Preferences.get({ key: 'onboarding_complete' });

    // Check if user is authenticated using AuthGuard's static method
    const isAuthenticated = AuthGuard.isAuthenticated(this.supabaseService);
    console.log('MainRedirect: User authenticated:', isAuthenticated);

    // Check if we have a stored URL to redirect back to
    if (AuthGuard.lastAttemptedUrl) {
      console.log('MainRedirect: Found stored URL:', AuthGuard.lastAttemptedUrl);
    }

    if (isAuthenticated) {
      // Get current user from Supabase
      const user = this.supabaseService._currentUser.value;
      console.log('MainRedirect: Current user:', user);

      // Safety check - this should never happen since we already checked isAuthenticated
      if (!user) {
        console.error('MainRedirect: User is authenticated but user object is null');
        this.router.navigateByUrl('/signup');
        return;
      }

      if (!onboarding) {
        this.router.navigateByUrl('/onboarding');
        return;
      }

      try {
        // Try to ensure profile exists first
        await this.supabaseService.ensureProfileExists(user);

        // Then fetch the user data
        const { data: userData, error } = await this.supabaseService.getClient()
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('MainRedirect: Error fetching user data:', error);
          this.router.navigateByUrl('/signup');
          return;
        }

        // Profile should exist at this point
        if (!userData) {
          console.error('MainRedirect: No user data found after ensuring profile exists');
          this.router.navigateByUrl('/signup');
          return;
        }

        console.log('MainRedirect: User data:', userData);

        // Check if user has a valid plan
        const endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
        const username = userData.username;
        console.log('MainRedirect: End date:', endDate);

        if (endDate && endDate >= new Date()) {
          if (username) {
            console.log('MainRedirect: User has valid plan and username, checking for stored URL');
            // Check if we have a stored URL to redirect back to
            if (AuthGuard.lastAttemptedUrl) {
              console.log('MainRedirect: Redirecting to stored URL:', AuthGuard.lastAttemptedUrl);
              const url = AuthGuard.lastAttemptedUrl;
              // Use the static method to clear the URL
              AuthGuard.clearLastAttemptedUrl();
              // Use a longer delay to ensure everything is loaded
              setTimeout(() => {
                console.log('Delayed redirect to:', url);
                // Check if the URL is still valid (not a public path)
                if (!AuthGuard.isPublicPath(url)) {
                  this.router.navigateByUrl(url);
                } else {
                  console.log('URL is a public path, redirecting to /today instead');
                  this.router.navigateByUrl('/today');
                }
              }, 1500);
            } else {
              console.log('MainRedirect: No stored URL, redirecting to home');
              this.router.navigateByUrl('/today');
            }
          } else {
            console.log('MainRedirect: User has valid plan but no username, redirecting to signup-step3');
            this.router.navigateByUrl('/signup-step3');
          }
        } else {
          console.log('MainRedirect: User has no valid plan or plan has expired, redirecting to pricing');
          this.router.navigateByUrl('/pricing');
        }
      } catch (error) {
        console.error('MainRedirect: Error checking user data:', error);
      }
    } else {
      console.log('MainRedirect: No user found, redirecting to signup');
      this.router.navigateByUrl('/signup');
    }
  }
}
