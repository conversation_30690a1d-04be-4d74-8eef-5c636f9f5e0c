{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _TodayPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { QuestService } from '../../services/quest.service';\nimport { SideQuestService } from '../../services/sidequest.service';\nimport { UserService } from '../../services/user.service';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { Quest } from '../../models/quest.model';\nimport { Subscription, forkJoin, map, of, switchMap, take, firstValueFrom } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\nimport { CelebrationComponent } from '../../components/celebration/celebration.component';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { PreferencesService } from '../../services/preferences.service';\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\nimport { StreakCalculatorService } from '../../services/streak-calculator';\nimport { HeaderComponent } from 'src/app/components/header/header.component';\nimport { AuroraComponent } from 'src/app/components/aurora/aurora.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"emojiInput\"];\nconst _c1 = () => [\"M\", \"T\", \"W\", \"T\", \"F\", \"S\", \"S\"];\nfunction TodayPage_div_8__svg_circle_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"circle\", 27);\n  }\n  if (rf & 2) {\n    const date_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"low\", date_r2.completion_percentage < 50);\n    i0.ɵɵattribute(\"stroke-dasharray\", date_r2.completion_percentage * 81.68 / 100 + \", 81.68\")(\"data-date\", date_r2.date);\n  }\n}\nfunction TodayPage_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"ion-text\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function TodayPage_div_8_Template_div_click_3_listener() {\n      const date_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(!date_r2.is_future && ctx_r2.selectDate(date_r2));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 24);\n    i0.ɵɵelement(5, \"circle\", 25);\n    i0.ɵɵtemplate(6, TodayPage_div_8__svg_circle_6_Template, 1, 4, \"circle\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const date_r2 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", date_r2.is_today)(\"selected\", date_r2.is_selected && !date_r2.is_today)(\"unselected\", !date_r2.is_selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpureFunction0(12, _c1)[i_r4], \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", date_r2.is_selected)(\"disabled\", date_r2.is_future);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !date_r2.is_future);\n  }\n}\nfunction TodayPage_ion_card_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-card\", 28)(1, \"ion-card-header\")(2, \"h2\");\n    i0.ɵɵtext(3, \"No quests found..\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-card-content\")(5, \"ion-row\")(6, \"ion-col\", 29)(7, \"ion-text\");\n    i0.ɵɵtext(8, \"No quests found. Try adding a quest ;)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"ion-col\", 30);\n    i0.ɵɵelement(10, \"ion-icon\", 31);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction TodayPage_ion_card_22_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"ion-range\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ion_card_22_div_11_Template_ion_range_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(quest_r6.value_achieved, $event) || (quest_r6.value_achieved = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ion_card_22_div_11_Template_ion_range_ionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updateQuestProgress(quest_r6, $event));\n    })(\"ionInput\", function TodayPage_ion_card_22_div_11_Template_ion_range_ionInput_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView($event.target && ctx_r2.updateSliderBackground($event.target));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"ion-text\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMapInterpolate1(\"--progress-value: \", quest_r6.value_achieved / quest_r6.goal_value * 100, \"%\");\n    i0.ɵɵproperty(\"max\", quest_r6.goal_value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", quest_r6.value_achieved);\n    i0.ɵɵproperty(\"step\", 1);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r6.id)(\"data-quest-type\", quest_r6.quest_type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate4(\" \", quest_r6.value_achieved, \"\", quest_r6.goal_unit === \"min\" ? \"m\" : quest_r6.goal_unit === \"hr\" ? \"h\" : \"s\", \"/\", quest_r6.goal_value, \"\", quest_r6.goal_unit === \"min\" ? \"m\" : quest_r6.goal_unit === \"hr\" ? \"h\" : \"s\", \" \");\n  }\n}\nfunction TodayPage_ion_card_22_div_12_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", quest_r6.goal_unit, \" \");\n  }\n}\nfunction TodayPage_ion_card_22_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"ion-range\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ion_card_22_div_12_Template_ion_range_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(quest_r6.value_achieved, $event) || (quest_r6.value_achieved = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ion_card_22_div_12_Template_ion_range_ionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updateQuestProgress(quest_r6, $event));\n    })(\"ionInput\", function TodayPage_ion_card_22_div_12_Template_ion_range_ionInput_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView($event.target && ctx_r2.updateSliderBackground($event.target));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"ion-text\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TodayPage_ion_card_22_div_12_ng_container_4_Template, 2, 1, \"ng-container\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMapInterpolate1(\"--progress-value: \", quest_r6.value_achieved / quest_r6.goal_value * 100, \"%\");\n    i0.ɵɵproperty(\"max\", quest_r6.goal_value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", quest_r6.value_achieved);\n    i0.ɵɵproperty(\"step\", 1);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r6.id)(\"data-quest-type\", quest_r6.quest_type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", quest_r6.value_achieved, \"/\", quest_r6.goal_value, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", quest_r6.goal_unit !== \"count\");\n  }\n}\nfunction TodayPage_ion_card_22_ion_text_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-text\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\uD83D\\uDD25\", quest_r6.streak, \"d \");\n  }\n}\nfunction TodayPage_ion_card_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-card\", 32);\n    i0.ɵɵlistener(\"click\", function TodayPage_ion_card_22_Template_ion_card_click_0_listener() {\n      const quest_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleQuest(quest_r6));\n    });\n    i0.ɵɵelementStart(1, \"ion-row\")(2, \"ion-col\", 33)(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-col\", 35)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-text\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 36);\n    i0.ɵɵtemplate(11, TodayPage_ion_card_22_div_11_Template, 4, 12, \"div\", 37)(12, TodayPage_ion_card_22_div_12_Template, 5, 11, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"ion-col\", 33);\n    i0.ɵɵtemplate(14, TodayPage_ion_card_22_ion_text_14_Template, 2, 1, \"ion-text\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const quest_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"completed\", quest_r6.completed);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r6.id)(\"data-regular-quest\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(quest_r6.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(quest_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r6.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", quest_r6.goal_unit === \"time\" || quest_r6.goal_unit === \"min\" || quest_r6.goal_unit === \"hr\" || quest_r6.goal_unit === \"sec\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", quest_r6.goal_unit !== \"time\" && quest_r6.goal_unit !== \"min\" && quest_r6.goal_unit !== \"hr\" && quest_r6.goal_unit !== \"sec\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSameDay(ctx_r2.selectedDate, ctx_r2.getToday()));\n  }\n}\nfunction TodayPage_ion_card_29_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\uD83D\\uDD25\", ctx_r2.dailyQuest.streak, \"d \");\n  }\n}\nfunction TodayPage_ion_card_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-card\", 46);\n    i0.ɵɵlistener(\"click\", function TodayPage_ion_card_29_Template_ion_card_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSideQuest(ctx_r2.dailyQuest));\n    });\n    i0.ɵɵelementStart(1, \"ion-row\")(2, \"ion-col\", 33)(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-col\", 35)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-text\", 47);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"ion-col\", 33);\n    i0.ɵɵtemplate(11, TodayPage_ion_card_29_div_11_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"completed\", ctx_r2.dailyQuest.completed);\n    i0.ɵɵattribute(\"data-quest-id\", ctx_r2.dailyQuest.id)(\"data-side-quest\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.dailyQuest.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.dailyQuest.current_quest.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.dailyQuest.current_quest.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSameDay(ctx_r2.selectedDate, ctx_r2.getToday()));\n  }\n}\nfunction TodayPage_ion_card_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-card\", 48)(1, \"ion-card-header\")(2, \"ion-card-title\");\n    i0.ɵɵtext(3, \" Daily Side Quest \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-card-content\")(5, \"ion-text\");\n    i0.ɵɵtext(6, \" No daily side quests are currently available. Please check back later or contact an administrator. \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TodayPage_ng_template_32_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"ion-row\")(2, \"ion-col\", 61)(3, \"ion-card\", 62);\n    i0.ɵɵelement(4, \"ion-icon\", 63);\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6, \"Explore\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ion-text\");\n    i0.ɵɵtext(8, \"Our upshift qusts!\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"ion-col\", 61)(10, \"ion-card\", 62);\n    i0.ɵɵelement(11, \"ion-icon\", 64);\n    i0.ɵɵelementStart(12, \"h2\");\n    i0.ɵɵtext(13, \"Create\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ion-text\");\n    i0.ɵɵtext(15, \"Create your own!\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction TodayPage_ng_template_32_div_12_ion_col_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-col\", 74)(1, \"ion-text\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const emoji_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(emoji_r12);\n  }\n}\nfunction TodayPage_ng_template_32_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"ion-row\", 66)(2, \"ion-col\")(3, \"h2\");\n    i0.ɵɵtext(4, \"Let's choose the \");\n    i0.ɵɵelementStart(5, \"span\", 67);\n    i0.ɵɵtext(6, \"emoji!!\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"ion-row\")(8, \"ion-col\")(9, \"ion-text\", 68);\n    i0.ɵɵtext(10, \"Choose an EMOJI that you think represents this quest!\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"ion-row\", 69)(12, \"ion-col\")(13, \"ion-input\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_12_Template_ion_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.emoji, $event) || (ctx_r2.newQuest.emoji = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"ion-row\", 71);\n    i0.ɵɵtemplate(15, TodayPage_ng_template_32_div_12_ion_col_15_Template, 3, 1, \"ion-col\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ion-button\", 73);\n    i0.ɵɵtext(17, \"Custom\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.emojis);\n  }\n}\nfunction TodayPage_ng_template_32_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"ion-row\", 76)(2, \"ion-col\", 77)(3, \"div\", 78);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6, \"Tell us about your \");\n    i0.ɵɵelementStart(7, \"span\", 67);\n    i0.ɵɵtext(8, \"quest!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"ion-text\", 68);\n    i0.ɵɵtext(10, \"Give your quest a name and describe what you want to achieve\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"ion-row\", 79)(12, \"ion-col\")(13, \"div\", 80)(14, \"div\", 81);\n    i0.ɵɵtext(15, \"\\u270F\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ion-input\", 82);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_13_Template_ion_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.name, $event) || (ctx_r2.newQuest.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 83);\n    i0.ɵɵtext(18, \"Quest Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"div\", 84);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"ion-row\", 79)(21, \"ion-col\")(22, \"div\", 85)(23, \"div\", 81);\n    i0.ɵɵtext(24, \"\\uD83D\\uDCDD\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"ion-textarea\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_13_Template_ion_textarea_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.description, $event) || (ctx_r2.newQuest.description = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"label\", 87);\n    i0.ɵɵtext(27, \"Quest Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"div\", 84);\n    i0.ɵɵelementStart(29, \"div\", 88)(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.emoji || \"\\uD83C\\uDFAF\");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.name);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"warning\", ((ctx_r2.newQuest.description == null ? null : ctx_r2.newQuest.description.length) || 0) > 120);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.newQuest.description == null ? null : ctx_r2.newQuest.description.length) || 0, \"/150 \");\n  }\n}\nfunction TodayPage_ng_template_32_div_14_ion_row_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-row\", 98)(1, \"ion-col\")(2, \"div\", 99);\n    i0.ɵɵelement(3, \"ion-icon\", 100);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Quest Type\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 101)(7, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_12_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectQuestType(\"build\"));\n    });\n    i0.ɵɵelementStart(8, \"div\", 103);\n    i0.ɵɵtext(9, \"\\uD83C\\uDFD7\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 104)(11, \"h4\");\n    i0.ɵɵtext(12, \"Build Habit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14, \"Create a new positive habit\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_12_Template_div_click_15_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectQuestType(\"quit\"));\n    });\n    i0.ɵɵelementStart(16, \"div\", 103);\n    i0.ɵɵtext(17, \"\\uD83D\\uDEAB\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 104)(19, \"h4\");\n    i0.ɵɵtext(20, \"Quit Habit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22, \"Break a negative habit\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"slide-in-from-right\", ctx_r2.questTypeAnimated)(\"slide-out-left\", ctx_r2.questTypeAnimating && ctx_r2.selectedQuestType);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"active\", ctx_r2.newQuest.quest_type === \"build\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"active\", ctx_r2.newQuest.quest_type === \"quit\");\n  }\n}\nfunction TodayPage_ng_template_32_div_14_ion_row_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-row\", 105)(1, \"ion-col\")(2, \"div\", 99);\n    i0.ɵɵelement(3, \"ion-icon\", 106);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 107)(7, \"div\", 108);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectCategory(\"strength\"));\n    });\n    i0.ɵɵelementStart(8, \"div\", 109);\n    i0.ɵɵtext(9, \"\\uD83D\\uDCAA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Strength\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 108);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_12_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectCategory(\"money\"));\n    });\n    i0.ɵɵelementStart(13, \"div\", 109);\n    i0.ɵɵtext(14, \"\\uD83D\\uDCB0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Money\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 108);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_17_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectCategory(\"health\"));\n    });\n    i0.ɵɵelementStart(18, \"div\", 109);\n    i0.ɵɵtext(19, \"\\uD83C\\uDFE5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \"Health\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 108);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_22_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectCategory(\"knowledge\"));\n    });\n    i0.ɵɵelementStart(23, \"div\", 109);\n    i0.ɵɵtext(24, \"\\uD83E\\uDDE0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"Knowledge\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"slide-in-from-right\", ctx_r2.categoryAnimated)(\"slide-out-left\", ctx_r2.categoryAnimating && ctx_r2.selectedCategory);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.category === \"strength\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.category === \"money\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.category === \"health\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.category === \"knowledge\");\n  }\n}\nfunction TodayPage_ng_template_32_div_14_ion_row_14_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵelement(1, \"ion-icon\", 118);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"You already have a high priority quest in this category\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TodayPage_ng_template_32_div_14_ion_row_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-row\", 110)(1, \"ion-col\")(2, \"div\", 99);\n    i0.ɵɵelement(3, \"ion-icon\", 111);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Priority\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 112)(7, \"div\", 113);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_14_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectPriority(\"basic\"));\n    });\n    i0.ɵɵelement(8, \"div\", 114);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"Basic Priority\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 113);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_14_Template_div_click_11_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectPriority(\"high\"));\n    });\n    i0.ɵɵelement(12, \"div\", 115);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"High Priority\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(15, TodayPage_ng_template_32_div_14_ion_row_14_div_15_Template, 4, 0, \"div\", 116);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"slide-in-from-right\", ctx_r2.priorityAnimated)(\"slide-out-left\", ctx_r2.priorityAnimating);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"active\", ctx_r2.newQuest.priority === \"basic\")(\"disabled\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"active\", ctx_r2.newQuest.priority === \"high\")(\"disabled\", ctx_r2.hasHighPriorityQuest);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasHighPriorityQuest);\n  }\n}\nfunction TodayPage_ng_template_32_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"ion-row\", 90)(2, \"ion-col\", 77)(3, \"div\", 91)(4, \"div\", 92);\n    i0.ɵɵtext(5, \"\\u2699\\uFE0F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"h2\", 93);\n    i0.ɵɵtext(7, \"Configure your \");\n    i0.ɵɵelementStart(8, \"span\", 67);\n    i0.ɵɵtext(9, \"quest!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p\", 94);\n    i0.ɵɵtext(11, \"Choose the type, category and set your goal\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, TodayPage_ng_template_32_div_14_ion_row_12_Template, 23, 8, \"ion-row\", 95)(13, TodayPage_ng_template_32_div_14_ion_row_13_Template, 27, 12, \"ion-row\", 96)(14, TodayPage_ng_template_32_div_14_ion_row_14_Template, 16, 13, \"ion-row\", 97);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.newQuest.quest_type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.quest_type && !ctx_r2.categorySelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.category);\n  }\n}\nfunction TodayPage_ng_template_32_div_15_ion_row_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-row\", 123)(1, \"ion-col\")(2, \"div\", 124)(3, \"div\", 125)(4, \"div\", 126);\n    i0.ɵɵtext(5, \"\\uD83C\\uDFAF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Set Your Goal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 127)(9, \"div\", 128)(10, \"ion-input\", 129);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_15_ion_row_12_Template_ion_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.goal_value, $event) || (ctx_r2.newQuest.goal_value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"label\", 130);\n    i0.ɵɵtext(12, \"Target\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 128)(14, \"ion-select\", 131);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_15_ion_row_12_Template_ion_select_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.goal_unit, $event) || (ctx_r2.newQuest.goal_unit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(15, \"ion-select-option\", 132);\n    i0.ɵɵtext(16, \"times\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ion-select-option\", 133);\n    i0.ɵɵtext(18, \"steps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"ion-select-option\", 134);\n    i0.ɵɵtext(20, \"meters\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"ion-select-option\", 135);\n    i0.ɵɵtext(22, \"kilometers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"ion-select-option\", 136);\n    i0.ɵɵtext(24, \"seconds\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"ion-select-option\", 137);\n    i0.ɵɵtext(26, \"minutes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"ion-select-option\", 138);\n    i0.ɵɵtext(28, \"hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"ion-select-option\", 139);\n    i0.ɵɵtext(30, \"calories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"ion-select-option\", 140);\n    i0.ɵɵtext(32, \"grams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"ion-select-option\", 141);\n    i0.ɵɵtext(34, \"milligrams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"ion-select-option\", 142);\n    i0.ɵɵtext(36, \"liters\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"ion-select-option\", 143);\n    i0.ɵɵtext(38, \"drinks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"ion-select-option\", 144);\n    i0.ɵɵtext(40, \"pages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"ion-select-option\", 145);\n    i0.ɵɵtext(42, \"books\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"ion-select-option\", 146);\n    i0.ɵɵtext(44, \"percent\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"ion-select-option\", 147);\n    i0.ɵɵtext(46, \"euros\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"ion-select-option\", 148);\n    i0.ɵɵtext(48, \"dollars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"ion-select-option\", 149);\n    i0.ɵɵtext(50, \"pounds\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"label\", 130);\n    i0.ɵɵtext(52, \"Unit\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 150)(54, \"span\", 151);\n    i0.ɵɵtext(55, \"Goal:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\", 152);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"slide-in\", ctx_r2.goalAnimated);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.goal_value);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.goal_unit);\n    i0.ɵɵadvance(43);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.newQuest.goal_value ? ctx_r2.newQuest.goal_value : 1, \" \", ctx_r2.newQuest.goal_unit ? ctx_r2.newQuest.goal_unit : \"times\", \"\");\n  }\n}\nfunction TodayPage_ng_template_32_div_15_ion_row_13_div_16_ion_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 167);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_15_ion_row_13_div_16_ion_button_4_Template_ion_button_click_0_listener() {\n      const day_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r2.updateDaysOfWeek(day_r20.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r20 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.selectedDaysOfWeek.includes(day_r20.value));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", day_r20.label, \" \");\n  }\n}\nfunction TodayPage_ng_template_32_div_15_ion_row_13_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"h4\");\n    i0.ɵɵtext(2, \"Select Days\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 165);\n    i0.ɵɵtemplate(4, TodayPage_ng_template_32_div_15_ion_row_13_div_16_ion_button_4_Template, 2, 3, \"ion-button\", 166);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.weekDays);\n  }\n}\nfunction TodayPage_ng_template_32_div_15_ion_row_13_div_17_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 171)(1, \"ion-checkbox\", 172);\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ng_template_32_div_15_ion_row_13_div_17_div_4_Template_ion_checkbox_ionChange_1_listener($event) {\n      const day_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r2.updateDaysOfMonth($event, day_r22));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 173);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r22 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"month-day-\" + day_r22)(\"value\", day_r22);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"month-day-\" + day_r22);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(day_r22);\n  }\n}\nfunction TodayPage_ng_template_32_div_15_ion_row_13_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 168)(1, \"h4\");\n    i0.ɵɵtext(2, \"Select Days\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 169);\n    i0.ɵɵtemplate(4, TodayPage_ng_template_32_div_15_ion_row_13_div_17_div_4_Template, 4, 4, \"div\", 170);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.monthDays);\n  }\n}\nfunction TodayPage_ng_template_32_div_15_ion_row_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-row\", 153)(1, \"ion-col\")(2, \"div\", 154)(3, \"div\", 155)(4, \"div\", 156);\n    i0.ɵɵtext(5, \"\\uD83D\\uDCC5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Choose Frequency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 157)(9, \"ion-select\", 158);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_15_ion_row_13_Template_ion_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.goal_period, $event) || (ctx_r2.newQuest.goal_period = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ng_template_32_div_15_ion_row_13_Template_ion_select_ionChange_9_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.updatePeriodDisplay());\n    });\n    i0.ɵɵelementStart(10, \"ion-select-option\", 159);\n    i0.ɵɵtext(11, \"Every Day\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"ion-select-option\", 160);\n    i0.ɵɵtext(13, \"Specific days of the week\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ion-select-option\", 161);\n    i0.ɵɵtext(15, \"Specific days of the month\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, TodayPage_ng_template_32_div_15_ion_row_13_div_16_Template, 5, 1, \"div\", 162)(17, TodayPage_ng_template_32_div_15_ion_row_13_div_17_Template, 5, 1, \"div\", 163);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.goal_period);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.goal_period === \"week\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.goal_period === \"month\");\n  }\n}\nfunction TodayPage_ng_template_32_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"ion-row\", 90)(2, \"ion-col\", 77)(3, \"div\", 120)(4, \"div\", 92);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"h2\", 93);\n    i0.ɵɵtext(7, \"Finalize your \");\n    i0.ɵɵelementStart(8, \"span\", 67);\n    i0.ɵɵtext(9, \"quest!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p\", 94);\n    i0.ɵɵtext(11, \"Set your goal and frequency to complete your quest\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, TodayPage_ng_template_32_div_15_ion_row_12_Template, 58, 6, \"ion-row\", 121)(13, TodayPage_ng_template_32_div_15_ion_row_13_Template, 18, 3, \"ion-row\", 122);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.emoji ? ctx_r2.newQuest.emoji : \"\\uD83C\\uDFAF\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.goal_value === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.goal_value);\n  }\n}\nfunction TodayPage_ng_template_32_ion_col_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-col\")(1, \"ion-button\", 174);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_ion_col_17_Template_ion_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.prevStep());\n    });\n    i0.ɵɵtext(2, \"Back\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TodayPage_ng_template_32_ion_col_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-col\")(1, \"ion-button\", 175);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_ion_col_18_Template_ion_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(2, \"Next\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TodayPage_ng_template_32_ion_col_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-col\")(1, \"ion-button\", 176);\n    i0.ɵɵtext(2, \"Create Quest\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TodayPage_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-content\", 49);\n    i0.ɵɵelement(1, \"app-aurora\");\n    i0.ɵɵelementStart(2, \"ion-grid\")(3, \"ion-row\", 50)(4, \"ion-col\")(5, \"h2\");\n    i0.ɵɵtext(6, \"Add New Quest\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"ion-progress-bar\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-icon\", 52);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_Template_ion_icon_click_8_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeAddQuestModal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"form\", 53, 0);\n    i0.ɵɵlistener(\"ngSubmit\", function TodayPage_ng_template_32_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.createQuest());\n    });\n    i0.ɵɵtemplate(11, TodayPage_ng_template_32_div_11_Template, 16, 0, \"div\", 54)(12, TodayPage_ng_template_32_div_12_Template, 18, 2, \"div\", 55)(13, TodayPage_ng_template_32_div_13_Template, 32, 6, \"div\", 56)(14, TodayPage_ng_template_32_div_14_Template, 15, 3, \"div\", 57)(15, TodayPage_ng_template_32_div_15_Template, 14, 3, \"div\", 58);\n    i0.ɵɵelementStart(16, \"ion-row\", 59);\n    i0.ɵɵtemplate(17, TodayPage_ng_template_32_ion_col_17_Template, 3, 0, \"ion-col\", 44)(18, TodayPage_ng_template_32_ion_col_18_Template, 3, 0, \"ion-col\", 44)(19, TodayPage_ng_template_32_ion_col_19_Template, 3, 0, \"ion-col\", 44);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r2.progress);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep !== 5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep !== 5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 5);\n  }\n}\nfunction TodayPage_app_celebration_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-celebration\", 177);\n    i0.ɵɵlistener(\"close\", function TodayPage_app_celebration_33_Template_app_celebration_close_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeCelebration());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"user\", ctx_r2.currentUser)(\"date\", ctx_r2.formatDate(ctx_r2.selectedDate));\n  }\n}\nexport class TodayPage {\n  // Method to load daily side quest\n  loadDailySideQuest() {\n    // Only load for today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const selectedDate = new Date(this.selectedDate);\n    selectedDate.setHours(0, 0, 0, 0);\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\n    // Reset dailyQuest if not today's date\n    if (!isTodaySelected) {\n      this.dailyQuest = null;\n      return;\n    }\n    if (this.showSidequests && isTodaySelected && this.userId) {\n      // Use the ensureUserHasDailySideQuests method\n      this.sideQuestService.ensureUserHasDailySideQuests(this.userId).pipe(take(1)).subscribe({\n        next: sideQuests => {\n          if (sideQuests && sideQuests.length > 0) {\n            const sideQuest = sideQuests[0];\n            // Get the quest details from the pool\n            this.supabaseService.getClient().from('daily_sidequest_pool').select('*').eq('id', sideQuest.current_quest_id).single().then(response => {\n              if (response.error) {\n                return;\n              }\n              const questDetails = response.data;\n              // Create the daily quest object\n              this.dailyQuest = {\n                id: sideQuest.id,\n                current_quest: {\n                  id: sideQuest.current_quest_id,\n                  name: questDetails.name || 'Daily Side Quest',\n                  description: questDetails.description || 'Complete this daily side quest',\n                  goal_value: questDetails.goal_value || 1,\n                  goal_unit: questDetails.goal_unit || 'count'\n                },\n                streak: sideQuest.streak || 0,\n                completed: sideQuest.completed || false,\n                value_achieved: sideQuest.value_achieved || 0,\n                emoji: questDetails.emoji || '🎯'\n              };\n            });\n          } else {\n            this.dailyQuest = null;\n          }\n        },\n        error: () => {\n          this.dailyQuest = null;\n        }\n      });\n    }\n  }\n  constructor() {\n    // User data\n    this.user$ = of(null);\n    this.userId = null;\n    this.showSidequests = true;\n    // Date and calendar\n    this.selectedDate = new Date();\n    this.weekDates = [];\n    this.dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];\n    this.headerText = 'Today';\n    this.weekOffset = 0;\n    // Quests\n    this.quests = [];\n    this.dailyQuest = null;\n    this.currentStep = 5;\n    this.totalSteps = 5;\n    this.emojis = ['🚀', '🪐', '⏳', '💊', '⚔️', '🧠', '🦷', '👨‍🍳', '🏃', '🥬', '🏆', '🎮', '🎯', '💻', '🚴‍♂️', '🏋️‍♂️', '💰', '💸', '🪬', '🧪', '😴', '📈', '📚', '❌', '🎓', '💪', '🧘‍♂️', '📵', '🚭', '💧'];\n    // Cache for quest data to improve performance\n    this.questCache = {};\n    // Flag to track if we're currently loading data\n    this.isLoadingData = false;\n    // Add Quest Modal\n    this.showAddQuestModal = false;\n    this.newQuest = this.getEmptyQuest();\n    this.hasHighPriorityQuest = false;\n    // Animation states\n    this.questTypeAnimated = false;\n    this.questTypeAnimating = false;\n    this.selectedQuestType = '';\n    this.categoryAnimated = false;\n    this.categoryAnimating = false;\n    this.categorySelected = false;\n    this.selectedCategory = '';\n    this.priorityAnimated = false;\n    this.priorityAnimating = false;\n    this.goalAnimated = false;\n    // Celebration Modal\n    this.showCelebration = false;\n    this.currentUser = null;\n    this.celebrationShownDates = [];\n    // Days selection for new quest\n    this.weekDays = [{\n      value: 'Sun',\n      label: 'Su'\n    }, {\n      value: 'Mon',\n      label: 'Mo'\n    }, {\n      value: 'Tue',\n      label: 'Tu'\n    }, {\n      value: 'Wed',\n      label: 'We'\n    }, {\n      value: 'Thu',\n      label: 'Th'\n    }, {\n      value: 'Fri',\n      label: 'Fr'\n    }, {\n      value: 'Sat',\n      label: 'Sa'\n    }];\n    this.monthDays = Array.from({\n      length: 31\n    }, (_, i) => i + 1);\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    // Use inject instead of constructor injection\n    this.questService = inject(QuestService);\n    this.sideQuestService = inject(SideQuestService);\n    this.userService = inject(UserService);\n    this.supabaseService = inject(SupabaseService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n    this.preferencesService = inject(PreferencesService);\n    this.streakCalculator = inject(StreakCalculatorService);\n    this.isRedirecting = false; // Flag to prevent multiple redirects\n    // Cache for week date progress\n    this.weekProgressCache = {};\n    // Flag to track if we're currently changing weeks\n    this.isChangingWeek = false;\n    // Map to track which quests are currently being toggled\n    this.togglingQuestIds = {};\n    // Map to track which quests are currently being updated\n    this.updatingQuestIds = {};\n    // Map to track which side quests are currently being toggled\n    this.togglingSideQuestIds = {};\n    // Subscribe to query params to get date and week_offset from URL\n    this.route.queryParams.subscribe(params => {\n      const dateParam = params['date'];\n      const weekOffsetParam = params['week_offset'];\n      console.log('TodayPage: Date param from URL query:', dateParam);\n      console.log('TodayPage: Week offset param from URL query:', weekOffsetParam);\n      // Process week offset parameter\n      if (weekOffsetParam !== undefined) {\n        try {\n          this.weekOffset = parseInt(weekOffsetParam);\n          console.log('TodayPage: Week offset set to:', this.weekOffset);\n        } catch (error) {\n          console.error('TodayPage: Error parsing week offset:', error);\n          this.weekOffset = 0;\n        }\n      } else {\n        this.weekOffset = 0;\n      }\n      // Process date parameter\n      if (dateParam) {\n        try {\n          // Validate date format (YYYY-MM-DD)\n          if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateParam)) {\n            this.selectedDate = new Date(dateParam);\n            console.log('TodayPage: Selected date from URL query:', this.selectedDate);\n          } else {\n            console.error('TodayPage: Invalid date format in URL query:', dateParam);\n            this.selectedDate = new Date(); // Default to today\n          }\n        } catch (error) {\n          console.error('TodayPage: Error parsing date from URL query:', error);\n          this.selectedDate = new Date(); // Default to today\n        }\n      } else {\n        this.selectedDate = new Date(); // Default to today\n      }\n      // Initialize week dates based on selected date and week offset\n      this.generateWeekDates();\n      // Update header text and load data\n      this.updateHeaderText();\n      // Only load data if we have a userId\n      if (this.userId) {\n        this.loadData();\n      }\n    });\n    // Subscribe to auth state changes\n    this.userSubscription = this.supabaseService.currentUser$.subscribe(authUser => {\n      if (!authUser) {\n        console.log('TodayPage: No authenticated user, but not redirecting');\n        // Removed redirect to allow direct access\n        return;\n      }\n      // User is authenticated, get user data\n      this.userId = authUser.id;\n      // Get user data from Supabase\n      this.userService.getUserById(authUser.id).subscribe(userData => {\n        if (!userData) {\n          console.log('TodayPage: No user data found, but not redirecting');\n          // Removed redirect to allow direct access\n          return;\n        }\n        console.log('TodayPage: User data loaded:', userData);\n        this.loadData();\n      });\n    });\n    // Set up user$ observable for template binding\n    this.user$ = this.supabaseService.currentUser$.pipe(switchMap(authUser => {\n      if (!authUser) {\n        return of(null);\n      }\n      return this.userService.getUserById(authUser.id);\n    }));\n    // Subscribe to user$ to get user preferences\n    const userDataSubscription = this.user$.subscribe({\n      next: user => {\n        if (user) {\n          this.showSidequests = user.sidequests_switch;\n          this.currentUser = user;\n        }\n      }\n    });\n    // Add the subscription to be cleaned up\n    this.userSubscription = new Subscription();\n    this.userSubscription.add(userDataSubscription);\n  }\n  ngOnInit() {\n    // Generate week dates and preload data for all days\n    this.generateWeekDates();\n    // Preload data for all days in the week\n    setTimeout(() => {\n      this.preloadWeekData();\n    }, 0);\n    // Load celebration shown dates from localStorage and clean up old ones\n    try {\n      // Get today's date\n      const today = new Date();\n      const todayStr = this.formatDate(today);\n      // First, collect all localStorage keys\n      const allKeys = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key) {\n          allKeys.push(key);\n        }\n      }\n      // Find and remove all celebration_shown keys except today's\n      allKeys.forEach(key => {\n        if (key.startsWith('celebration_shown_') && key !== `celebration_shown_${todayStr}`) {\n          localStorage.removeItem(key);\n        }\n      });\n      // Check if we have a celebration shown for today\n      const todayCelebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\n      // Add to our tracking array if found\n      this.celebrationShownDates = [];\n      if (todayCelebrationShown) {\n        this.celebrationShownDates.push(todayStr);\n      }\n    } catch (error) {\n      this.celebrationShownDates = [];\n    }\n  }\n  ionViewWillEnter() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Authentication is now handled by the AuthGuard\n      // Just get the current user and load data\n      const authUser = _this.supabaseService._currentUser.value;\n      if (!authUser) {\n        console.log('TodayPage: No authenticated user, but not redirecting');\n        return;\n      }\n      // Use the UserService to get or create the user document\n      _this.userService.ensureUserExists(authUser).subscribe(userData => {\n        if (!userData) {\n          _this.router.navigateByUrl('/signup');\n          return;\n        }\n        // Get end date\n        let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;\n        const currentDate = new Date();\n        // Compare dates properly\n        let isValidPlan = false;\n        if (endDate instanceof Date) {\n          isValidPlan = endDate > currentDate;\n        }\n        if (!isValidPlan) {\n          // Prevent multiple redirects\n          if (_this.isRedirecting) return;\n          _this.isRedirecting = true;\n          setTimeout(() => {\n            _this.router.navigateByUrl('/pricing');\n            setTimeout(() => {\n              _this.isRedirecting = false;\n            }, 2000);\n          }, 500);\n          return;\n        }\n        // Check if we have cached data for this date\n        const dateKey = _this.formatDate(_this.selectedDate);\n        if (_this.questCache[dateKey]) {\n          // Use cached data\n          _this.quests = _this.questCache[dateKey];\n          // Initialize slider backgrounds immediately\n          requestAnimationFrame(() => {\n            _this.initializeSliderBackgrounds();\n          });\n          // Load daily side quest if needed\n          _this.loadDailySideQuest();\n        } else {\n          // Load data with the current selected date\n          _this.loadData();\n        }\n      });\n      // Make sure the URL reflects the selected date and week offset\n      const route = _this.router.url;\n      const dateParam = _this.formatDate(_this.selectedDate);\n      if (route === '/today') {\n        // If we're on the base route, update to include the date and week_offset as query parameters\n        _this.router.navigate(['/today'], {\n          queryParams: {\n            date: dateParam,\n            week_offset: _this.weekOffset !== 0 ? _this.weekOffset : null\n          },\n          replaceUrl: true\n        });\n      }\n    })();\n  }\n  // Initialize all slider backgrounds\n  initializeSliderBackgrounds() {\n    // Use requestAnimationFrame for better performance\n    requestAnimationFrame(() => {\n      const sliders = document.querySelectorAll('.progress-slider');\n      if (sliders.length === 0) {\n        return;\n      }\n      sliders.forEach(slider => {\n        if (slider instanceof HTMLInputElement) {\n          // Get the slider's quest ID for debugging\n          const sliderQuestId = slider.getAttribute('data-quest-id');\n          if (!sliderQuestId) {\n            return;\n          }\n          // Get the exact value from the slider (no rounding)\n          const sliderValue = parseInt(slider.value);\n          const minValue = parseInt(slider.min);\n          const maxValue = parseInt(slider.max);\n          // Calculate the percentage value\n          const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n          // Set the background directly with hardcoded colors\n          slider.style.background = `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\n          // Add a data attribute to track the current value\n          slider.setAttribute('data-current-value', slider.value);\n        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n          // Get the slider's quest ID for debugging\n          const sliderQuestId = slider.getAttribute('data-quest-id');\n          if (!sliderQuestId) {\n            return;\n          }\n          // Get the value from the element's properties or attributes\n          const valueAttr = slider.getAttribute('value') || '0';\n          const minAttr = slider.getAttribute('min') || '0';\n          const maxAttr = slider.getAttribute('max') || '100';\n          const sliderValue = parseInt(valueAttr);\n          const minValue = parseInt(minAttr);\n          const maxValue = parseInt(maxAttr);\n          // Calculate the percentage value\n          const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n          // Set the CSS variable for the progress\n          slider.style.setProperty('--progress-value', `${percentage}%`);\n          // Add a data attribute to track the current value\n          slider.setAttribute('data-current-value', sliderValue.toString());\n        }\n      });\n    });\n  }\n  ionViewWillLeave() {\n    console.log('TodayPage: ionViewWillLeave called');\n  }\n  ngOnDestroy() {\n    console.log('TodayPage: ngOnDestroy called');\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n  loadData() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.userId) {\n        return;\n      }\n      // Update header text\n      _this2.updateHeaderText();\n      // Check if we have cached data for this date\n      const dateKey = _this2.formatDate(_this2.selectedDate);\n      if (_this2.questCache[dateKey]) {\n        // Use cached data\n        _this2.quests = _this2.questCache[dateKey];\n        // Initialize slider backgrounds immediately\n        requestAnimationFrame(() => {\n          _this2.initializeSliderBackgrounds();\n        });\n        // Load daily side quest if needed\n        _this2.loadDailySideQuest();\n        return;\n      }\n      // Set up date variables\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(_this2.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      console.log('TodayPage: Loading data for date:', _this2.formatDate(_this2.selectedDate));\n      if (isTodaySelected) {\n        // Check if we've already calculated streaks for today\n        const todayDateString = _this2.formatDate(today);\n        try {\n          const {\n            value: lastStreakCalculation\n          } = yield _this2.preferencesService.get('last_streak_calculation');\n          if (lastStreakCalculation !== todayDateString) {\n            console.log('TodayPage: First time loading today, calculating streaks');\n            // Najprv spracujeme všetky questy pomocou checkMissedDays\n            yield firstValueFrom(_this2.questService.getQuests(_this2.userId).pipe(take(1), switchMap(/*#__PURE__*/function () {\n              var _ref = _asyncToGenerator(function* (quests) {\n                // Check missed days for each quest\n                for (const quest of quests) {\n                  if (quest.id) {\n                    yield _this2.questService.checkMissedDays(quest.id);\n                  }\n                }\n                // Potom vytvoríme progress záznamy pre quit questy\n                yield _this2.questService.createQuitQuestProgressForToday();\n                // NEBUDEME tu nastavovať last_streak_calculation, aby sa mohli vypočítať streaky v ďalšej časti kódu\n                return quests;\n              });\n              return function (_x) {\n                return _ref.apply(this, arguments);\n              };\n            }())));\n            // Streaky sa vypočítajú v ďalšej časti kódu\n          } else {\n            console.log('TodayPage: Streaks already calculated for today');\n          }\n        } catch (error) {\n          console.error('TodayPage: Error checking last streak calculation:', error);\n          // Ak nastane chyba, nenastavujeme last_streak_calculation, aby sa mohli vypočítať streaky\n        }\n        // Recalculate streak for the daily side quest only for today\n        if (_this2.showSidequests) {\n          _this2.sideQuestService.recalculateSideQuestStreak(_this2.userId, _this2.selectedDate).subscribe({\n            error: error => {\n              console.error('Error recalculating side quest streak:', error);\n            }\n          });\n        }\n      }\n      // Load quests\n      _this2.questService.getQuests(_this2.userId).pipe(take(1), switchMap(quests => {\n        // Filter active quests for the selected date\n        const filteredQuests = _this2.filterQuestsForDate(quests, _this2.selectedDate);\n        if (filteredQuests.length === 0) {\n          return of([]);\n        }\n        // Sort filtered quests by creation date (newest first) or ID\n        const sortedFilteredQuests = [...filteredQuests].sort((a, b) => {\n          if (a.created_at && b.created_at) {\n            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n          }\n          return a.id && b.id ? a.id.localeCompare(b.id) : 0;\n        });\n        // Get all progress for all quests at once\n        return _this2.questService.getQuestProgressForDate(_this2.userId, _this2.selectedDate).pipe(take(1), switchMap(allProgress => {\n          // Create a lookup for quick access\n          const progressLookup = {};\n          allProgress.forEach(progress => {\n            progressLookup[progress.quest_id] = progress;\n          });\n          // For today's view, calculate streaks once per day\n          // For other days, just use the streak from the database\n          if (isTodaySelected) {\n            // Check if we've already calculated streaks for today\n            const todayDateString = _this2.formatDate(today);\n            return _this2.preferencesService.get('last_streak_calculation').then(({\n              value: lastStreakCalculation\n            }) => {\n              if (lastStreakCalculation !== todayDateString) {\n                console.log('TodayPage: First time loading today, calculating streaks');\n                // Calculate streaks using our streak calculator\n                return _this2.streakCalculator.calculateStreaks(_this2.userId, sortedFilteredQuests).then(streaks => {\n                  // Map quests with progress and calculated streaks\n                  return sortedFilteredQuests.map(quest => {\n                    const progress = progressLookup[quest.id];\n                    const calculatedStreak = streaks[quest.id] || 0;\n                    // Update the streak in the database\n                    _this2.questService.updateQuestStreak(quest.id, calculatedStreak).subscribe();\n                    return {\n                      ...quest,\n                      completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                      value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                      streak: calculatedStreak\n                    };\n                  });\n                }).then(result => {\n                  // Po výpočte streakov nastavíme last_streak_calculation\n                  _this2.preferencesService.set('last_streak_calculation', todayDateString);\n                  return result;\n                });\n              } else {\n                console.log('TodayPage: Streaks already calculated for today, using database values');\n                // Just use the streak from the database\n                return sortedFilteredQuests.map(quest => {\n                  const progress = progressLookup[quest.id];\n                  return {\n                    ...quest,\n                    completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                    value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                    streak: quest.streak || 0\n                  };\n                });\n              }\n            }).catch(error => {\n              console.error('TodayPage: Error checking last streak calculation:', error);\n              // If there's an error, just use the streak from the database\n              return sortedFilteredQuests.map(quest => {\n                const progress = progressLookup[quest.id];\n                return {\n                  ...quest,\n                  completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                  value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                  streak: quest.streak || 0\n                };\n              });\n            });\n          } else {\n            // For previous days, just use the streak from the database but set it to 0 for display\n            return Promise.resolve(sortedFilteredQuests.map(quest => {\n              const progress = progressLookup[quest.id];\n              return {\n                ...quest,\n                completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                streak: 0 // Don't show streak for previous days\n              };\n            }));\n          }\n        }));\n      })).subscribe({\n        next: questsWithProgress => {\n          // Sort quests by creation date (newest first) or ID\n          const sortedQuests = [...questsWithProgress].sort((a, b) => {\n            if (a.created_at && b.created_at) {\n              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n            }\n            return a.id && b.id ? a.id.localeCompare(b.id) : 0;\n          });\n          // Check if all quests are completed for today\n          _this2.checkAllQuestsCompleted(sortedQuests);\n          // Update the quests array\n          _this2.quests = sortedQuests;\n          // Cache the quests for this date\n          const dateKey = _this2.formatDate(_this2.selectedDate);\n          _this2.questCache[dateKey] = sortedQuests;\n          // Update the week date progress\n          _this2.updateWeekDateProgress();\n          // Initialize slider backgrounds\n          requestAnimationFrame(() => {\n            _this2.initializeSliderBackgrounds();\n          });\n          // Load daily side quest if needed\n          _this2.loadDailySideQuest();\n        },\n        error: error => {\n          console.error('Error loading quests:', error);\n        }\n      });\n    })();\n  }\n  generateWeekDates() {\n    const today = new Date();\n    // Calculate the start of the week based on week offset\n    // This starts on Monday (1) instead of Sunday (0)\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\n    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // Convert to Monday-based (0 = Monday)\n    const startOfWeek = new Date(today);\n    startOfWeek.setDate(today.getDate() - daysFromMonday + 7 * this.weekOffset);\n    this.weekDates = [];\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startOfWeek);\n      date.setDate(startOfWeek.getDate() + i);\n      const dateString = this.formatDate(date);\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n      // Check if we have cached progress for this date\n      const dateKey = dateString;\n      let totalQuests = 0;\n      let completedQuests = 0;\n      let completionPercentage = 0;\n      if (this.weekProgressCache[dateKey]) {\n        const cached = this.weekProgressCache[dateKey];\n        totalQuests = cached.total;\n        completedQuests = cached.completed;\n        completionPercentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n      }\n      this.weekDates.push({\n        date: dateString,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture,\n        total_quests: totalQuests,\n        completed_quests: completedQuests,\n        completion_percentage: completionPercentage\n      });\n    }\n    // Preload data for all days in the week\n    if (this.userId) {\n      // Use setTimeout to allow the UI to render first\n      setTimeout(() => {\n        this.preloadWeekData();\n      }, 0);\n    }\n  }\n  updateWeekDateProgress() {\n    if (!this.userId) return;\n    // For each date in the week, update the progress\n    this.weekDates.forEach((weekDate, index) => {\n      if (weekDate.is_future) return;\n      const date = new Date(weekDate.date);\n      const dateKey = this.formatDate(date);\n      // Check if we have cached progress for this date\n      if (this.weekProgressCache[dateKey]) {\n        const cached = this.weekProgressCache[dateKey];\n        this.weekDates[index].total_quests = cached.total;\n        this.weekDates[index].completed_quests = cached.completed;\n        this.weekDates[index].completion_percentage = cached.total > 0 ? Math.round(cached.completed / cached.total * 100) : 0;\n        return;\n      }\n      // If we have cached quests for this date, use them to calculate progress\n      if (this.questCache[dateKey]) {\n        const cachedQuests = this.questCache[dateKey];\n        const totalQuests = cachedQuests.length;\n        const completedQuests = cachedQuests.filter(q => q.completed).length;\n        // Cache the progress\n        this.weekProgressCache[dateKey] = {\n          total: totalQuests,\n          completed: completedQuests\n        };\n        // Update the week date\n        this.weekDates[index].total_quests = totalQuests;\n        this.weekDates[index].completed_quests = completedQuests;\n        this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n        return;\n      }\n    });\n    // Preload data for all days in the week\n    this.preloadWeekData();\n  }\n  // Helper method to filter quests for a specific date\n  filterQuestsForDate(quests, date) {\n    const dateObj = new Date(date);\n    const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.\n    // Django uses Monday=0, Sunday=6 format, so we need to convert\n    const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format\n    const dayOfMonth = dateObj.getDate(); // 1-31\n    console.log(`TodayPage: Filtering quests for date ${this.formatDate(date)}, day of week: ${dayOfWeek} (Django: ${djangoDayOfWeek}), day of month: ${dayOfMonth}`);\n    const filteredQuests = quests.filter(quest => {\n      console.log(`TodayPage: Checking quest ${quest.id} (${quest.name}), type: ${quest.quest_type}, period: ${quest.goal_period}, task_days_of_week: ${quest.task_days_of_week}, task_days_of_month: ${quest.task_days_of_month}`);\n      if (!quest.active) {\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is not active, filtering out`);\n        return false;\n      }\n      // Only show quests from the date they were created\n      if (quest.created_at) {\n        const createdDate = new Date(quest.created_at);\n        createdDate.setHours(0, 0, 0, 0);\n        dateObj.setHours(0, 0, 0, 0);\n        // If the selected date is before the quest was created, don't show it\n        if (dateObj < createdDate) {\n          return false;\n        }\n      }\n      // Daily quests are always shown\n      if (quest.goal_period === 'day') {\n        return true;\n      }\n      // Weekly quests are shown on specific days\n      if (quest.goal_period === 'week') {\n        if (!quest.task_days_of_week) {\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has no task_days_of_week specified, showing every day`);\n          return true; // If no days specified, show every day\n        }\n        // Parse task_days_of_week\n        let taskDays = [];\n        if (typeof quest.task_days_of_week === 'string') {\n          taskDays = quest.task_days_of_week.split(',').map(day => day.trim());\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as string: ${quest.task_days_of_week}, parsed to:`, taskDays);\n        } else if (Array.isArray(quest.task_days_of_week)) {\n          taskDays = quest.task_days_of_week;\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as array:`, taskDays);\n        }\n        // Check if current day is in task days\n        // Convert current day to different formats for comparison\n        const dayNameShort = this.getDayNameShort(djangoDayOfWeek);\n        const dayNameFull = this.getDayNameFull(djangoDayOfWeek);\n        console.log(`TodayPage: Checking if day ${dayNameFull} (${dayNameShort}, ${djangoDayOfWeek}) is in task days:`, taskDays);\n        const isIncluded = taskDays.includes(djangoDayOfWeek) || taskDays.includes(djangoDayOfWeek.toString()) || taskDays.includes(dayNameShort) || taskDays.includes(dayNameFull);\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) should be shown on ${dayNameFull}? ${isIncluded}`);\n        return isIncluded;\n      }\n      // Monthly quests are shown on specific days of month\n      if (quest.goal_period === 'month') {\n        if (!quest.task_days_of_month) return true; // If no days specified, show every day\n        // Parse task_days_of_month\n        let taskDays = [];\n        if (typeof quest.task_days_of_month === 'string') {\n          taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));\n        } else if (Array.isArray(quest.task_days_of_month)) {\n          taskDays = quest.task_days_of_month;\n        }\n        // Check if current day is in task days\n        return taskDays.includes(dayOfMonth) || taskDays.includes(dayOfMonth.toString());\n      }\n      return false;\n    });\n    console.log(`TodayPage: Filtered ${quests.length} quests to ${filteredQuests.length} for date ${this.formatDate(date)}`);\n    return filteredQuests;\n  }\n  selectDate(dateObj) {\n    if (this.isLoadingData) {\n      return;\n    }\n    const dateData = dateObj.date;\n    this.isLoadingData = true;\n    this.selectedDateData = dateObj;\n    const date = new Date(dateData);\n    this.selectedDate = date;\n    this.weekDates.forEach(weekDate => {\n      weekDate.is_selected = weekDate.date === dateData;\n    });\n    const formattedDate = this.formatDate(date);\n    this.router.navigate(['/today'], {\n      queryParams: {\n        date: formattedDate,\n        week_offset: this.weekOffset !== 0 ? this.weekOffset : null\n      },\n      replaceUrl: true\n    });\n    this.updateHeaderText();\n    setTimeout(() => {\n      this.loadData();\n      this.isLoadingData = false;\n    }, 10);\n  }\n  changeWeek(direction) {\n    // Prevent multiple rapid week changes\n    if (this.isChangingWeek) {\n      return;\n    }\n    this.isChangingWeek = true;\n    // Update the week offset\n    this.weekOffset += direction;\n    // Generate new week dates with the updated offset\n    this.generateWeekDates();\n    // Preload quest data for all days in the week\n    this.preloadWeekData();\n    // Update the URL with the new week offset while preserving the selected date\n    const dateParam = this.formatDate(this.selectedDate);\n    this.router.navigate(['/today'], {\n      queryParams: {\n        date: dateParam,\n        week_offset: this.weekOffset\n      },\n      replaceUrl: true\n    });\n    // Reset the flag after a short delay\n    setTimeout(() => {\n      this.isChangingWeek = false;\n    }, 300);\n  }\n  // Preload data for all days in the current week\n  preloadWeekData() {\n    if (!this.userId) return;\n    // Get all quests once to avoid multiple API calls\n    this.questService.getQuests(this.userId).pipe(take(1)).subscribe(allQuests => {\n      // Create an array of observables for each date\n      const dateObservables = this.weekDates.filter(weekDate => !weekDate.is_future).map(weekDate => {\n        const date = new Date(weekDate.date);\n        const dateKey = this.formatDate(date);\n        // Skip if we already have cached data\n        if (this.weekProgressCache[dateKey]) {\n          return of({\n            date: weekDate.date,\n            progress: this.weekProgressCache[dateKey]\n          });\n        }\n        // Filter active quests for this date\n        const activeQuests = this.filterQuestsForDate(allQuests, date);\n        // If no active quests, return empty progress\n        if (activeQuests.length === 0) {\n          const emptyProgress = {\n            total: 0,\n            completed: 0\n          };\n          this.weekProgressCache[dateKey] = emptyProgress;\n          return of({\n            date: weekDate.date,\n            progress: emptyProgress\n          });\n        }\n        // Get progress for this date\n        return this.questService.getQuestProgressForDate(this.userId, date).pipe(take(1), map(progressList => {\n          // Count completed quests\n          const questIds = activeQuests.map(q => q.id);\n          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\n          const completedQuests = relevantProgress.filter(p => p.completed).length;\n          const totalQuests = activeQuests.length;\n          // Create progress object\n          const progress = {\n            total: totalQuests,\n            completed: completedQuests\n          };\n          // Cache the progress\n          this.weekProgressCache[dateKey] = progress;\n          return {\n            date: weekDate.date,\n            progress\n          };\n        }));\n      });\n      // Process all date observables in parallel\n      forkJoin(dateObservables).subscribe(results => {\n        // Update the week dates with the progress\n        results.forEach(result => {\n          const index = this.weekDates.findIndex(wd => wd.date === result.date);\n          if (index >= 0) {\n            this.weekDates[index].total_quests = result.progress.total;\n            this.weekDates[index].completed_quests = result.progress.completed;\n            this.weekDates[index].completion_percentage = result.progress.total > 0 ? Math.round(result.progress.completed / result.progress.total * 100) : 0;\n          }\n        });\n      });\n    });\n  }\n  updateHeaderText() {\n    const today = new Date();\n    if (this.isSameDay(this.selectedDate, today)) {\n      this.headerText = 'Today';\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() - 1)))) {\n      this.headerText = 'Yesterday';\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() + 2)))) {\n      this.headerText = 'Tomorrow';\n    } else {\n      // Format as \"Mon, 15 Jan\"\n      this.headerText = this.selectedDate.toLocaleDateString('en-US', {\n        weekday: 'short',\n        day: 'numeric',\n        month: 'short'\n      });\n    }\n  }\n  toggleQuest(quest) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.userId || !quest.id) return;\n      // Check if this specific quest is already being toggled\n      if (_this3.togglingQuestIds[quest.id]) {\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is already being toggled, ignoring duplicate call`);\n        return;\n      }\n      // Set flag for this specific quest\n      _this3.togglingQuestIds[quest.id] = true;\n      console.log(`TodayPage: Starting toggle for quest ${quest.id} (${quest.name})`);\n      try {\n        // For normal quests, we don't want to toggle the value when clicking on the quest\n        // Instead, we want to keep the current value from the slider\n        // This is different from the original behavior where clicking would toggle between 0 and goal_value\n        // We'll just log that the quest was clicked but not change any values\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) clicked, keeping current value: ${quest.value_achieved}`);\n        // No need to update the database since we're not changing any values\n        // Just release the flag and return\n        delete _this3.togglingQuestIds[quest.id];\n        return;\n      } catch (error) {\n        console.error(`TodayPage: Error in toggleQuest for ${quest.id} (${quest.name}):`, error);\n      } finally {\n        // Reset flag for this specific quest\n        delete _this3.togglingQuestIds[quest.id];\n        console.log(`TodayPage: Finished toggle for quest ${quest.id} (${quest.name})`);\n      }\n    })();\n  }\n  updateQuestProgress(quest, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.userId || !quest.id) return;\n      // Check if this specific quest is already being updated\n      if (_this4.updatingQuestIds[quest.id]) {\n        return;\n      }\n      // Set flag for this specific quest\n      _this4.updatingQuestIds[quest.id] = true;\n      try {\n        // Store the original completed state before any changes\n        const wasCompletedBefore = quest.completed;\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) original completed state: ${wasCompletedBefore}`);\n        // Update the slider background if an event is provided\n        if (event) {\n          // Handle both standard Event and Ionic's CustomEvent\n          const slider = event.target || (event.detail ? event.detail.value : null);\n          _this4.updateSliderBackground(slider);\n          // Verify that the slider is for the correct quest\n          const sliderQuestId = slider instanceof HTMLElement ? slider.getAttribute('data-quest-id') : null;\n          if (sliderQuestId && sliderQuestId !== quest.id) {\n            delete _this4.updatingQuestIds[quest.id];\n            return;\n          }\n          // Get the value from the slider\n          let sliderValue = 0;\n          if (event.detail && event.detail.value !== undefined) {\n            // Ionic range event\n            sliderValue = event.detail.value;\n          } else if (slider instanceof HTMLInputElement) {\n            // Standard input event\n            sliderValue = parseInt(slider.value);\n          } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n            // Ionic range element\n            const valueAttr = slider.getAttribute('value') || '0';\n            sliderValue = parseInt(valueAttr);\n          }\n          // Update the quest's value_achieved with the slider value\n          quest.value_achieved = sliderValue;\n          // Update completed status based on quest type and value\n          // This exactly matches the Django implementation in toggle_quest view\n          if (quest.quest_type === 'build') {\n            // For build quests, completed when value >= goal\n            quest.completed = sliderValue >= quest.goal_value;\n          } else {\n            // 'quit' type\n            // For quit quests, completed when value < goal (opposite of build)\n            quest.completed = sliderValue < quest.goal_value;\n          }\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) new completed state: ${quest.completed}`);\n        }\n        // Make a deep copy of the quest to avoid reference issues\n        const questCopy = {\n          ...quest\n        };\n        // Call the service and get the updated values\n        const result = yield _this4.questService.toggleQuestCompletion(_this4.userId, quest.id, _this4.selectedDate, quest.value_achieved, questCopy);\n        // Update the quest in the UI with the returned values\n        quest.completed = result.completed;\n        quest.value_achieved = result.value_achieved;\n        // Get today's date for comparison\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = new Date(_this4.selectedDate);\n        selectedDate.setHours(0, 0, 0, 0);\n        const isTodaySelected = selectedDate.getTime() === today.getTime();\n        // Handle streak calculation differently based on whether we're in today's view or a previous day\n        if (isTodaySelected) {\n          // For today's view, manually calculate the streak by going backward from today\n          // until we find a non-completed progress entry\n          // Use the streak from the result (from Supabase)\n          let streak = result.streak;\n          // Get the current completed state after the update\n          const isCompletedNow = quest.completed;\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status: was ${wasCompletedBefore}, now ${isCompletedNow}`);\n          // Only update streak if the completion status has changed\n          if (wasCompletedBefore !== isCompletedNow) {\n            if (isCompletedNow) {\n              // Changed from incomplete to complete\n              streak++;\n              console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from incomplete to complete, streak increased to ${streak}`);\n            } else {\n              // Changed from complete to incomplete\n              streak = Math.max(0, streak - 1);\n              console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from complete to incomplete, streak decreased to ${streak}`);\n            }\n            // Update the streak in the database\n            _this4.questService.updateQuestStreak(quest.id, streak).subscribe({\n              next: () => {\n                console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${streak}`);\n                // Update the quest in the cache\n                const dateKey = _this4.formatDate(_this4.selectedDate);\n                if (_this4.questCache[dateKey]) {\n                  const cachedQuestIndex = _this4.questCache[dateKey].findIndex(q => q.id === quest.id);\n                  if (cachedQuestIndex >= 0) {\n                    _this4.questCache[dateKey][cachedQuestIndex].streak = streak;\n                  }\n                }\n              },\n              error: error => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\n            });\n          } else {\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status did not change, keeping streak at ${streak}`);\n          }\n        } else {\n          // For previous days, recalculate streak for today\n          console.log(`TodayPage: Quest toggled in previous day (${_this4.formatDate(_this4.selectedDate)}), recalculating streak for today`);\n          // Get the quest details\n          _this4.questService.getQuest(quest.id).subscribe(questDetails => {\n            if (!questDetails) {\n              console.error(`TodayPage: Could not get quest details for ${quest.id}`);\n              return;\n            }\n            // Calculate the streak for today using our streak calculator\n            _this4.streakCalculator.calculateStreak(_this4.userId, quest.id).then(calculatedStreak => {\n              console.log(`TodayPage: Recalculated streak for quest ${quest.id} for today: ${calculatedStreak}`);\n              // Update the streak in the database\n              _this4.questService.updateQuestStreak(quest.id, calculatedStreak).subscribe({\n                next: () => {\n                  console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${calculatedStreak}`);\n                  // Clear today's cache for next time\n                  const todayString = _this4.formatDate(today);\n                  console.log('TodayPage: Clearing today\\'s cache to force reload of updated streak next time today is viewed');\n                  delete _this4.questCache[todayString];\n                  // If we have today's date in the week view, update its progress\n                  const todayIndex = _this4.weekDates.findIndex(wd => wd.date === todayString);\n                  if (todayIndex >= 0) {\n                    delete _this4.weekProgressCache[todayString];\n                    _this4.updateProgressRingForDate(todayString);\n                  }\n                },\n                error: error => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\n              });\n            }).catch(error => {\n              console.error(`TodayPage: Error calculating streak for quest ${quest.id}:`, error);\n            });\n          });\n        }\n        // Update the UI element for this quest\n        _this4.updateQuestUI(quest);\n        // Cache the updated quest data and update progress ring\n        const dateKey = _this4.formatDate(_this4.selectedDate);\n        if (_this4.questCache[dateKey]) {\n          // Find and update the quest in the cache\n          const cachedQuestIndex = _this4.questCache[dateKey].findIndex(q => q.id === quest.id);\n          if (cachedQuestIndex >= 0) {\n            _this4.questCache[dateKey][cachedQuestIndex] = {\n              ...quest\n            };\n          }\n        }\n        // Clear the cache for this date to force a refresh\n        delete _this4.weekProgressCache[dateKey];\n        // Update the progress ring for this date\n        _this4.updateProgressRingForDate(dateKey);\n        // Check if all quests are completed\n        _this4.checkAllQuestsCompleted(_this4.quests);\n      } catch (error) {\n        console.error(`TodayPage: Error updating quest progress:`, error);\n      } finally {\n        // Reset flag for this specific quest\n        delete _this4.updatingQuestIds[quest.id];\n      }\n    })();\n  }\n  // Helper method to update the progress ring for a specific date\n  updateProgressRingForDate(dateKey) {\n    // Find the index of this date in weekDates\n    const index = this.weekDates.findIndex(wd => wd.date === dateKey);\n    if (index < 0) return;\n    // If we have cached quests for this date, use them to calculate progress\n    if (this.questCache[dateKey]) {\n      const cachedQuests = this.questCache[dateKey];\n      const totalQuests = cachedQuests.length;\n      const completedQuests = cachedQuests.filter(q => q.completed).length;\n      // Cache the progress\n      this.weekProgressCache[dateKey] = {\n        total: totalQuests,\n        completed: completedQuests\n      };\n      // Update the week date\n      this.weekDates[index].total_quests = totalQuests;\n      this.weekDates[index].completed_quests = completedQuests;\n      this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n      return;\n    }\n    // If no cached quests, fetch from server\n    if (this.userId) {\n      const date = new Date(dateKey);\n      this.questService.getQuestProgressForDate(this.userId, date).pipe(take(1)).subscribe(progressList => {\n        this.questService.getQuests(this.userId).pipe(take(1)).subscribe(quests => {\n          // Filter active quests for this date\n          const activeQuests = this.filterQuestsForDate(quests, date);\n          // Count completed quests\n          const questIds = activeQuests.map(q => q.id);\n          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\n          const completedQuests = relevantProgress.filter(p => p.completed).length;\n          const totalQuests = activeQuests.length;\n          // Cache the progress\n          this.weekProgressCache[dateKey] = {\n            total: totalQuests,\n            completed: completedQuests\n          };\n          // Update the week date\n          this.weekDates[index].total_quests = totalQuests;\n          this.weekDates[index].completed_quests = completedQuests;\n          this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n        });\n      });\n    }\n  }\n  // Helper method to update the UI for a specific quest\n  updateQuestUI(quest) {\n    // Find the quest element in the DOM\n    const questElement = document.querySelector(`[data-quest-id=\"${quest.id}\"]`);\n    if (!questElement) {\n      console.error(`TodayPage: Could not find quest element for ID: ${quest.id}`);\n      return;\n    }\n    // Update the completed class\n    if (quest.completed) {\n      questElement.classList.add('completed');\n    } else {\n      questElement.classList.remove('completed');\n    }\n    // Update the streak display - only show streak for today\n    const streakElements = questElement.querySelectorAll('.quest-streak');\n    if (streakElements && streakElements.length > 0) {\n      // Get today's date for comparison\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(this.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      // Only show streak for today's view\n      if (isTodaySelected) {\n        const streakValue = quest.streak || 0;\n        console.log(`TodayPage: Quest ${quest.id}, completed: ${quest.completed}, streak: ${streakValue}`);\n        // Update all streak elements (there might be multiple due to ngIf)\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            // Make sure the streak is visible\n            element.style.display = 'block';\n            element.textContent = `🔥${streakValue}d`;\n          }\n        });\n      } else {\n        // Hide streak for previous days\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            element.style.display = 'none';\n            element.textContent = '';\n          }\n        });\n      }\n    }\n    // Update the progress text\n    const progressText = questElement.querySelector('.progress-text');\n    if (progressText) {\n      var _progressText$parentE;\n      const isTimeUnit = (_progressText$parentE = progressText.parentElement) === null || _progressText$parentE === void 0 ? void 0 : _progressText$parentE.classList.contains('progress-time');\n      const unitSuffix = isTimeUnit ? 'm' : '';\n      const goalUnitSuffix = quest.goal_unit !== 'count' && !isTimeUnit ? ` ${quest.goal_unit}` : '';\n      progressText.textContent = `${quest.value_achieved}${unitSuffix}/${quest.goal_value}${unitSuffix}${goalUnitSuffix}`;\n    }\n    console.log(`TodayPage: Updated UI for quest ${quest.id}`);\n  }\n  // Update slider background based on value\n  updateSliderBackground(slider) {\n    if (!slider) {\n      return;\n    }\n    // Handle different types of slider elements\n    let sliderElement;\n    let sliderValue = 0;\n    let minValue = 0;\n    let maxValue = 100;\n    let sliderQuestId = '';\n    if (slider instanceof HTMLInputElement) {\n      // Standard HTML input range\n      sliderElement = slider;\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\n      sliderValue = parseInt(slider.value);\n      minValue = parseInt(slider.min);\n      maxValue = parseInt(slider.max);\n    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n      // Ionic range element\n      sliderElement = slider;\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\n      // Get the value from the element's properties or attributes\n      const valueAttr = slider.getAttribute('value') || '0';\n      const minAttr = slider.getAttribute('min') || '0';\n      const maxAttr = slider.getAttribute('max') || '100';\n      sliderValue = parseInt(valueAttr);\n      minValue = parseInt(minAttr);\n      maxValue = parseInt(maxAttr);\n    } else {\n      return;\n    }\n    if (!sliderQuestId) {\n      return;\n    }\n    // Calculate the percentage value\n    const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n    // For Ionic range, we need to set the CSS variable\n    if (sliderElement.tagName === 'ION-RANGE') {\n      sliderElement.style.setProperty('--progress-value', `${percentage}%`);\n    } else {\n      // Set the background directly with hardcoded colors for standard HTML input\n      sliderElement.style.background = `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\n    }\n    // Add a data attribute to track the current value\n    sliderElement.setAttribute('data-current-value', sliderValue.toString());\n  }\n  /**\n   * Toggle side quest completion\n   * This matches the Django implementation in toggle_daily_side_quest view\n   * Side quests are always toggled between 0 and goal value\n   */\n  toggleSideQuest(sideQuest) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.userId || !sideQuest.id) return;\n      // Check if this specific side quest is already being toggled\n      if (_this5.togglingSideQuestIds[sideQuest.id]) {\n        console.log(`TodayPage: Side quest ${sideQuest.id} is already being toggled, ignoring duplicate call`);\n        return;\n      }\n      // Set flag for this specific side quest\n      _this5.togglingSideQuestIds[sideQuest.id] = true;\n      console.log(`TodayPage: Starting toggle for side quest ${sideQuest.id}`);\n      try {\n        // For side quests, we always toggle between 0 and goal value\n        // This matches the Django implementation where side quests are either completed or not\n        console.log(`TodayPage: Click event on side quest ${sideQuest.id}`);\n        // Toggle the value immediately for better UI feedback\n        const newValue = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\n        const newCompletedState = newValue === sideQuest.current_quest.goal_value;\n        // Update local state first for immediate feedback\n        sideQuest.value_achieved = newValue;\n        sideQuest.completed = newCompletedState;\n        console.log(`TodayPage: Updated side quest ${sideQuest.id} value to ${sideQuest.value_achieved}, completed: ${sideQuest.completed}`);\n        // Get today's date for comparison\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = new Date(_this5.selectedDate);\n        selectedDate.setHours(0, 0, 0, 0);\n        const isToday = selectedDate.getTime() === today.getTime();\n        // Only allow toggling side quests for today\n        if (!isToday) {\n          console.log(`TodayPage: Cannot toggle side quest for past date: ${_this5.formatDate(_this5.selectedDate)}`);\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n          return;\n        }\n        // Update the UI element immediately for better feedback\n        _this5.updateSideQuestUI(sideQuest);\n        try {\n          const result = yield _this5.sideQuestService.toggleSideQuestCompletion(sideQuest.id, _this5.userId, _this5.selectedDate // Pass the selected date\n          );\n          console.log(`TodayPage: Successfully toggled side quest ${sideQuest.id}`);\n          console.log(`TodayPage: Updated values:`, result);\n          // Update the side quest in the UI with the returned values from the server\n          sideQuest.completed = result.completed;\n          sideQuest.value_achieved = result.value_achieved;\n          sideQuest.streak = result.streak;\n          // Update the UI element with the updated streak\n          _this5.updateSideQuestUI(sideQuest);\n          // Update the week date progress for the selected date\n          // Clear the cache for this date to force a refresh\n          const dateKey = _this5.formatDate(_this5.selectedDate);\n          delete _this5.weekProgressCache[dateKey];\n          // Update the progress ring for this date\n          _this5.updateProgressRingForDate(dateKey);\n          // Check if all quests are completed\n          _this5.checkAllQuestsCompleted(_this5.quests);\n          // Reset flag for this specific side quest\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n          console.log(`TodayPage: Finished toggle for side quest ${sideQuest.id}`);\n        } catch (error) {\n          console.error(`TodayPage: Error toggling side quest ${sideQuest.id}:`, error);\n          // Revert the local state if the server update failed\n          sideQuest.value_achieved = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\n          sideQuest.completed = sideQuest.value_achieved === sideQuest.current_quest.goal_value;\n          _this5.updateSideQuestUI(sideQuest);\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n        }\n      } catch (error) {\n        console.error(`TodayPage: Error in toggleSideQuest for ${sideQuest.id}:`, error);\n        delete _this5.togglingSideQuestIds[sideQuest.id];\n      }\n    })();\n  }\n  // Helper method to update the UI for a specific side quest\n  updateSideQuestUI(sideQuest) {\n    // Find the side quest element in the DOM\n    const questElement = document.querySelector(`.daily-side-quest [data-quest-id=\"${sideQuest.id}\"]`);\n    if (!questElement) {\n      console.error(`TodayPage: Could not find side quest element for ID: ${sideQuest.id}`);\n      return;\n    }\n    // Update the completed class\n    if (sideQuest.completed) {\n      questElement.classList.add('completed');\n    } else {\n      questElement.classList.remove('completed');\n    }\n    // Update the streak display - only show streak for today\n    const streakElements = questElement.querySelectorAll('.quest-streak');\n    if (streakElements && streakElements.length > 0) {\n      // Get today's date for comparison\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(this.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      // Only show streak for today's view\n      if (isTodaySelected) {\n        const streakValue = sideQuest.streak || 0;\n        console.log(`TodayPage: Side quest ${sideQuest.id}, completed: ${sideQuest.completed}, streak: ${streakValue}`);\n        // Update all streak elements (there might be multiple due to ngIf)\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            // Make sure the streak is visible\n            element.style.display = 'block';\n            element.textContent = `🔥${streakValue}d`;\n          }\n        });\n      } else {\n        // Hide streak for previous days\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            element.style.display = 'none';\n            element.textContent = '';\n          }\n        });\n      }\n    }\n    // Update the progress text\n    const progressText = questElement.querySelector('.progress-text');\n    if (progressText) {\n      const goalUnit = sideQuest.current_quest.goal_unit !== 'count' ? ` ${sideQuest.current_quest.goal_unit}` : '';\n      progressText.textContent = `${sideQuest.value_achieved}/${sideQuest.current_quest.goal_value}${goalUnit}`;\n    }\n    // Force a repaint to ensure the UI updates\n    setTimeout(() => {\n      if (questElement.parentElement) {\n        const display = questElement.parentElement.style.display;\n        questElement.parentElement.style.display = 'none';\n        // Force a reflow\n        void questElement.parentElement.offsetHeight;\n        questElement.parentElement.style.display = display;\n      }\n    }, 0);\n    console.log(`TodayPage: Updated UI for side quest ${sideQuest.id}`);\n  }\n  openAddQuestModal(event) {\n    event.preventDefault();\n    this.showAddQuestModal = true;\n    this.newQuest = this.getEmptyQuest();\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    // Reset hasHighPriorityQuest flag\n    this.hasHighPriorityQuest = false;\n    // Reset animation states\n    this.resetAnimationStates();\n    // Start quest type animation after modal opens\n    setTimeout(() => {\n      console.log('Setting questTypeAnimated to true');\n      this.questTypeAnimated = true;\n    }, 300);\n  }\n  resetAnimationStates() {\n    this.questTypeAnimated = false;\n    this.questTypeAnimating = false;\n    this.selectedQuestType = '';\n    this.categoryAnimated = false;\n    this.categoryAnimating = false;\n    this.categorySelected = false;\n    this.selectedCategory = '';\n    this.priorityAnimated = false;\n    this.priorityAnimating = false;\n    this.goalAnimated = false;\n  }\n  closeAddQuestModal() {\n    this.showAddQuestModal = false;\n    // Reset form state\n    this.newQuest = this.getEmptyQuest();\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    this.hasHighPriorityQuest = false;\n    this.resetAnimationStates();\n  }\n  // Animation methods\n  selectQuestType(type) {\n    this.selectedQuestType = type;\n    this.questTypeAnimating = true;\n    // Start slide out animation\n    setTimeout(() => {\n      this.newQuest.quest_type = type;\n      // After quest type is set, trigger category animation\n      setTimeout(() => {\n        this.categoryAnimated = true;\n      }, 100);\n    }, 300); // Half of the animation duration\n  }\n  selectCategory(category) {\n    this.selectedCategory = category;\n    this.categoryAnimating = true;\n    // Start slide out animation based on category\n    setTimeout(() => {\n      this.newQuest.category = category;\n      this.categorySelected = true;\n      this.checkCategoryPriority({\n        detail: {\n          value: category\n        }\n      });\n      // After category is set, trigger priority animation\n      setTimeout(() => {\n        this.priorityAnimated = true;\n      }, 100);\n    }, 300); // Half of the animation duration\n  }\n  selectPriority(priority) {\n    if (priority === 'high' && this.hasHighPriorityQuest) {\n      return; // Don't allow high priority if already exists\n    }\n    this.priorityAnimating = true;\n    setTimeout(() => {\n      this.newQuest.priority = priority;\n    }, 300);\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n      // If moving to step 5 (goal section), trigger goal animation\n      if (this.currentStep === 5) {\n        setTimeout(() => {\n          this.goalAnimated = true;\n        }, 200);\n      }\n    }\n  }\n  prevStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  moveCaretToEnd() {\n    setTimeout(() => {\n      this.emojiInput.getInputElement().then(input => {\n        const pos = input.value.length;\n        input.setSelectionRange(pos, pos);\n        input.scrollLeft = input.scrollWidth;\n      });\n    }, 100);\n  }\n  get progress() {\n    return this.currentStep / this.totalSteps;\n  }\n  createQuest() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.userId || !_this6.newQuest.name || !_this6.newQuest.emoji || !_this6.newQuest.quest_type || !_this6.newQuest.category || !_this6.newQuest.goal_value || !_this6.newQuest.goal_unit || !_this6.newQuest.goal_period) {\n        console.error('TodayPage: Cannot create quest - missing required fields');\n        return;\n      }\n      try {\n        if (_this6.newQuest.goal_period === 'week' && _this6.selectedDaysOfWeek.length > 0) {\n          _this6.newQuest.task_days_of_week = _this6.selectedDaysOfWeek.join(',');\n        } else if (_this6.newQuest.goal_period === 'month' && _this6.selectedDaysOfMonth.length > 0) {\n          _this6.newQuest.task_days_of_month = _this6.selectedDaysOfMonth.join(',');\n        }\n        const {\n          data: userProfile,\n          error: userError\n        } = yield _this6.supabaseService.getClient().from('profiles').select('id').eq('id', _this6.userId).single();\n        if (userError || !userProfile) {\n          console.error('TodayPage: User profile not found:', userError || 'No profile found');\n          throw new Error('User profile not found. Please ensure you are logged in.');\n        }\n        const questToCreate = {\n          name: _this6.newQuest.name || '',\n          description: _this6.newQuest.description || '',\n          quest_type: _this6.newQuest.quest_type || 'build',\n          goal_value: _this6.newQuest.goal_value || 1,\n          goal_unit: _this6.newQuest.goal_unit || 'count',\n          goal_period: _this6.newQuest.goal_period || 'day',\n          priority: _this6.newQuest.priority || 'basic',\n          category: _this6.newQuest.category || 'strength',\n          emoji: _this6.newQuest.emoji || '🎯',\n          task_days_of_week: _this6.newQuest.task_days_of_week || '',\n          task_days_of_month: _this6.newQuest.task_days_of_month || '',\n          user_id: _this6.userId,\n          active: true\n        };\n        try {\n          const questId = yield _this6.questService.createQuest(questToCreate);\n          if (_this6.newQuest.quest_type === 'quit') {\n            yield _this6.questService.toggleQuestCompletion(_this6.userId, questId, new Date(), 0, {\n              ...questToCreate,\n              id: questId\n            });\n          }\n          const dateKey = _this6.formatDate(_this6.selectedDate);\n          delete _this6.questCache[dateKey];\n          delete _this6.weekProgressCache[dateKey];\n          _this6.closeAddQuestModal();\n          _this6.loadData();\n        } catch (questError) {\n          console.error('TodayPage: Error creating quest:', questError);\n          if (questError.message && questError.message.includes('foreign key constraint')) {\n            alert('Database configuration issue detected. Please run the fix_quest_constraints.sql script in the Supabase SQL Editor to fix the foreign key constraints.');\n          } else if (questError.message && questError.message.includes('fix_quest_constraints.sql')) {\n            alert(questError.message);\n          } else {\n            alert(`Error creating quest: ${questError.message}`);\n          }\n        }\n      } catch (error) {\n        console.error('TodayPage: Error in createQuest:', error);\n        alert(`Error: ${error.message || 'Unknown error occurred'}`);\n      }\n    })();\n  }\n  updateDaysOfWeek(day) {\n    const index = this.selectedDaysOfWeek.indexOf(day);\n    if (index !== -1) {\n      this.selectedDaysOfWeek.splice(index, 1);\n    } else {\n      this.selectedDaysOfWeek.push(day);\n    }\n  }\n  updateDaysOfMonth(event, day) {\n    // Handle both standard Event and Ionic's CustomEvent\n    let isChecked = false;\n    if (event.detail !== undefined) {\n      // Ionic checkbox event\n      isChecked = event.detail.checked;\n    } else if (event.target instanceof HTMLInputElement) {\n      // Standard checkbox event\n      isChecked = event.target.checked;\n    }\n    if (isChecked) {\n      this.selectedDaysOfMonth.push(day);\n    } else {\n      const index = this.selectedDaysOfMonth.indexOf(day);\n      if (index !== -1) {\n        this.selectedDaysOfMonth.splice(index, 1);\n      }\n    }\n    console.log(`TodayPage: Updated days of month: ${this.selectedDaysOfMonth.join(', ')}`);\n  }\n  updatePeriodDisplay() {\n    // Reset selections when period changes\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    console.log(`TodayPage: Period changed to ${this.newQuest.goal_period}, reset selections`);\n  }\n  checkCategoryPriority(event) {\n    if (!this.userId || !this.newQuest.category) return;\n    // If this is an Ionic event, make sure we have the latest category value\n    if (event && event.detail) {\n      this.newQuest.category = event.detail.value;\n      console.log(`TodayPage: Category changed to ${this.newQuest.category} via Ionic event`);\n    }\n    // Check if user already has a high priority quest in this category\n    this.questService.getQuests(this.userId).pipe(take(1), map(quests => {\n      return quests.some(q => q.category === this.newQuest.category && q.priority === 'high' && q.active);\n    })).subscribe({\n      next: hasHighPriority => {\n        this.hasHighPriorityQuest = hasHighPriority;\n        // If user already has a high priority quest, set this one to basic\n        if (hasHighPriority) {\n          this.newQuest.priority = 'basic';\n        }\n        console.log(`TodayPage: Category ${this.newQuest.category} has high priority quest: ${hasHighPriority}`);\n      }\n    });\n  }\n  /**\n   * Check if all quests are completed for today and show celebration if enabled\n   */\n  checkAllQuestsCompleted(quests) {\n    // Only check for today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const selectedDate = new Date(this.selectedDate);\n    selectedDate.setHours(0, 0, 0, 0);\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\n    const todayStr = this.formatDate(today);\n    if (!isTodaySelected || !this.currentUser) {\n      return;\n    }\n    // Check if celebration has already been shown for today\n    const celebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\n    if (celebrationShown) {\n      console.log('TodayPage: Celebration already shown for today:', todayStr);\n      return;\n    }\n    // Check if all quests are completed\n    const allQuestsCompleted = quests.length > 0 && quests.every(quest => quest.completed);\n    // Check if side quest is completed (if enabled)\n    const sideQuestCompleted = !this.showSidequests || !this.dailyQuest || this.dailyQuest.completed;\n    // Show celebration if all quests and side quests are completed and celebration is enabled\n    if (allQuestsCompleted && sideQuestCompleted && this.currentUser.show_celebration) {\n      // Make sure we have the latest user data\n      this.userService.getUserById(this.userId).subscribe(userData => {\n        if (userData) {\n          this.currentUser = userData;\n        }\n        // Show the celebration\n        this.showCelebration = true;\n        // Save today's date to localStorage\n        localStorage.setItem(`celebration_shown_${todayStr}`, 'true');\n        // Update our tracking array\n        if (!this.celebrationShownDates.includes(todayStr)) {\n          this.celebrationShownDates.push(todayStr);\n        }\n      });\n    }\n  }\n  /**\n   * Close the celebration modal\n   */\n  closeCelebration() {\n    this.showCelebration = false;\n  }\n  // Helper methods\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  isSameDay(date1, date2) {\n    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n  }\n  getToday() {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  // Convert Django day index (0=Monday, 6=Sunday) to short day name\n  getDayNameShort(djangoDayIndex) {\n    // Map Django day index to day name\n    const dayMap = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];\n    return dayMap[djangoDayIndex];\n  }\n  // Convert Django day index (0=Monday, 6=Sunday) to full day name\n  getDayNameFull(djangoDayIndex) {\n    // Map Django day index to full day name\n    const dayMap = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    return dayMap[djangoDayIndex];\n  }\n  getEmptyQuest() {\n    return {\n      name: '',\n      description: '',\n      quest_type: '',\n      // Empty by default, user must select\n      goal_value: 1,\n      goal_unit: 'count',\n      goal_period: 'day',\n      priority: 'basic',\n      // Default to basic priority\n      category: '',\n      emoji: '🎯'\n    };\n  }\n}\n_TodayPage = TodayPage;\n_TodayPage.ɵfac = function TodayPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TodayPage)();\n};\n_TodayPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _TodayPage,\n  selectors: [[\"app-today\"]],\n  viewQuery: function TodayPage_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.emojiInput = _t.first);\n    }\n  },\n  decls: 35,\n  vars: 10,\n  consts: [[\"questForm\", \"ngForm\"], [1, \"ion-padding\", 3, \"fullscreen\"], [1, \"background-container\"], [1, \"gradient-bg\"], [1, \"celestial-body\"], [1, \"ion-no-border\"], [3, \"headerText\"], [1, \"week-row\", \"ion-padding-top\"], [\"class\", \"day-container\", 4, \"ngFor\", \"ngForOf\"], [1, \"ion-justify-content-center\"], [1, \"heartbeat-circle\", \"gradient-text\"], [1, \"add-quest\"], [\"fill\", \"clear\", \"id\", \"add-quest-btn\", 1, \"add-quest-btn\", 3, \"click\"], [1, \"quests\"], [\"class\", \"ion-text-center no-quest-card\", 4, \"ngif\"], [\"class\", \"quest-item ion-margin-bottom\", 3, \"completed\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"ion-padding-top\"], [\"class\", \"quest-item\", 3, \"completed\", \"click\", 4, \"ngIf\"], [\"class\", \"quest-item\", 4, \"ngIf\"], [1, \"add-quest-modal\", 3, \"ionModalDidDismiss\", \"isOpen\", \"backdropDismiss\"], [3, \"user\", \"date\", \"close\", 4, \"ngIf\"], [1, \"day-container\"], [1, \"day-name\"], [1, \"date\", 3, \"click\"], [\"viewBox\", \"0 0 36 36\", 1, \"date-progress\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"13\", \"stroke-dasharray\", \"81.68, 81.68\", 1, \"background-circle\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"13\", \"class\", \"progress-circle\", 3, \"low\", 4, \"ngIf\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"13\", 1, \"progress-circle\"], [1, \"ion-text-center\", \"no-quest-card\"], [\"size\", \"8\"], [\"size\", \"4\"], [\"name\", \"warning\"], [1, \"quest-item\", \"ion-margin-bottom\", 3, \"click\"], [\"size\", \"2\"], [1, \"quest-icon\"], [\"size\", \"8\", 1, \"quest-info\"], [1, \"progress-container\"], [\"class\", \"progress-time\", 4, \"ngIf\"], [\"class\", \"progress\", 4, \"ngIf\"], [\"class\", \"quest-streak\", 4, \"ngIf\"], [1, \"progress-time\"], [\"min\", \"0\", \"snaps\", \"true\", \"ticks\", \"false\", \"snaps-per-step\", \"true\", 1, \"progress-slider\", 3, \"ngModelChange\", \"ionChange\", \"ionInput\", \"max\", \"ngModel\", \"step\"], [1, \"progress-text\"], [1, \"progress\"], [4, \"ngIf\"], [1, \"quest-streak\"], [1, \"quest-item\", 3, \"click\"], [1, \"quest-description\"], [1, \"quest-item\"], [1, \"ion-padding\"], [1, \"modal-header\", \"ion-padding-top\", \"ion-text-center\"], [\"color\", \"success\", 3, \"value\"], [\"name\", \"close\", 3, \"click\"], [3, \"ngSubmit\"], [\"class\", \"choose-quest\", 4, \"ngIf\"], [\"class\", \"first-step\", 4, \"ngIf\"], [\"class\", \"step-three-container\", 4, \"ngIf\"], [\"class\", \"step-four-container\", 4, \"ngIf\"], [\"class\", \"step-five-container\", 4, \"ngIf\"], [1, \"ion-padding\", \"create-quest-row\"], [1, \"choose-quest\"], [\"size\", \"6\"], [1, \"ion-padding\", \"ion-no-margin\", \"ion-text-center\"], [\"name\", \"earth-outline\", 1, \"ion-margin-bottom\"], [\"name\", \"create-outline\", 1, \"ion-margin-bottom\"], [1, \"first-step\"], [1, \"ion-margin-top\"], [1, \"gradient-text\"], [1, \"dark-text\"], [1, \"preview-emoji\"], [\"type\", \"text\", \"id\", \"emoji\", \"name\", \"emoji\", \"value\", \"\\uD83C\\uDFAF\", \"appEmojiInput\", \"\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"emoji-row\", \"ion-margin-top\", \"ion-margin-bottom\"], [\"size\", \"2\", \"class\", \"ion-justify-content-center ion-align-items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"social-button\"], [\"size\", \"2\", 1, \"ion-justify-content-center\", \"ion-align-items-center\"], [1, \"step-three-container\"], [1, \"step-header\", \"ion-margin-top\"], [1, \"ion-text-center\"], [1, \"floating-emoji\", \"ion-margin-bottom\"], [1, \"input-section\"], [1, \"floating-input-container\"], [1, \"input-icon\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \" \", \"required\", \"\", 1, \"dark-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"name\", 1, \"floating-label\"], [1, \"input-border\"], [1, \"floating-textarea-container\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \" \", \"rows\", \"3\", \"maxlength\", \"150\", 1, \"dark-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"description\", 1, \"floating-label\"], [1, \"character-counter\"], [1, \"step-four-container\"], [1, \"step-header\"], [1, \"step-icon-container\"], [1, \"floating-emoji\"], [1, \"step-title\"], [1, \"step-subtitle\"], [\"class\", \"quest-type-section\", 3, \"slide-in-from-right\", \"slide-out-left\", 4, \"ngIf\"], [\"class\", \"category-section\", 3, \"slide-in-from-right\", \"slide-out-left\", 4, \"ngIf\"], [\"class\", \"priority-section\", 3, \"slide-in-from-right\", \"slide-out-left\", 4, \"ngIf\"], [1, \"quest-type-section\"], [1, \"section-label\"], [\"name\", \"trending-up-outline\"], [1, \"toggle-container\"], [1, \"toggle-option\", 3, \"click\"], [1, \"toggle-icon\"], [1, \"toggle-text\"], [1, \"category-section\"], [\"name\", \"grid-outline\"], [1, \"category-grid\"], [1, \"category-card\", 3, \"click\"], [1, \"category-icon\"], [1, \"priority-section\"], [\"name\", \"flag-outline\"], [1, \"priority-container\"], [1, \"priority-option\", 3, \"click\"], [1, \"priority-indicator\", \"basic\"], [1, \"priority-indicator\", \"high\"], [\"class\", \"priority-warning\", 4, \"ngIf\"], [1, \"priority-warning\"], [\"name\", \"warning-outline\"], [1, \"step-five-container\"], [1, \"floating-emoji-container\"], [\"class\", \"goal-section\", 3, \"slide-in\", 4, \"ngIf\"], [\"class\", \"frequency-section\", 4, \"ngIf\"], [1, \"goal-section\"], [1, \"goal-card\"], [1, \"goal-header\"], [1, \"goal-icon\"], [1, \"goal-inputs\"], [1, \"goal-input-group\"], [\"type\", \"number\", \"id\", \"goal_value\", \"name\", \"goal_value\", \"placeholder\", \"1\", \"min\", \"1\", \"required\", \"\", 1, \"dark-input\", \"goal-value-input\", 3, \"ngModelChange\", \"ngModel\"], [1, \"input-label\"], [\"id\", \"goal_unit\", \"name\", \"goal_unit\", \"interface\", \"popover\", \"required\", \"\", 1, \"dark-input\", \"goal-unit-select\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"count\"], [\"value\", \"steps\"], [\"value\", \"m\"], [\"value\", \"km\"], [\"value\", \"sec\"], [\"value\", \"min\"], [\"value\", \"hr\"], [\"value\", \"Cal\"], [\"value\", \"g\"], [\"value\", \"mg\"], [\"value\", \"l\"], [\"value\", \"drink\"], [\"value\", \"pages\"], [\"value\", \"books\"], [\"value\", \"%\"], [\"value\", \"\\u20AC\"], [\"value\", \"$\"], [\"value\", \"\\u00A3\"], [1, \"goal-preview\"], [1, \"preview-label\"], [1, \"preview-value\"], [1, \"frequency-section\"], [1, \"frequency-card\"], [1, \"frequency-header\"], [1, \"frequency-icon\"], [1, \"frequency-select-container\"], [\"id\", \"goal_period\", \"name\", \"goal_period\", \"interface\", \"popover\", \"required\", \"\", 1, \"dark-input\", \"frequency-select\", 3, \"ngModelChange\", \"ionChange\", \"ngModel\"], [\"value\", \"day\"], [\"value\", \"week\"], [\"value\", \"month\"], [\"class\", \"week-days-container\", 4, \"ngIf\"], [\"class\", \"month-days-container\", 4, \"ngIf\"], [1, \"week-days-container\"], [1, \"week-days-grid\"], [\"type\", \"button\", \"class\", \"day-chip\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"day-chip\", 3, \"click\"], [1, \"month-days-container\"], [1, \"month-days-grid\"], [\"class\", \"month-day-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"month-day-item\"], [\"name\", \"days_of_month\", 3, \"ionChange\", \"id\", \"value\"], [3, \"for\"], [1, \"social-button\", 3, \"click\"], [1, \"blue-button\", 3, \"click\"], [\"type\", \"submit\", 1, \"blue-button\", \"create-quest\"], [3, \"close\", \"user\", \"date\"]],\n  template: function TodayPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-content\", 1)(1, \"div\", 2);\n      i0.ɵɵelement(2, \"div\", 3)(3, \"div\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-header\", 5)(5, \"ion-toolbar\");\n      i0.ɵɵelement(6, \"app-header\", 6);\n      i0.ɵɵelementStart(7, \"ion-row\", 7);\n      i0.ɵɵtemplate(8, TodayPage_div_8_Template, 7, 13, \"div\", 8);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(9, \"ion-grid\")(10, \"ion-row\", 9);\n      i0.ɵɵelement(11, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"ion-row\", 11)(13, \"ion-col\")(14, \"h2\");\n      i0.ɵɵtext(15, \"Quests\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"ion-col\")(17, \"ion-button\", 12);\n      i0.ɵɵlistener(\"click\", function TodayPage_Template_ion_button_click_17_listener($event) {\n        return ctx.openAddQuestModal($event);\n      });\n      i0.ɵɵtext(18, \" + Add Quest \");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(19, \"ion-row\", 13)(20, \"ion-col\");\n      i0.ɵɵtemplate(21, TodayPage_ion_card_21_Template, 11, 0, \"ion-card\", 14)(22, TodayPage_ion_card_22_Template, 15, 10, \"ion-card\", 15);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(23, \"ion-row\", 16)(24, \"ion-col\")(25, \"h2\");\n      i0.ɵɵtext(26, \"Daily Side Quest\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(27, \"ion-row\", 13)(28, \"ion-col\");\n      i0.ɵɵtemplate(29, TodayPage_ion_card_29_Template, 12, 8, \"ion-card\", 17)(30, TodayPage_ion_card_30_Template, 7, 0, \"ion-card\", 18);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(31, \"ion-modal\", 19);\n      i0.ɵɵlistener(\"ionModalDidDismiss\", function TodayPage_Template_ion_modal_ionModalDidDismiss_31_listener() {\n        return ctx.closeAddQuestModal();\n      });\n      i0.ɵɵtemplate(32, TodayPage_ng_template_32_Template, 20, 9, \"ng-template\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(33, TodayPage_app_celebration_33_Template, 1, 2, \"app-celebration\", 20);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(34, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"headerText\", ctx.headerText);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.weekDates);\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngif\", ctx.quests.length === 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngForOf\", ctx.quests);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", ctx.dailyQuest && ctx.dailyQuest.current_quest);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.dailyQuest);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"isOpen\", true)(\"backdropDismiss\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showCelebration);\n    }\n  },\n  dependencies: [IonicModule, i1.IonButton, i1.IonCard, i1.IonCardContent, i1.IonCardHeader, i1.IonCardTitle, i1.IonCheckbox, i1.IonCol, i1.IonContent, i1.IonGrid, i1.IonHeader, i1.IonIcon, i1.IonInput, i1.IonProgressBar, i1.IonRange, i1.IonRow, i1.IonSelect, i1.IonSelectOption, i1.IonText, i1.IonTextarea, i1.IonToolbar, i1.IonModal, i1.BooleanValueAccessor, i1.NumericValueAccessor, i1.SelectValueAccessor, i1.TextValueAccessor, i1.IonMinValidator, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, NavigationComponent, CelebrationComponent, EmojiInputDirective, HeaderComponent, AuroraComponent],\n  styles: [\"ion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  margin: 0 auto;\\n  position: relative;\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  pointer-events: none;\\n  z-index: 0;\\n  transform: rotate(-90deg);\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\n  fill: transparent;\\n  stroke-width: 5;\\n  stroke-linecap: round;\\n  transform-origin: center;\\n  transition: stroke-dasharray 0.5s ease;\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .background-circle[_ngcontent-%COMP%] {\\n  stroke: rgba(255, 255, 255, 0.1);\\n  stroke-width: 5;\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: #4169E1;\\n  stroke-opacity: 1;\\n  stroke-width: 5;\\n}\\nion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle.low[_ngcontent-%COMP%] {\\n  stroke: #FF9500 !important;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 40px;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .day-name[_ngcontent-%COMP%] {\\n  padding: 5px;\\n  font-size: 12px;\\n  color: white;\\n  margin-bottom: 8px;\\n  font-weight: lighter;\\n  width: 22px;\\n  height: 22px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .day-name.active[_ngcontent-%COMP%] {\\n  background-color: var(--accent);\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .day-name.selected[_ngcontent-%COMP%] {\\n  background-color: var(--text-muted);\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: #4169E1;\\n  stroke-opacity: 0.9;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .date.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\nion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .date.unselected[_ngcontent-%COMP%] {\\n  color: var(--text-muted);\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .heartbeat-circle[_ngcontent-%COMP%] {\\n  margin: 32px;\\n  width: 100px;\\n  height: 100px;\\n  background: linear-gradient(220deg, #4169e1 0%, #6b85e8 20%, #95a5ef 40%, #bfc5f6 60%, #e7e9fd 80%, #ffffff 100%);\\n  background-size: 300% 100%;\\n  border-radius: 50%;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_heartbeat 1.2s infinite, _ngcontent-%COMP%_gradient 2s ease-in-out infinite alternate;\\n}\\n@keyframes _ngcontent-%COMP%_heartbeat {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  25% {\\n    transform: scale(1.03);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  75% {\\n    transform: scale(1.03);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_gradient {\\n  0% {\\n    background-position: 100% 0%;\\n  }\\n  100% {\\n    background-position: 0% 0%;\\n  }\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .big-date[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  max-width: 450px;\\n  min-height: 200px;\\n  max-height: 450px;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .big-date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\n  stroke-width: 3;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .add-quest[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .add-quest[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .add-quest[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%] {\\n  color: var(--text);\\n  margin: 16px 0;\\n  border: 1px solid var(--error);\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 20px;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: left;\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 5rem;\\n  color: var(--error);\\n}\\nion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  width: 80%;\\n}\\n\\n.quest-item[_ngcontent-%COMP%] {\\n  padding: 5px 0 5px 0;\\n  margin: 16px 0 16px 0;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%] {\\n  align-items: flex-start;\\n}\\n.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: var(--accent);\\n}\\n.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin-top: 5px;\\n  color: var(--text-secondary);\\n}\\n.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .quest-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n}\\n.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .quest-streak[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--text-secondary);\\n}\\n.quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n}\\n.quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-time[_ngcontent-%COMP%], \\n.quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  margin: 5px auto;\\n  color: var(--text-secondary);\\n  text-align: right;\\n}\\n\\n.add-quest-btn[_ngcontent-%COMP%] {\\n  --background: rgba(65, 105, 225, 0.1);\\n  --color: #4169E1;\\n  --border-radius: 6px;\\n  --padding-start: 12px;\\n  --padding-end: 12px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  height: 32px;\\n  margin: 0;\\n}\\n\\n.add-quest-btn[_ngcontent-%COMP%]:hover {\\n  --background: rgba(65, 105, 225, 0.2);\\n}\\n\\n.quest-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.quest-item[_ngcontent-%COMP%] {\\n  background-color: #1C1C1E;\\n  border: 1px solid #2C2C2E;\\n  border-radius: 8px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.quest-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%] {\\n  border-color: #4169E1;\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%]   .quest-title[_ngcontent-%COMP%] {\\n  color: #4169E1;\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(65, 105, 225, 0.05);\\n  pointer-events: none;\\n}\\n\\n.quest-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  min-width: 24px;\\n  text-align: center;\\n}\\n\\n.quest-info[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.quest-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n  color: #4169E1;\\n}\\n\\n.quest-description[_ngcontent-%COMP%] {\\n  color: #8E8E93;\\n  font-size: 12px;\\n  margin-bottom: 4px;\\n}\\n\\n.progress[_ngcontent-%COMP%], \\n.progress-time[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 12px;\\n}\\n\\n\\n\\n.side-quests[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding-top: 32px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n\\n.quests[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], \\n.daily-side-quest[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n       appearance: none;\\n  width: 100%;\\n  height: 4px;\\n  background: #2C2C2E;\\n  \\n\\n  outline: none;\\n  position: relative;\\n  \\n\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  cursor: pointer;\\n  position: relative;\\n  margin-top: -4px;\\n  \\n\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  cursor: pointer;\\n  border: none;\\n  position: relative;\\n  margin-top: 0;\\n  \\n\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-runnable-track {\\n  height: 4px;\\n  border-radius: 2px;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-track {\\n  height: 4px;\\n  border-radius: 2px;\\n}\\n\\n\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  \\n\\n  background: var(--inactive-date);\\n}\\n\\n\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%] {\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n       appearance: none;\\n  height: 4px;\\n  border-radius: 2px;\\n  outline: none;\\n  position: relative;\\n  z-index: 1;\\n  \\n\\n}\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%]::-webkit-slider-runnable-track {\\n  height: 4px;\\n  border-radius: 2px;\\n  background: transparent;\\n}\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%]::-moz-range-track {\\n  height: 4px;\\n  border-radius: 2px;\\n  background: transparent;\\n}\\n\\n\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  \\n\\n  cursor: pointer;\\n  margin-top: -4px;\\n  \\n\\n  position: relative;\\n  z-index: 2;\\n}\\n\\ninput[type=range].progress-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  \\n\\n  cursor: pointer;\\n  border: none;\\n  position: relative;\\n  margin-top: 0;\\n  \\n\\n  z-index: 2;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--secondary-text);\\n  margin-top: 2px;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  overflow-y: auto;\\n  scrollbar-width: none;\\n  height: 100%;\\n  padding-bottom: 74px;\\n}\\n\\n.container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n  \\n\\n}\\n\\n.header-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.week-calendar[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.days[_ngcontent-%COMP%], \\n.dates[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  text-align: center;\\n  gap: 8px;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 14px;\\n}\\n\\n.date-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 16px;\\n}\\n\\n.side-quests[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 90%;\\n  height: 1px;\\n  background: linear-gradient(to right, transparent, #4B0082, transparent);\\n}\\n\\n.calendar[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  padding: 10px;\\n  background: var(--bg-secondary);\\n  border-radius: 8px;\\n}\\n\\n.calendar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 10px;\\n}\\n\\n.calendar-days[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 5px;\\n  text-align: center;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: #8E8E93;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.day-number[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  text-decoration: none;\\n  color: var(--text-primary);\\n  margin: 0 auto;\\n}\\n\\n.day-number[_ngcontent-%COMP%]:hover {\\n  background: var(--bg-hover);\\n}\\n\\n.day-number.selected[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.day-number.today[_ngcontent-%COMP%] {\\n  border: 2px solid var(--primary-color);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --color: #FFFFFF;\\n  --border-radius: 50%;\\n  --padding-start: 0;\\n  --padding-end: 0;\\n  width: 32px;\\n  height: 32px;\\n  margin: 0;\\n  font-size: 18px;\\n  cursor: pointer;\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover {\\n  --background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.time-display[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-color);\\n  margin-right: 16px;\\n}\\n\\n\\n\\ninput[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, \\ninput[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button {\\n  opacity: 0.3;\\n}\\n\\n\\n\\nion-range.progress-slider[_ngcontent-%COMP%] {\\n  --bar-height: 6px;\\n  --bar-border-radius: 3px;\\n  --knob-size: 16px;\\n  --bar-background: #2C2C2E;\\n  --bar-background-active: #4169E1;\\n  --knob-background: #4169E1;\\n  --pin-background: #4169E1;\\n  --pin-color: #FFFFFF;\\n  --step: 1;\\n  --tick-height: 0;\\n  --tick-width: 0;\\n  --tick-background: transparent;\\n  --tick-background-active: transparent;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n       appearance: none;\\n  width: 100%;\\n  height: 6px;\\n  border-radius: 3px;\\n  outline: none;\\n  background: linear-gradient(to right, #4169E1 0%, #4169E1 var(--progress-value), #2C2C2E var(--progress-value), #2C2C2E 100%);\\n}\\n\\n\\n\\nion-range.progress-slider[_ngcontent-%COMP%]::part(tick) {\\n  display: none !important;\\n}\\n\\nion-range.progress-slider[_ngcontent-%COMP%]::part(tick-active) {\\n  display: none !important;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  cursor: pointer;\\n  margin-top: -5px;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 16px;\\n  height: 16px;\\n  border-radius: 50%;\\n  background: #4169E1;\\n  cursor: pointer;\\n  margin-top: 0;\\n  border: none;\\n}\\n\\n.daily-side-quest[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  padding-bottom: 20px;\\n}\\n\\n.add-quest-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%] {\\n  --background: var(--bg);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   .create-quest-row[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-progress-bar[_ngcontent-%COMP%] {\\n  --background: var(--progress-bg);\\n  height: 8px;\\n  border-radius: 4px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 15px;\\n  font-size: 30px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .choose-quest[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 90%;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .choose-quest[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%] {\\n  height: 150px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .choose-quest[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-size: 32px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .choose-quest[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .first-step[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .first-step[_ngcontent-%COMP%]   .preview-emoji[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .first-step[_ngcontent-%COMP%]   .preview-emoji[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%] {\\n  height: 100%;\\n  font-size: 8em;\\n  max-width: 60%;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .first-step[_ngcontent-%COMP%]   .emoji-row[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .floating-emoji[_ngcontent-%COMP%] {\\n  font-size: 4em;\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_float-emoji 3s ease-in-out infinite;\\n  filter: drop-shadow(0 0 20px rgba(65, 105, 225, 0.3));\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .input-section[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 18px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  font-size: 20px;\\n  z-index: 3;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\\n  opacity: 0.8;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%] {\\n  --padding-start: 55px;\\n  --padding-end: 20px;\\n  --padding-top: 24px;\\n  --padding-bottom: 24px;\\n  border-radius: 16px;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:focus-within, \\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--accent);\\n  box-shadow: 0 0 0 1px var(--accent), 0 0 30px rgba(65, 105, 225, 0.4), 0 12px 40px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .floating-label[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 20%;\\n  top: -10px;\\n  color: var(--text-secondary);\\n  font-size: 16px;\\n  font-weight: 600;\\n  padding: 4px 12px;\\n  border-radius: 8px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n  z-index: 4;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .character-counter[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .character-counter[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 20px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  color: var(--text-muted);\\n  background: rgba(22, 23, 28, 0.8);\\n  padding: 4px 8px;\\n  border-radius: 6px;\\n  border: 1px solid rgba(255, 255, 255, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .character-counter[_ngcontent-%COMP%]   span.warning[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .character-counter[_ngcontent-%COMP%]   span.warning[_ngcontent-%COMP%] {\\n  color: var(--warning);\\n  text-shadow: 0 0 8px rgba(250, 204, 21, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:focus    + .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:not(:placeholder-shown)    + .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:focus    + .floating-label[_ngcontent-%COMP%], \\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:not(:placeholder-shown)    + .floating-label[_ngcontent-%COMP%] {\\n  top: -12px;\\n  left: 50px;\\n  font-size: 12px;\\n  color: var(--accent);\\n  transform: translateY(0) scale(0.9);\\n  background: linear-gradient(135deg, rgba(65, 105, 225, 0.2), rgba(82, 119, 232, 0.15));\\n  border-color: rgba(65, 105, 225, 0.3);\\n  box-shadow: 0 4px 12px rgba(65, 105, 225, 0.2), 0 0 0 1px rgba(65, 105, 225, 0.1);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%] {\\n  margin-top: 10%;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-icon-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 20px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-icon-container[_ngcontent-%COMP%]   .floating-emoji[_ngcontent-%COMP%] {\\n  font-size: 4em;\\n  display: inline-block;\\n  animation: _ngcontent-%COMP%_float-emoji 3s ease-in-out infinite;\\n  filter: drop-shadow(0 0 20px rgba(65, 105, 225, 0.3));\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 700;\\n  margin-bottom: 10px;\\n  color: var(--text);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: var(--text-secondary);\\n  margin: 0;\\n  opacity: 0.8;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .section-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--text);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .section-label[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n  color: var(--accent);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n  transform: translateX(0);\\n  opacity: 1;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section.slide-in-from-right[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n  opacity: 1;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section.slide-out-left[_ngcontent-%COMP%] {\\n  transform: translateX(-100%);\\n  opacity: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));\\n  border: 2px solid var(--border);\\n  border-radius: 16px;\\n  padding: 20px;\\n  cursor: pointer;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(65, 105, 225, 0.1) 0%, rgba(82, 119, 232, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.4s ease;\\n  pointer-events: none;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]   .toggle-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5em;\\n  margin-bottom: 10px;\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--text);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n  opacity: 0.8;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option.active[_ngcontent-%COMP%] {\\n  border-color: var(--accent);\\n  box-shadow: 0 0 0 1px var(--accent), 0 0 20px rgba(65, 105, 225, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option.active[_ngcontent-%COMP%]::before {\\n  opacity: 1;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option.active[_ngcontent-%COMP%]   .toggle-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n  transform: translateX(100%);\\n  opacity: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section.slide-in-from-right[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n  opacity: 1;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section.slide-out-left[_ngcontent-%COMP%] {\\n  transform: translateX(-100%);\\n  opacity: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 12px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));\\n  border: 2px solid var(--border);\\n  border-radius: 16px;\\n  padding: 20px;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(65, 105, 225, 0.1) 0%, rgba(82, 119, 232, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.4s ease;\\n  pointer-events: none;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5em;\\n  margin-bottom: 8px;\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\\n  transition: transform 0.3s ease;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--text);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]:hover   .category-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card.selected[_ngcontent-%COMP%] {\\n  border-color: var(--accent);\\n  box-shadow: 0 0 0 1px var(--accent), 0 0 20px rgba(65, 105, 225, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card.selected[_ngcontent-%COMP%]::before {\\n  opacity: 1;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card.selected[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n  transform: translateX(100%);\\n  opacity: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section.slide-in-from-right[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n  opacity: 1;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section.slide-out-left[_ngcontent-%COMP%] {\\n  transform: translateX(-100%);\\n  opacity: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));\\n  border: 2px solid var(--border);\\n  border-radius: 16px;\\n  padding: 15px 20px;\\n  cursor: pointer;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]   .priority-indicator[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  transition: all 0.3s ease;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]   .priority-indicator.basic[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4ade80, #22c55e);\\n  box-shadow: 0 0 10px rgba(74, 222, 128, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]   .priority-indicator.high[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f59e0b, #d97706);\\n  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--text);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option.active[_ngcontent-%COMP%] {\\n  border-color: var(--accent);\\n  box-shadow: 0 0 0 1px var(--accent), 0 0 15px rgba(65, 105, 225, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option.active[_ngcontent-%COMP%]   .priority-indicator[_ngcontent-%COMP%] {\\n  transform: scale(1.2);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-warning[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  padding: 10px 15px;\\n  background: rgba(245, 158, 11, 0.1);\\n  border: 1px solid rgba(245, 158, 11, 0.3);\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-warning[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: var(--warning);\\n  font-size: 16px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-warning[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--warning);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%] {\\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n  transform: translateY(30px);\\n  opacity: 0;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container.slide-in[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n  opacity: 1;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 15px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-input-wrapper[_ngcontent-%COMP%]   .goal-number[_ngcontent-%COMP%] {\\n  flex: 0 0 100px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-input-wrapper[_ngcontent-%COMP%]   .goal-number[_ngcontent-%COMP%]   .goal-value-input[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));\\n  --color: var(--text);\\n  --border-color: var(--border);\\n  --border-radius: 12px;\\n  --padding-start: 15px;\\n  --padding-end: 15px;\\n  --padding-top: 15px;\\n  --padding-bottom: 15px;\\n  border: 2px solid var(--border);\\n  border-radius: 12px;\\n  text-align: center;\\n  font-size: 18px;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-input-wrapper[_ngcontent-%COMP%]   .goal-number[_ngcontent-%COMP%]   .goal-value-input[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--accent);\\n  box-shadow: 0 0 15px rgba(65, 105, 225, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-input-wrapper[_ngcontent-%COMP%]   .goal-unit[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-input-wrapper[_ngcontent-%COMP%]   .goal-unit[_ngcontent-%COMP%]   .goal-unit-select[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));\\n  --color: var(--text);\\n  --border-color: var(--border);\\n  --border-radius: 12px;\\n  --padding-start: 15px;\\n  --padding-end: 15px;\\n  --padding-top: 15px;\\n  --padding-bottom: 15px;\\n  border: 2px solid var(--border);\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-input-wrapper[_ngcontent-%COMP%]   .goal-unit[_ngcontent-%COMP%]   .goal-unit-select[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--accent);\\n  box-shadow: 0 0 15px rgba(65, 105, 225, 0.3);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-preview[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(65, 105, 225, 0.1), rgba(82, 119, 232, 0.05));\\n  border: 1px solid rgba(65, 105, 225, 0.2);\\n  border-radius: 12px;\\n  padding: 15px;\\n  text-align: center;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-preview[_ngcontent-%COMP%]   .preview-text[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: var(--text-secondary);\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-container[_ngcontent-%COMP%]   .goal-preview[_ngcontent-%COMP%]   .preview-text[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-weight: 700;\\n  font-size: 18px;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .select-days[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-evenly;\\n}\\n.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .select-days[_ngcontent-%COMP%]   .day-checked[_ngcontent-%COMP%] {\\n  width: 100%;\\n  flex: 1;\\n  --background: var(--surface);\\n  --color: var(--text);\\n  --border-color: var(--border);\\n  --border-style: solid;\\n  --border-width: 1px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_aurora-curve {\\n  0% {\\n    transform: translateX(-10px) translateY(5px) scale(1);\\n    opacity: 0.8;\\n  }\\n  50% {\\n    transform: translateX(15px) translateY(-8px) scale(1.05);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: translateX(-5px) translateY(3px) scale(0.98);\\n    opacity: 0.9;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_aurora-flow {\\n  0% {\\n    transform: translateX(-15px) translateY(-5px) skewX(1deg);\\n    opacity: 0.6;\\n  }\\n  50% {\\n    transform: translateX(20px) translateY(10px) skewX(-1.5deg);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    transform: translateX(-8px) translateY(-3px) skewX(0.5deg);\\n    opacity: 0.7;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_aurora-glow {\\n  0% {\\n    transform: scale(1) rotate(0deg);\\n    opacity: 0.4;\\n  }\\n  50% {\\n    transform: scale(1.1) rotate(1deg);\\n    opacity: 0.6;\\n  }\\n  100% {\\n    transform: scale(0.95) rotate(-0.5deg);\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_float-emoji {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n  }\\n  33% {\\n    transform: translateY(-10px) rotate(2deg);\\n  }\\n  66% {\\n    transform: translateY(-5px) rotate(-2deg);\\n  }\\n}\\n.step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  text-align: center;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .floating-emoji-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .floating-emoji-container[_ngcontent-%COMP%]   .floating-emoji[_ngcontent-%COMP%] {\\n  font-size: 4em;\\n  animation: float 3s ease-in-out infinite;\\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));\\n}\\n.step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 700;\\n  margin-bottom: 10px;\\n  color: var(--text);\\n}\\n.step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]   .gradient-text[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--accent), #5277e8);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: var(--text-secondary);\\n  opacity: 0.8;\\n  margin: 0;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));\\n  border: 2px solid var(--border);\\n  border-radius: 20px;\\n  padding: 25px;\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(65, 105, 225, 0.05) 0%, rgba(82, 119, 232, 0.02) 100%);\\n  pointer-events: none;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 25px;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-header[_ngcontent-%COMP%]   .goal-icon[_ngcontent-%COMP%] {\\n  font-size: 2em;\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: var(--text);\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-inputs[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1.5fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-inputs[_ngcontent-%COMP%]   .goal-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-inputs[_ngcontent-%COMP%]   .goal-input-group[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  left: 15px;\\n  background: var(--background);\\n  padding: 0 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: var(--accent);\\n  z-index: 1;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-inputs[_ngcontent-%COMP%]   .goal-input-group[_ngcontent-%COMP%]   .goal-value-input[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-preview[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(65, 105, 225, 0.15), rgba(82, 119, 232, 0.08));\\n  border: 1px solid rgba(65, 105, 225, 0.3);\\n  border-radius: 12px;\\n  padding: 15px;\\n  text-align: center;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-preview[_ngcontent-%COMP%]   .preview-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-secondary);\\n  margin-right: 10px;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-preview[_ngcontent-%COMP%]   .preview-value[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: var(--accent);\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));\\n  border: 2px solid var(--border);\\n  border-radius: 20px;\\n  padding: 25px;\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(65, 105, 225, 0.05) 0%, rgba(82, 119, 232, 0.02) 100%);\\n  pointer-events: none;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 25px;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-header[_ngcontent-%COMP%]   .frequency-icon[_ngcontent-%COMP%] {\\n  font-size: 2em;\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: var(--text);\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-select-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .week-days-container[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .month-days-container[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--text);\\n  text-align: center;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 10px;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-chip[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, rgba(40, 41, 47, 0.95), rgba(32, 33, 38, 0.9));\\n  --color: var(--text);\\n  --border-color: var(--border);\\n  --border-radius: 12px;\\n  --padding-top: 12px;\\n  --padding-bottom: 12px;\\n  --padding-start: 8px;\\n  --padding-end: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-chip[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-chip.selected[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, rgba(65, 105, 225, 0.2), rgba(82, 119, 232, 0.1));\\n  --border-color: var(--accent);\\n  --box-shadow: 0 0 15px rgba(65, 105, 225, 0.3);\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 8px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px;\\n  background: linear-gradient(135deg, rgba(40, 41, 47, 0.95), rgba(32, 33, 38, 0.9));\\n  border: 2px solid var(--border);\\n  border-radius: 10px;\\n  transition: all 0.3s ease;\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\\n  border-color: rgba(65, 105, 225, 0.5);\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-item[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%] {\\n  --size: 16px;\\n  --checkmark-color: var(--accent);\\n  --border-color: var(--border);\\n  --border-color-checked: var(--accent);\\n  --background-checked: var(--accent);\\n}\\n.step-five-container[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: var(--text);\\n  margin: 0;\\n  cursor: pointer;\\n}\\n\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(30, 31, 37, 0.95), rgba(22, 23, 28, 0.9));\\n  border: 2px solid var(--border);\\n  border-radius: 24px;\\n  padding: 30px;\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(65, 105, 225, 0.08) 0%, rgba(82, 119, 232, 0.04) 100%);\\n  pointer-events: none;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 25px;\\n  position: relative;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-floating-emoji[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -15px;\\n  right: -15px;\\n  font-size: 2.5em;\\n  animation: float 3s ease-in-out infinite;\\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));\\n  z-index: 2;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 22px;\\n  font-weight: 700;\\n  color: var(--text);\\n  background: linear-gradient(135deg, var(--text), rgba(255, 255, 255, 0.8));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .quest-info-preview[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .quest-info-preview[_ngcontent-%COMP%]   .quest-name[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: var(--text);\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .quest-info-preview[_ngcontent-%COMP%]   .quest-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: var(--text-secondary);\\n  opacity: 0.9;\\n  line-height: 1.5;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .quest-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 12px;\\n  margin-bottom: 25px;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .quest-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 15px;\\n  background: linear-gradient(135deg, rgba(40, 41, 47, 0.6), rgba(32, 33, 38, 0.4));\\n  border: 1px solid rgba(65, 105, 225, 0.2);\\n  border-radius: 12px;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .quest-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n  opacity: 0.8;\\n  font-weight: 600;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .quest-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--text);\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .goal-display[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .goal-display[_ngcontent-%COMP%]   .goal-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(65, 105, 225, 0.2), rgba(82, 119, 232, 0.1));\\n  border: 2px solid rgba(65, 105, 225, 0.4);\\n  border-radius: 16px;\\n  padding: 20px;\\n  text-align: center;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .goal-display[_ngcontent-%COMP%]   .goal-badge[_ngcontent-%COMP%]   .goal-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 28px;\\n  font-weight: 700;\\n  color: var(--accent);\\n  margin-bottom: 5px;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .goal-display[_ngcontent-%COMP%]   .goal-badge[_ngcontent-%COMP%]   .goal-unit[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--text);\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .progress-preview[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(107, 114, 128, 0.3);\\n  border-radius: 6px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .progress-preview[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, var(--accent), #5277e8);\\n  border-radius: 6px;\\n  transition: width 0.3s ease;\\n}\\n.preview-section[_ngcontent-%COMP%]   .preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .progress-preview[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n  opacity: 0.8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "QuestService", "SideQuestService", "UserService", "SupabaseService", "Quest", "Subscription", "fork<PERSON><PERSON>n", "map", "of", "switchMap", "take", "firstValueFrom", "NavigationComponent", "CelebrationComponent", "ActivatedRoute", "Router", "PreferencesService", "EmojiInputDirective", "StreakCalculatorService", "HeaderComponent", "AuroraComponent", "i0", "ɵɵelement", "ɵɵclassProp", "date_r2", "completion_percentage", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TodayPage_div_8_Template_div_click_3_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "is_future", "selectDate", "ɵɵtemplate", "TodayPage_div_8__svg_circle_6_Template", "ɵɵadvance", "is_today", "is_selected", "ɵɵtextInterpolate1", "ɵɵpureFunction0", "_c1", "i_r4", "ɵɵproperty", "ɵɵtwoWayListener", "TodayPage_ion_card_22_div_11_Template_ion_range_ngModelChange_1_listener", "$event", "_r7", "quest_r6", "ɵɵtwoWayBindingSet", "value_achieved", "TodayPage_ion_card_22_div_11_Template_ion_range_ionChange_1_listener", "updateQuestProgress", "TodayPage_ion_card_22_div_11_Template_ion_range_ionInput_1_listener", "target", "updateSliderBackground", "ɵɵstyleMapInterpolate1", "goal_value", "ɵɵtwoWayProperty", "ɵɵtextInterpolate4", "goal_unit", "ɵɵelementContainerStart", "TodayPage_ion_card_22_div_12_Template_ion_range_ngModelChange_1_listener", "_r8", "TodayPage_ion_card_22_div_12_Template_ion_range_ionChange_1_listener", "TodayPage_ion_card_22_div_12_Template_ion_range_ionInput_1_listener", "TodayPage_ion_card_22_div_12_ng_container_4_Template", "ɵɵtextInterpolate2", "streak", "TodayPage_ion_card_22_Template_ion_card_click_0_listener", "_r5", "toggleQuest", "TodayPage_ion_card_22_div_11_Template", "TodayPage_ion_card_22_div_12_Template", "TodayPage_ion_card_22_ion_text_14_Template", "completed", "ɵɵtextInterpolate", "emoji", "name", "description", "isSameDay", "selectedDate", "get<PERSON><PERSON>y", "dailyQuest", "TodayPage_ion_card_29_Template_ion_card_click_0_listener", "_r9", "toggleSideQuest", "TodayPage_ion_card_29_div_11_Template", "current_quest", "emoji_r12", "TodayPage_ng_template_32_div_12_Template_ion_input_ngModelChange_13_listener", "_r11", "newQuest", "TodayPage_ng_template_32_div_12_ion_col_15_Template", "emojis", "TodayPage_ng_template_32_div_13_Template_ion_input_ngModelChange_16_listener", "_r13", "TodayPage_ng_template_32_div_13_Template_ion_textarea_ngModelChange_25_listener", "length", "TodayPage_ng_template_32_div_14_ion_row_12_Template_div_click_7_listener", "_r14", "selectQuestType", "TodayPage_ng_template_32_div_14_ion_row_12_Template_div_click_15_listener", "questTypeAnimated", "questTypeAnimating", "selectedQuestType", "quest_type", "TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_7_listener", "_r15", "selectCategory", "TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_12_listener", "TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_17_listener", "TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_22_listener", "categoryAnimated", "categoryAnimating", "selectedCate<PERSON><PERSON>", "category", "TodayPage_ng_template_32_div_14_ion_row_14_Template_div_click_7_listener", "_r16", "selectPriority", "TodayPage_ng_template_32_div_14_ion_row_14_Template_div_click_11_listener", "TodayPage_ng_template_32_div_14_ion_row_14_div_15_Template", "priorityAnimated", "priorityAnimating", "priority", "hasHighPriorityQuest", "TodayPage_ng_template_32_div_14_ion_row_12_Template", "TodayPage_ng_template_32_div_14_ion_row_13_Template", "TodayPage_ng_template_32_div_14_ion_row_14_Template", "categorySelected", "TodayPage_ng_template_32_div_15_ion_row_12_Template_ion_input_ngModelChange_10_listener", "_r17", "TodayPage_ng_template_32_div_15_ion_row_12_Template_ion_select_ngModelChange_14_listener", "goalAnimated", "TodayPage_ng_template_32_div_15_ion_row_13_div_16_ion_button_4_Template_ion_button_click_0_listener", "day_r20", "_r19", "updateDaysOfWeek", "value", "selectedDaysOfWeek", "includes", "label", "TodayPage_ng_template_32_div_15_ion_row_13_div_16_ion_button_4_Template", "weekDays", "TodayPage_ng_template_32_div_15_ion_row_13_div_17_div_4_Template_ion_checkbox_ionChange_1_listener", "day_r22", "_r21", "updateDaysOfMonth", "TodayPage_ng_template_32_div_15_ion_row_13_div_17_div_4_Template", "monthDays", "TodayPage_ng_template_32_div_15_ion_row_13_Template_ion_select_ngModelChange_9_listener", "_r18", "goal_period", "TodayPage_ng_template_32_div_15_ion_row_13_Template_ion_select_ionChange_9_listener", "updatePeriodDisplay", "TodayPage_ng_template_32_div_15_ion_row_13_div_16_Template", "TodayPage_ng_template_32_div_15_ion_row_13_div_17_Template", "TodayPage_ng_template_32_div_15_ion_row_12_Template", "TodayPage_ng_template_32_div_15_ion_row_13_Template", "TodayPage_ng_template_32_ion_col_17_Template_ion_button_click_1_listener", "_r23", "prevStep", "TodayPage_ng_template_32_ion_col_18_Template_ion_button_click_1_listener", "_r24", "nextStep", "TodayPage_ng_template_32_Template_ion_icon_click_8_listener", "_r10", "closeAddQuestModal", "TodayPage_ng_template_32_Template_form_ngSubmit_9_listener", "createQuest", "TodayPage_ng_template_32_div_11_Template", "TodayPage_ng_template_32_div_12_Template", "TodayPage_ng_template_32_div_13_Template", "TodayPage_ng_template_32_div_14_Template", "TodayPage_ng_template_32_div_15_Template", "TodayPage_ng_template_32_ion_col_17_Template", "TodayPage_ng_template_32_ion_col_18_Template", "TodayPage_ng_template_32_ion_col_19_Template", "progress", "currentStep", "TodayPage_app_celebration_33_Template_app_celebration_close_0_listener", "_r25", "closeCelebration", "currentUser", "formatDate", "TodayPage", "loadDailySideQuest", "today", "Date", "setHours", "isTodaySelected", "getTime", "showSidequests", "userId", "sideQuestService", "ensureUserHasDailySideQuests", "pipe", "subscribe", "next", "sideQuests", "sideQuest", "supabaseService", "getClient", "from", "select", "eq", "current_quest_id", "single", "then", "response", "error", "questDetails", "data", "id", "constructor", "user$", "weekDates", "dayNames", "headerText", "weekOffset", "quests", "totalSteps", "questCache", "isLoadingData", "showAddQuestModal", "getEmptyQuest", "showCelebration", "celebrationShownDates", "Array", "_", "i", "selectedDaysOfMonth", "questService", "userService", "route", "router", "preferencesService", "streakCalculator", "isRedirecting", "weekProgressCache", "isChangingWeek", "togglingQuestIds", "updatingQuestIds", "togglingSideQuestIds", "queryParams", "params", "dateParam", "weekOffsetParam", "console", "log", "undefined", "parseInt", "test", "generateWeekDates", "updateHeaderText", "loadData", "userSubscription", "currentUser$", "authUser", "getUserById", "userData", "userDataSubscription", "user", "sidequests_switch", "add", "ngOnInit", "setTimeout", "preloadWeekData", "todayStr", "allKeys", "localStorage", "key", "push", "for<PERSON>ach", "startsWith", "removeItem", "todayCelebrationShown", "getItem", "ionViewWillEnter", "_this", "_asyncToGenerator", "_currentUser", "ensureUserExists", "navigateByUrl", "endDate", "end_of_current_plan", "currentDate", "isValidPlan", "<PERSON><PERSON><PERSON>", "requestAnimationFrame", "initializeSliderBackgrounds", "url", "navigate", "date", "week_offset", "replaceUrl", "sliders", "document", "querySelectorAll", "slider", "HTMLInputElement", "sliderQuestId", "getAttribute", "slider<PERSON><PERSON><PERSON>", "minValue", "min", "maxValue", "max", "percentage", "style", "background", "setAttribute", "HTMLElement", "tagName", "valueAttr", "minAttr", "maxAttr", "setProperty", "toString", "ionViewWillLeave", "ngOnDestroy", "unsubscribe", "_this2", "todayDateString", "lastStreakCalculation", "get", "getQuests", "_ref", "quest", "checkMissedDays", "createQuitQuestProgressForToday", "_x", "apply", "arguments", "recalculateSideQuestStreak", "filteredQuests", "filterQuestsForDate", "sortedFilteredQuests", "sort", "a", "b", "created_at", "localeCompare", "getQuestProgressForDate", "allProgress", "progressLookup", "quest_id", "calculateStreaks", "streaks", "calculatedStreak", "updateQuestStreak", "result", "set", "catch", "Promise", "resolve", "questsWithProgress", "sortedQuests", "checkAllQuestsCompleted", "updateWeekDateProgress", "currentDay", "getDay", "daysFromMonday", "startOfWeek", "setDate", "getDate", "dateString", "isToday", "isSelected", "isFuture", "totalQuests", "completedQuests", "completionPercentage", "cached", "total", "Math", "round", "day", "total_quests", "completed_quests", "weekDate", "index", "cachedQuests", "filter", "q", "date<PERSON><PERSON>j", "dayOfWeek", "djangoDayOfWeek", "dayOfMonth", "task_days_of_week", "task_days_of_month", "active", "createdDate", "taskDays", "split", "trim", "isArray", "dayNameShort", "getDayNameShort", "dayNameFull", "getDayNameFull", "isIncluded", "dateData", "selectedDateData", "formattedDate", "changeWeek", "direction", "allQuests", "dateObservables", "activeQuests", "emptyProgress", "progressList", "questIds", "relevantProgress", "p", "results", "findIndex", "wd", "toLocaleDateString", "weekday", "month", "_this3", "event", "_this4", "wasCompletedBefore", "detail", "questCopy", "toggleQuestCompletion", "isCompletedNow", "cachedQuestIndex", "getQuest", "calculateStreak", "todayString", "todayIndex", "updateProgressRingForDate", "updateQuestUI", "questElement", "querySelector", "classList", "remove", "streakElements", "streakValue", "element", "parentElement", "contains", "display", "textContent", "progressText", "_progressText$parentE", "isTimeUnit", "unitSuffix", "goalUnitSuffix", "sliderElement", "_this5", "newValue", "newCompletedState", "updateSideQuestUI", "toggleSideQuestCompletion", "goalUnit", "offsetHeight", "openAddQuestModal", "preventDefault", "resetAnimationStates", "type", "checkCategoryPriority", "moveCaretToEnd", "emojiInput", "getInputElement", "input", "pos", "setSelectionRange", "scrollLeft", "scrollWidth", "_this6", "join", "userProfile", "userError", "Error", "questToCreate", "user_id", "questId", "questError", "message", "alert", "indexOf", "splice", "isChecked", "checked", "some", "hasHighPriority", "celebrationShown", "allQuestsCompleted", "every", "sideQuestCompleted", "show_celebration", "setItem", "year", "getFullYear", "String", "getMonth", "padStart", "date1", "date2", "djangoDayIndex", "dayMap", "selectors", "viewQuery", "TodayPage_Query", "rf", "ctx", "TodayPage_div_8_Template", "TodayPage_Template_ion_button_click_17_listener", "TodayPage_ion_card_21_Template", "TodayPage_ion_card_22_Template", "TodayPage_ion_card_29_Template", "TodayPage_ion_card_30_Template", "TodayPage_Template_ion_modal_ionModalDidDismiss_31_listener", "TodayPage_ng_template_32_Template", "TodayPage_app_celebration_33_Template", "i1", "IonButton", "IonCard", "IonCardContent", "IonCardHeader", "IonCardTitle", "IonCheckbox", "IonCol", "IonContent", "IonGrid", "IonHeader", "IonIcon", "IonInput", "IonProgressBar", "IonRange", "IonRow", "IonSelect", "IonSelectOption", "IonText", "IonTextarea", "IonToolbar", "IonModal", "BooleanValueAccessor", "NumericValueAccessor", "SelectValueAccessor", "TextValueAccessor", "IonMinValidator", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "NgModel", "NgForm", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\today\\today.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\today\\today.page.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { QuestService } from '../../services/quest.service';\r\nimport { SideQuestService } from '../../services/sidequest.service';\r\nimport { UserService } from '../../services/user.service';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\nimport { Quest, QuestCategory, QuestGoalUnit, QuestPeriod, QuestPriority, QuestProgress, QuestType } from '../../models/quest.model';\r\nimport { User } from '../../models/user.model';\r\nimport { Observable, Subscription, forkJoin, map, of, switchMap, take, firstValueFrom } from 'rxjs';\r\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\r\nimport { CelebrationComponent } from '../../components/celebration/celebration.component';\r\nimport { Activated<PERSON>out<PERSON>, Router } from '@angular/router';\r\nimport { PreferencesService } from '../../services/preferences.service';\r\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\r\nimport { StreakCalculatorService } from '../../services/streak-calculator';\r\nimport { HeaderComponent } from 'src/app/components/header/header.component';\r\nimport { AuroraComponent } from 'src/app/components/aurora/aurora.component';\r\n\r\n\r\ninterface WeekDate {\r\n  date: string; // YYYY-MM-DD format\r\n  day: number;\r\n  is_today: boolean;\r\n  is_selected: boolean;\r\n  is_future: boolean;\r\n  total_quests: number;\r\n  completed_quests: number;\r\n  completion_percentage: number;\r\n}\r\n\r\ninterface DailyQuest {\r\n  id: string;\r\n  current_quest: {\r\n    id: string;\r\n    name: string;\r\n    description: string;\r\n    goal_value: number;\r\n    goal_unit: string;\r\n  };\r\n  streak: number;\r\n  completed: boolean;\r\n  value_achieved: number;\r\n  emoji: string;\r\n}\r\n\r\ninterface QuestDisplay extends Quest {\r\n  completed: boolean;\r\n  value_achieved: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-today',\r\n  templateUrl: './today.page.html',\r\n  styleUrls: ['./today.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, NavigationComponent, CelebrationComponent, EmojiInputDirective, HeaderComponent, AuroraComponent]\r\n})\r\nexport class TodayPage implements OnInit, OnDestroy {\r\n  @ViewChild('emojiInput') emojiInput: any;\r\n  // User data\r\n  user$: Observable<User | null> = of(null);\r\n  userId: string | null = null;\r\n  userSubscription: Subscription | undefined;\r\n  showSidequests = true;\r\n\r\n  // Date and calendar\r\n  selectedDate: Date = new Date();\r\n  weekDates: WeekDate[] = [];\r\n  dayNames: string[] = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];\r\n  headerText: string = 'Today';\r\n  weekOffset: number = 0;\r\n  selectedDateData: any;\r\n\r\n\r\n  // Quests\r\n  quests: QuestDisplay[] = [];\r\n  dailyQuest: DailyQuest | null = null;\r\n  currentStep = 5;\r\n  totalSteps = 5;\r\n\r\n  emojis: string[] = [\r\n    '🚀', '🪐', '⏳', '💊', '⚔️', '🧠', '🦷', '👨‍🍳',\r\n    '🏃', '🥬', '🏆', '🎮', '🎯', '💻', '🚴‍♂️', '🏋️‍♂️',\r\n    '💰', '💸', '🪬', '🧪', '😴', '📈', '📚', '❌',\r\n    '🎓', '💪', '🧘‍♂️', '📵', '🚭', '💧'\r\n  ];\r\n\r\n\r\n  // Cache for quest data to improve performance\r\n  private questCache: { [dateKey: string]: QuestDisplay[] } = {};\r\n\r\n  // Flag to track if we're currently loading data\r\n  private isLoadingData = false;\r\n\r\n  // Method to load daily side quest\r\n  private loadDailySideQuest() {\r\n    // Only load for today's date\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(this.selectedDate);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n    // Reset dailyQuest if not today's date\r\n    if (!isTodaySelected) {\r\n      this.dailyQuest = null;\r\n      return;\r\n    }\r\n\r\n    if (this.showSidequests && isTodaySelected && this.userId) {\r\n      // Use the ensureUserHasDailySideQuests method\r\n      this.sideQuestService.ensureUserHasDailySideQuests(this.userId!).pipe(\r\n        take(1)\r\n      ).subscribe({\r\n        next: (sideQuests) => {\r\n          if (sideQuests && sideQuests.length > 0) {\r\n            const sideQuest = sideQuests[0];\r\n\r\n            // Get the quest details from the pool\r\n            this.supabaseService.getClient()\r\n              .from('daily_sidequest_pool')\r\n              .select('*')\r\n              .eq('id', sideQuest.current_quest_id)\r\n              .single()\r\n              .then(response => {\r\n                if (response.error) {\r\n                  return;\r\n                }\r\n\r\n                const questDetails = response.data;\r\n\r\n                // Create the daily quest object\r\n                this.dailyQuest = {\r\n                  id: sideQuest.id!,\r\n                  current_quest: {\r\n                    id: sideQuest.current_quest_id!,\r\n                    name: questDetails.name || 'Daily Side Quest',\r\n                    description: questDetails.description || 'Complete this daily side quest',\r\n                    goal_value: questDetails.goal_value || 1,\r\n                    goal_unit: questDetails.goal_unit || 'count'\r\n                  },\r\n                  streak: sideQuest.streak || 0,\r\n                  completed: sideQuest.completed || false,\r\n                  value_achieved: sideQuest.value_achieved || 0,\r\n                  emoji: questDetails.emoji || '🎯'\r\n                };\r\n              });\r\n          } else {\r\n            this.dailyQuest = null;\r\n          }\r\n        },\r\n        error: () => {\r\n          this.dailyQuest = null;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Add Quest Modal\r\n  showAddQuestModal = false;\r\n  newQuest = this.getEmptyQuest();\r\n  hasHighPriorityQuest = false;\r\n\r\n  // Animation states\r\n  questTypeAnimated = false;\r\n  questTypeAnimating = false;\r\n  selectedQuestType = '';\r\n  categoryAnimated = false;\r\n  categoryAnimating = false;\r\n  categorySelected = false;\r\n  selectedCategory = '';\r\n  priorityAnimated = false;\r\n  priorityAnimating = false;\r\n  goalAnimated = false;\r\n\r\n  // Celebration Modal\r\n  showCelebration = false;\r\n  currentUser: User | null = null;\r\n  celebrationShownDates: string[] = [];\r\n\r\n  // Days selection for new quest\r\n  weekDays = [\r\n    { value: 'Sun', label: 'Su' },\r\n    { value: 'Mon', label: 'Mo' },\r\n    { value: 'Tue', label: 'Tu' },\r\n    { value: 'Wed', label: 'We' },\r\n    { value: 'Thu', label: 'Th' },\r\n    { value: 'Fri', label: 'Fr' },\r\n    { value: 'Sat', label: 'Sa' }\r\n  ];\r\n  monthDays = Array.from({ length: 31 }, (_, i) => i + 1);\r\n  selectedDaysOfWeek: string[] = [];\r\n  selectedDaysOfMonth: number[] = [];\r\n\r\n  // Use inject instead of constructor injection\r\n  private questService = inject(QuestService);\r\n  private sideQuestService = inject(SideQuestService);\r\n  private userService = inject(UserService);\r\n  private supabaseService = inject(SupabaseService);\r\n  private route = inject(ActivatedRoute);\r\n  private router = inject(Router);\r\n  private preferencesService = inject(PreferencesService);\r\n  private streakCalculator = inject(StreakCalculatorService);\r\n  private isRedirecting = false; // Flag to prevent multiple redirects\r\n\r\n  constructor() {\r\n    // Subscribe to query params to get date and week_offset from URL\r\n    this.route.queryParams.subscribe(params => {\r\n      const dateParam = params['date'];\r\n      const weekOffsetParam = params['week_offset'];\r\n\r\n      console.log('TodayPage: Date param from URL query:', dateParam);\r\n      console.log('TodayPage: Week offset param from URL query:', weekOffsetParam);\r\n\r\n      // Process week offset parameter\r\n      if (weekOffsetParam !== undefined) {\r\n        try {\r\n          this.weekOffset = parseInt(weekOffsetParam);\r\n          console.log('TodayPage: Week offset set to:', this.weekOffset);\r\n        } catch (error) {\r\n          console.error('TodayPage: Error parsing week offset:', error);\r\n          this.weekOffset = 0;\r\n        }\r\n      } else {\r\n        this.weekOffset = 0;\r\n      }\r\n\r\n      // Process date parameter\r\n      if (dateParam) {\r\n        try {\r\n          // Validate date format (YYYY-MM-DD)\r\n          if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateParam)) {\r\n            this.selectedDate = new Date(dateParam);\r\n            console.log('TodayPage: Selected date from URL query:', this.selectedDate);\r\n          } else {\r\n            console.error('TodayPage: Invalid date format in URL query:', dateParam);\r\n            this.selectedDate = new Date(); // Default to today\r\n          }\r\n        } catch (error) {\r\n          console.error('TodayPage: Error parsing date from URL query:', error);\r\n          this.selectedDate = new Date(); // Default to today\r\n        }\r\n      } else {\r\n        this.selectedDate = new Date(); // Default to today\r\n      }\r\n\r\n      // Initialize week dates based on selected date and week offset\r\n      this.generateWeekDates();\r\n\r\n      // Update header text and load data\r\n      this.updateHeaderText();\r\n\r\n      // Only load data if we have a userId\r\n      if (this.userId) {\r\n        this.loadData();\r\n      }\r\n    });\r\n\r\n    // Subscribe to auth state changes\r\n    this.userSubscription = this.supabaseService.currentUser$.subscribe(authUser => {\r\n\r\n\r\n      if (!authUser) {\r\n        console.log('TodayPage: No authenticated user, but not redirecting');\r\n        // Removed redirect to allow direct access\r\n        return;\r\n      }\r\n\r\n      // User is authenticated, get user data\r\n      this.userId = authUser.id;\r\n\r\n\r\n      // Get user data from Supabase\r\n      this.userService.getUserById(authUser.id).subscribe(userData => {\r\n        if (!userData) {\r\n          console.log('TodayPage: No user data found, but not redirecting');\r\n          // Removed redirect to allow direct access\r\n          return;\r\n        }\r\n\r\n        console.log('TodayPage: User data loaded:', userData);\r\n        this.loadData();\r\n      });\r\n    });\r\n\r\n    // Set up user$ observable for template binding\r\n    this.user$ = this.supabaseService.currentUser$.pipe(\r\n      switchMap(authUser => {\r\n        if (!authUser) {\r\n          return of(null);\r\n        }\r\n\r\n        return this.userService.getUserById(authUser.id);\r\n      })\r\n    );\r\n\r\n    // Subscribe to user$ to get user preferences\r\n    const userDataSubscription = this.user$.subscribe({\r\n      next: (user) => {\r\n        if (user) {\r\n          this.showSidequests = user.sidequests_switch;\r\n          this.currentUser = user;\r\n        }\r\n      }\r\n    });\r\n\r\n    // Add the subscription to be cleaned up\r\n    this.userSubscription = new Subscription();\r\n    this.userSubscription.add(userDataSubscription);\r\n  }\r\n\r\n  ngOnInit() {\r\n    // Generate week dates and preload data for all days\r\n    this.generateWeekDates();\r\n\r\n    // Preload data for all days in the week\r\n    setTimeout(() => {\r\n      this.preloadWeekData();\r\n    }, 0);\r\n\r\n    // Load celebration shown dates from localStorage and clean up old ones\r\n    try {\r\n      // Get today's date\r\n      const today = new Date();\r\n      const todayStr = this.formatDate(today);\r\n\r\n      // First, collect all localStorage keys\r\n      const allKeys: string[] = [];\r\n      for (let i = 0; i < localStorage.length; i++) {\r\n        const key = localStorage.key(i);\r\n        if (key) {\r\n          allKeys.push(key);\r\n        }\r\n      }\r\n\r\n      // Find and remove all celebration_shown keys except today's\r\n      allKeys.forEach(key => {\r\n        if (key.startsWith('celebration_shown_') && key !== `celebration_shown_${todayStr}`) {\r\n          localStorage.removeItem(key);\r\n        }\r\n      });\r\n\r\n      // Check if we have a celebration shown for today\r\n      const todayCelebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\r\n\r\n      // Add to our tracking array if found\r\n      this.celebrationShownDates = [];\r\n      if (todayCelebrationShown) {\r\n        this.celebrationShownDates.push(todayStr);\r\n      }\r\n    } catch (error) {\r\n      this.celebrationShownDates = [];\r\n    }\r\n  }\r\n\r\n  async ionViewWillEnter() {\r\n    // Authentication is now handled by the AuthGuard\r\n    // Just get the current user and load data\r\n    const authUser = this.supabaseService._currentUser.value;\r\n\r\n    if (!authUser) {\r\n      console.log('TodayPage: No authenticated user, but not redirecting');\r\n      return;\r\n    }\r\n\r\n    // Use the UserService to get or create the user document\r\n    this.userService.ensureUserExists(authUser).subscribe(userData => {\r\n      if (!userData) {\r\n        this.router.navigateByUrl('/signup');\r\n        return;\r\n      }\r\n\r\n      // Get end date\r\n      let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;\r\n      const currentDate = new Date();\r\n\r\n      // Compare dates properly\r\n      let isValidPlan = false;\r\n      if (endDate instanceof Date) {\r\n        isValidPlan = endDate > currentDate;\r\n      }\r\n\r\n      if (!isValidPlan) {\r\n        // Prevent multiple redirects\r\n        if (this.isRedirecting) return;\r\n        this.isRedirecting = true;\r\n\r\n        setTimeout(() => {\r\n          this.router.navigateByUrl('/pricing');\r\n          setTimeout(() => {\r\n            this.isRedirecting = false;\r\n          }, 2000);\r\n        }, 500);\r\n        return;\r\n      }\r\n\r\n      // Check if we have cached data for this date\r\n      const dateKey = this.formatDate(this.selectedDate);\r\n      if (this.questCache[dateKey]) {\r\n        // Use cached data\r\n        this.quests = this.questCache[dateKey];\r\n\r\n        // Initialize slider backgrounds immediately\r\n        requestAnimationFrame(() => {\r\n          this.initializeSliderBackgrounds();\r\n        });\r\n\r\n        // Load daily side quest if needed\r\n        this.loadDailySideQuest();\r\n      } else {\r\n        // Load data with the current selected date\r\n        this.loadData();\r\n      }\r\n    });\r\n\r\n    // Make sure the URL reflects the selected date and week offset\r\n    const route = this.router.url;\r\n    const dateParam = this.formatDate(this.selectedDate);\r\n\r\n    if (route === '/today') {\r\n      // If we're on the base route, update to include the date and week_offset as query parameters\r\n      this.router.navigate(['/today'], {\r\n        queryParams: {\r\n          date: dateParam,\r\n          week_offset: this.weekOffset !== 0 ? this.weekOffset : null\r\n        },\r\n        replaceUrl: true\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  // Initialize all slider backgrounds\r\n  initializeSliderBackgrounds() {\r\n    // Use requestAnimationFrame for better performance\r\n    requestAnimationFrame(() => {\r\n      const sliders = document.querySelectorAll('.progress-slider');\r\n      if (sliders.length === 0) {\r\n        return;\r\n      }\r\n\r\n      sliders.forEach(slider => {\r\n        if (slider instanceof HTMLInputElement) {\r\n          // Get the slider's quest ID for debugging\r\n          const sliderQuestId = slider.getAttribute('data-quest-id');\r\n          if (!sliderQuestId) {\r\n            return;\r\n          }\r\n\r\n          // Get the exact value from the slider (no rounding)\r\n          const sliderValue = parseInt(slider.value);\r\n          const minValue = parseInt(slider.min);\r\n          const maxValue = parseInt(slider.max);\r\n\r\n          // Calculate the percentage value\r\n          const percentage = maxValue > minValue ?\r\n            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n          // Set the background directly with hardcoded colors\r\n          slider.style.background =\r\n            `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\r\n\r\n          // Add a data attribute to track the current value\r\n          slider.setAttribute('data-current-value', slider.value);\r\n        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n          // Get the slider's quest ID for debugging\r\n          const sliderQuestId = slider.getAttribute('data-quest-id');\r\n          if (!sliderQuestId) {\r\n            return;\r\n          }\r\n\r\n          // Get the value from the element's properties or attributes\r\n          const valueAttr = slider.getAttribute('value') || '0';\r\n          const minAttr = slider.getAttribute('min') || '0';\r\n          const maxAttr = slider.getAttribute('max') || '100';\r\n\r\n          const sliderValue = parseInt(valueAttr);\r\n          const minValue = parseInt(minAttr);\r\n          const maxValue = parseInt(maxAttr);\r\n\r\n          // Calculate the percentage value\r\n          const percentage = maxValue > minValue ?\r\n            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n          // Set the CSS variable for the progress\r\n          slider.style.setProperty('--progress-value', `${percentage}%`);\r\n\r\n          // Add a data attribute to track the current value\r\n          slider.setAttribute('data-current-value', sliderValue.toString());\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  ionViewWillLeave() {\r\n    console.log('TodayPage: ionViewWillLeave called');\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    console.log('TodayPage: ngOnDestroy called');\r\n    if (this.userSubscription) {\r\n      this.userSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  async loadData() {\r\n    if (!this.userId) {\r\n      return;\r\n    }\r\n\r\n    // Update header text\r\n    this.updateHeaderText();\r\n\r\n    // Check if we have cached data for this date\r\n    const dateKey = this.formatDate(this.selectedDate);\r\n    if (this.questCache[dateKey]) {\r\n      // Use cached data\r\n      this.quests = this.questCache[dateKey];\r\n\r\n      // Initialize slider backgrounds immediately\r\n      requestAnimationFrame(() => {\r\n        this.initializeSliderBackgrounds();\r\n      });\r\n\r\n      // Load daily side quest if needed\r\n      this.loadDailySideQuest();\r\n\r\n      return;\r\n    }\r\n\r\n    // Set up date variables\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(this.selectedDate);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n    console.log('TodayPage: Loading data for date:', this.formatDate(this.selectedDate));\r\n    if (isTodaySelected) {\r\n      // Check if we've already calculated streaks for today\r\n      const todayDateString = this.formatDate(today);\r\n      try {\r\n        const { value: lastStreakCalculation } = await this.preferencesService.get('last_streak_calculation');\r\n\r\n        if (lastStreakCalculation !== todayDateString) {\r\n          console.log('TodayPage: First time loading today, calculating streaks');\r\n\r\n          // Najprv spracujeme všetky questy pomocou checkMissedDays\r\n          await firstValueFrom(this.questService.getQuests(this.userId!).pipe(\r\n            take(1),\r\n            switchMap(async quests => {\r\n              // Check missed days for each quest\r\n              for (const quest of quests) {\r\n                if (quest.id) {\r\n                  await this.questService.checkMissedDays(quest.id);\r\n                }\r\n              }\r\n\r\n              // Potom vytvoríme progress záznamy pre quit questy\r\n              await this.questService.createQuitQuestProgressForToday();\r\n\r\n              // NEBUDEME tu nastavovať last_streak_calculation, aby sa mohli vypočítať streaky v ďalšej časti kódu\r\n              return quests;\r\n            })\r\n          ));\r\n\r\n          // Streaky sa vypočítajú v ďalšej časti kódu\r\n        } else {\r\n          console.log('TodayPage: Streaks already calculated for today');\r\n        }\r\n      } catch (error) {\r\n        console.error('TodayPage: Error checking last streak calculation:', error);\r\n\r\n        // Ak nastane chyba, nenastavujeme last_streak_calculation, aby sa mohli vypočítať streaky\r\n      }\r\n\r\n      // Recalculate streak for the daily side quest only for today\r\n      if (this.showSidequests) {\r\n        this.sideQuestService.recalculateSideQuestStreak(this.userId, this.selectedDate)\r\n          .subscribe({\r\n            error: (error) => {\r\n              console.error('Error recalculating side quest streak:', error);\r\n            }\r\n          });\r\n      }\r\n    }\r\n    // Load quests\r\n    this.questService.getQuests(this.userId).pipe(\r\n      take(1),\r\n      switchMap(quests => {\r\n        // Filter active quests for the selected date\r\n        const filteredQuests = this.filterQuestsForDate(quests, this.selectedDate);\r\n\r\n        if (filteredQuests.length === 0) {\r\n          return of([]);\r\n        }\r\n\r\n        // Sort filtered quests by creation date (newest first) or ID\r\n        const sortedFilteredQuests = [...filteredQuests].sort((a, b) => {\r\n          if (a.created_at && b.created_at) {\r\n            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n          }\r\n          return a.id && b.id ? a.id.localeCompare(b.id) : 0;\r\n        });\r\n\r\n        // Get all progress for all quests at once\r\n        return this.questService.getQuestProgressForDate(this.userId!, this.selectedDate).pipe(\r\n          take(1),\r\n          switchMap(allProgress => {\r\n            // Create a lookup for quick access\r\n            const progressLookup: { [questId: string]: QuestProgress } = {};\r\n            allProgress.forEach(progress => {\r\n              progressLookup[progress.quest_id] = progress;\r\n            });\r\n\r\n            // For today's view, calculate streaks once per day\r\n            // For other days, just use the streak from the database\r\n            if (isTodaySelected) {\r\n              // Check if we've already calculated streaks for today\r\n              const todayDateString = this.formatDate(today);\r\n              return this.preferencesService.get('last_streak_calculation').then(({ value: lastStreakCalculation }) => {\r\n                if (lastStreakCalculation !== todayDateString) {\r\n                  console.log('TodayPage: First time loading today, calculating streaks');\r\n\r\n                  // Calculate streaks using our streak calculator\r\n                  return this.streakCalculator.calculateStreaks(this.userId!, sortedFilteredQuests).then(streaks => {\r\n                    // Map quests with progress and calculated streaks\r\n                    return sortedFilteredQuests.map(quest => {\r\n                      const progress = progressLookup[quest.id!];\r\n                      const calculatedStreak = streaks[quest.id!] || 0;\r\n\r\n                      // Update the streak in the database\r\n                      this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe();\r\n\r\n                      return {\r\n                        ...quest,\r\n                        completed: progress?.completed || false,\r\n                        value_achieved: progress?.value_achieved || 0,\r\n                        streak: calculatedStreak\r\n                      } as QuestDisplay;\r\n                    });\r\n                  }).then(result => {\r\n                    // Po výpočte streakov nastavíme last_streak_calculation\r\n                    this.preferencesService.set('last_streak_calculation', todayDateString);\r\n                    return result;\r\n                  });\r\n                } else {\r\n                  console.log('TodayPage: Streaks already calculated for today, using database values');\r\n\r\n                  // Just use the streak from the database\r\n                  return sortedFilteredQuests.map(quest => {\r\n                    const progress = progressLookup[quest.id!];\r\n\r\n                    return {\r\n                      ...quest,\r\n                      completed: progress?.completed || false,\r\n                      value_achieved: progress?.value_achieved || 0,\r\n                      streak: quest.streak || 0\r\n                    } as QuestDisplay;\r\n                  });\r\n                }\r\n              }).catch(error => {\r\n                console.error('TodayPage: Error checking last streak calculation:', error);\r\n\r\n                // If there's an error, just use the streak from the database\r\n                return sortedFilteredQuests.map(quest => {\r\n                  const progress = progressLookup[quest.id!];\r\n\r\n                  return {\r\n                    ...quest,\r\n                    completed: progress?.completed || false,\r\n                    value_achieved: progress?.value_achieved || 0,\r\n                    streak: quest.streak || 0\r\n                  } as QuestDisplay;\r\n                });\r\n              });\r\n            } else {\r\n              // For previous days, just use the streak from the database but set it to 0 for display\r\n              return Promise.resolve(sortedFilteredQuests.map(quest => {\r\n                const progress = progressLookup[quest.id!];\r\n\r\n                return {\r\n                  ...quest,\r\n                  completed: progress?.completed || false,\r\n                  value_achieved: progress?.value_achieved || 0,\r\n                  streak: 0 // Don't show streak for previous days\r\n                } as QuestDisplay;\r\n              }));\r\n            }\r\n          })\r\n        );\r\n\r\n\r\n      })\r\n    ).subscribe({\r\n      next: (questsWithProgress) => {\r\n        // Sort quests by creation date (newest first) or ID\r\n        const sortedQuests = [...questsWithProgress].sort((a, b) => {\r\n          if (a.created_at && b.created_at) {\r\n            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n          }\r\n          return a.id && b.id ? a.id.localeCompare(b.id) : 0;\r\n        });\r\n\r\n        // Check if all quests are completed for today\r\n        this.checkAllQuestsCompleted(sortedQuests);\r\n\r\n        // Update the quests array\r\n        this.quests = sortedQuests;\r\n\r\n        // Cache the quests for this date\r\n        const dateKey = this.formatDate(this.selectedDate);\r\n        this.questCache[dateKey] = sortedQuests;\r\n\r\n        // Update the week date progress\r\n        this.updateWeekDateProgress();\r\n\r\n        // Initialize slider backgrounds\r\n        requestAnimationFrame(() => {\r\n          this.initializeSliderBackgrounds();\r\n        });\r\n\r\n        // Load daily side quest if needed\r\n        this.loadDailySideQuest();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading quests:', error);\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  generateWeekDates() {\r\n    const today = new Date();\r\n\r\n    // Calculate the start of the week based on week offset\r\n    // This starts on Monday (1) instead of Sunday (0)\r\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\r\n    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // Convert to Monday-based (0 = Monday)\r\n    const startOfWeek = new Date(today);\r\n    startOfWeek.setDate(today.getDate() - daysFromMonday + (7 * this.weekOffset));\r\n\r\n    this.weekDates = [];\r\n    for (let i = 0; i < 7; i++) {\r\n      const date = new Date(startOfWeek);\r\n      date.setDate(startOfWeek.getDate() + i);\r\n\r\n      const dateString = this.formatDate(date);\r\n      const isToday = this.isSameDay(date, today);\r\n      const isSelected = this.isSameDay(date, this.selectedDate);\r\n      const isFuture = date > today;\r\n\r\n      // Check if we have cached progress for this date\r\n      const dateKey = dateString;\r\n      let totalQuests = 0;\r\n      let completedQuests = 0;\r\n      let completionPercentage = 0;\r\n\r\n      if (this.weekProgressCache[dateKey]) {\r\n        const cached = this.weekProgressCache[dateKey];\r\n        totalQuests = cached.total;\r\n        completedQuests = cached.completed;\r\n        completionPercentage = totalQuests > 0\r\n          ? Math.round((completedQuests / totalQuests) * 100)\r\n          : 0;\r\n      }\r\n\r\n      this.weekDates.push({\r\n        date: dateString,\r\n        day: date.getDate(),\r\n        is_today: isToday,\r\n        is_selected: isSelected,\r\n        is_future: isFuture,\r\n        total_quests: totalQuests,\r\n        completed_quests: completedQuests,\r\n        completion_percentage: completionPercentage\r\n      });\r\n    }\r\n\r\n    // Preload data for all days in the week\r\n    if (this.userId) {\r\n      // Use setTimeout to allow the UI to render first\r\n      setTimeout(() => {\r\n        this.preloadWeekData();\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  // Cache for week date progress\r\n  private weekProgressCache: { [dateKey: string]: { total: number, completed: number } } = {};\r\n\r\n  updateWeekDateProgress() {\r\n    if (!this.userId) return;\r\n\r\n    // For each date in the week, update the progress\r\n    this.weekDates.forEach((weekDate, index) => {\r\n      if (weekDate.is_future) return;\r\n\r\n      const date = new Date(weekDate.date);\r\n      const dateKey = this.formatDate(date);\r\n\r\n      // Check if we have cached progress for this date\r\n      if (this.weekProgressCache[dateKey]) {\r\n        const cached = this.weekProgressCache[dateKey];\r\n        this.weekDates[index].total_quests = cached.total;\r\n        this.weekDates[index].completed_quests = cached.completed;\r\n        this.weekDates[index].completion_percentage = cached.total > 0\r\n          ? Math.round((cached.completed / cached.total) * 100)\r\n          : 0;\r\n        return;\r\n      }\r\n\r\n      // If we have cached quests for this date, use them to calculate progress\r\n      if (this.questCache[dateKey]) {\r\n        const cachedQuests = this.questCache[dateKey];\r\n        const totalQuests = cachedQuests.length;\r\n        const completedQuests = cachedQuests.filter(q => q.completed).length;\r\n\r\n        // Cache the progress\r\n        this.weekProgressCache[dateKey] = {\r\n          total: totalQuests,\r\n          completed: completedQuests\r\n        };\r\n\r\n        // Update the week date\r\n        this.weekDates[index].total_quests = totalQuests;\r\n        this.weekDates[index].completed_quests = completedQuests;\r\n        this.weekDates[index].completion_percentage = totalQuests > 0\r\n          ? Math.round((completedQuests / totalQuests) * 100)\r\n          : 0;\r\n        return;\r\n      }\r\n    });\r\n\r\n    // Preload data for all days in the week\r\n    this.preloadWeekData();\r\n  }\r\n\r\n  // Helper method to filter quests for a specific date\r\n  private filterQuestsForDate(quests: Quest[], date: Date): Quest[] {\r\n    const dateObj = new Date(date);\r\n    const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.\r\n    // Django uses Monday=0, Sunday=6 format, so we need to convert\r\n    const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format\r\n    const dayOfMonth = dateObj.getDate(); // 1-31\r\n\r\n    console.log(`TodayPage: Filtering quests for date ${this.formatDate(date)}, day of week: ${dayOfWeek} (Django: ${djangoDayOfWeek}), day of month: ${dayOfMonth}`);\r\n\r\n    const filteredQuests = quests.filter(quest => {\r\n      console.log(`TodayPage: Checking quest ${quest.id} (${quest.name}), type: ${quest.quest_type}, period: ${quest.goal_period}, task_days_of_week: ${quest.task_days_of_week}, task_days_of_month: ${quest.task_days_of_month}`);\r\n\r\n      if (!quest.active) {\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is not active, filtering out`);\r\n        return false;\r\n      }\r\n\r\n      // Only show quests from the date they were created\r\n      if (quest.created_at) {\r\n        const createdDate = new Date(quest.created_at);\r\n        createdDate.setHours(0, 0, 0, 0);\r\n        dateObj.setHours(0, 0, 0, 0);\r\n\r\n        // If the selected date is before the quest was created, don't show it\r\n        if (dateObj < createdDate) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // Daily quests are always shown\r\n      if (quest.goal_period === 'day') {\r\n        return true;\r\n      }\r\n\r\n      // Weekly quests are shown on specific days\r\n      if (quest.goal_period === 'week') {\r\n        if (!quest.task_days_of_week) {\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has no task_days_of_week specified, showing every day`);\r\n          return true; // If no days specified, show every day\r\n        }\r\n\r\n        // Parse task_days_of_week\r\n        let taskDays: any[] = [];\r\n        if (typeof quest.task_days_of_week === 'string') {\r\n          taskDays = quest.task_days_of_week.split(',').map(day => day.trim());\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as string: ${quest.task_days_of_week}, parsed to:`, taskDays);\r\n        } else if (Array.isArray(quest.task_days_of_week)) {\r\n          taskDays = quest.task_days_of_week;\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as array:`, taskDays);\r\n        }\r\n\r\n        // Check if current day is in task days\r\n        // Convert current day to different formats for comparison\r\n        const dayNameShort = this.getDayNameShort(djangoDayOfWeek);\r\n        const dayNameFull = this.getDayNameFull(djangoDayOfWeek);\r\n\r\n        console.log(`TodayPage: Checking if day ${dayNameFull} (${dayNameShort}, ${djangoDayOfWeek}) is in task days:`, taskDays);\r\n\r\n        const isIncluded = taskDays.includes(djangoDayOfWeek) ||\r\n          taskDays.includes(djangoDayOfWeek.toString()) ||\r\n          taskDays.includes(dayNameShort) ||\r\n          taskDays.includes(dayNameFull);\r\n\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) should be shown on ${dayNameFull}? ${isIncluded}`);\r\n\r\n        return isIncluded;\r\n      }\r\n\r\n      // Monthly quests are shown on specific days of month\r\n      if (quest.goal_period === 'month') {\r\n        if (!quest.task_days_of_month) return true; // If no days specified, show every day\r\n\r\n        // Parse task_days_of_month\r\n        let taskDays: any[] = [];\r\n        if (typeof quest.task_days_of_month === 'string') {\r\n          taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));\r\n        } else if (Array.isArray(quest.task_days_of_month)) {\r\n          taskDays = quest.task_days_of_month;\r\n        }\r\n\r\n        // Check if current day is in task days\r\n        return taskDays.includes(dayOfMonth) ||\r\n          taskDays.includes(dayOfMonth.toString());\r\n      }\r\n\r\n      return false;\r\n    });\r\n\r\n    console.log(`TodayPage: Filtered ${quests.length} quests to ${filteredQuests.length} for date ${this.formatDate(date)}`);\r\n\r\n    return filteredQuests;\r\n  }\r\n\r\n  selectDate(dateObj: any) {\r\n    if (this.isLoadingData) {\r\n      return;\r\n    }\r\n\r\n    const dateData = dateObj.date\r\n\r\n    this.isLoadingData = true;\r\n\r\n    this.selectedDateData = dateObj;\r\n\r\n    const date = new Date(dateData);\r\n    this.selectedDate = date;\r\n\r\n    this.weekDates.forEach(weekDate => {\r\n      weekDate.is_selected = weekDate.date === dateData;\r\n    });\r\n\r\n    const formattedDate = this.formatDate(date);\r\n\r\n    this.router.navigate(['/today'], {\r\n      queryParams: {\r\n        date: formattedDate,\r\n        week_offset: this.weekOffset !== 0 ? this.weekOffset : null\r\n      },\r\n      replaceUrl: true\r\n    });\r\n\r\n    this.updateHeaderText();\r\n\r\n    setTimeout(() => {\r\n      this.loadData();\r\n      this.isLoadingData = false;\r\n    }, 10);\r\n  }\r\n\r\n  // Flag to track if we're currently changing weeks\r\n  private isChangingWeek = false;\r\n\r\n  changeWeek(direction: number) {\r\n    // Prevent multiple rapid week changes\r\n    if (this.isChangingWeek) {\r\n      return;\r\n    }\r\n\r\n    this.isChangingWeek = true;\r\n\r\n    // Update the week offset\r\n    this.weekOffset += direction;\r\n\r\n    // Generate new week dates with the updated offset\r\n    this.generateWeekDates();\r\n\r\n    // Preload quest data for all days in the week\r\n    this.preloadWeekData();\r\n\r\n    // Update the URL with the new week offset while preserving the selected date\r\n    const dateParam = this.formatDate(this.selectedDate);\r\n    this.router.navigate(['/today'], {\r\n      queryParams: {\r\n        date: dateParam,\r\n        week_offset: this.weekOffset\r\n      },\r\n      replaceUrl: true\r\n    });\r\n\r\n    // Reset the flag after a short delay\r\n    setTimeout(() => {\r\n      this.isChangingWeek = false;\r\n    }, 300);\r\n  }\r\n\r\n  // Preload data for all days in the current week\r\n  private preloadWeekData() {\r\n    if (!this.userId) return;\r\n\r\n    // Get all quests once to avoid multiple API calls\r\n    this.questService.getQuests(this.userId!).pipe(\r\n      take(1)\r\n    ).subscribe(allQuests => {\r\n      // Create an array of observables for each date\r\n      const dateObservables = this.weekDates\r\n        .filter(weekDate => !weekDate.is_future)\r\n        .map(weekDate => {\r\n          const date = new Date(weekDate.date);\r\n          const dateKey = this.formatDate(date);\r\n\r\n          // Skip if we already have cached data\r\n          if (this.weekProgressCache[dateKey]) {\r\n            return of({\r\n              date: weekDate.date,\r\n              progress: this.weekProgressCache[dateKey]\r\n            });\r\n          }\r\n\r\n          // Filter active quests for this date\r\n          const activeQuests = this.filterQuestsForDate(allQuests, date);\r\n\r\n          // If no active quests, return empty progress\r\n          if (activeQuests.length === 0) {\r\n            const emptyProgress = { total: 0, completed: 0 };\r\n            this.weekProgressCache[dateKey] = emptyProgress;\r\n            return of({\r\n              date: weekDate.date,\r\n              progress: emptyProgress\r\n            });\r\n          }\r\n\r\n          // Get progress for this date\r\n          return this.questService.getQuestProgressForDate(this.userId!, date).pipe(\r\n            take(1),\r\n            map(progressList => {\r\n              // Count completed quests\r\n              const questIds = activeQuests.map(q => q.id);\r\n              const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\r\n              const completedQuests = relevantProgress.filter(p => p.completed).length;\r\n              const totalQuests = activeQuests.length;\r\n\r\n              // Create progress object\r\n              const progress = {\r\n                total: totalQuests,\r\n                completed: completedQuests\r\n              };\r\n\r\n              // Cache the progress\r\n              this.weekProgressCache[dateKey] = progress;\r\n\r\n              return {\r\n                date: weekDate.date,\r\n                progress\r\n              };\r\n            })\r\n          );\r\n        });\r\n\r\n      // Process all date observables in parallel\r\n      forkJoin(dateObservables).subscribe(results => {\r\n        // Update the week dates with the progress\r\n        results.forEach(result => {\r\n          const index = this.weekDates.findIndex(wd => wd.date === result.date);\r\n          if (index >= 0) {\r\n            this.weekDates[index].total_quests = result.progress.total;\r\n            this.weekDates[index].completed_quests = result.progress.completed;\r\n            this.weekDates[index].completion_percentage = result.progress.total > 0\r\n              ? Math.round((result.progress.completed / result.progress.total) * 100)\r\n              : 0;\r\n          }\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  updateHeaderText() {\r\n    const today = new Date();\r\n    if (this.isSameDay(this.selectedDate, today)) {\r\n      this.headerText = 'Today';\r\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() - 1)))) {\r\n      this.headerText = 'Yesterday';\r\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() + 2)))) {\r\n      this.headerText = 'Tomorrow';\r\n    } else {\r\n      // Format as \"Mon, 15 Jan\"\r\n      this.headerText = this.selectedDate.toLocaleDateString('en-US', {\r\n        weekday: 'short',\r\n        day: 'numeric',\r\n        month: 'short'\r\n      });\r\n    }\r\n  }\r\n\r\n  // Map to track which quests are currently being toggled\r\n  private togglingQuestIds: { [questId: string]: boolean } = {};\r\n\r\n  async toggleQuest(quest: QuestDisplay) {\r\n    if (!this.userId || !quest.id) return;\r\n\r\n    // Check if this specific quest is already being toggled\r\n    if (this.togglingQuestIds[quest.id]) {\r\n      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is already being toggled, ignoring duplicate call`);\r\n      return;\r\n    }\r\n\r\n    // Set flag for this specific quest\r\n    this.togglingQuestIds[quest.id] = true;\r\n    console.log(`TodayPage: Starting toggle for quest ${quest.id} (${quest.name})`);\r\n\r\n    try {\r\n      // For normal quests, we don't want to toggle the value when clicking on the quest\r\n      // Instead, we want to keep the current value from the slider\r\n      // This is different from the original behavior where clicking would toggle between 0 and goal_value\r\n\r\n      // We'll just log that the quest was clicked but not change any values\r\n      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) clicked, keeping current value: ${quest.value_achieved}`);\r\n\r\n      // No need to update the database since we're not changing any values\r\n      // Just release the flag and return\r\n      delete this.togglingQuestIds[quest.id];\r\n      return;\r\n    } catch (error) {\r\n      console.error(`TodayPage: Error in toggleQuest for ${quest.id} (${quest.name}):`, error);\r\n    } finally {\r\n      // Reset flag for this specific quest\r\n      delete this.togglingQuestIds[quest.id];\r\n      console.log(`TodayPage: Finished toggle for quest ${quest.id} (${quest.name})`);\r\n    }\r\n  }\r\n\r\n  // Map to track which quests are currently being updated\r\n  private updatingQuestIds: { [questId: string]: boolean } = {};\r\n\r\n  async updateQuestProgress(quest: QuestDisplay, event?: any) {\r\n    if (!this.userId || !quest.id) return;\r\n\r\n    // Check if this specific quest is already being updated\r\n    if (this.updatingQuestIds[quest.id]) {\r\n      return;\r\n    }\r\n\r\n    // Set flag for this specific quest\r\n    this.updatingQuestIds[quest.id] = true;\r\n\r\n    try {\r\n      // Store the original completed state before any changes\r\n      const wasCompletedBefore = quest.completed;\r\n      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) original completed state: ${wasCompletedBefore}`);\r\n\r\n      // Update the slider background if an event is provided\r\n      if (event) {\r\n        // Handle both standard Event and Ionic's CustomEvent\r\n        const slider = event.target || (event.detail ? event.detail.value : null);\r\n        this.updateSliderBackground(slider);\r\n\r\n        // Verify that the slider is for the correct quest\r\n        const sliderQuestId = slider instanceof HTMLElement ? slider.getAttribute('data-quest-id') : null;\r\n        if (sliderQuestId && sliderQuestId !== quest.id) {\r\n          delete this.updatingQuestIds[quest.id];\r\n          return;\r\n        }\r\n\r\n        // Get the value from the slider\r\n        let sliderValue = 0;\r\n        if (event.detail && event.detail.value !== undefined) {\r\n          // Ionic range event\r\n          sliderValue = event.detail.value;\r\n        } else if (slider instanceof HTMLInputElement) {\r\n          // Standard input event\r\n          sliderValue = parseInt(slider.value);\r\n        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n          // Ionic range element\r\n          const valueAttr = slider.getAttribute('value') || '0';\r\n          sliderValue = parseInt(valueAttr);\r\n        }\r\n\r\n        // Update the quest's value_achieved with the slider value\r\n        quest.value_achieved = sliderValue;\r\n\r\n        // Update completed status based on quest type and value\r\n        // This exactly matches the Django implementation in toggle_quest view\r\n        if (quest.quest_type === 'build') {\r\n          // For build quests, completed when value >= goal\r\n          quest.completed = sliderValue >= quest.goal_value;\r\n        } else { // 'quit' type\r\n          // For quit quests, completed when value < goal (opposite of build)\r\n          quest.completed = sliderValue < quest.goal_value;\r\n        }\r\n\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) new completed state: ${quest.completed}`);\r\n      }\r\n\r\n      // Make a deep copy of the quest to avoid reference issues\r\n      const questCopy = { ...quest };\r\n\r\n      // Call the service and get the updated values\r\n      const result = await this.questService.toggleQuestCompletion(\r\n        this.userId,\r\n        quest.id,\r\n        this.selectedDate,\r\n        quest.value_achieved,\r\n        questCopy\r\n      );\r\n\r\n      // Update the quest in the UI with the returned values\r\n      quest.completed = result.completed;\r\n      quest.value_achieved = result.value_achieved;\r\n\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n      // Handle streak calculation differently based on whether we're in today's view or a previous day\r\n      if (isTodaySelected) {\r\n        // For today's view, manually calculate the streak by going backward from today\r\n        // until we find a non-completed progress entry\r\n\r\n        // Use the streak from the result (from Supabase)\r\n        let streak = result.streak;\r\n\r\n        // Get the current completed state after the update\r\n        const isCompletedNow = quest.completed;\r\n\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status: was ${wasCompletedBefore}, now ${isCompletedNow}`);\r\n\r\n        // Only update streak if the completion status has changed\r\n        if (wasCompletedBefore !== isCompletedNow) {\r\n          if (isCompletedNow) {\r\n            // Changed from incomplete to complete\r\n            streak++;\r\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from incomplete to complete, streak increased to ${streak}`);\r\n          } else {\r\n            // Changed from complete to incomplete\r\n            streak = Math.max(0, streak - 1);\r\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from complete to incomplete, streak decreased to ${streak}`);\r\n          }\r\n\r\n          // Update the streak in the database\r\n          this.questService.updateQuestStreak(quest.id!, streak).subscribe({\r\n            next: () => {\r\n              console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${streak}`);\r\n\r\n              // Update the quest in the cache\r\n              const dateKey = this.formatDate(this.selectedDate);\r\n              if (this.questCache[dateKey]) {\r\n                const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);\r\n                if (cachedQuestIndex >= 0) {\r\n                  this.questCache[dateKey][cachedQuestIndex].streak = streak;\r\n                }\r\n              }\r\n            },\r\n            error: (error: any) => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\r\n          });\r\n        } else {\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status did not change, keeping streak at ${streak}`);\r\n        }\r\n      } else {\r\n        // For previous days, recalculate streak for today\r\n        console.log(`TodayPage: Quest toggled in previous day (${this.formatDate(this.selectedDate)}), recalculating streak for today`);\r\n\r\n        // Get the quest details\r\n        this.questService.getQuest(quest.id!).subscribe(questDetails => {\r\n          if (!questDetails) {\r\n            console.error(`TodayPage: Could not get quest details for ${quest.id}`);\r\n            return;\r\n          }\r\n\r\n          // Calculate the streak for today using our streak calculator\r\n          this.streakCalculator.calculateStreak(this.userId!, quest.id!)\r\n            .then(calculatedStreak => {\r\n              console.log(`TodayPage: Recalculated streak for quest ${quest.id} for today: ${calculatedStreak}`);\r\n\r\n              // Update the streak in the database\r\n              this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe({\r\n                next: () => {\r\n                  console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${calculatedStreak}`);\r\n\r\n                  // Clear today's cache for next time\r\n                  const todayString = this.formatDate(today);\r\n                  console.log('TodayPage: Clearing today\\'s cache to force reload of updated streak next time today is viewed');\r\n                  delete this.questCache[todayString];\r\n\r\n                  // If we have today's date in the week view, update its progress\r\n                  const todayIndex = this.weekDates.findIndex(wd => wd.date === todayString);\r\n                  if (todayIndex >= 0) {\r\n                    delete this.weekProgressCache[todayString];\r\n                    this.updateProgressRingForDate(todayString);\r\n                  }\r\n                },\r\n                error: (error: any) => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\r\n              });\r\n            })\r\n            .catch(error => {\r\n              console.error(`TodayPage: Error calculating streak for quest ${quest.id}:`, error);\r\n            });\r\n        });\r\n      }\r\n\r\n      // Update the UI element for this quest\r\n      this.updateQuestUI(quest);\r\n\r\n      // Cache the updated quest data and update progress ring\r\n      const dateKey = this.formatDate(this.selectedDate);\r\n      if (this.questCache[dateKey]) {\r\n        // Find and update the quest in the cache\r\n        const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);\r\n        if (cachedQuestIndex >= 0) {\r\n          this.questCache[dateKey][cachedQuestIndex] = { ...quest };\r\n        }\r\n      }\r\n\r\n      // Clear the cache for this date to force a refresh\r\n      delete this.weekProgressCache[dateKey];\r\n\r\n      // Update the progress ring for this date\r\n      this.updateProgressRingForDate(dateKey);\r\n\r\n      // Check if all quests are completed\r\n      this.checkAllQuestsCompleted(this.quests);\r\n    } catch (error) {\r\n      console.error(`TodayPage: Error updating quest progress:`, error);\r\n    } finally {\r\n      // Reset flag for this specific quest\r\n      delete this.updatingQuestIds[quest.id];\r\n    }\r\n  }\r\n\r\n  // Helper method to update the progress ring for a specific date\r\n  private updateProgressRingForDate(dateKey: string) {\r\n    // Find the index of this date in weekDates\r\n    const index = this.weekDates.findIndex(wd => wd.date === dateKey);\r\n    if (index < 0) return;\r\n\r\n    // If we have cached quests for this date, use them to calculate progress\r\n    if (this.questCache[dateKey]) {\r\n      const cachedQuests = this.questCache[dateKey];\r\n      const totalQuests = cachedQuests.length;\r\n      const completedQuests = cachedQuests.filter(q => q.completed).length;\r\n\r\n      // Cache the progress\r\n      this.weekProgressCache[dateKey] = {\r\n        total: totalQuests,\r\n        completed: completedQuests\r\n      };\r\n\r\n      // Update the week date\r\n      this.weekDates[index].total_quests = totalQuests;\r\n      this.weekDates[index].completed_quests = completedQuests;\r\n      this.weekDates[index].completion_percentage = totalQuests > 0\r\n        ? Math.round((completedQuests / totalQuests) * 100)\r\n        : 0;\r\n\r\n      return;\r\n    }\r\n\r\n    // If no cached quests, fetch from server\r\n    if (this.userId) {\r\n      const date = new Date(dateKey);\r\n\r\n      this.questService.getQuestProgressForDate(this.userId, date).pipe(\r\n        take(1)\r\n      ).subscribe(progressList => {\r\n        this.questService.getQuests(this.userId!).pipe(\r\n          take(1)\r\n        ).subscribe(quests => {\r\n          // Filter active quests for this date\r\n          const activeQuests = this.filterQuestsForDate(quests, date);\r\n\r\n          // Count completed quests\r\n          const questIds = activeQuests.map(q => q.id);\r\n          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\r\n          const completedQuests = relevantProgress.filter(p => p.completed).length;\r\n          const totalQuests = activeQuests.length;\r\n\r\n          // Cache the progress\r\n          this.weekProgressCache[dateKey] = {\r\n            total: totalQuests,\r\n            completed: completedQuests\r\n          };\r\n\r\n          // Update the week date\r\n          this.weekDates[index].total_quests = totalQuests;\r\n          this.weekDates[index].completed_quests = completedQuests;\r\n          this.weekDates[index].completion_percentage = totalQuests > 0\r\n            ? Math.round((completedQuests / totalQuests) * 100)\r\n            : 0;\r\n        });\r\n      });\r\n    }\r\n  }\r\n\r\n  // Helper method to update the UI for a specific quest\r\n  private updateQuestUI(quest: QuestDisplay) {\r\n    // Find the quest element in the DOM\r\n    const questElement = document.querySelector(`[data-quest-id=\"${quest.id}\"]`);\r\n    if (!questElement) {\r\n      console.error(`TodayPage: Could not find quest element for ID: ${quest.id}`);\r\n      return;\r\n    }\r\n\r\n    // Update the completed class\r\n    if (quest.completed) {\r\n      questElement.classList.add('completed');\r\n    } else {\r\n      questElement.classList.remove('completed');\r\n    }\r\n\r\n    // Update the streak display - only show streak for today\r\n    const streakElements = questElement.querySelectorAll('.quest-streak');\r\n    if (streakElements && streakElements.length > 0) {\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n      // Only show streak for today's view\r\n      if (isTodaySelected) {\r\n        const streakValue = quest.streak || 0;\r\n        console.log(`TodayPage: Quest ${quest.id}, completed: ${quest.completed}, streak: ${streakValue}`);\r\n\r\n        // Update all streak elements (there might be multiple due to ngIf)\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            // Make sure the streak is visible\r\n            (element as HTMLElement).style.display = 'block';\r\n            element.textContent = `🔥${streakValue}d`;\r\n          }\r\n        });\r\n      } else {\r\n        // Hide streak for previous days\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            (element as HTMLElement).style.display = 'none';\r\n            element.textContent = '';\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // Update the progress text\r\n    const progressText = questElement.querySelector('.progress-text');\r\n    if (progressText) {\r\n      const isTimeUnit = progressText.parentElement?.classList.contains('progress-time');\r\n      const unitSuffix = isTimeUnit ? 'm' : '';\r\n      const goalUnitSuffix = quest.goal_unit !== 'count' && !isTimeUnit ? ` ${quest.goal_unit}` : '';\r\n\r\n      progressText.textContent = `${quest.value_achieved}${unitSuffix}/${quest.goal_value}${unitSuffix}${goalUnitSuffix}`;\r\n    }\r\n\r\n    console.log(`TodayPage: Updated UI for quest ${quest.id}`);\r\n  }\r\n\r\n  // Update slider background based on value\r\n  updateSliderBackground(slider: HTMLInputElement | EventTarget | null) {\r\n    if (!slider) {\r\n      return;\r\n    }\r\n\r\n    // Handle different types of slider elements\r\n    let sliderElement: HTMLElement;\r\n    let sliderValue = 0;\r\n    let minValue = 0;\r\n    let maxValue = 100;\r\n    let sliderQuestId = '';\r\n\r\n    if (slider instanceof HTMLInputElement) {\r\n      // Standard HTML input range\r\n      sliderElement = slider;\r\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\r\n      sliderValue = parseInt(slider.value);\r\n      minValue = parseInt(slider.min);\r\n      maxValue = parseInt(slider.max);\r\n    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n      // Ionic range element\r\n      sliderElement = slider;\r\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\r\n\r\n      // Get the value from the element's properties or attributes\r\n      const valueAttr = slider.getAttribute('value') || '0';\r\n      const minAttr = slider.getAttribute('min') || '0';\r\n      const maxAttr = slider.getAttribute('max') || '100';\r\n\r\n      sliderValue = parseInt(valueAttr);\r\n      minValue = parseInt(minAttr);\r\n      maxValue = parseInt(maxAttr);\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    if (!sliderQuestId) {\r\n      return;\r\n    }\r\n\r\n    // Calculate the percentage value\r\n    const percentage = maxValue > minValue ?\r\n      ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n    // For Ionic range, we need to set the CSS variable\r\n    if (sliderElement.tagName === 'ION-RANGE') {\r\n      sliderElement.style.setProperty('--progress-value', `${percentage}%`);\r\n    } else {\r\n      // Set the background directly with hardcoded colors for standard HTML input\r\n      sliderElement.style.background =\r\n        `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\r\n    }\r\n\r\n    // Add a data attribute to track the current value\r\n    sliderElement.setAttribute('data-current-value', sliderValue.toString());\r\n  }\r\n\r\n  // Map to track which side quests are currently being toggled\r\n  private togglingSideQuestIds: { [questId: string]: boolean } = {};\r\n\r\n  /**\r\n   * Toggle side quest completion\r\n   * This matches the Django implementation in toggle_daily_side_quest view\r\n   * Side quests are always toggled between 0 and goal value\r\n   */\r\n  async toggleSideQuest(sideQuest: DailyQuest) {\r\n    if (!this.userId || !sideQuest.id) return;\r\n\r\n    // Check if this specific side quest is already being toggled\r\n    if (this.togglingSideQuestIds[sideQuest.id]) {\r\n      console.log(`TodayPage: Side quest ${sideQuest.id} is already being toggled, ignoring duplicate call`);\r\n      return;\r\n    }\r\n\r\n    // Set flag for this specific side quest\r\n    this.togglingSideQuestIds[sideQuest.id] = true;\r\n    console.log(`TodayPage: Starting toggle for side quest ${sideQuest.id}`);\r\n\r\n    try {\r\n      // For side quests, we always toggle between 0 and goal value\r\n      // This matches the Django implementation where side quests are either completed or not\r\n      console.log(`TodayPage: Click event on side quest ${sideQuest.id}`);\r\n\r\n      // Toggle the value immediately for better UI feedback\r\n      const newValue = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\r\n      const newCompletedState = newValue === sideQuest.current_quest.goal_value;\r\n\r\n      // Update local state first for immediate feedback\r\n      sideQuest.value_achieved = newValue;\r\n      sideQuest.completed = newCompletedState;\r\n\r\n      console.log(`TodayPage: Updated side quest ${sideQuest.id} value to ${sideQuest.value_achieved}, completed: ${sideQuest.completed}`);\r\n\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isToday = selectedDate.getTime() === today.getTime();\r\n\r\n      // Only allow toggling side quests for today\r\n      if (!isToday) {\r\n        console.log(`TodayPage: Cannot toggle side quest for past date: ${this.formatDate(this.selectedDate)}`);\r\n        delete this.togglingSideQuestIds[sideQuest.id];\r\n        return;\r\n      }\r\n\r\n      // Update the UI element immediately for better feedback\r\n      this.updateSideQuestUI(sideQuest);\r\n\r\n      try {\r\n        const result = await this.sideQuestService.toggleSideQuestCompletion(\r\n          sideQuest.id,\r\n          this.userId,\r\n          this.selectedDate // Pass the selected date\r\n        );\r\n\r\n        console.log(`TodayPage: Successfully toggled side quest ${sideQuest.id}`);\r\n        console.log(`TodayPage: Updated values:`, result);\r\n\r\n        // Update the side quest in the UI with the returned values from the server\r\n        sideQuest.completed = result.completed;\r\n        sideQuest.value_achieved = result.value_achieved;\r\n        sideQuest.streak = result.streak;\r\n\r\n        // Update the UI element with the updated streak\r\n        this.updateSideQuestUI(sideQuest);\r\n\r\n        // Update the week date progress for the selected date\r\n        // Clear the cache for this date to force a refresh\r\n        const dateKey = this.formatDate(this.selectedDate);\r\n        delete this.weekProgressCache[dateKey];\r\n\r\n        // Update the progress ring for this date\r\n        this.updateProgressRingForDate(dateKey);\r\n\r\n        // Check if all quests are completed\r\n        this.checkAllQuestsCompleted(this.quests);\r\n\r\n        // Reset flag for this specific side quest\r\n        delete this.togglingSideQuestIds[sideQuest.id];\r\n        console.log(`TodayPage: Finished toggle for side quest ${sideQuest.id}`);\r\n      } catch (error) {\r\n        console.error(`TodayPage: Error toggling side quest ${sideQuest.id}:`, error);\r\n\r\n        // Revert the local state if the server update failed\r\n        sideQuest.value_achieved = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\r\n        sideQuest.completed = sideQuest.value_achieved === sideQuest.current_quest.goal_value;\r\n        this.updateSideQuestUI(sideQuest);\r\n\r\n        delete this.togglingSideQuestIds[sideQuest.id];\r\n      }\r\n    } catch (error) {\r\n      console.error(`TodayPage: Error in toggleSideQuest for ${sideQuest.id}:`, error);\r\n      delete this.togglingSideQuestIds[sideQuest.id];\r\n    }\r\n  }\r\n\r\n  // Helper method to update the UI for a specific side quest\r\n  private updateSideQuestUI(sideQuest: DailyQuest) {\r\n    // Find the side quest element in the DOM\r\n    const questElement = document.querySelector(`.daily-side-quest [data-quest-id=\"${sideQuest.id}\"]`);\r\n    if (!questElement) {\r\n      console.error(`TodayPage: Could not find side quest element for ID: ${sideQuest.id}`);\r\n      return;\r\n    }\r\n\r\n    // Update the completed class\r\n    if (sideQuest.completed) {\r\n      questElement.classList.add('completed');\r\n    } else {\r\n      questElement.classList.remove('completed');\r\n    }\r\n\r\n    // Update the streak display - only show streak for today\r\n    const streakElements = questElement.querySelectorAll('.quest-streak');\r\n    if (streakElements && streakElements.length > 0) {\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n      // Only show streak for today's view\r\n      if (isTodaySelected) {\r\n        const streakValue = sideQuest.streak || 0;\r\n        console.log(`TodayPage: Side quest ${sideQuest.id}, completed: ${sideQuest.completed}, streak: ${streakValue}`);\r\n\r\n        // Update all streak elements (there might be multiple due to ngIf)\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            // Make sure the streak is visible\r\n            (element as HTMLElement).style.display = 'block';\r\n            element.textContent = `🔥${streakValue}d`;\r\n          }\r\n        });\r\n      } else {\r\n        // Hide streak for previous days\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            (element as HTMLElement).style.display = 'none';\r\n            element.textContent = '';\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // Update the progress text\r\n    const progressText = questElement.querySelector('.progress-text');\r\n    if (progressText) {\r\n      const goalUnit = sideQuest.current_quest.goal_unit !== 'count' ? ` ${sideQuest.current_quest.goal_unit}` : '';\r\n      progressText.textContent = `${sideQuest.value_achieved}/${sideQuest.current_quest.goal_value}${goalUnit}`;\r\n    }\r\n\r\n    // Force a repaint to ensure the UI updates\r\n    setTimeout(() => {\r\n      if (questElement.parentElement) {\r\n        const display = questElement.parentElement.style.display;\r\n        questElement.parentElement.style.display = 'none';\r\n        // Force a reflow\r\n        void questElement.parentElement.offsetHeight;\r\n        questElement.parentElement.style.display = display;\r\n      }\r\n    }, 0);\r\n\r\n    console.log(`TodayPage: Updated UI for side quest ${sideQuest.id}`);\r\n  }\r\n\r\n  openAddQuestModal(event: Event) {\r\n    event.preventDefault();\r\n    this.showAddQuestModal = true;\r\n    this.newQuest = this.getEmptyQuest();\r\n    this.selectedDaysOfWeek = [];\r\n    this.selectedDaysOfMonth = [];\r\n\r\n    // Reset hasHighPriorityQuest flag\r\n    this.hasHighPriorityQuest = false;\r\n\r\n    // Reset animation states\r\n    this.resetAnimationStates();\r\n\r\n    // Start quest type animation after modal opens\r\n    setTimeout(() => {\r\n      console.log('Setting questTypeAnimated to true');\r\n      this.questTypeAnimated = true;\r\n    }, 300);\r\n  }\r\n\r\n  resetAnimationStates() {\r\n    this.questTypeAnimated = false;\r\n    this.questTypeAnimating = false;\r\n    this.selectedQuestType = '';\r\n    this.categoryAnimated = false;\r\n    this.categoryAnimating = false;\r\n    this.categorySelected = false;\r\n    this.selectedCategory = '';\r\n    this.priorityAnimated = false;\r\n    this.priorityAnimating = false;\r\n    this.goalAnimated = false;\r\n  }\r\n\r\n  closeAddQuestModal() {\r\n    this.showAddQuestModal = false;\r\n    // Reset form state\r\n    this.newQuest = this.getEmptyQuest();\r\n    this.selectedDaysOfWeek = [];\r\n    this.selectedDaysOfMonth = [];\r\n    this.hasHighPriorityQuest = false;\r\n    this.resetAnimationStates();\r\n  }\r\n\r\n  // Animation methods\r\n  selectQuestType(type: 'build' | 'quit') {\r\n    this.selectedQuestType = type;\r\n    this.questTypeAnimating = true;\r\n\r\n    // Start slide out animation\r\n    setTimeout(() => {\r\n      this.newQuest.quest_type = type as any;\r\n\r\n      // After quest type is set, trigger category animation\r\n      setTimeout(() => {\r\n        this.categoryAnimated = true;\r\n      }, 100);\r\n    }, 300); // Half of the animation duration\r\n  }\r\n\r\n  selectCategory(category: string) {\r\n    this.selectedCategory = category;\r\n    this.categoryAnimating = true;\r\n\r\n    // Start slide out animation based on category\r\n    setTimeout(() => {\r\n      this.newQuest.category = category as any;\r\n      this.categorySelected = true;\r\n      this.checkCategoryPriority({ detail: { value: category } });\r\n\r\n      // After category is set, trigger priority animation\r\n      setTimeout(() => {\r\n        this.priorityAnimated = true;\r\n      }, 100);\r\n    }, 300); // Half of the animation duration\r\n  }\r\n\r\n  selectPriority(priority: 'basic' | 'high') {\r\n    if (priority === 'high' && this.hasHighPriorityQuest) {\r\n      return; // Don't allow high priority if already exists\r\n    }\r\n\r\n    this.priorityAnimating = true;\r\n\r\n    setTimeout(() => {\r\n      this.newQuest.priority = priority as any;\r\n    }, 300);\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.currentStep < this.totalSteps) {\r\n      this.currentStep++;\r\n\r\n      // If moving to step 5 (goal section), trigger goal animation\r\n      if (this.currentStep === 5) {\r\n        setTimeout(() => {\r\n          this.goalAnimated = true;\r\n        }, 200);\r\n      }\r\n    }\r\n  }\r\n\r\n  prevStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  moveCaretToEnd() {\r\n    setTimeout(() => {\r\n      this.emojiInput.getInputElement().then((input: HTMLInputElement) => {\r\n        const pos = input.value.length;\r\n        input.setSelectionRange(pos, pos);\r\n        input.scrollLeft = input.scrollWidth;\r\n      });\r\n    }, 100);\r\n  }\r\n\r\n  get progress(): number {\r\n    return (this.currentStep) / (this.totalSteps);\r\n  }\r\n\r\n\r\n  async createQuest() {\r\n    if (!this.userId || !this.newQuest.name || !this.newQuest.emoji || !this.newQuest.quest_type ||\r\n      !this.newQuest.category || !this.newQuest.goal_value || !this.newQuest.goal_unit || !this.newQuest.goal_period) {\r\n      console.error('TodayPage: Cannot create quest - missing required fields');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      if (this.newQuest.goal_period === 'week' && this.selectedDaysOfWeek.length > 0) {\r\n        this.newQuest.task_days_of_week = this.selectedDaysOfWeek.join(',');\r\n      } else if (this.newQuest.goal_period === 'month' && this.selectedDaysOfMonth.length > 0) {\r\n        this.newQuest.task_days_of_month = this.selectedDaysOfMonth.join(',');\r\n      }\r\n\r\n      const { data: userProfile, error: userError } = await this.supabaseService.getClient()\r\n        .from('profiles')\r\n        .select('id')\r\n        .eq('id', this.userId)\r\n        .single();\r\n\r\n      if (userError || !userProfile) {\r\n        console.error('TodayPage: User profile not found:', userError || 'No profile found');\r\n        throw new Error('User profile not found. Please ensure you are logged in.');\r\n      }\r\n\r\n      const questToCreate: Omit<Quest, 'id' | 'streak' | 'created_at'> = {\r\n        name: this.newQuest.name || '',\r\n        description: this.newQuest.description || '',\r\n        quest_type: this.newQuest.quest_type || 'build',\r\n        goal_value: this.newQuest.goal_value || 1,\r\n        goal_unit: this.newQuest.goal_unit || 'count',\r\n        goal_period: this.newQuest.goal_period || 'day',\r\n        priority: this.newQuest.priority || 'basic',\r\n        category: this.newQuest.category || 'strength',\r\n        emoji: this.newQuest.emoji || '🎯',\r\n        task_days_of_week: this.newQuest.task_days_of_week || '',\r\n        task_days_of_month: this.newQuest.task_days_of_month || '',\r\n        user_id: this.userId,\r\n        active: true\r\n      };\r\n\r\n      try {\r\n        const questId = await this.questService.createQuest(questToCreate);\r\n\r\n        if (this.newQuest.quest_type === 'quit') {\r\n\r\n          await this.questService.toggleQuestCompletion(\r\n            this.userId,\r\n            questId,\r\n            new Date(),\r\n            0,\r\n            { ...questToCreate, id: questId } as Quest\r\n          );\r\n        }\r\n\r\n        const dateKey = this.formatDate(this.selectedDate);\r\n        delete this.questCache[dateKey];\r\n        delete this.weekProgressCache[dateKey];\r\n\r\n        this.closeAddQuestModal();\r\n        this.loadData();\r\n      } catch (questError: any) {\r\n        console.error('TodayPage: Error creating quest:', questError);\r\n\r\n        if (questError.message && questError.message.includes('foreign key constraint')) {\r\n          alert('Database configuration issue detected. Please run the fix_quest_constraints.sql script in the Supabase SQL Editor to fix the foreign key constraints.');\r\n        } else if (questError.message && questError.message.includes('fix_quest_constraints.sql')) {\r\n          alert(questError.message);\r\n        } else {\r\n          alert(`Error creating quest: ${questError.message}`);\r\n        }\r\n      }\r\n    } catch (error: any) {\r\n      console.error('TodayPage: Error in createQuest:', error);\r\n      alert(`Error: ${error.message || 'Unknown error occurred'}`);\r\n    }\r\n  }\r\n\r\n  updateDaysOfWeek(day: string) {\r\n    const index = this.selectedDaysOfWeek.indexOf(day);\r\n\r\n    if (index !== -1) {\r\n      this.selectedDaysOfWeek.splice(index, 1);\r\n    } else {\r\n      this.selectedDaysOfWeek.push(day);\r\n    }\r\n  }\r\n\r\n  updateDaysOfMonth(event: any, day: number) {\r\n    // Handle both standard Event and Ionic's CustomEvent\r\n    let isChecked = false;\r\n\r\n    if (event.detail !== undefined) {\r\n      // Ionic checkbox event\r\n      isChecked = event.detail.checked;\r\n    } else if (event.target instanceof HTMLInputElement) {\r\n      // Standard checkbox event\r\n      isChecked = event.target.checked;\r\n    }\r\n\r\n    if (isChecked) {\r\n      this.selectedDaysOfMonth.push(day);\r\n    } else {\r\n      const index = this.selectedDaysOfMonth.indexOf(day);\r\n      if (index !== -1) {\r\n        this.selectedDaysOfMonth.splice(index, 1);\r\n      }\r\n    }\r\n\r\n    console.log(`TodayPage: Updated days of month: ${this.selectedDaysOfMonth.join(', ')}`);\r\n  }\r\n\r\n  updatePeriodDisplay() {\r\n    // Reset selections when period changes\r\n    this.selectedDaysOfWeek = [];\r\n    this.selectedDaysOfMonth = [];\r\n    console.log(`TodayPage: Period changed to ${this.newQuest.goal_period}, reset selections`);\r\n  }\r\n\r\n  checkCategoryPriority(event?: any) {\r\n    if (!this.userId || !this.newQuest.category) return;\r\n\r\n    // If this is an Ionic event, make sure we have the latest category value\r\n    if (event && event.detail) {\r\n      this.newQuest.category = event.detail.value;\r\n      console.log(`TodayPage: Category changed to ${this.newQuest.category} via Ionic event`);\r\n    }\r\n\r\n    // Check if user already has a high priority quest in this category\r\n    this.questService.getQuests(this.userId).pipe(\r\n      take(1),\r\n      map(quests => {\r\n        return quests.some(q =>\r\n          q.category === this.newQuest.category &&\r\n          q.priority === 'high' &&\r\n          q.active\r\n        );\r\n      })\r\n    ).subscribe({\r\n      next: (hasHighPriority) => {\r\n        this.hasHighPriorityQuest = hasHighPriority;\r\n\r\n        // If user already has a high priority quest, set this one to basic\r\n        if (hasHighPriority) {\r\n          this.newQuest.priority = 'basic';\r\n        }\r\n\r\n        console.log(`TodayPage: Category ${this.newQuest.category} has high priority quest: ${hasHighPriority}`);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Check if all quests are completed for today and show celebration if enabled\r\n   */\r\n  checkAllQuestsCompleted(quests: QuestDisplay[]) {\r\n    // Only check for today's date\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(this.selectedDate);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n    const todayStr = this.formatDate(today);\r\n\r\n    if (!isTodaySelected || !this.currentUser) {\r\n      return;\r\n    }\r\n\r\n    // Check if celebration has already been shown for today\r\n    const celebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\r\n    if (celebrationShown) {\r\n      console.log('TodayPage: Celebration already shown for today:', todayStr);\r\n      return;\r\n    }\r\n\r\n    // Check if all quests are completed\r\n    const allQuestsCompleted = quests.length > 0 && quests.every(quest => quest.completed);\r\n\r\n    // Check if side quest is completed (if enabled)\r\n    const sideQuestCompleted = !this.showSidequests || !this.dailyQuest || this.dailyQuest.completed;\r\n\r\n    // Show celebration if all quests and side quests are completed and celebration is enabled\r\n\r\n    if (allQuestsCompleted && sideQuestCompleted && this.currentUser.show_celebration) {\r\n      // Make sure we have the latest user data\r\n      this.userService.getUserById(this.userId!).subscribe(userData => {\r\n        if (userData) {\r\n          this.currentUser = userData;\r\n        }\r\n\r\n        // Show the celebration\r\n        this.showCelebration = true;\r\n\r\n        // Save today's date to localStorage\r\n        localStorage.setItem(`celebration_shown_${todayStr}`, 'true');\r\n\r\n        // Update our tracking array\r\n        if (!this.celebrationShownDates.includes(todayStr)) {\r\n          this.celebrationShownDates.push(todayStr);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Close the celebration modal\r\n   */\r\n  closeCelebration() {\r\n    this.showCelebration = false;\r\n  }\r\n\r\n\r\n\r\n  // Helper methods\r\n  formatDate(date: Date): string {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  isSameDay(date1: Date, date2: Date): boolean {\r\n    return date1.getFullYear() === date2.getFullYear() &&\r\n      date1.getMonth() === date2.getMonth() &&\r\n      date1.getDate() === date2.getDate();\r\n  }\r\n\r\n  getToday(): Date {\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    return today;\r\n  }\r\n\r\n  // Convert Django day index (0=Monday, 6=Sunday) to short day name\r\n  private getDayNameShort(djangoDayIndex: number): string {\r\n    // Map Django day index to day name\r\n    const dayMap = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];\r\n    return dayMap[djangoDayIndex];\r\n  }\r\n\r\n  // Convert Django day index (0=Monday, 6=Sunday) to full day name\r\n  private getDayNameFull(djangoDayIndex: number): string {\r\n    // Map Django day index to full day name\r\n    const dayMap = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\r\n    return dayMap[djangoDayIndex];\r\n  }\r\n\r\n  private getEmptyQuest(): Partial<Quest> {\r\n    return {\r\n      name: '',\r\n      description: '',\r\n      quest_type: '' as any, // Empty by default, user must select\r\n      goal_value: 1,\r\n      goal_unit: 'count' as QuestGoalUnit,\r\n      goal_period: 'day' as QuestPeriod,\r\n      priority: 'basic' as QuestPriority, // Default to basic priority\r\n      category: '' as QuestCategory,\r\n      emoji: '🎯'\r\n    };\r\n  }\r\n\r\n  // Helper methods for the template\r\n  // Note: Progress slider background is now handled via CSS variables\r\n}\r\n", "<ion-content class=\"ion-padding\" [fullscreen]=\"true\">\r\n  <div class=\"background-container\">\r\n    <div class=\"gradient-bg\"></div>\r\n    <div class=\"celestial-body\"></div>\r\n  </div>\r\n  <ion-header class=\"ion-no-border\">\r\n    <ion-toolbar>\r\n      <app-header [headerText]=\"headerText\"></app-header>\r\n      <ion-row class=\"week-row ion-padding-top\">\r\n        <div class=\"day-container\" *ngFor=\"let date of weekDates; let i = index\">\r\n          <ion-text class=\"day-name\" [class.active]=\"date.is_today\"\r\n            [class.selected]=\"date.is_selected && !date.is_today\" [class.unselected]=\"!date.is_selected\">\r\n            {{ ['M', 'T', 'W', 'T', 'F', 'S', 'S'][i] }}\r\n          </ion-text>\r\n          <div class=\"date\" [class.selected]=\"date.is_selected\" [class.disabled]=\"date.is_future\"\r\n            (click)=\"!date.is_future && selectDate(date)\">\r\n            <svg class=\"date-progress\" viewBox=\"0 0 36 36\">\r\n              <circle cx=\"18\" cy=\"18\" r=\"13\" stroke-dasharray=\"81.68, 81.68\" class=\"background-circle\"></circle>\r\n\r\n              <circle cx=\"18\" cy=\"18\" r=\"13\" *ngIf=\"!date.is_future\"\r\n                [attr.stroke-dasharray]=\"(date.completion_percentage * 81.68 / 100) + ', 81.68'\"\r\n                [attr.data-date]=\"date.date\" class=\"progress-circle\" [class.low]=\"date.completion_percentage < 50\">\r\n              </circle>\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      </ion-row>\r\n    </ion-toolbar>\r\n  </ion-header>\r\n  <ion-grid>\r\n    <ion-row class=\"ion-justify-content-center\">\r\n      <div class=\"heartbeat-circle gradient-text\"></div>\r\n    </ion-row>\r\n    <ion-row class=\"add-quest\">\r\n      <ion-col>\r\n        <h2>Quests</h2>\r\n      </ion-col>\r\n      <ion-col>\r\n        <ion-button fill=\"clear\" id=\"add-quest-btn\" class=\"add-quest-btn\" (click)=\"openAddQuestModal($event)\">\r\n          + Add Quest\r\n        </ion-button>\r\n      </ion-col>\r\n    </ion-row>\r\n    <ion-row class=\"quests\">\r\n      <ion-col>\r\n        <ion-card *ngif=\"quests.length === 0\" class=\"ion-text-center no-quest-card\">\r\n          <ion-card-header>\r\n            <h2>No quests found..</h2>\r\n          </ion-card-header>\r\n          <ion-card-content>\r\n            <ion-row>\r\n              <ion-col size=\"8\">\r\n                <ion-text>No quests found. Try adding a quest ;)</ion-text>\r\n              </ion-col>\r\n              <ion-col size=\"4\">\r\n                <ion-icon name=\"warning\"></ion-icon>\r\n              </ion-col>\r\n            </ion-row>\r\n          </ion-card-content>\r\n        </ion-card>\r\n        <ion-card *ngFor=\"let quest of quests\" class=\"quest-item ion-margin-bottom\" [class.completed]=\"quest.completed\"\r\n          [attr.data-quest-id]=\"quest.id\" [attr.data-regular-quest]=\"true\" (click)=\"toggleQuest(quest)\">\r\n          <ion-row>\r\n            <ion-col size=\"2\">\r\n              <div class=\"quest-icon\">{{ quest.emoji }}</div>\r\n            </ion-col>\r\n            <ion-col size=\"8\" class=\"quest-info\">\r\n              <h3>{{ quest.name }}</h3>\r\n              <ion-text>{{ quest.description }}</ion-text>\r\n              <div class=\"progress-container\">\r\n                <div class=\"progress-time\"\r\n                  *ngIf=\"quest.goal_unit === 'time' || quest.goal_unit === 'min' || quest.goal_unit === 'hr' || quest.goal_unit === 'sec'\">\r\n                  <ion-range min=\"0\" [max]=\"quest.goal_value\" [(ngModel)]=\"quest.value_achieved\" class=\"progress-slider\"\r\n                    [attr.data-quest-id]=\"quest.id\" [attr.data-quest-type]=\"quest.quest_type\" [step]=\"1\" snaps=\"true\"\r\n                    ticks=\"false\" snaps-per-step=\"true\" (ionChange)=\"updateQuestProgress(quest, $event)\"\r\n                    (ionInput)=\"$event.target && updateSliderBackground($event.target)\"\r\n                    style=\"--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%\">\r\n                  </ion-range>\r\n                  <ion-text class=\"progress-text\">\r\n                    {{ quest.value_achieved }}{{ quest.goal_unit === 'min' ? 'm'\r\n                    : quest.goal_unit === 'hr' ? 'h' : 's' }}/{{\r\n                    quest.goal_value }}{{ quest.goal_unit === 'min' ? 'm' :\r\n                    quest.goal_unit === 'hr' ? 'h' : 's' }}\r\n                  </ion-text>\r\n                </div>\r\n                <div class=\"progress\"\r\n                  *ngIf=\"quest.goal_unit !== 'time' && quest.goal_unit !== 'min' && quest.goal_unit !== 'hr' && quest.goal_unit !== 'sec'\">\r\n                  <ion-range min=\"0\" [max]=\"quest.goal_value\" [(ngModel)]=\"quest.value_achieved\" class=\"progress-slider\"\r\n                    [attr.data-quest-id]=\"quest.id\" [attr.data-quest-type]=\"quest.quest_type\" [step]=\"1\" snaps=\"true\"\r\n                    ticks=\"false\" snaps-per-step=\"true\" (ionChange)=\"updateQuestProgress(quest, $event)\"\r\n                    (ionInput)=\"$event.target && updateSliderBackground($event.target)\"\r\n                    style=\"--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%\">\r\n                  </ion-range>\r\n                  <ion-text class=\"progress-text\">\r\n                    {{ quest.value_achieved }}/{{ quest.goal_value }}\r\n                    <ng-container *ngIf=\"quest.goal_unit !== 'count'\">\r\n                      {{ quest.goal_unit }}\r\n                    </ng-container>\r\n                  </ion-text>\r\n                </div>\r\n              </div>\r\n            </ion-col>\r\n            <ion-col size=\"2\">\r\n              <ion-text class=\"quest-streak\" *ngIf=\"isSameDay(selectedDate, getToday())\">\r\n                🔥{{ quest.streak }}d\r\n              </ion-text>\r\n            </ion-col>\r\n          </ion-row>\r\n        </ion-card>\r\n      </ion-col>\r\n    </ion-row>\r\n    <ion-row class=\"ion-padding-top\">\r\n      <ion-col>\r\n        <h2>Daily Side Quest</h2>\r\n      </ion-col>\r\n    </ion-row>\r\n    <ion-row class=\"quests\">\r\n      <ion-col>\r\n        <ion-card *ngIf=\"dailyQuest && dailyQuest.current_quest\" class=\"quest-item\"\r\n          [class.completed]=\"dailyQuest.completed\" [attr.data-quest-id]=\"dailyQuest.id\" [attr.data-side-quest]=\"true\"\r\n          (click)=\"toggleSideQuest(dailyQuest)\">\r\n          <ion-row>\r\n            <ion-col size=\"2\">\r\n              <div class=\"quest-icon\">{{ dailyQuest.emoji }}</div>\r\n            </ion-col>\r\n            <ion-col class=\"quest-info\" size=\"8\">\r\n              <h3>{{ dailyQuest.current_quest.name }}</h3>\r\n              <ion-text class=\"quest-description\">{{ dailyQuest.current_quest.description }}</ion-text>\r\n              <!-- <div class=\"progress-text\">\r\n              {{ dailyQuest.value_achieved }}/{{ dailyQuest.current_quest.goal_value }}\r\n              <ng-container *ngIf=\"dailyQuest.current_quest.goal_unit !== 'count'\">\r\n                {{ dailyQuest.current_quest.goal_unit }}\r\n              </ng-container>\r\n            </div> -->\r\n            </ion-col>\r\n            <ion-col size=\"2\">\r\n              <div class=\"quest-streak\" *ngIf=\"isSameDay(selectedDate, getToday())\">\r\n                🔥{{ dailyQuest.streak }}d\r\n              </div>\r\n            </ion-col>\r\n          </ion-row>\r\n        </ion-card>\r\n        <ion-card class=\"quest-item\" *ngIf=\"!dailyQuest\">\r\n          <ion-card-header>\r\n            <ion-card-title> Daily Side Quest </ion-card-title>\r\n          </ion-card-header>\r\n          <ion-card-content>\r\n            <ion-text>\r\n              No daily side quests are currently available. Please check back\r\n              later or contact an administrator.\r\n            </ion-text>\r\n          </ion-card-content>\r\n        </ion-card>\r\n      </ion-col>\r\n    </ion-row>\r\n  </ion-grid>\r\n\r\n  <!-- Add Quest Modal -->\r\n  <!-- <ion-modal [isOpen]=\"showAddQuestModal\" class=\"add-quest-modal\" (ionModalDidDismiss)=\"closeAddQuestModal()\" -->\r\n  <ion-modal [isOpen]=\"true\" class=\"add-quest-modal\" (ionModalDidDismiss)=\"closeAddQuestModal()\"\r\n    [backdropDismiss]=\"true\">\r\n    <ng-template>\r\n      <ion-content class=\"ion-padding\">\r\n        <app-aurora></app-aurora>\r\n        <ion-grid>\r\n          <ion-row class=\"modal-header ion-padding-top ion-text-center\">\r\n            <ion-col>\r\n              <h2>Add New Quest</h2>\r\n              <ion-progress-bar [value]=\"progress\" color=\"success\"></ion-progress-bar>\r\n            </ion-col>\r\n            <ion-icon (click)=\"closeAddQuestModal()\" name=\"close\"></ion-icon>\r\n          </ion-row>\r\n          <form (ngSubmit)=\"createQuest()\" #questForm=\"ngForm\">\r\n\r\n            <div *ngIf=\"currentStep === 1\" class=\"choose-quest\">\r\n              <ion-row>\r\n                <ion-col size=\"6\">\r\n                  <ion-card class=\"ion-padding ion-no-margin ion-text-center\">\r\n                    <ion-icon class=\"ion-margin-bottom\" name=\"earth-outline\"></ion-icon>\r\n                    <h2>Explore</h2>\r\n                    <ion-text>Our upshift qusts!</ion-text>\r\n                  </ion-card>\r\n                </ion-col>\r\n                <ion-col size=\"6\">\r\n                  <ion-card class=\"ion-padding ion-no-margin ion-text-center\">\r\n                    <ion-icon class=\"ion-margin-bottom\" name=\"create-outline\"></ion-icon>\r\n                    <h2>Create</h2>\r\n                    <ion-text>Create your own!</ion-text>\r\n                  </ion-card>\r\n                </ion-col>\r\n              </ion-row>\r\n            </div>\r\n\r\n\r\n            <div *ngIf=\"currentStep === 2\" class=\"first-step\">\r\n              <ion-row class=\"ion-margin-top\">\r\n                <ion-col>\r\n                  <h2>Let's choose the <span class=\"gradient-text\">emoji!!</span></h2>\r\n                </ion-col>\r\n              </ion-row>\r\n              <ion-row>\r\n                <ion-col>\r\n                  <ion-text class=\"dark-text\">Choose an EMOJI that you think represents this quest!</ion-text>\r\n                </ion-col>\r\n              </ion-row>\r\n              <ion-row class=\"preview-emoji\">\r\n                <ion-col>\r\n                  <ion-input type=\"text\" id=\"emoji\" name=\"emoji\" [(ngModel)]=\"newQuest.emoji\" value=\"🎯\" appEmojiInput\r\n                    required></ion-input>\r\n                </ion-col>\r\n              </ion-row>\r\n              <ion-row class=\"emoji-row ion-margin-top ion-margin-bottom\">\r\n                <ion-col *ngFor=\"let emoji of emojis\" size=\"2\"\r\n                  class=\"ion-justify-content-center ion-align-items-center\">\r\n                  <ion-text>{{ emoji }}</ion-text>\r\n                </ion-col>\r\n              </ion-row>\r\n              <ion-button class=\"social-button\">Custom</ion-button>\r\n            </div>\r\n\r\n            <div *ngIf=\"currentStep === 3\" class=\"step-three-container\">\r\n              <ion-row class=\"step-header ion-margin-top\">\r\n                <ion-col class=\"ion-text-center\">\r\n                  <div class=\"floating-emoji ion-margin-bottom\">{{ newQuest.emoji || '🎯' }}</div>\r\n                  <h2>Tell us about your <span class=\"gradient-text\">quest!</span></h2>\r\n                  <ion-text class=\"dark-text\">Give your quest a name and describe what you want to\r\n                    achieve</ion-text>\r\n                </ion-col>\r\n              </ion-row>\r\n\r\n              <ion-row class=\"input-section\">\r\n                <ion-col>\r\n                  <div class=\"floating-input-container\">\r\n                    <div class=\"input-icon\">✏️</div>\r\n                    <ion-input class=\"dark-input\" type=\"text\" id=\"name\" name=\"name\" [(ngModel)]=\"newQuest.name\"\r\n                      placeholder=\" \" required>\r\n                    </ion-input>\r\n                    <label class=\"floating-label\" for=\"name\">Quest Name</label>\r\n                    <div class=\"input-border\"></div>\r\n                  </div>\r\n                </ion-col>\r\n              </ion-row>\r\n\r\n              <ion-row class=\"input-section\">\r\n                <ion-col>\r\n                  <div class=\"floating-textarea-container\">\r\n                    <div class=\"input-icon\">📝</div>\r\n                    <ion-textarea class=\"dark-input\" id=\"description\" name=\"description\"\r\n                      [(ngModel)]=\"newQuest.description\" placeholder=\" \" rows=\"3\" maxlength=\"150\">\r\n                    </ion-textarea>\r\n                    <label class=\"floating-label\" for=\"description\">Quest Description</label>\r\n                    <div class=\"input-border\"></div>\r\n                    <div class=\"character-counter\">\r\n                      <span [class.warning]=\"(newQuest.description?.length || 0) > 120\">\r\n                        {{ newQuest.description?.length || 0 }}/150\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </ion-col>\r\n              </ion-row>\r\n            </div>\r\n\r\n            <div *ngIf=\"currentStep === 4\" class=\"step-four-container\">\r\n              <ion-row class=\"step-header\">\r\n                <ion-col class=\"ion-text-center\">\r\n                  <div class=\"step-icon-container\">\r\n                    <div class=\"floating-emoji\">⚙️</div>\r\n                  </div>\r\n                  <h2 class=\"step-title\">Configure your <span class=\"gradient-text\">quest!</span></h2>\r\n                  <p class=\"step-subtitle\">Choose the type, category and set your goal</p>\r\n                </ion-col>\r\n              </ion-row>\r\n\r\n              <!-- Quest Type Toggle -->\r\n              <ion-row class=\"quest-type-section\" *ngIf=\"!newQuest.quest_type\"\r\n                [class.slide-in-from-right]=\"questTypeAnimated\"\r\n                [class.slide-out-left]=\"questTypeAnimating && selectedQuestType\">\r\n                <ion-col>\r\n                  <div class=\"section-label\">\r\n                    <ion-icon name=\"trending-up-outline\"></ion-icon>\r\n                    <span>Quest Type</span>\r\n                  </div>\r\n                  <div class=\"toggle-container\">\r\n                    <div class=\"toggle-option\" [class.active]=\"newQuest.quest_type === 'build'\"\r\n                      (click)=\"selectQuestType('build')\">\r\n                      <div class=\"toggle-icon\">🏗️</div>\r\n                      <div class=\"toggle-text\">\r\n                        <h4>Build Habit</h4>\r\n                        <p>Create a new positive habit</p>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"toggle-option\" [class.active]=\"newQuest.quest_type === 'quit'\"\r\n                      (click)=\"selectQuestType('quit')\">\r\n                      <div class=\"toggle-icon\">🚫</div>\r\n                      <div class=\"toggle-text\">\r\n                        <h4>Quit Habit</h4>\r\n                        <p>Break a negative habit</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </ion-col>\r\n              </ion-row>\r\n\r\n              <!-- Category Cards -->\r\n              <ion-row class=\"category-section\" *ngIf=\"newQuest.quest_type && !categorySelected\"\r\n                [class.slide-in-from-right]=\"categoryAnimated\"\r\n                [class.slide-out-left]=\"categoryAnimating && selectedCategory\">\r\n                <ion-col>\r\n                  <div class=\"section-label\">\r\n                    <ion-icon name=\"grid-outline\"></ion-icon>\r\n                    <span>Category</span>\r\n                  </div>\r\n                  <div class=\"category-grid\">\r\n                    <div class=\"category-card\" [class.selected]=\"newQuest.category === 'strength'\"\r\n                      (click)=\"selectCategory('strength')\">\r\n                      <div class=\"category-icon\">💪</div>\r\n                      <span>Strength</span>\r\n                    </div>\r\n                    <div class=\"category-card\" [class.selected]=\"newQuest.category === 'money'\"\r\n                      (click)=\"selectCategory('money')\">\r\n                      <div class=\"category-icon\">💰</div>\r\n                      <span>Money</span>\r\n                    </div>\r\n                    <div class=\"category-card\" [class.selected]=\"newQuest.category === 'health'\"\r\n                      (click)=\"selectCategory('health')\">\r\n                      <div class=\"category-icon\">🏥</div>\r\n                      <span>Health</span>\r\n                    </div>\r\n                    <div class=\"category-card\" [class.selected]=\"newQuest.category === 'knowledge'\"\r\n                      (click)=\"selectCategory('knowledge')\">\r\n                      <div class=\"category-icon\">🧠</div>\r\n                      <span>Knowledge</span>\r\n                    </div>\r\n                  </div>\r\n                </ion-col>\r\n              </ion-row>\r\n\r\n              <!-- Priority Selection -->\r\n              <ion-row class=\"priority-section\" *ngIf=\"newQuest.category\" [class.slide-in-from-right]=\"priorityAnimated\"\r\n                [class.slide-out-left]=\"priorityAnimating\">\r\n                <ion-col>\r\n                  <div class=\"section-label\">\r\n                    <ion-icon name=\"flag-outline\"></ion-icon>\r\n                    <span>Priority</span>\r\n                  </div>\r\n                  <div class=\"priority-container\">\r\n                    <div class=\"priority-option\" [class.active]=\"newQuest.priority === 'basic'\" [class.disabled]=\"false\"\r\n                      (click)=\"selectPriority('basic')\">\r\n                      <div class=\"priority-indicator basic\"></div>\r\n                      <span>Basic Priority</span>\r\n                    </div>\r\n                    <div class=\"priority-option\" [class.active]=\"newQuest.priority === 'high'\"\r\n                      [class.disabled]=\"hasHighPriorityQuest\" (click)=\"selectPriority('high')\">\r\n                      <div class=\"priority-indicator high\"></div>\r\n                      <span>High Priority</span>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"priority-warning\" *ngIf=\"hasHighPriorityQuest\">\r\n                    <ion-icon name=\"warning-outline\"></ion-icon>\r\n                    <span>You already have a high priority quest in this category</span>\r\n                  </div>\r\n                </ion-col>\r\n              </ion-row>\r\n            </div>\r\n\r\n            <div *ngIf=\"currentStep === 5\" class=\"step-five-container\">\r\n              <!-- Step Header with Floating Emoji -->\r\n              <ion-row class=\"step-header\">\r\n                <ion-col class=\"ion-text-center\">\r\n                  <div class=\"floating-emoji-container\">\r\n                    <div class=\"floating-emoji\">{{ newQuest.emoji ? newQuest.emoji : '🎯' }}</div>\r\n                  </div>\r\n                  <h2 class=\"step-title\">Finalize your <span class=\"gradient-text\">quest!</span></h2>\r\n                  <p class=\"step-subtitle\">Set your goal and frequency to complete your quest</p>\r\n                </ion-col>\r\n              </ion-row>\r\n\r\n              <!-- Goal Section -->\r\n              <ion-row class=\"goal-section\" [class.slide-in]=\"goalAnimated\" *ngIf=\"newQuest.goal_value === 1\">\r\n                <ion-col>\r\n                  <div class=\"goal-card\">\r\n                    <div class=\"goal-header\">\r\n                      <div class=\"goal-icon\">🎯</div>\r\n                      <h3>Set Your Goal</h3>\r\n                    </div>\r\n                    <div class=\"goal-inputs\">\r\n                      <div class=\"goal-input-group\">\r\n                        <ion-input class=\"dark-input goal-value-input\" type=\"number\" id=\"goal_value\" name=\"goal_value\"\r\n                          [(ngModel)]=\"newQuest.goal_value\" placeholder=\"1\" min=\"1\" required>\r\n                        </ion-input>\r\n                        <label class=\"input-label\">Target</label>\r\n                      </div>\r\n                      <div class=\"goal-input-group\">\r\n                        <ion-select class=\"dark-input goal-unit-select\" id=\"goal_unit\" name=\"goal_unit\"\r\n                          [(ngModel)]=\"newQuest.goal_unit\" interface=\"popover\" required>\r\n                          <ion-select-option value=\"count\">times</ion-select-option>\r\n                          <ion-select-option value=\"steps\">steps</ion-select-option>\r\n                          <ion-select-option value=\"m\">meters</ion-select-option>\r\n                          <ion-select-option value=\"km\">kilometers</ion-select-option>\r\n                          <ion-select-option value=\"sec\">seconds</ion-select-option>\r\n                          <ion-select-option value=\"min\">minutes</ion-select-option>\r\n                          <ion-select-option value=\"hr\">hours</ion-select-option>\r\n                          <ion-select-option value=\"Cal\">calories</ion-select-option>\r\n                          <ion-select-option value=\"g\">grams</ion-select-option>\r\n                          <ion-select-option value=\"mg\">milligrams</ion-select-option>\r\n                          <ion-select-option value=\"l\">liters</ion-select-option>\r\n                          <ion-select-option value=\"drink\">drinks</ion-select-option>\r\n                          <ion-select-option value=\"pages\">pages</ion-select-option>\r\n                          <ion-select-option value=\"books\">books</ion-select-option>\r\n                          <ion-select-option value=\"%\">percent</ion-select-option>\r\n                          <ion-select-option value=\"€\">euros</ion-select-option>\r\n                          <ion-select-option value=\"$\">dollars</ion-select-option>\r\n                          <ion-select-option value=\"£\">pounds</ion-select-option>\r\n                        </ion-select>\r\n                        <label class=\"input-label\">Unit</label>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"goal-preview\">\r\n                      <span class=\"preview-label\">Goal:</span>\r\n                      <span class=\"preview-value\">{{ newQuest.goal_value ? newQuest.goal_value : 1 }} {{\r\n                        newQuest.goal_unit ? newQuest.goal_unit : 'times' }}</span>\r\n                    </div>\r\n                  </div>\r\n                </ion-col>\r\n              </ion-row>\r\n              <!-- Frequency Section -->\r\n              <ion-row class=\"frequency-section\" *ngIf=\"newQuest.goal_value\">\r\n                <ion-col>\r\n                  <div class=\"frequency-card\">\r\n                    <div class=\"frequency-header\">\r\n                      <div class=\"frequency-icon\">📅</div>\r\n                      <h3>Choose Frequency</h3>\r\n                    </div>\r\n                    <div class=\"frequency-select-container\">\r\n                      <ion-select class=\"dark-input frequency-select\" id=\"goal_period\" name=\"goal_period\"\r\n                        [(ngModel)]=\"newQuest.goal_period\" (ionChange)=\"updatePeriodDisplay()\" interface=\"popover\"\r\n                        required>\r\n                        <ion-select-option value=\"day\">Every Day</ion-select-option>\r\n                        <ion-select-option value=\"week\">Specific days of the week</ion-select-option>\r\n                        <ion-select-option value=\"month\">Specific days of the month</ion-select-option>\r\n                      </ion-select>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"newQuest.goal_period === 'week'\" class=\"week-days-container\">\r\n                      <h4>Select Days</h4>\r\n                      <div class=\"week-days-grid\">\r\n                        <ion-button *ngFor=\"let day of weekDays\" type=\"button\" class=\"day-chip\"\r\n                          (click)=\"updateDaysOfWeek(day.value)\"\r\n                          [class.selected]=\"selectedDaysOfWeek.includes(day.value)\">\r\n                          {{ day.label }}\r\n                        </ion-button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"newQuest.goal_period === 'month'\" class=\"month-days-container\">\r\n                      <h4>Select Days</h4>\r\n                      <div class=\"month-days-grid\">\r\n                        <div *ngFor=\"let day of monthDays\" class=\"month-day-item\">\r\n                          <ion-checkbox [id]=\"'month-day-' + day\" name=\"days_of_month\" [value]=\"day\"\r\n                            (ionChange)=\"updateDaysOfMonth($event, day)\">\r\n                          </ion-checkbox>\r\n                          <label [for]=\"'month-day-' + day\">{{ day }}</label>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </ion-col>\r\n              </ion-row>\r\n            </div>\r\n\r\n            <ion-row class=\"ion-padding create-quest-row\">\r\n              <ion-col *ngIf=\"currentStep !== 5\">\r\n                <ion-button (click)=\"prevStep()\" class=\"social-button\">Back</ion-button>\r\n              </ion-col>\r\n              <ion-col *ngIf=\"currentStep !== 5\">\r\n                <ion-button (click)=\"nextStep()\" class=\"blue-button\">Next</ion-button>\r\n              </ion-col>\r\n              <ion-col *ngIf=\"currentStep === 5\">\r\n                <ion-button type=\"submit\" class=\"blue-button create-quest\">Create Quest</ion-button>\r\n              </ion-col>\r\n            </ion-row>\r\n          </form>\r\n        </ion-grid>\r\n      </ion-content>\r\n    </ng-template>\r\n  </ion-modal>\r\n\r\n  <app-celebration *ngIf=\"showCelebration\" [user]=\"currentUser\" [date]=\"formatDate(selectedDate)\"\r\n    (close)=\"closeCelebration()\">\r\n  </app-celebration>\r\n</ion-content>\r\n<app-navigation></app-navigation>"], "mappings": ";;AAAA,SAAuCA,MAAM,QAAmB,eAAe;AAC/E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,KAAK,QAA4F,0BAA0B;AAEpI,SAAqBC,YAAY,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,IAAI,EAAEC,cAAc,QAAQ,MAAM;AACnG,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,oBAAoB,QAAQ,oDAAoD;AACzF,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,mBAAmB,QAAQ,wCAAwC;AAC5E,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;;;;;;;;;;ICC9DC,EAAA,CAAAC,SAAA,iBAGS;;;;IAD8CD,EAAA,CAAAE,WAAA,QAAAC,OAAA,CAAAC,qBAAA,MAA6C;;;;;;;IAXxGJ,EADF,CAAAK,cAAA,cAAyE,mBAEwB;IAC7FL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACXP,EAAA,CAAAK,cAAA,cACgD;IAA9CL,EAAA,CAAAQ,UAAA,mBAAAC,8CAAA;MAAA,MAAAN,OAAA,GAAAH,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,EAAAZ,OAAA,CAAAa,SAAA,IAA4BH,MAAA,CAAAI,UAAA,CAAAd,OAAA,CAAgB;IAAA,EAAC;;IAC7CH,EAAA,CAAAK,cAAA,cAA+C;IAC7CL,EAAA,CAAAC,SAAA,iBAAkG;IAElGD,EAAA,CAAAkB,UAAA,IAAAC,sCAAA,qBAEqG;IAI3GnB,EAFI,CAAAO,YAAA,EAAM,EACF,EACF;;;;;IAfuBP,EAAA,CAAAoB,SAAA,EAA8B;IACDpB,EAD7B,CAAAE,WAAA,WAAAC,OAAA,CAAAkB,QAAA,CAA8B,aAAAlB,OAAA,CAAAmB,WAAA,KAAAnB,OAAA,CAAAkB,QAAA,CACF,gBAAAlB,OAAA,CAAAmB,WAAA,CAAuC;IAC5FtB,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAvB,EAAA,CAAAwB,eAAA,KAAAC,GAAA,EAAAC,IAAA,OACF;IACkB1B,EAAA,CAAAoB,SAAA,EAAmC;IAACpB,EAApC,CAAAE,WAAA,aAAAC,OAAA,CAAAmB,WAAA,CAAmC,aAAAnB,OAAA,CAAAa,SAAA,CAAkC;IAKnDhB,EAAA,CAAAoB,SAAA,GAAqB;IAArBpB,EAAA,CAAA2B,UAAA,UAAAxB,OAAA,CAAAa,SAAA,CAAqB;;;;;IA4BvDhB,EAFJ,CAAAK,cAAA,mBAA4E,sBACzD,SACX;IAAAL,EAAA,CAAAM,MAAA,wBAAiB;IACvBN,EADuB,CAAAO,YAAA,EAAK,EACV;IAIZP,EAHN,CAAAK,cAAA,uBAAkB,cACP,kBACW,eACN;IAAAL,EAAA,CAAAM,MAAA,6CAAsC;IAClDN,EADkD,CAAAO,YAAA,EAAW,EACnD;IACVP,EAAA,CAAAK,cAAA,kBAAkB;IAChBL,EAAA,CAAAC,SAAA,oBAAoC;IAI5CD,EAHM,CAAAO,YAAA,EAAU,EACF,EACO,EACV;;;;;;IAaDP,EAFF,CAAAK,cAAA,cAC2H,oBAK1C;IAJnCL,EAAA,CAAA4B,gBAAA,2BAAAC,yEAAAC,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqB,GAAA;MAAA,MAAAC,QAAA,GAAAhC,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAAZ,EAAA,CAAAiC,kBAAA,CAAAD,QAAA,CAAAE,cAAA,EAAAJ,MAAA,MAAAE,QAAA,CAAAE,cAAA,GAAAJ,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAkC;IAG5E9B,EADoC,CAAAQ,UAAA,uBAAA2B,qEAAAL,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqB,GAAA;MAAA,MAAAC,QAAA,GAAAhC,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAAuB,mBAAA,CAAAJ,QAAA,EAAAF,MAAA,CAAkC;IAAA,EAAC,sBAAAO,oEAAAP,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAAe,MAAA,CAAAQ,MAAA,IACvDzB,MAAA,CAAA0B,sBAAA,CAAAT,MAAA,CAAAQ,MAAA,CAAqC;IAAA,EAAC;IAErEtC,EAAA,CAAAO,YAAA,EAAY;IACZP,EAAA,CAAAK,cAAA,mBAAgC;IAC9BL,EAAA,CAAAM,MAAA,GAIF;IACFN,EADE,CAAAO,YAAA,EAAW,EACP;;;;IARFP,EAAA,CAAAoB,SAAA,EAA4E;IAA5EpB,EAAA,CAAAwC,sBAAA,uBAAAR,QAAA,CAAAE,cAAA,GAAAF,QAAA,CAAAS,UAAA,YAA4E;IAJ3DzC,EAAA,CAAA2B,UAAA,QAAAK,QAAA,CAAAS,UAAA,CAAwB;IAACzC,EAAA,CAAA0C,gBAAA,YAAAV,QAAA,CAAAE,cAAA,CAAkC;IACFlC,EAAA,CAAA2B,UAAA,WAAU;;IAMpF3B,EAAA,CAAAoB,SAAA,GAIF;IAJEpB,EAAA,CAAA2C,kBAAA,MAAAX,QAAA,CAAAE,cAAA,MAAAF,QAAA,CAAAY,SAAA,mBAAAZ,QAAA,CAAAY,SAAA,4BAAAZ,QAAA,CAAAS,UAAA,MAAAT,QAAA,CAAAY,SAAA,mBAAAZ,QAAA,CAAAY,SAAA,2BAIF;;;;;IAYE5C,EAAA,CAAA6C,uBAAA,GAAkD;IAChD7C,EAAA,CAAAM,MAAA,GACF;;;;;IADEN,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAS,QAAA,CAAAY,SAAA,MACF;;;;;;IAVF5C,EAFF,CAAAK,cAAA,cAC2H,oBAK1C;IAJnCL,EAAA,CAAA4B,gBAAA,2BAAAkB,yEAAAhB,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqC,GAAA;MAAA,MAAAf,QAAA,GAAAhC,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAAZ,EAAA,CAAAiC,kBAAA,CAAAD,QAAA,CAAAE,cAAA,EAAAJ,MAAA,MAAAE,QAAA,CAAAE,cAAA,GAAAJ,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAkC;IAG5E9B,EADoC,CAAAQ,UAAA,uBAAAwC,qEAAAlB,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqC,GAAA;MAAA,MAAAf,QAAA,GAAAhC,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAAuB,mBAAA,CAAAJ,QAAA,EAAAF,MAAA,CAAkC;IAAA,EAAC,sBAAAmB,oEAAAnB,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAqC,GAAA;MAAA,MAAAlC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAAe,MAAA,CAAAQ,MAAA,IACvDzB,MAAA,CAAA0B,sBAAA,CAAAT,MAAA,CAAAQ,MAAA,CAAqC;IAAA,EAAC;IAErEtC,EAAA,CAAAO,YAAA,EAAY;IACZP,EAAA,CAAAK,cAAA,mBAAgC;IAC9BL,EAAA,CAAAM,MAAA,GACA;IAAAN,EAAA,CAAAkB,UAAA,IAAAgC,oDAAA,2BAAkD;IAItDlD,EADE,CAAAO,YAAA,EAAW,EACP;;;;IARFP,EAAA,CAAAoB,SAAA,EAA4E;IAA5EpB,EAAA,CAAAwC,sBAAA,uBAAAR,QAAA,CAAAE,cAAA,GAAAF,QAAA,CAAAS,UAAA,YAA4E;IAJ3DzC,EAAA,CAAA2B,UAAA,QAAAK,QAAA,CAAAS,UAAA,CAAwB;IAACzC,EAAA,CAAA0C,gBAAA,YAAAV,QAAA,CAAAE,cAAA,CAAkC;IACFlC,EAAA,CAAA2B,UAAA,WAAU;;IAMpF3B,EAAA,CAAAoB,SAAA,GACA;IADApB,EAAA,CAAAmD,kBAAA,MAAAnB,QAAA,CAAAE,cAAA,OAAAF,QAAA,CAAAS,UAAA,MACA;IAAezC,EAAA,CAAAoB,SAAA,EAAiC;IAAjCpB,EAAA,CAAA2B,UAAA,SAAAK,QAAA,CAAAY,SAAA,aAAiC;;;;;IAQtD5C,EAAA,CAAAK,cAAA,mBAA2E;IACzEL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAW;;;;IADTP,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,kBAAAS,QAAA,CAAAoB,MAAA,OACF;;;;;;IA7CNpD,EAAA,CAAAK,cAAA,mBACgG;IAA7BL,EAAA,CAAAQ,UAAA,mBAAA6C,yDAAA;MAAA,MAAArB,QAAA,GAAAhC,EAAA,CAAAU,aAAA,CAAA4C,GAAA,EAAA1C,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAA0C,WAAA,CAAAvB,QAAA,CAAkB;IAAA,EAAC;IAGzFhC,EAFJ,CAAAK,cAAA,cAAS,kBACW,cACQ;IAAAL,EAAA,CAAAM,MAAA,GAAiB;IAC3CN,EAD2C,CAAAO,YAAA,EAAM,EACvC;IAERP,EADF,CAAAK,cAAA,kBAAqC,SAC/B;IAAAL,EAAA,CAAAM,MAAA,GAAgB;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACzBP,EAAA,CAAAK,cAAA,eAAU;IAAAL,EAAA,CAAAM,MAAA,GAAuB;IAAAN,EAAA,CAAAO,YAAA,EAAW;IAC5CP,EAAA,CAAAK,cAAA,eAAgC;IAgB9BL,EAfA,CAAAkB,UAAA,KAAAsC,qCAAA,mBAC2H,KAAAC,qCAAA,mBAeA;IAe/HzD,EADE,CAAAO,YAAA,EAAM,EACE;IACVP,EAAA,CAAAK,cAAA,mBAAkB;IAChBL,EAAA,CAAAkB,UAAA,KAAAwC,0CAAA,uBAA2E;IAKjF1D,EAFI,CAAAO,YAAA,EAAU,EACF,EACD;;;;;IAhDiEP,EAAA,CAAAE,WAAA,cAAA8B,QAAA,CAAA2B,SAAA,CAAmC;;IAIjF3D,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAA4D,iBAAA,CAAA5B,QAAA,CAAA6B,KAAA,CAAiB;IAGrC7D,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAA4D,iBAAA,CAAA5B,QAAA,CAAA8B,IAAA,CAAgB;IACV9D,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAA4D,iBAAA,CAAA5B,QAAA,CAAA+B,WAAA,CAAuB;IAG5B/D,EAAA,CAAAoB,SAAA,GAAsH;IAAtHpB,EAAA,CAAA2B,UAAA,SAAAK,QAAA,CAAAY,SAAA,eAAAZ,QAAA,CAAAY,SAAA,cAAAZ,QAAA,CAAAY,SAAA,aAAAZ,QAAA,CAAAY,SAAA,WAAsH;IAetH5C,EAAA,CAAAoB,SAAA,EAAsH;IAAtHpB,EAAA,CAAA2B,UAAA,SAAAK,QAAA,CAAAY,SAAA,eAAAZ,QAAA,CAAAY,SAAA,cAAAZ,QAAA,CAAAY,SAAA,aAAAZ,QAAA,CAAAY,SAAA,WAAsH;IAiB3F5C,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAmD,SAAA,CAAAnD,MAAA,CAAAoD,YAAA,EAAApD,MAAA,CAAAqD,QAAA,IAAyC;;;;;IAiCzElE,EAAA,CAAAK,cAAA,cAAsE;IACpEL,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;IADJP,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,kBAAAV,MAAA,CAAAsD,UAAA,CAAAf,MAAA,OACF;;;;;;IApBNpD,EAAA,CAAAK,cAAA,mBAEwC;IAAtCL,EAAA,CAAAQ,UAAA,mBAAA4D,yDAAA;MAAApE,EAAA,CAAAU,aAAA,CAAA2D,GAAA;MAAA,MAAAxD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAyD,eAAA,CAAAzD,MAAA,CAAAsD,UAAA,CAA2B;IAAA,EAAC;IAGjCnE,EAFJ,CAAAK,cAAA,cAAS,kBACW,cACQ;IAAAL,EAAA,CAAAM,MAAA,GAAsB;IAChDN,EADgD,CAAAO,YAAA,EAAM,EAC5C;IAERP,EADF,CAAAK,cAAA,kBAAqC,SAC/B;IAAAL,EAAA,CAAAM,MAAA,GAAmC;IAAAN,EAAA,CAAAO,YAAA,EAAK;IAC5CP,EAAA,CAAAK,cAAA,mBAAoC;IAAAL,EAAA,CAAAM,MAAA,GAA0C;IAOhFN,EAPgF,CAAAO,YAAA,EAAW,EAOjF;IACVP,EAAA,CAAAK,cAAA,mBAAkB;IAChBL,EAAA,CAAAkB,UAAA,KAAAqD,qCAAA,kBAAsE;IAK5EvE,EAFI,CAAAO,YAAA,EAAU,EACF,EACD;;;;IAtBTP,EAAA,CAAAE,WAAA,cAAAW,MAAA,CAAAsD,UAAA,CAAAR,SAAA,CAAwC;;IAIZ3D,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAA4D,iBAAA,CAAA/C,MAAA,CAAAsD,UAAA,CAAAN,KAAA,CAAsB;IAG1C7D,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAA4D,iBAAA,CAAA/C,MAAA,CAAAsD,UAAA,CAAAK,aAAA,CAAAV,IAAA,CAAmC;IACH9D,EAAA,CAAAoB,SAAA,GAA0C;IAA1CpB,EAAA,CAAA4D,iBAAA,CAAA/C,MAAA,CAAAsD,UAAA,CAAAK,aAAA,CAAAT,WAAA,CAA0C;IASnD/D,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAmD,SAAA,CAAAnD,MAAA,CAAAoD,YAAA,EAAApD,MAAA,CAAAqD,QAAA,IAAyC;;;;;IAQtElE,EAFJ,CAAAK,cAAA,mBAAiD,sBAC9B,qBACC;IAACL,EAAA,CAAAM,MAAA,yBAAiB;IACpCN,EADoC,CAAAO,YAAA,EAAiB,EACnC;IAEhBP,EADF,CAAAK,cAAA,uBAAkB,eACN;IACRL,EAAA,CAAAM,MAAA,2GAEF;IAEJN,EAFI,CAAAO,YAAA,EAAW,EACM,EACV;;;;;IAyBDP,EAHN,CAAAK,cAAA,cAAoD,cACzC,kBACW,mBAC4C;IAC1DL,EAAA,CAAAC,SAAA,mBAAoE;IACpED,EAAA,CAAAK,cAAA,SAAI;IAAAL,EAAA,CAAAM,MAAA,cAAO;IAAAN,EAAA,CAAAO,YAAA,EAAK;IAChBP,EAAA,CAAAK,cAAA,eAAU;IAAAL,EAAA,CAAAM,MAAA,yBAAkB;IAEhCN,EAFgC,CAAAO,YAAA,EAAW,EAC9B,EACH;IAERP,EADF,CAAAK,cAAA,kBAAkB,oBAC4C;IAC1DL,EAAA,CAAAC,SAAA,oBAAqE;IACrED,EAAA,CAAAK,cAAA,UAAI;IAAAL,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACfP,EAAA,CAAAK,cAAA,gBAAU;IAAAL,EAAA,CAAAM,MAAA,wBAAgB;IAIlCN,EAJkC,CAAAO,YAAA,EAAW,EAC5B,EACH,EACF,EACN;;;;;IAuBAP,EAFF,CAAAK,cAAA,kBAC4D,eAChD;IAAAL,EAAA,CAAAM,MAAA,GAAW;IACvBN,EADuB,CAAAO,YAAA,EAAW,EACxB;;;;IADEP,EAAA,CAAAoB,SAAA,GAAW;IAAXpB,EAAA,CAAA4D,iBAAA,CAAAa,SAAA,CAAW;;;;;;IAjBrBzE,EAHN,CAAAK,cAAA,cAAkD,kBAChB,cACrB,SACH;IAAAL,EAAA,CAAAM,MAAA,wBAAiB;IAAAN,EAAA,CAAAK,cAAA,eAA4B;IAAAL,EAAA,CAAAM,MAAA,cAAO;IAE5DN,EAF4D,CAAAO,YAAA,EAAO,EAAK,EAC5D,EACF;IAGNP,EAFJ,CAAAK,cAAA,cAAS,cACE,mBACqB;IAAAL,EAAA,CAAAM,MAAA,6DAAqD;IAErFN,EAFqF,CAAAO,YAAA,EAAW,EACpF,EACF;IAGNP,EAFJ,CAAAK,cAAA,mBAA+B,eACpB,qBAEI;IADoCL,EAAA,CAAA4B,gBAAA,2BAAA8C,6EAAA5C,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAiE,IAAA;MAAA,MAAA9D,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA+D,QAAA,CAAAf,KAAA,EAAA/B,MAAA,MAAAjB,MAAA,CAAA+D,QAAA,CAAAf,KAAA,GAAA/B,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAA4B;IAG/E9B,EAFe,CAAAO,YAAA,EAAY,EACf,EACF;IACVP,EAAA,CAAAK,cAAA,mBAA4D;IAC1DL,EAAA,CAAAkB,UAAA,KAAA2D,mDAAA,sBAC4D;IAG9D7E,EAAA,CAAAO,YAAA,EAAU;IACVP,EAAA,CAAAK,cAAA,sBAAkC;IAAAL,EAAA,CAAAM,MAAA,cAAM;IAC1CN,EAD0C,CAAAO,YAAA,EAAa,EACjD;;;;IAX+CP,EAAA,CAAAoB,SAAA,IAA4B;IAA5BpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA+D,QAAA,CAAAf,KAAA,CAA4B;IAKlD7D,EAAA,CAAAoB,SAAA,GAAS;IAATpB,EAAA,CAAA2B,UAAA,YAAAd,MAAA,CAAAiE,MAAA,CAAS;;;;;;IAWlC9E,EAHN,CAAAK,cAAA,cAA4D,kBACd,kBACT,cACe;IAAAL,EAAA,CAAAM,MAAA,GAA4B;IAAAN,EAAA,CAAAO,YAAA,EAAM;IAChFP,EAAA,CAAAK,cAAA,SAAI;IAAAL,EAAA,CAAAM,MAAA,0BAAmB;IAAAN,EAAA,CAAAK,cAAA,eAA4B;IAAAL,EAAA,CAAAM,MAAA,aAAM;IAAON,EAAP,CAAAO,YAAA,EAAO,EAAK;IACrEP,EAAA,CAAAK,cAAA,mBAA4B;IAAAL,EAAA,CAAAM,MAAA,oEACnB;IAEbN,EAFa,CAAAO,YAAA,EAAW,EACZ,EACF;IAKJP,EAHN,CAAAK,cAAA,mBAA+B,eACpB,eAC+B,eACZ;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAM;IAChCP,EAAA,CAAAK,cAAA,qBAC2B;IADqCL,EAAA,CAAA4B,gBAAA,2BAAAmD,6EAAAjD,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAsE,IAAA;MAAA,MAAAnE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA+D,QAAA,CAAAd,IAAA,EAAAhC,MAAA,MAAAjB,MAAA,CAAA+D,QAAA,CAAAd,IAAA,GAAAhC,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAA2B;IAE3F9B,EAAA,CAAAO,YAAA,EAAY;IACZP,EAAA,CAAAK,cAAA,iBAAyC;IAAAL,EAAA,CAAAM,MAAA,kBAAU;IAAAN,EAAA,CAAAO,YAAA,EAAQ;IAC3DP,EAAA,CAAAC,SAAA,eAAgC;IAGtCD,EAFI,CAAAO,YAAA,EAAM,EACE,EACF;IAKJP,EAHN,CAAAK,cAAA,mBAA+B,eACpB,eACkC,eACf;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAM;IAChCP,EAAA,CAAAK,cAAA,wBAC8E;IAA5EL,EAAA,CAAA4B,gBAAA,2BAAAqD,gFAAAnD,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAsE,IAAA;MAAA,MAAAnE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA+D,QAAA,CAAAb,WAAA,EAAAjC,MAAA,MAAAjB,MAAA,CAAA+D,QAAA,CAAAb,WAAA,GAAAjC,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAkC;IACpC9B,EAAA,CAAAO,YAAA,EAAe;IACfP,EAAA,CAAAK,cAAA,iBAAgD;IAAAL,EAAA,CAAAM,MAAA,yBAAiB;IAAAN,EAAA,CAAAO,YAAA,EAAQ;IACzEP,EAAA,CAAAC,SAAA,eAAgC;IAE9BD,EADF,CAAAK,cAAA,eAA+B,YACqC;IAChEL,EAAA,CAAAM,MAAA,IACF;IAKVN,EALU,CAAAO,YAAA,EAAO,EACH,EACF,EACE,EACF,EACN;;;;IArC8CP,EAAA,CAAAoB,SAAA,GAA4B;IAA5BpB,EAAA,CAAA4D,iBAAA,CAAA/C,MAAA,CAAA+D,QAAA,CAAAf,KAAA,mBAA4B;IAWR7D,EAAA,CAAAoB,SAAA,IAA2B;IAA3BpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA+D,QAAA,CAAAd,IAAA,CAA2B;IAczF9D,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA+D,QAAA,CAAAb,WAAA,CAAkC;IAK5B/D,EAAA,CAAAoB,SAAA,GAA2D;IAA3DpB,EAAA,CAAAE,WAAA,cAAAW,MAAA,CAAA+D,QAAA,CAAAb,WAAA,kBAAAlD,MAAA,CAAA+D,QAAA,CAAAb,WAAA,CAAAmB,MAAA,cAA2D;IAC/DlF,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,OAAAV,MAAA,CAAA+D,QAAA,CAAAb,WAAA,kBAAAlD,MAAA,CAAA+D,QAAA,CAAAb,WAAA,CAAAmB,MAAA,gBACF;;;;;;IAuBJlF,EAJJ,CAAAK,cAAA,kBAEmE,cACxD,cACoB;IACzBL,EAAA,CAAAC,SAAA,oBAAgD;IAChDD,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,iBAAU;IAClBN,EADkB,CAAAO,YAAA,EAAO,EACnB;IAEJP,EADF,CAAAK,cAAA,eAA8B,eAES;IAAnCL,EAAA,CAAAQ,UAAA,mBAAA2E,yEAAA;MAAAnF,EAAA,CAAAU,aAAA,CAAA0E,IAAA;MAAA,MAAAvE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAwE,eAAA,CAAgB,OAAO,CAAC;IAAA,EAAC;IAClCrF,EAAA,CAAAK,cAAA,eAAyB;IAAAL,EAAA,CAAAM,MAAA,yBAAG;IAAAN,EAAA,CAAAO,YAAA,EAAM;IAEhCP,EADF,CAAAK,cAAA,gBAAyB,UACnB;IAAAL,EAAA,CAAAM,MAAA,mBAAW;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACpBP,EAAA,CAAAK,cAAA,SAAG;IAAAL,EAAA,CAAAM,MAAA,mCAA2B;IAElCN,EAFkC,CAAAO,YAAA,EAAI,EAC9B,EACF;IACNP,EAAA,CAAAK,cAAA,gBACoC;IAAlCL,EAAA,CAAAQ,UAAA,mBAAA8E,0EAAA;MAAAtF,EAAA,CAAAU,aAAA,CAAA0E,IAAA;MAAA,MAAAvE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAwE,eAAA,CAAgB,MAAM,CAAC;IAAA,EAAC;IACjCrF,EAAA,CAAAK,cAAA,gBAAyB;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAM;IAE/BP,EADF,CAAAK,cAAA,gBAAyB,UACnB;IAAAL,EAAA,CAAAM,MAAA,kBAAU;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACnBP,EAAA,CAAAK,cAAA,SAAG;IAAAL,EAAA,CAAAM,MAAA,8BAAsB;IAKnCN,EALmC,CAAAO,YAAA,EAAI,EACzB,EACF,EACF,EACE,EACF;;;;IAzBRP,EADA,CAAAE,WAAA,wBAAAW,MAAA,CAAA0E,iBAAA,CAA+C,mBAAA1E,MAAA,CAAA2E,kBAAA,IAAA3E,MAAA,CAAA4E,iBAAA,CACiB;IAOjCzF,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAAE,WAAA,WAAAW,MAAA,CAAA+D,QAAA,CAAAc,UAAA,aAAgD;IAQhD1F,EAAA,CAAAoB,SAAA,GAA+C;IAA/CpB,EAAA,CAAAE,WAAA,WAAAW,MAAA,CAAA+D,QAAA,CAAAc,UAAA,YAA+C;;;;;;IAiB5E1F,EAJJ,CAAAK,cAAA,mBAEiE,cACtD,cACoB;IACzBL,EAAA,CAAAC,SAAA,oBAAyC;IACzCD,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,eAAQ;IAChBN,EADgB,CAAAO,YAAA,EAAO,EACjB;IAEJP,EADF,CAAAK,cAAA,eAA2B,eAEc;IAArCL,EAAA,CAAAQ,UAAA,mBAAAmF,yEAAA;MAAA3F,EAAA,CAAAU,aAAA,CAAAkF,IAAA;MAAA,MAAA/E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgF,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACpC7F,EAAA,CAAAK,cAAA,eAA2B;IAAAL,EAAA,CAAAM,MAAA,mBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACnCP,EAAA,CAAAK,cAAA,YAAM;IAAAL,EAAA,CAAAM,MAAA,gBAAQ;IAChBN,EADgB,CAAAO,YAAA,EAAO,EACjB;IACNP,EAAA,CAAAK,cAAA,gBACoC;IAAlCL,EAAA,CAAAQ,UAAA,mBAAAsF,0EAAA;MAAA9F,EAAA,CAAAU,aAAA,CAAAkF,IAAA;MAAA,MAAA/E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgF,cAAA,CAAe,OAAO,CAAC;IAAA,EAAC;IACjC7F,EAAA,CAAAK,cAAA,gBAA2B;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACnCP,EAAA,CAAAK,cAAA,YAAM;IAAAL,EAAA,CAAAM,MAAA,aAAK;IACbN,EADa,CAAAO,YAAA,EAAO,EACd;IACNP,EAAA,CAAAK,cAAA,gBACqC;IAAnCL,EAAA,CAAAQ,UAAA,mBAAAuF,0EAAA;MAAA/F,EAAA,CAAAU,aAAA,CAAAkF,IAAA;MAAA,MAAA/E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgF,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IAClC7F,EAAA,CAAAK,cAAA,gBAA2B;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACnCP,EAAA,CAAAK,cAAA,YAAM;IAAAL,EAAA,CAAAM,MAAA,cAAM;IACdN,EADc,CAAAO,YAAA,EAAO,EACf;IACNP,EAAA,CAAAK,cAAA,gBACwC;IAAtCL,EAAA,CAAAQ,UAAA,mBAAAwF,0EAAA;MAAAhG,EAAA,CAAAU,aAAA,CAAAkF,IAAA;MAAA,MAAA/E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgF,cAAA,CAAe,WAAW,CAAC;IAAA,EAAC;IACrC7F,EAAA,CAAAK,cAAA,gBAA2B;IAAAL,EAAA,CAAAM,MAAA,oBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACnCP,EAAA,CAAAK,cAAA,YAAM;IAAAL,EAAA,CAAAM,MAAA,iBAAS;IAIvBN,EAJuB,CAAAO,YAAA,EAAO,EAClB,EACF,EACE,EACF;;;;IA7BRP,EADA,CAAAE,WAAA,wBAAAW,MAAA,CAAAoF,gBAAA,CAA8C,mBAAApF,MAAA,CAAAqF,iBAAA,IAAArF,MAAA,CAAAsF,gBAAA,CACgB;IAO/BnG,EAAA,CAAAoB,SAAA,GAAmD;IAAnDpB,EAAA,CAAAE,WAAA,aAAAW,MAAA,CAAA+D,QAAA,CAAAwB,QAAA,gBAAmD;IAKnDpG,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAAE,WAAA,aAAAW,MAAA,CAAA+D,QAAA,CAAAwB,QAAA,aAAgD;IAKhDpG,EAAA,CAAAoB,SAAA,GAAiD;IAAjDpB,EAAA,CAAAE,WAAA,aAAAW,MAAA,CAAA+D,QAAA,CAAAwB,QAAA,cAAiD;IAKjDpG,EAAA,CAAAoB,SAAA,GAAoD;IAApDpB,EAAA,CAAAE,WAAA,aAAAW,MAAA,CAAA+D,QAAA,CAAAwB,QAAA,iBAAoD;;;;;IA6BjFpG,EAAA,CAAAK,cAAA,eAA2D;IACzDL,EAAA,CAAAC,SAAA,oBAA4C;IAC5CD,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,8DAAuD;IAC/DN,EAD+D,CAAAO,YAAA,EAAO,EAChE;;;;;;IAnBNP,EAHJ,CAAAK,cAAA,mBAC6C,cAClC,cACoB;IACzBL,EAAA,CAAAC,SAAA,oBAAyC;IACzCD,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,eAAQ;IAChBN,EADgB,CAAAO,YAAA,EAAO,EACjB;IAEJP,EADF,CAAAK,cAAA,eAAgC,eAEM;IAAlCL,EAAA,CAAAQ,UAAA,mBAAA6F,yEAAA;MAAArG,EAAA,CAAAU,aAAA,CAAA4F,IAAA;MAAA,MAAAzF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAA0F,cAAA,CAAe,OAAO,CAAC;IAAA,EAAC;IACjCvG,EAAA,CAAAC,SAAA,eAA4C;IAC5CD,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAM,MAAA,sBAAc;IACtBN,EADsB,CAAAO,YAAA,EAAO,EACvB;IACNP,EAAA,CAAAK,cAAA,gBAC2E;IAAjCL,EAAA,CAAAQ,UAAA,mBAAAgG,0EAAA;MAAAxG,EAAA,CAAAU,aAAA,CAAA4F,IAAA;MAAA,MAAAzF,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAA0F,cAAA,CAAe,MAAM,CAAC;IAAA,EAAC;IACxEvG,EAAA,CAAAC,SAAA,gBAA2C;IAC3CD,EAAA,CAAAK,cAAA,YAAM;IAAAL,EAAA,CAAAM,MAAA,qBAAa;IAEvBN,EAFuB,CAAAO,YAAA,EAAO,EACtB,EACF;IACNP,EAAA,CAAAkB,UAAA,KAAAuF,0DAAA,mBAA2D;IAK/DzG,EADE,CAAAO,YAAA,EAAU,EACF;;;;IAvBRP,EAD0D,CAAAE,WAAA,wBAAAW,MAAA,CAAA6F,gBAAA,CAA8C,mBAAA7F,MAAA,CAAA8F,iBAAA,CAC9D;IAOT3G,EAAA,CAAAoB,SAAA,GAA8C;IAACpB,EAA/C,CAAAE,WAAA,WAAAW,MAAA,CAAA+D,QAAA,CAAAgC,QAAA,aAA8C,mBAAyB;IAKvE5G,EAAA,CAAAoB,SAAA,GAA6C;IACxEpB,EAD2B,CAAAE,WAAA,WAAAW,MAAA,CAAA+D,QAAA,CAAAgC,QAAA,YAA6C,aAAA/F,MAAA,CAAAgG,oBAAA,CACjC;IAKZ7G,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAgG,oBAAA,CAA0B;;;;;IA3FvD7G,EAJR,CAAAK,cAAA,cAA2D,kBAC5B,kBACM,cACE,cACH;IAAAL,EAAA,CAAAM,MAAA,mBAAE;IAChCN,EADgC,CAAAO,YAAA,EAAM,EAChC;IACNP,EAAA,CAAAK,cAAA,aAAuB;IAAAL,EAAA,CAAAM,MAAA,sBAAe;IAAAN,EAAA,CAAAK,cAAA,eAA4B;IAAAL,EAAA,CAAAM,MAAA,aAAM;IAAON,EAAP,CAAAO,YAAA,EAAO,EAAK;IACpFP,EAAA,CAAAK,cAAA,aAAyB;IAAAL,EAAA,CAAAM,MAAA,mDAA2C;IAExEN,EAFwE,CAAAO,YAAA,EAAI,EAChE,EACF;IAmEVP,EAhEA,CAAAkB,UAAA,KAAA4F,mDAAA,uBAEmE,KAAAC,mDAAA,wBA8BF,KAAAC,mDAAA,wBAiCpB;IAwB/ChH,EAAA,CAAAO,YAAA,EAAM;;;;IAzFiCP,EAAA,CAAAoB,SAAA,IAA0B;IAA1BpB,EAAA,CAAA2B,UAAA,UAAAd,MAAA,CAAA+D,QAAA,CAAAc,UAAA,CAA0B;IA8B5B1F,EAAA,CAAAoB,SAAA,EAA8C;IAA9CpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAA+D,QAAA,CAAAc,UAAA,KAAA7E,MAAA,CAAAoG,gBAAA,CAA8C;IAkC9CjH,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAA+D,QAAA,CAAAwB,QAAA,CAAuB;;;;;;IA4ClDpG,EAJR,CAAAK,cAAA,mBAAgG,cACrF,eACgB,eACI,eACA;IAAAL,EAAA,CAAAM,MAAA,mBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAM;IAC/BP,EAAA,CAAAK,cAAA,SAAI;IAAAL,EAAA,CAAAM,MAAA,oBAAa;IACnBN,EADmB,CAAAO,YAAA,EAAK,EAClB;IAGFP,EAFJ,CAAAK,cAAA,eAAyB,eACO,sBAEyC;IAAnEL,EAAA,CAAA4B,gBAAA,2BAAAsF,wFAAApF,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAyG,IAAA;MAAA,MAAAtG,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA+D,QAAA,CAAAnC,UAAA,EAAAX,MAAA,MAAAjB,MAAA,CAAA+D,QAAA,CAAAnC,UAAA,GAAAX,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAiC;IACnC9B,EAAA,CAAAO,YAAA,EAAY;IACZP,EAAA,CAAAK,cAAA,kBAA2B;IAAAL,EAAA,CAAAM,MAAA,cAAM;IACnCN,EADmC,CAAAO,YAAA,EAAQ,EACrC;IAEJP,EADF,CAAAK,cAAA,gBAA8B,uBAEoC;IAA9DL,EAAA,CAAA4B,gBAAA,2BAAAwF,yFAAAtF,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAAyG,IAAA;MAAA,MAAAtG,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA+D,QAAA,CAAAhC,SAAA,EAAAd,MAAA,MAAAjB,MAAA,CAAA+D,QAAA,CAAAhC,SAAA,GAAAd,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAgC;IAChC9B,EAAA,CAAAK,cAAA,8BAAiC;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,8BAAiC;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,8BAA6B;IAAAL,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACvDP,EAAA,CAAAK,cAAA,8BAA8B;IAAAL,EAAA,CAAAM,MAAA,kBAAU;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC5DP,EAAA,CAAAK,cAAA,8BAA+B;IAAAL,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,8BAA+B;IAAAL,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,8BAA8B;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACvDP,EAAA,CAAAK,cAAA,8BAA+B;IAAAL,EAAA,CAAAM,MAAA,gBAAQ;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC3DP,EAAA,CAAAK,cAAA,8BAA6B;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACtDP,EAAA,CAAAK,cAAA,8BAA8B;IAAAL,EAAA,CAAAM,MAAA,kBAAU;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC5DP,EAAA,CAAAK,cAAA,8BAA6B;IAAAL,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACvDP,EAAA,CAAAK,cAAA,8BAAiC;IAAAL,EAAA,CAAAM,MAAA,cAAM;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC3DP,EAAA,CAAAK,cAAA,8BAAiC;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,8BAAiC;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC1DP,EAAA,CAAAK,cAAA,8BAA6B;IAAAL,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACxDP,EAAA,CAAAK,cAAA,8BAA6B;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACtDP,EAAA,CAAAK,cAAA,8BAA6B;IAAAL,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IACxDP,EAAA,CAAAK,cAAA,8BAA6B;IAAAL,EAAA,CAAAM,MAAA,cAAM;IACrCN,EADqC,CAAAO,YAAA,EAAoB,EAC5C;IACbP,EAAA,CAAAK,cAAA,kBAA2B;IAAAL,EAAA,CAAAM,MAAA,YAAI;IAEnCN,EAFmC,CAAAO,YAAA,EAAQ,EACnC,EACF;IAEJP,EADF,CAAAK,cAAA,gBAA0B,iBACI;IAAAL,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAO,YAAA,EAAO;IACxCP,EAAA,CAAAK,cAAA,iBAA4B;IAAAL,EAAA,CAAAM,MAAA,IAC0B;IAI9DN,EAJ8D,CAAAO,YAAA,EAAO,EACzD,EACF,EACE,EACF;;;;IA9CoBP,EAAA,CAAAE,WAAA,aAAAW,MAAA,CAAAwG,YAAA,CAA+B;IAUjDrH,EAAA,CAAAoB,SAAA,IAAiC;IAAjCpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA+D,QAAA,CAAAnC,UAAA,CAAiC;IAMjCzC,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA+D,QAAA,CAAAhC,SAAA,CAAgC;IAyBR5C,EAAA,CAAAoB,SAAA,IAC0B;IAD1BpB,EAAA,CAAAmD,kBAAA,KAAAtC,MAAA,CAAA+D,QAAA,CAAAnC,UAAA,GAAA5B,MAAA,CAAA+D,QAAA,CAAAnC,UAAA,WAAA5B,MAAA,CAAA+D,QAAA,CAAAhC,SAAA,GAAA/B,MAAA,CAAA+D,QAAA,CAAAhC,SAAA,eAC0B;;;;;;IA0BpD5C,EAAA,CAAAK,cAAA,sBAE4D;IAD1DL,EAAA,CAAAQ,UAAA,mBAAA8G,oGAAA;MAAA,MAAAC,OAAA,GAAAvH,EAAA,CAAAU,aAAA,CAAA8G,IAAA,EAAA5G,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAA4G,gBAAA,CAAAF,OAAA,CAAAG,KAAA,CAA2B;IAAA,EAAC;IAErC1H,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAO,YAAA,EAAa;;;;;IAFXP,EAAA,CAAAE,WAAA,aAAAW,MAAA,CAAA8G,kBAAA,CAAAC,QAAA,CAAAL,OAAA,CAAAG,KAAA,EAAyD;IACzD1H,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAgG,OAAA,CAAAM,KAAA,MACF;;;;;IANF7H,EADF,CAAAK,cAAA,eAAyE,SACnE;IAAAL,EAAA,CAAAM,MAAA,kBAAW;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACpBP,EAAA,CAAAK,cAAA,eAA4B;IAC1BL,EAAA,CAAAkB,UAAA,IAAA4G,uEAAA,0BAE4D;IAIhE9H,EADE,CAAAO,YAAA,EAAM,EACF;;;;IAN0BP,EAAA,CAAAoB,SAAA,GAAW;IAAXpB,EAAA,CAAA2B,UAAA,YAAAd,MAAA,CAAAkH,QAAA,CAAW;;;;;;IAYrC/H,EADF,CAAAK,cAAA,eAA0D,wBAET;IAA7CL,EAAA,CAAAQ,UAAA,uBAAAwH,mGAAAlG,MAAA;MAAA,MAAAmG,OAAA,GAAAjI,EAAA,CAAAU,aAAA,CAAAwH,IAAA,EAAAtH,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAAsH,iBAAA,CAAArG,MAAA,EAAAmG,OAAA,CAA8B;IAAA,EAAC;IAC9CjI,EAAA,CAAAO,YAAA,EAAe;IACfP,EAAA,CAAAK,cAAA,iBAAkC;IAAAL,EAAA,CAAAM,MAAA,GAAS;IAC7CN,EAD6C,CAAAO,YAAA,EAAQ,EAC/C;;;;IAJUP,EAAA,CAAAoB,SAAA,EAAyB;IAAsBpB,EAA/C,CAAA2B,UAAA,sBAAAsG,OAAA,CAAyB,UAAAA,OAAA,CAAmC;IAGnEjI,EAAA,CAAAoB,SAAA,EAA0B;IAA1BpB,EAAA,CAAA2B,UAAA,uBAAAsG,OAAA,CAA0B;IAACjI,EAAA,CAAAoB,SAAA,EAAS;IAATpB,EAAA,CAAA4D,iBAAA,CAAAqE,OAAA,CAAS;;;;;IAN/CjI,EADF,CAAAK,cAAA,eAA2E,SACrE;IAAAL,EAAA,CAAAM,MAAA,kBAAW;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACpBP,EAAA,CAAAK,cAAA,eAA6B;IAC3BL,EAAA,CAAAkB,UAAA,IAAAkH,gEAAA,mBAA0D;IAO9DpI,EADE,CAAAO,YAAA,EAAM,EACF;;;;IAPmBP,EAAA,CAAAoB,SAAA,GAAY;IAAZpB,EAAA,CAAA2B,UAAA,YAAAd,MAAA,CAAAwH,SAAA,CAAY;;;;;;IA3BnCrI,EAJR,CAAAK,cAAA,mBAA+D,cACpD,eACqB,eACI,eACA;IAAAL,EAAA,CAAAM,MAAA,mBAAE;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACpCP,EAAA,CAAAK,cAAA,SAAI;IAAAL,EAAA,CAAAM,MAAA,uBAAgB;IACtBN,EADsB,CAAAO,YAAA,EAAK,EACrB;IAEJP,EADF,CAAAK,cAAA,eAAwC,sBAG3B;IADTL,EAAA,CAAA4B,gBAAA,2BAAA0G,wFAAAxG,MAAA;MAAA9B,EAAA,CAAAU,aAAA,CAAA6H,IAAA;MAAA,MAAA1H,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAiC,kBAAA,CAAApB,MAAA,CAAA+D,QAAA,CAAA4D,WAAA,EAAA1G,MAAA,MAAAjB,MAAA,CAAA+D,QAAA,CAAA4D,WAAA,GAAA1G,MAAA;MAAA,OAAA9B,EAAA,CAAAe,WAAA,CAAAe,MAAA;IAAA,EAAkC;IAAC9B,EAAA,CAAAQ,UAAA,uBAAAiI,oFAAA;MAAAzI,EAAA,CAAAU,aAAA,CAAA6H,IAAA;MAAA,MAAA1H,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaF,MAAA,CAAA6H,mBAAA,EAAqB;IAAA,EAAC;IAEtE1I,EAAA,CAAAK,cAAA,8BAA+B;IAAAL,EAAA,CAAAM,MAAA,iBAAS;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC5DP,EAAA,CAAAK,cAAA,8BAAgC;IAAAL,EAAA,CAAAM,MAAA,iCAAyB;IAAAN,EAAA,CAAAO,YAAA,EAAoB;IAC7EP,EAAA,CAAAK,cAAA,8BAAiC;IAAAL,EAAA,CAAAM,MAAA,kCAA0B;IAE/DN,EAF+D,CAAAO,YAAA,EAAoB,EACpE,EACT;IAaNP,EAXA,CAAAkB,UAAA,KAAAyH,0DAAA,mBAAyE,KAAAC,0DAAA,mBAWE;IAajF5I,EAFI,CAAAO,YAAA,EAAM,EACE,EACF;;;;IAhCAP,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAA0C,gBAAA,YAAA7B,MAAA,CAAA+D,QAAA,CAAA4D,WAAA,CAAkC;IAQhCxI,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAA+D,QAAA,CAAA4D,WAAA,YAAqC;IAWrCxI,EAAA,CAAAoB,SAAA,EAAsC;IAAtCpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAA+D,QAAA,CAAA4D,WAAA,aAAsC;;;;;IApF5CxI,EALR,CAAAK,cAAA,eAA2D,kBAE5B,kBACM,eACO,cACR;IAAAL,EAAA,CAAAM,MAAA,GAA4C;IAC1EN,EAD0E,CAAAO,YAAA,EAAM,EAC1E;IACNP,EAAA,CAAAK,cAAA,aAAuB;IAAAL,EAAA,CAAAM,MAAA,qBAAc;IAAAN,EAAA,CAAAK,cAAA,eAA4B;IAAAL,EAAA,CAAAM,MAAA,aAAM;IAAON,EAAP,CAAAO,YAAA,EAAO,EAAK;IACnFP,EAAA,CAAAK,cAAA,aAAyB;IAAAL,EAAA,CAAAM,MAAA,0DAAkD;IAE/EN,EAF+E,CAAAO,YAAA,EAAI,EACvE,EACF;IAmDVP,EAhDA,CAAAkB,UAAA,KAAA2H,mDAAA,wBAAgG,KAAAC,mDAAA,wBAgDjC;IA0CjE9I,EAAA,CAAAO,YAAA,EAAM;;;;IAlG8BP,EAAA,CAAAoB,SAAA,GAA4C;IAA5CpB,EAAA,CAAA4D,iBAAA,CAAA/C,MAAA,CAAA+D,QAAA,CAAAf,KAAA,GAAAhD,MAAA,CAAA+D,QAAA,CAAAf,KAAA,kBAA4C;IAQf7D,EAAA,CAAAoB,SAAA,GAA+B;IAA/BpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAA+D,QAAA,CAAAnC,UAAA,OAA+B;IAgD1DzC,EAAA,CAAAoB,SAAA,EAAyB;IAAzBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAA+D,QAAA,CAAAnC,UAAA,CAAyB;;;;;;IA8C3DzC,EADF,CAAAK,cAAA,cAAmC,sBACsB;IAA3CL,EAAA,CAAAQ,UAAA,mBAAAuI,yEAAA;MAAA/I,EAAA,CAAAU,aAAA,CAAAsI,IAAA;MAAA,MAAAnI,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAoI,QAAA,EAAU;IAAA,EAAC;IAAuBjJ,EAAA,CAAAM,MAAA,WAAI;IAC7DN,EAD6D,CAAAO,YAAA,EAAa,EAChE;;;;;;IAERP,EADF,CAAAK,cAAA,cAAmC,sBACoB;IAAzCL,EAAA,CAAAQ,UAAA,mBAAA0I,yEAAA;MAAAlJ,EAAA,CAAAU,aAAA,CAAAyI,IAAA;MAAA,MAAAtI,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAuI,QAAA,EAAU;IAAA,EAAC;IAAqBpJ,EAAA,CAAAM,MAAA,WAAI;IAC3DN,EAD2D,CAAAO,YAAA,EAAa,EAC9D;;;;;IAERP,EADF,CAAAK,cAAA,cAAmC,sBAC0B;IAAAL,EAAA,CAAAM,MAAA,mBAAY;IACzEN,EADyE,CAAAO,YAAA,EAAa,EAC5E;;;;;;IA7TlBP,EAAA,CAAAK,cAAA,sBAAiC;IAC/BL,EAAA,CAAAC,SAAA,iBAAyB;IAInBD,EAHN,CAAAK,cAAA,eAAU,kBACsD,cACnD,SACH;IAAAL,EAAA,CAAAM,MAAA,oBAAa;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACtBP,EAAA,CAAAC,SAAA,2BAAwE;IAC1ED,EAAA,CAAAO,YAAA,EAAU;IACVP,EAAA,CAAAK,cAAA,mBAAsD;IAA5CL,EAAA,CAAAQ,UAAA,mBAAA6I,4DAAA;MAAArJ,EAAA,CAAAU,aAAA,CAAA4I,IAAA;MAAA,MAAAzI,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAA0I,kBAAA,EAAoB;IAAA,EAAC;IAC1CvJ,EADwD,CAAAO,YAAA,EAAW,EACzD;IACVP,EAAA,CAAAK,cAAA,kBAAqD;IAA/CL,EAAA,CAAAQ,UAAA,sBAAAgJ,2DAAA;MAAAxJ,EAAA,CAAAU,aAAA,CAAA4I,IAAA;MAAA,MAAAzI,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAYF,MAAA,CAAA4I,WAAA,EAAa;IAAA,EAAC;IAiM9BzJ,EA/LA,CAAAkB,UAAA,KAAAwI,wCAAA,mBAAoD,KAAAC,wCAAA,mBAoBF,KAAAC,wCAAA,mBA0BU,KAAAC,wCAAA,mBA0CD,KAAAC,wCAAA,mBAuGA;IAyG3D9J,EAAA,CAAAK,cAAA,mBAA8C;IAO5CL,EANA,CAAAkB,UAAA,KAAA6I,4CAAA,sBAAmC,KAAAC,4CAAA,sBAGA,KAAAC,4CAAA,sBAGA;IAM3CjK,EAHM,CAAAO,YAAA,EAAU,EACL,EACE,EACC;;;;IA3TYP,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAA2B,UAAA,UAAAd,MAAA,CAAAqJ,QAAA,CAAkB;IAMhClK,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAsJ,WAAA,OAAuB;IAoBvBnK,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAsJ,WAAA,OAAuB;IA0BvBnK,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAsJ,WAAA,OAAuB;IA0CvBnK,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAsJ,WAAA,OAAuB;IAuGvBnK,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAsJ,WAAA,OAAuB;IA0GjBnK,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAsJ,WAAA,OAAuB;IAGvBnK,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAsJ,WAAA,OAAuB;IAGvBnK,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,SAAAd,MAAA,CAAAsJ,WAAA,OAAuB;;;;;;IAU7CnK,EAAA,CAAAK,cAAA,2BAC+B;IAA7BL,EAAA,CAAAQ,UAAA,mBAAA4J,uEAAA;MAAApK,EAAA,CAAAU,aAAA,CAAA2J,IAAA;MAAA,MAAAxJ,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAyJ,gBAAA,EAAkB;IAAA,EAAC;IAC9BtK,EAAA,CAAAO,YAAA,EAAkB;;;;IAF4CP,EAArB,CAAA2B,UAAA,SAAAd,MAAA,CAAA0J,WAAA,CAAoB,SAAA1J,MAAA,CAAA2J,UAAA,CAAA3J,MAAA,CAAAoD,YAAA,EAAkC;;;AD5ajG,OAAM,MAAOwG,SAAS;EAqCpB;EACQC,kBAAkBA,CAAA;IACxB;IACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,MAAM5G,YAAY,GAAG,IAAI2G,IAAI,CAAC,IAAI,CAAC3G,YAAY,CAAC;IAChDA,YAAY,CAAC4G,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACjC,MAAMC,eAAe,GAAG7G,YAAY,CAAC8G,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;IAElE;IACA,IAAI,CAACD,eAAe,EAAE;MACpB,IAAI,CAAC3G,UAAU,GAAG,IAAI;MACtB;IACF;IAEA,IAAI,IAAI,CAAC6G,cAAc,IAAIF,eAAe,IAAI,IAAI,CAACG,MAAM,EAAE;MACzD;MACA,IAAI,CAACC,gBAAgB,CAACC,4BAA4B,CAAC,IAAI,CAACF,MAAO,CAAC,CAACG,IAAI,CACnE/L,IAAI,CAAC,CAAC,CAAC,CACR,CAACgM,SAAS,CAAC;QACVC,IAAI,EAAGC,UAAU,IAAI;UACnB,IAAIA,UAAU,IAAIA,UAAU,CAACrG,MAAM,GAAG,CAAC,EAAE;YACvC,MAAMsG,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;YAE/B;YACA,IAAI,CAACE,eAAe,CAACC,SAAS,EAAE,CAC7BC,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEL,SAAS,CAACM,gBAAgB,CAAC,CACpCC,MAAM,EAAE,CACRC,IAAI,CAACC,QAAQ,IAAG;cACf,IAAIA,QAAQ,CAACC,KAAK,EAAE;gBAClB;cACF;cAEA,MAAMC,YAAY,GAAGF,QAAQ,CAACG,IAAI;cAElC;cACA,IAAI,CAACjI,UAAU,GAAG;gBAChBkI,EAAE,EAAEb,SAAS,CAACa,EAAG;gBACjB7H,aAAa,EAAE;kBACb6H,EAAE,EAAEb,SAAS,CAACM,gBAAiB;kBAC/BhI,IAAI,EAAEqI,YAAY,CAACrI,IAAI,IAAI,kBAAkB;kBAC7CC,WAAW,EAAEoI,YAAY,CAACpI,WAAW,IAAI,gCAAgC;kBACzEtB,UAAU,EAAE0J,YAAY,CAAC1J,UAAU,IAAI,CAAC;kBACxCG,SAAS,EAAEuJ,YAAY,CAACvJ,SAAS,IAAI;iBACtC;gBACDQ,MAAM,EAAEoI,SAAS,CAACpI,MAAM,IAAI,CAAC;gBAC7BO,SAAS,EAAE6H,SAAS,CAAC7H,SAAS,IAAI,KAAK;gBACvCzB,cAAc,EAAEsJ,SAAS,CAACtJ,cAAc,IAAI,CAAC;gBAC7C2B,KAAK,EAAEsI,YAAY,CAACtI,KAAK,IAAI;eAC9B;YACH,CAAC,CAAC;UACN,CAAC,MAAM;YACL,IAAI,CAACM,UAAU,GAAG,IAAI;UACxB;QACF,CAAC;QACD+H,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAAC/H,UAAU,GAAG,IAAI;QACxB;OACD,CAAC;IACJ;EACF;EAiDAmI,YAAA;IAlJA;IACA,KAAAC,KAAK,GAA4BpN,EAAE,CAAC,IAAI,CAAC;IACzC,KAAA8L,MAAM,GAAkB,IAAI;IAE5B,KAAAD,cAAc,GAAG,IAAI;IAErB;IACA,KAAA/G,YAAY,GAAS,IAAI2G,IAAI,EAAE;IAC/B,KAAA4B,SAAS,GAAe,EAAE;IAC1B,KAAAC,QAAQ,GAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/D,KAAAC,UAAU,GAAW,OAAO;IAC5B,KAAAC,UAAU,GAAW,CAAC;IAItB;IACA,KAAAC,MAAM,GAAmB,EAAE;IAC3B,KAAAzI,UAAU,GAAsB,IAAI;IACpC,KAAAgG,WAAW,GAAG,CAAC;IACf,KAAA0C,UAAU,GAAG,CAAC;IAEd,KAAA/H,MAAM,GAAa,CACjB,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAChD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EACrD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAC7C,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CACtC;IAGD;IACQ,KAAAgI,UAAU,GAA0C,EAAE;IAE9D;IACQ,KAAAC,aAAa,GAAG,KAAK;IAkE7B;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAApI,QAAQ,GAAG,IAAI,CAACqI,aAAa,EAAE;IAC/B,KAAApG,oBAAoB,GAAG,KAAK;IAE5B;IACA,KAAAtB,iBAAiB,GAAG,KAAK;IACzB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAQ,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAe,gBAAgB,GAAG,KAAK;IACxB,KAAAd,gBAAgB,GAAG,EAAE;IACrB,KAAAO,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAU,YAAY,GAAG,KAAK;IAEpB;IACA,KAAA6F,eAAe,GAAG,KAAK;IACvB,KAAA3C,WAAW,GAAgB,IAAI;IAC/B,KAAA4C,qBAAqB,GAAa,EAAE;IAEpC;IACA,KAAApF,QAAQ,GAAG,CACT;MAAEL,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAI,CAAE,EAC7B;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAI,CAAE,CAC9B;IACD,KAAAQ,SAAS,GAAG+E,KAAK,CAACzB,IAAI,CAAC;MAAEzG,MAAM,EAAE;IAAE,CAAE,EAAE,CAACmI,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;IACvD,KAAA3F,kBAAkB,GAAa,EAAE;IACjC,KAAA4F,mBAAmB,GAAa,EAAE;IAElC;IACQ,KAAAC,YAAY,GAAGjP,MAAM,CAACI,YAAY,CAAC;IACnC,KAAAuM,gBAAgB,GAAG3M,MAAM,CAACK,gBAAgB,CAAC;IAC3C,KAAA6O,WAAW,GAAGlP,MAAM,CAACM,WAAW,CAAC;IACjC,KAAA4M,eAAe,GAAGlN,MAAM,CAACO,eAAe,CAAC;IACzC,KAAA4O,KAAK,GAAGnP,MAAM,CAACkB,cAAc,CAAC;IAC9B,KAAAkO,MAAM,GAAGpP,MAAM,CAACmB,MAAM,CAAC;IACvB,KAAAkO,kBAAkB,GAAGrP,MAAM,CAACoB,kBAAkB,CAAC;IAC/C,KAAAkO,gBAAgB,GAAGtP,MAAM,CAACsB,uBAAuB,CAAC;IAClD,KAAAiO,aAAa,GAAG,KAAK,CAAC,CAAC;IAykB/B;IACQ,KAAAC,iBAAiB,GAAgE,EAAE;IAmL3F;IACQ,KAAAC,cAAc,GAAG,KAAK;IAqI9B;IACQ,KAAAC,gBAAgB,GAAmC,EAAE;IAoC7D;IACQ,KAAAC,gBAAgB,GAAmC,EAAE;IAgY7D;IACQ,KAAAC,oBAAoB,GAAmC,EAAE;IAvyC/D;IACA,IAAI,CAACT,KAAK,CAACU,WAAW,CAAC/C,SAAS,CAACgD,MAAM,IAAG;MACxC,MAAMC,SAAS,GAAGD,MAAM,CAAC,MAAM,CAAC;MAChC,MAAME,eAAe,GAAGF,MAAM,CAAC,aAAa,CAAC;MAE7CG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEH,SAAS,CAAC;MAC/DE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEF,eAAe,CAAC;MAE5E;MACA,IAAIA,eAAe,KAAKG,SAAS,EAAE;QACjC,IAAI;UACF,IAAI,CAAC/B,UAAU,GAAGgC,QAAQ,CAACJ,eAAe,CAAC;UAC3CC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC9B,UAAU,CAAC;QAChE,CAAC,CAAC,OAAOT,KAAK,EAAE;UACdsC,OAAO,CAACtC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,IAAI,CAACS,UAAU,GAAG,CAAC;QACrB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,UAAU,GAAG,CAAC;MACrB;MAEA;MACA,IAAI2B,SAAS,EAAE;QACb,IAAI;UACF;UACA,IAAI,qBAAqB,CAACM,IAAI,CAACN,SAAS,CAAC,EAAE;YACzC,IAAI,CAACrK,YAAY,GAAG,IAAI2G,IAAI,CAAC0D,SAAS,CAAC;YACvCE,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAACxK,YAAY,CAAC;UAC5E,CAAC,MAAM;YACLuK,OAAO,CAACtC,KAAK,CAAC,8CAA8C,EAAEoC,SAAS,CAAC;YACxE,IAAI,CAACrK,YAAY,GAAG,IAAI2G,IAAI,EAAE,CAAC,CAAC;UAClC;QACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;UACdsC,OAAO,CAACtC,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrE,IAAI,CAACjI,YAAY,GAAG,IAAI2G,IAAI,EAAE,CAAC,CAAC;QAClC;MACF,CAAC,MAAM;QACL,IAAI,CAAC3G,YAAY,GAAG,IAAI2G,IAAI,EAAE,CAAC,CAAC;MAClC;MAEA;MACA,IAAI,CAACiE,iBAAiB,EAAE;MAExB;MACA,IAAI,CAACC,gBAAgB,EAAE;MAEvB;MACA,IAAI,IAAI,CAAC7D,MAAM,EAAE;QACf,IAAI,CAAC8D,QAAQ,EAAE;MACjB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACvD,eAAe,CAACwD,YAAY,CAAC5D,SAAS,CAAC6D,QAAQ,IAAG;MAG7E,IAAI,CAACA,QAAQ,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE;QACA;MACF;MAEA;MACA,IAAI,CAACxD,MAAM,GAAGiE,QAAQ,CAAC7C,EAAE;MAGzB;MACA,IAAI,CAACoB,WAAW,CAAC0B,WAAW,CAACD,QAAQ,CAAC7C,EAAE,CAAC,CAAChB,SAAS,CAAC+D,QAAQ,IAAG;QAC7D,IAAI,CAACA,QAAQ,EAAE;UACbZ,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE;UACA;QACF;QAEAD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEW,QAAQ,CAAC;QACrD,IAAI,CAACL,QAAQ,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACxC,KAAK,GAAG,IAAI,CAACd,eAAe,CAACwD,YAAY,CAAC7D,IAAI,CACjDhM,SAAS,CAAC8P,QAAQ,IAAG;MACnB,IAAI,CAACA,QAAQ,EAAE;QACb,OAAO/P,EAAE,CAAC,IAAI,CAAC;MACjB;MAEA,OAAO,IAAI,CAACsO,WAAW,CAAC0B,WAAW,CAACD,QAAQ,CAAC7C,EAAE,CAAC;IAClD,CAAC,CAAC,CACH;IAED;IACA,MAAMgD,oBAAoB,GAAG,IAAI,CAAC9C,KAAK,CAAClB,SAAS,CAAC;MAChDC,IAAI,EAAGgE,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACtE,cAAc,GAAGsE,IAAI,CAACC,iBAAiB;UAC5C,IAAI,CAAChF,WAAW,GAAG+E,IAAI;QACzB;MACF;KACD,CAAC;IAEF;IACA,IAAI,CAACN,gBAAgB,GAAG,IAAIhQ,YAAY,EAAE;IAC1C,IAAI,CAACgQ,gBAAgB,CAACQ,GAAG,CAACH,oBAAoB,CAAC;EACjD;EAEAI,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,iBAAiB,EAAE;IAExB;IACAa,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,eAAe,EAAE;IACxB,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,IAAI;MACF;MACA,MAAMhF,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxB,MAAMgF,QAAQ,GAAG,IAAI,CAACpF,UAAU,CAACG,KAAK,CAAC;MAEvC;MACA,MAAMkF,OAAO,GAAa,EAAE;MAC5B,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,YAAY,CAAC5K,MAAM,EAAEoI,CAAC,EAAE,EAAE;QAC5C,MAAMyC,GAAG,GAAGD,YAAY,CAACC,GAAG,CAACzC,CAAC,CAAC;QAC/B,IAAIyC,GAAG,EAAE;UACPF,OAAO,CAACG,IAAI,CAACD,GAAG,CAAC;QACnB;MACF;MAEA;MACAF,OAAO,CAACI,OAAO,CAACF,GAAG,IAAG;QACpB,IAAIA,GAAG,CAACG,UAAU,CAAC,oBAAoB,CAAC,IAAIH,GAAG,KAAK,qBAAqBH,QAAQ,EAAE,EAAE;UACnFE,YAAY,CAACK,UAAU,CAACJ,GAAG,CAAC;QAC9B;MACF,CAAC,CAAC;MAEF;MACA,MAAMK,qBAAqB,GAAGN,YAAY,CAACO,OAAO,CAAC,qBAAqBT,QAAQ,EAAE,CAAC;MAEnF;MACA,IAAI,CAACzC,qBAAqB,GAAG,EAAE;MAC/B,IAAIiD,qBAAqB,EAAE;QACzB,IAAI,CAACjD,qBAAqB,CAAC6C,IAAI,CAACJ,QAAQ,CAAC;MAC3C;IACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACd,IAAI,CAACiB,qBAAqB,GAAG,EAAE;IACjC;EACF;EAEMmD,gBAAgBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpB;MACA;MACA,MAAMtB,QAAQ,GAAGqB,KAAI,CAAC9E,eAAe,CAACgF,YAAY,CAAC/I,KAAK;MAExD,IAAI,CAACwH,QAAQ,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE;MACF;MAEA;MACA8B,KAAI,CAAC9C,WAAW,CAACiD,gBAAgB,CAACxB,QAAQ,CAAC,CAAC7D,SAAS,CAAC+D,QAAQ,IAAG;QAC/D,IAAI,CAACA,QAAQ,EAAE;UACbmB,KAAI,CAAC5C,MAAM,CAACgD,aAAa,CAAC,SAAS,CAAC;UACpC;QACF;QAEA;QACA,IAAIC,OAAO,GAAGxB,QAAQ,CAACyB,mBAAmB,GAAG,IAAIjG,IAAI,CAACwE,QAAQ,CAACyB,mBAAmB,CAAC,GAAG,IAAI;QAC1F,MAAMC,WAAW,GAAG,IAAIlG,IAAI,EAAE;QAE9B;QACA,IAAImG,WAAW,GAAG,KAAK;QACvB,IAAIH,OAAO,YAAYhG,IAAI,EAAE;UAC3BmG,WAAW,GAAGH,OAAO,GAAGE,WAAW;QACrC;QAEA,IAAI,CAACC,WAAW,EAAE;UAChB;UACA,IAAIR,KAAI,CAACzC,aAAa,EAAE;UACxByC,KAAI,CAACzC,aAAa,GAAG,IAAI;UAEzB4B,UAAU,CAAC,MAAK;YACda,KAAI,CAAC5C,MAAM,CAACgD,aAAa,CAAC,UAAU,CAAC;YACrCjB,UAAU,CAAC,MAAK;cACda,KAAI,CAACzC,aAAa,GAAG,KAAK;YAC5B,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,EAAE,GAAG,CAAC;UACP;QACF;QAEA;QACA,MAAMkD,OAAO,GAAGT,KAAI,CAAC/F,UAAU,CAAC+F,KAAI,CAACtM,YAAY,CAAC;QAClD,IAAIsM,KAAI,CAACzD,UAAU,CAACkE,OAAO,CAAC,EAAE;UAC5B;UACAT,KAAI,CAAC3D,MAAM,GAAG2D,KAAI,CAACzD,UAAU,CAACkE,OAAO,CAAC;UAEtC;UACAC,qBAAqB,CAAC,MAAK;YACzBV,KAAI,CAACW,2BAA2B,EAAE;UACpC,CAAC,CAAC;UAEF;UACAX,KAAI,CAAC7F,kBAAkB,EAAE;QAC3B,CAAC,MAAM;UACL;UACA6F,KAAI,CAACxB,QAAQ,EAAE;QACjB;MACF,CAAC,CAAC;MAEF;MACA,MAAMrB,KAAK,GAAG6C,KAAI,CAAC5C,MAAM,CAACwD,GAAG;MAC7B,MAAM7C,SAAS,GAAGiC,KAAI,CAAC/F,UAAU,CAAC+F,KAAI,CAACtM,YAAY,CAAC;MAEpD,IAAIyJ,KAAK,KAAK,QAAQ,EAAE;QACtB;QACA6C,KAAI,CAAC5C,MAAM,CAACyD,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAC/BhD,WAAW,EAAE;YACXiD,IAAI,EAAE/C,SAAS;YACfgD,WAAW,EAAEf,KAAI,CAAC5D,UAAU,KAAK,CAAC,GAAG4D,KAAI,CAAC5D,UAAU,GAAG;WACxD;UACD4E,UAAU,EAAE;SACb,CAAC;MACJ;IAAC;EACH;EAGA;EACAL,2BAA2BA,CAAA;IACzB;IACAD,qBAAqB,CAAC,MAAK;MACzB,MAAMO,OAAO,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,CAAC;MAC7D,IAAIF,OAAO,CAACtM,MAAM,KAAK,CAAC,EAAE;QACxB;MACF;MAEAsM,OAAO,CAACvB,OAAO,CAAC0B,MAAM,IAAG;QACvB,IAAIA,MAAM,YAAYC,gBAAgB,EAAE;UACtC;UACA,MAAMC,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC;UAC1D,IAAI,CAACD,aAAa,EAAE;YAClB;UACF;UAEA;UACA,MAAME,WAAW,GAAGpD,QAAQ,CAACgD,MAAM,CAACjK,KAAK,CAAC;UAC1C,MAAMsK,QAAQ,GAAGrD,QAAQ,CAACgD,MAAM,CAACM,GAAG,CAAC;UACrC,MAAMC,QAAQ,GAAGvD,QAAQ,CAACgD,MAAM,CAACQ,GAAG,CAAC;UAErC;UACA,MAAMC,UAAU,GAAGF,QAAQ,GAAGF,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKE,QAAQ,GAAGF,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;UAE9D;UACAL,MAAM,CAACU,KAAK,CAACC,UAAU,GACrB,iDAAiDF,UAAU,cAAcA,UAAU,kBAAkB;UAEvG;UACAT,MAAM,CAACY,YAAY,CAAC,oBAAoB,EAAEZ,MAAM,CAACjK,KAAK,CAAC;QACzD,CAAC,MAAM,IAAIiK,MAAM,YAAYa,WAAW,IAAIb,MAAM,CAACc,OAAO,KAAK,WAAW,EAAE;UAC1E;UACA,MAAMZ,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC;UAC1D,IAAI,CAACD,aAAa,EAAE;YAClB;UACF;UAEA;UACA,MAAMa,SAAS,GAAGf,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;UACrD,MAAMa,OAAO,GAAGhB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG;UACjD,MAAMc,OAAO,GAAGjB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;UAEnD,MAAMC,WAAW,GAAGpD,QAAQ,CAAC+D,SAAS,CAAC;UACvC,MAAMV,QAAQ,GAAGrD,QAAQ,CAACgE,OAAO,CAAC;UAClC,MAAMT,QAAQ,GAAGvD,QAAQ,CAACiE,OAAO,CAAC;UAElC;UACA,MAAMR,UAAU,GAAGF,QAAQ,GAAGF,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKE,QAAQ,GAAGF,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;UAE9D;UACAL,MAAM,CAACU,KAAK,CAACQ,WAAW,CAAC,kBAAkB,EAAE,GAAGT,UAAU,GAAG,CAAC;UAE9D;UACAT,MAAM,CAACY,YAAY,CAAC,oBAAoB,EAAER,WAAW,CAACe,QAAQ,EAAE,CAAC;QACnE;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACdvE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;EACnD;EAEAuE,WAAWA,CAAA;IACTxE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,IAAI,CAACO,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACiE,WAAW,EAAE;IACrC;EACF;EAEMlE,QAAQA,CAAA;IAAA,IAAAmE,MAAA;IAAA,OAAA1C,iBAAA;MACZ,IAAI,CAAC0C,MAAI,CAACjI,MAAM,EAAE;QAChB;MACF;MAEA;MACAiI,MAAI,CAACpE,gBAAgB,EAAE;MAEvB;MACA,MAAMkC,OAAO,GAAGkC,MAAI,CAAC1I,UAAU,CAAC0I,MAAI,CAACjP,YAAY,CAAC;MAClD,IAAIiP,MAAI,CAACpG,UAAU,CAACkE,OAAO,CAAC,EAAE;QAC5B;QACAkC,MAAI,CAACtG,MAAM,GAAGsG,MAAI,CAACpG,UAAU,CAACkE,OAAO,CAAC;QAEtC;QACAC,qBAAqB,CAAC,MAAK;UACzBiC,MAAI,CAAChC,2BAA2B,EAAE;QACpC,CAAC,CAAC;QAEF;QACAgC,MAAI,CAACxI,kBAAkB,EAAE;QAEzB;MACF;MAEA;MACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAM5G,YAAY,GAAG,IAAI2G,IAAI,CAACsI,MAAI,CAACjP,YAAY,CAAC;MAChDA,YAAY,CAAC4G,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,MAAMC,eAAe,GAAG7G,YAAY,CAAC8G,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;MAElEyD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyE,MAAI,CAAC1I,UAAU,CAAC0I,MAAI,CAACjP,YAAY,CAAC,CAAC;MACpF,IAAI6G,eAAe,EAAE;QACnB;QACA,MAAMqI,eAAe,GAAGD,MAAI,CAAC1I,UAAU,CAACG,KAAK,CAAC;QAC9C,IAAI;UACF,MAAM;YAAEjD,KAAK,EAAE0L;UAAqB,CAAE,SAASF,MAAI,CAACtF,kBAAkB,CAACyF,GAAG,CAAC,yBAAyB,CAAC;UAErG,IAAID,qBAAqB,KAAKD,eAAe,EAAE;YAC7C3E,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;YAEvE;YACA,MAAMnP,cAAc,CAAC4T,MAAI,CAAC1F,YAAY,CAAC8F,SAAS,CAACJ,MAAI,CAACjI,MAAO,CAAC,CAACG,IAAI,CACjE/L,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS;cAAA,IAAAmU,IAAA,GAAA/C,iBAAA,CAAC,WAAM5D,MAAM,EAAG;gBACvB;gBACA,KAAK,MAAM4G,KAAK,IAAI5G,MAAM,EAAE;kBAC1B,IAAI4G,KAAK,CAACnH,EAAE,EAAE;oBACZ,MAAM6G,MAAI,CAAC1F,YAAY,CAACiG,eAAe,CAACD,KAAK,CAACnH,EAAE,CAAC;kBACnD;gBACF;gBAEA;gBACA,MAAM6G,MAAI,CAAC1F,YAAY,CAACkG,+BAA+B,EAAE;gBAEzD;gBACA,OAAO9G,MAAM;cACf,CAAC;cAAA,iBAAA+G,EAAA;gBAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;cAAA;YAAA,IAAC,CACH,CAAC;YAEF;UACF,CAAC,MAAM;YACLrF,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAChE;QACF,CAAC,CAAC,OAAOvC,KAAK,EAAE;UACdsC,OAAO,CAACtC,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAE1E;QACF;QAEA;QACA,IAAIgH,MAAI,CAAClI,cAAc,EAAE;UACvBkI,MAAI,CAAChI,gBAAgB,CAAC4I,0BAA0B,CAACZ,MAAI,CAACjI,MAAM,EAAEiI,MAAI,CAACjP,YAAY,CAAC,CAC7EoH,SAAS,CAAC;YACTa,KAAK,EAAGA,KAAK,IAAI;cACfsC,OAAO,CAACtC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;YAChE;WACD,CAAC;QACN;MACF;MACA;MACAgH,MAAI,CAAC1F,YAAY,CAAC8F,SAAS,CAACJ,MAAI,CAACjI,MAAM,CAAC,CAACG,IAAI,CAC3C/L,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAACwN,MAAM,IAAG;QACjB;QACA,MAAMmH,cAAc,GAAGb,MAAI,CAACc,mBAAmB,CAACpH,MAAM,EAAEsG,MAAI,CAACjP,YAAY,CAAC;QAE1E,IAAI8P,cAAc,CAAC7O,MAAM,KAAK,CAAC,EAAE;UAC/B,OAAO/F,EAAE,CAAC,EAAE,CAAC;QACf;QAEA;QACA,MAAM8U,oBAAoB,GAAG,CAAC,GAAGF,cAAc,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC7D,IAAID,CAAC,CAACE,UAAU,IAAID,CAAC,CAACC,UAAU,EAAE;YAChC,OAAO,IAAIzJ,IAAI,CAACwJ,CAAC,CAACC,UAAU,CAAC,CAACtJ,OAAO,EAAE,GAAG,IAAIH,IAAI,CAACuJ,CAAC,CAACE,UAAU,CAAC,CAACtJ,OAAO,EAAE;UAC5E;UACA,OAAOoJ,CAAC,CAAC9H,EAAE,IAAI+H,CAAC,CAAC/H,EAAE,GAAG8H,CAAC,CAAC9H,EAAE,CAACiI,aAAa,CAACF,CAAC,CAAC/H,EAAE,CAAC,GAAG,CAAC;QACpD,CAAC,CAAC;QAEF;QACA,OAAO6G,MAAI,CAAC1F,YAAY,CAAC+G,uBAAuB,CAACrB,MAAI,CAACjI,MAAO,EAAEiI,MAAI,CAACjP,YAAY,CAAC,CAACmH,IAAI,CACpF/L,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAACoV,WAAW,IAAG;UACtB;UACA,MAAMC,cAAc,GAAyC,EAAE;UAC/DD,WAAW,CAACvE,OAAO,CAAC/F,QAAQ,IAAG;YAC7BuK,cAAc,CAACvK,QAAQ,CAACwK,QAAQ,CAAC,GAAGxK,QAAQ;UAC9C,CAAC,CAAC;UAEF;UACA;UACA,IAAIY,eAAe,EAAE;YACnB;YACA,MAAMqI,eAAe,GAAGD,MAAI,CAAC1I,UAAU,CAACG,KAAK,CAAC;YAC9C,OAAOuI,MAAI,CAACtF,kBAAkB,CAACyF,GAAG,CAAC,yBAAyB,CAAC,CAACrH,IAAI,CAAC,CAAC;cAAEtE,KAAK,EAAE0L;YAAqB,CAAE,KAAI;cACtG,IAAIA,qBAAqB,KAAKD,eAAe,EAAE;gBAC7C3E,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;gBAEvE;gBACA,OAAOyE,MAAI,CAACrF,gBAAgB,CAAC8G,gBAAgB,CAACzB,MAAI,CAACjI,MAAO,EAAEgJ,oBAAoB,CAAC,CAACjI,IAAI,CAAC4I,OAAO,IAAG;kBAC/F;kBACA,OAAOX,oBAAoB,CAAC/U,GAAG,CAACsU,KAAK,IAAG;oBACtC,MAAMtJ,QAAQ,GAAGuK,cAAc,CAACjB,KAAK,CAACnH,EAAG,CAAC;oBAC1C,MAAMwI,gBAAgB,GAAGD,OAAO,CAACpB,KAAK,CAACnH,EAAG,CAAC,IAAI,CAAC;oBAEhD;oBACA6G,MAAI,CAAC1F,YAAY,CAACsH,iBAAiB,CAACtB,KAAK,CAACnH,EAAG,EAAEwI,gBAAgB,CAAC,CAACxJ,SAAS,EAAE;oBAE5E,OAAO;sBACL,GAAGmI,KAAK;sBACR7P,SAAS,EAAE,CAAAuG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEvG,SAAS,KAAI,KAAK;sBACvCzB,cAAc,EAAE,CAAAgI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEhI,cAAc,KAAI,CAAC;sBAC7CkB,MAAM,EAAEyR;qBACO;kBACnB,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC7I,IAAI,CAAC+I,MAAM,IAAG;kBACf;kBACA7B,MAAI,CAACtF,kBAAkB,CAACoH,GAAG,CAAC,yBAAyB,EAAE7B,eAAe,CAAC;kBACvE,OAAO4B,MAAM;gBACf,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLvG,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;gBAErF;gBACA,OAAOwF,oBAAoB,CAAC/U,GAAG,CAACsU,KAAK,IAAG;kBACtC,MAAMtJ,QAAQ,GAAGuK,cAAc,CAACjB,KAAK,CAACnH,EAAG,CAAC;kBAE1C,OAAO;oBACL,GAAGmH,KAAK;oBACR7P,SAAS,EAAE,CAAAuG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEvG,SAAS,KAAI,KAAK;oBACvCzB,cAAc,EAAE,CAAAgI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEhI,cAAc,KAAI,CAAC;oBAC7CkB,MAAM,EAAEoQ,KAAK,CAACpQ,MAAM,IAAI;mBACT;gBACnB,CAAC,CAAC;cACJ;YACF,CAAC,CAAC,CAAC6R,KAAK,CAAC/I,KAAK,IAAG;cACfsC,OAAO,CAACtC,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;cAE1E;cACA,OAAO+H,oBAAoB,CAAC/U,GAAG,CAACsU,KAAK,IAAG;gBACtC,MAAMtJ,QAAQ,GAAGuK,cAAc,CAACjB,KAAK,CAACnH,EAAG,CAAC;gBAE1C,OAAO;kBACL,GAAGmH,KAAK;kBACR7P,SAAS,EAAE,CAAAuG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEvG,SAAS,KAAI,KAAK;kBACvCzB,cAAc,EAAE,CAAAgI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEhI,cAAc,KAAI,CAAC;kBAC7CkB,MAAM,EAAEoQ,KAAK,CAACpQ,MAAM,IAAI;iBACT;cACnB,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA,OAAO8R,OAAO,CAACC,OAAO,CAAClB,oBAAoB,CAAC/U,GAAG,CAACsU,KAAK,IAAG;cACtD,MAAMtJ,QAAQ,GAAGuK,cAAc,CAACjB,KAAK,CAACnH,EAAG,CAAC;cAE1C,OAAO;gBACL,GAAGmH,KAAK;gBACR7P,SAAS,EAAE,CAAAuG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEvG,SAAS,KAAI,KAAK;gBACvCzB,cAAc,EAAE,CAAAgI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEhI,cAAc,KAAI,CAAC;gBAC7CkB,MAAM,EAAE,CAAC,CAAC;eACK;YACnB,CAAC,CAAC,CAAC;UACL;QACF,CAAC,CAAC,CACH;MAGH,CAAC,CAAC,CACH,CAACiI,SAAS,CAAC;QACVC,IAAI,EAAG8J,kBAAkB,IAAI;UAC3B;UACA,MAAMC,YAAY,GAAG,CAAC,GAAGD,kBAAkB,CAAC,CAAClB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACzD,IAAID,CAAC,CAACE,UAAU,IAAID,CAAC,CAACC,UAAU,EAAE;cAChC,OAAO,IAAIzJ,IAAI,CAACwJ,CAAC,CAACC,UAAU,CAAC,CAACtJ,OAAO,EAAE,GAAG,IAAIH,IAAI,CAACuJ,CAAC,CAACE,UAAU,CAAC,CAACtJ,OAAO,EAAE;YAC5E;YACA,OAAOoJ,CAAC,CAAC9H,EAAE,IAAI+H,CAAC,CAAC/H,EAAE,GAAG8H,CAAC,CAAC9H,EAAE,CAACiI,aAAa,CAACF,CAAC,CAAC/H,EAAE,CAAC,GAAG,CAAC;UACpD,CAAC,CAAC;UAEF;UACA6G,MAAI,CAACoC,uBAAuB,CAACD,YAAY,CAAC;UAE1C;UACAnC,MAAI,CAACtG,MAAM,GAAGyI,YAAY;UAE1B;UACA,MAAMrE,OAAO,GAAGkC,MAAI,CAAC1I,UAAU,CAAC0I,MAAI,CAACjP,YAAY,CAAC;UAClDiP,MAAI,CAACpG,UAAU,CAACkE,OAAO,CAAC,GAAGqE,YAAY;UAEvC;UACAnC,MAAI,CAACqC,sBAAsB,EAAE;UAE7B;UACAtE,qBAAqB,CAAC,MAAK;YACzBiC,MAAI,CAAChC,2BAA2B,EAAE;UACpC,CAAC,CAAC;UAEF;UACAgC,MAAI,CAACxI,kBAAkB,EAAE;QAC3B,CAAC;QACDwB,KAAK,EAAGA,KAAK,IAAI;UACfsC,OAAO,CAACtC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC/C;OACD,CAAC;IAAC;EAEL;EAEA2C,iBAAiBA,CAAA;IACf,MAAMlE,KAAK,GAAG,IAAIC,IAAI,EAAE;IAExB;IACA;IACA,MAAM4K,UAAU,GAAG7K,KAAK,CAAC8K,MAAM,EAAE,CAAC,CAAC;IACnC,MAAMC,cAAc,GAAGF,UAAU,KAAK,CAAC,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC,CAAC,CAAC;IAC9D,MAAMG,WAAW,GAAG,IAAI/K,IAAI,CAACD,KAAK,CAAC;IACnCgL,WAAW,CAACC,OAAO,CAACjL,KAAK,CAACkL,OAAO,EAAE,GAAGH,cAAc,GAAI,CAAC,GAAG,IAAI,CAAC/I,UAAW,CAAC;IAE7E,IAAI,CAACH,SAAS,GAAG,EAAE;IACnB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAM+D,IAAI,GAAG,IAAIzG,IAAI,CAAC+K,WAAW,CAAC;MAClCtE,IAAI,CAACuE,OAAO,CAACD,WAAW,CAACE,OAAO,EAAE,GAAGvI,CAAC,CAAC;MAEvC,MAAMwI,UAAU,GAAG,IAAI,CAACtL,UAAU,CAAC6G,IAAI,CAAC;MACxC,MAAM0E,OAAO,GAAG,IAAI,CAAC/R,SAAS,CAACqN,IAAI,EAAE1G,KAAK,CAAC;MAC3C,MAAMqL,UAAU,GAAG,IAAI,CAAChS,SAAS,CAACqN,IAAI,EAAE,IAAI,CAACpN,YAAY,CAAC;MAC1D,MAAMgS,QAAQ,GAAG5E,IAAI,GAAG1G,KAAK;MAE7B;MACA,MAAMqG,OAAO,GAAG8E,UAAU;MAC1B,IAAII,WAAW,GAAG,CAAC;MACnB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,oBAAoB,GAAG,CAAC;MAE5B,IAAI,IAAI,CAACrI,iBAAiB,CAACiD,OAAO,CAAC,EAAE;QACnC,MAAMqF,MAAM,GAAG,IAAI,CAACtI,iBAAiB,CAACiD,OAAO,CAAC;QAC9CkF,WAAW,GAAGG,MAAM,CAACC,KAAK;QAC1BH,eAAe,GAAGE,MAAM,CAAC1S,SAAS;QAClCyS,oBAAoB,GAAGF,WAAW,GAAG,CAAC,GAClCK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;MACP;MAEA,IAAI,CAAC1J,SAAS,CAACwD,IAAI,CAAC;QAClBqB,IAAI,EAAEyE,UAAU;QAChBW,GAAG,EAAEpF,IAAI,CAACwE,OAAO,EAAE;QACnBxU,QAAQ,EAAE0U,OAAO;QACjBzU,WAAW,EAAE0U,UAAU;QACvBhV,SAAS,EAAEiV,QAAQ;QACnBS,YAAY,EAAER,WAAW;QACzBS,gBAAgB,EAAER,eAAe;QACjC/V,qBAAqB,EAAEgW;OACxB,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAACnL,MAAM,EAAE;MACf;MACAyE,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAKA4F,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACtK,MAAM,EAAE;IAElB;IACA,IAAI,CAACuB,SAAS,CAACyD,OAAO,CAAC,CAAC2G,QAAQ,EAAEC,KAAK,KAAI;MACzC,IAAID,QAAQ,CAAC5V,SAAS,EAAE;MAExB,MAAMqQ,IAAI,GAAG,IAAIzG,IAAI,CAACgM,QAAQ,CAACvF,IAAI,CAAC;MACpC,MAAML,OAAO,GAAG,IAAI,CAACxG,UAAU,CAAC6G,IAAI,CAAC;MAErC;MACA,IAAI,IAAI,CAACtD,iBAAiB,CAACiD,OAAO,CAAC,EAAE;QACnC,MAAMqF,MAAM,GAAG,IAAI,CAACtI,iBAAiB,CAACiD,OAAO,CAAC;QAC9C,IAAI,CAACxE,SAAS,CAACqK,KAAK,CAAC,CAACH,YAAY,GAAGL,MAAM,CAACC,KAAK;QACjD,IAAI,CAAC9J,SAAS,CAACqK,KAAK,CAAC,CAACF,gBAAgB,GAAGN,MAAM,CAAC1S,SAAS;QACzD,IAAI,CAAC6I,SAAS,CAACqK,KAAK,CAAC,CAACzW,qBAAqB,GAAGiW,MAAM,CAACC,KAAK,GAAG,CAAC,GAC1DC,IAAI,CAACC,KAAK,CAAEH,MAAM,CAAC1S,SAAS,GAAG0S,MAAM,CAACC,KAAK,GAAI,GAAG,CAAC,GACnD,CAAC;QACL;MACF;MAEA;MACA,IAAI,IAAI,CAACxJ,UAAU,CAACkE,OAAO,CAAC,EAAE;QAC5B,MAAM8F,YAAY,GAAG,IAAI,CAAChK,UAAU,CAACkE,OAAO,CAAC;QAC7C,MAAMkF,WAAW,GAAGY,YAAY,CAAC5R,MAAM;QACvC,MAAMiR,eAAe,GAAGW,YAAY,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrT,SAAS,CAAC,CAACuB,MAAM;QAEpE;QACA,IAAI,CAAC6I,iBAAiB,CAACiD,OAAO,CAAC,GAAG;UAChCsF,KAAK,EAAEJ,WAAW;UAClBvS,SAAS,EAAEwS;SACZ;QAED;QACA,IAAI,CAAC3J,SAAS,CAACqK,KAAK,CAAC,CAACH,YAAY,GAAGR,WAAW;QAChD,IAAI,CAAC1J,SAAS,CAACqK,KAAK,CAAC,CAACF,gBAAgB,GAAGR,eAAe;QACxD,IAAI,CAAC3J,SAAS,CAACqK,KAAK,CAAC,CAACzW,qBAAqB,GAAG8V,WAAW,GAAG,CAAC,GACzDK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;QACL;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACvG,eAAe,EAAE;EACxB;EAEA;EACQqE,mBAAmBA,CAACpH,MAAe,EAAEyE,IAAU;IACrD,MAAM4F,OAAO,GAAG,IAAIrM,IAAI,CAACyG,IAAI,CAAC;IAC9B,MAAM6F,SAAS,GAAGD,OAAO,CAACxB,MAAM,EAAE,CAAC,CAAC;IACpC;IACA,MAAM0B,eAAe,GAAGD,SAAS,KAAK,CAAC,GAAG,CAAC,GAAGA,SAAS,GAAG,CAAC,CAAC,CAAC;IAC7D,MAAME,UAAU,GAAGH,OAAO,CAACpB,OAAO,EAAE,CAAC,CAAC;IAEtCrH,OAAO,CAACC,GAAG,CAAC,wCAAwC,IAAI,CAACjE,UAAU,CAAC6G,IAAI,CAAC,kBAAkB6F,SAAS,aAAaC,eAAe,oBAAoBC,UAAU,EAAE,CAAC;IAEjK,MAAMrD,cAAc,GAAGnH,MAAM,CAACmK,MAAM,CAACvD,KAAK,IAAG;MAC3ChF,OAAO,CAACC,GAAG,CAAC,6BAA6B+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,YAAY0P,KAAK,CAAC9N,UAAU,aAAa8N,KAAK,CAAChL,WAAW,wBAAwBgL,KAAK,CAAC6D,iBAAiB,yBAAyB7D,KAAK,CAAC8D,kBAAkB,EAAE,CAAC;MAE7N,IAAI,CAAC9D,KAAK,CAAC+D,MAAM,EAAE;QACjB/I,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,gCAAgC,CAAC;QACxF,OAAO,KAAK;MACd;MAEA;MACA,IAAI0P,KAAK,CAACa,UAAU,EAAE;QACpB,MAAMmD,WAAW,GAAG,IAAI5M,IAAI,CAAC4I,KAAK,CAACa,UAAU,CAAC;QAC9CmD,WAAW,CAAC3M,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChCoM,OAAO,CAACpM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE5B;QACA,IAAIoM,OAAO,GAAGO,WAAW,EAAE;UACzB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAIhE,KAAK,CAAChL,WAAW,KAAK,KAAK,EAAE;QAC/B,OAAO,IAAI;MACb;MAEA;MACA,IAAIgL,KAAK,CAAChL,WAAW,KAAK,MAAM,EAAE;QAChC,IAAI,CAACgL,KAAK,CAAC6D,iBAAiB,EAAE;UAC5B7I,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,yDAAyD,CAAC;UACjH,OAAO,IAAI,CAAC,CAAC;QACf;QAEA;QACA,IAAI2T,QAAQ,GAAU,EAAE;QACxB,IAAI,OAAOjE,KAAK,CAAC6D,iBAAiB,KAAK,QAAQ,EAAE;UAC/CI,QAAQ,GAAGjE,KAAK,CAAC6D,iBAAiB,CAACK,KAAK,CAAC,GAAG,CAAC,CAACxY,GAAG,CAACuX,GAAG,IAAIA,GAAG,CAACkB,IAAI,EAAE,CAAC;UACpEnJ,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,sCAAsC0P,KAAK,CAAC6D,iBAAiB,cAAc,EAAEI,QAAQ,CAAC;QAC/I,CAAC,MAAM,IAAIrK,KAAK,CAACwK,OAAO,CAACpE,KAAK,CAAC6D,iBAAiB,CAAC,EAAE;UACjDI,QAAQ,GAAGjE,KAAK,CAAC6D,iBAAiB;UAClC7I,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,mCAAmC,EAAE2T,QAAQ,CAAC;QACvG;QAEA;QACA;QACA,MAAMI,YAAY,GAAG,IAAI,CAACC,eAAe,CAACX,eAAe,CAAC;QAC1D,MAAMY,WAAW,GAAG,IAAI,CAACC,cAAc,CAACb,eAAe,CAAC;QAExD3I,OAAO,CAACC,GAAG,CAAC,8BAA8BsJ,WAAW,KAAKF,YAAY,KAAKV,eAAe,oBAAoB,EAAEM,QAAQ,CAAC;QAEzH,MAAMQ,UAAU,GAAGR,QAAQ,CAAC7P,QAAQ,CAACuP,eAAe,CAAC,IACnDM,QAAQ,CAAC7P,QAAQ,CAACuP,eAAe,CAACrE,QAAQ,EAAE,CAAC,IAC7C2E,QAAQ,CAAC7P,QAAQ,CAACiQ,YAAY,CAAC,IAC/BJ,QAAQ,CAAC7P,QAAQ,CAACmQ,WAAW,CAAC;QAEhCvJ,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,wBAAwBiU,WAAW,KAAKE,UAAU,EAAE,CAAC;QAE5G,OAAOA,UAAU;MACnB;MAEA;MACA,IAAIzE,KAAK,CAAChL,WAAW,KAAK,OAAO,EAAE;QACjC,IAAI,CAACgL,KAAK,CAAC8D,kBAAkB,EAAE,OAAO,IAAI,CAAC,CAAC;QAE5C;QACA,IAAIG,QAAQ,GAAU,EAAE;QACxB,IAAI,OAAOjE,KAAK,CAAC8D,kBAAkB,KAAK,QAAQ,EAAE;UAChDG,QAAQ,GAAGjE,KAAK,CAAC8D,kBAAkB,CAACI,KAAK,CAAC,GAAG,CAAC,CAACxY,GAAG,CAACuX,GAAG,IAAI9H,QAAQ,CAAC8H,GAAG,CAACkB,IAAI,EAAE,CAAC,CAAC;QACjF,CAAC,MAAM,IAAIvK,KAAK,CAACwK,OAAO,CAACpE,KAAK,CAAC8D,kBAAkB,CAAC,EAAE;UAClDG,QAAQ,GAAGjE,KAAK,CAAC8D,kBAAkB;QACrC;QAEA;QACA,OAAOG,QAAQ,CAAC7P,QAAQ,CAACwP,UAAU,CAAC,IAClCK,QAAQ,CAAC7P,QAAQ,CAACwP,UAAU,CAACtE,QAAQ,EAAE,CAAC;MAC5C;MAEA,OAAO,KAAK;IACd,CAAC,CAAC;IAEFtE,OAAO,CAACC,GAAG,CAAC,uBAAuB7B,MAAM,CAAC1H,MAAM,cAAc6O,cAAc,CAAC7O,MAAM,aAAa,IAAI,CAACsF,UAAU,CAAC6G,IAAI,CAAC,EAAE,CAAC;IAExH,OAAO0C,cAAc;EACvB;EAEA9S,UAAUA,CAACgW,OAAY;IACrB,IAAI,IAAI,CAAClK,aAAa,EAAE;MACtB;IACF;IAEA,MAAMmL,QAAQ,GAAGjB,OAAO,CAAC5F,IAAI;IAE7B,IAAI,CAACtE,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACoL,gBAAgB,GAAGlB,OAAO;IAE/B,MAAM5F,IAAI,GAAG,IAAIzG,IAAI,CAACsN,QAAQ,CAAC;IAC/B,IAAI,CAACjU,YAAY,GAAGoN,IAAI;IAExB,IAAI,CAAC7E,SAAS,CAACyD,OAAO,CAAC2G,QAAQ,IAAG;MAChCA,QAAQ,CAACtV,WAAW,GAAGsV,QAAQ,CAACvF,IAAI,KAAK6G,QAAQ;IACnD,CAAC,CAAC;IAEF,MAAME,aAAa,GAAG,IAAI,CAAC5N,UAAU,CAAC6G,IAAI,CAAC;IAE3C,IAAI,CAAC1D,MAAM,CAACyD,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BhD,WAAW,EAAE;QACXiD,IAAI,EAAE+G,aAAa;QACnB9G,WAAW,EAAE,IAAI,CAAC3E,UAAU,KAAK,CAAC,GAAG,IAAI,CAACA,UAAU,GAAG;OACxD;MACD4E,UAAU,EAAE;KACb,CAAC;IAEF,IAAI,CAACzC,gBAAgB,EAAE;IAEvBY,UAAU,CAAC,MAAK;MACd,IAAI,CAACX,QAAQ,EAAE;MACf,IAAI,CAAChC,aAAa,GAAG,KAAK;IAC5B,CAAC,EAAE,EAAE,CAAC;EACR;EAKAsL,UAAUA,CAACC,SAAiB;IAC1B;IACA,IAAI,IAAI,CAACtK,cAAc,EAAE;MACvB;IACF;IAEA,IAAI,CAACA,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAACrB,UAAU,IAAI2L,SAAS;IAE5B;IACA,IAAI,CAACzJ,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACc,eAAe,EAAE;IAEtB;IACA,MAAMrB,SAAS,GAAG,IAAI,CAAC9D,UAAU,CAAC,IAAI,CAACvG,YAAY,CAAC;IACpD,IAAI,CAAC0J,MAAM,CAACyD,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BhD,WAAW,EAAE;QACXiD,IAAI,EAAE/C,SAAS;QACfgD,WAAW,EAAE,IAAI,CAAC3E;OACnB;MACD4E,UAAU,EAAE;KACb,CAAC;IAEF;IACA7B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC1B,cAAc,GAAG,KAAK;IAC7B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACQ2B,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC1E,MAAM,EAAE;IAElB;IACA,IAAI,CAACuC,YAAY,CAAC8F,SAAS,CAAC,IAAI,CAACrI,MAAO,CAAC,CAACG,IAAI,CAC5C/L,IAAI,CAAC,CAAC,CAAC,CACR,CAACgM,SAAS,CAACkN,SAAS,IAAG;MACtB;MACA,MAAMC,eAAe,GAAG,IAAI,CAAChM,SAAS,CACnCuK,MAAM,CAACH,QAAQ,IAAI,CAACA,QAAQ,CAAC5V,SAAS,CAAC,CACvC9B,GAAG,CAAC0X,QAAQ,IAAG;QACd,MAAMvF,IAAI,GAAG,IAAIzG,IAAI,CAACgM,QAAQ,CAACvF,IAAI,CAAC;QACpC,MAAML,OAAO,GAAG,IAAI,CAACxG,UAAU,CAAC6G,IAAI,CAAC;QAErC;QACA,IAAI,IAAI,CAACtD,iBAAiB,CAACiD,OAAO,CAAC,EAAE;UACnC,OAAO7R,EAAE,CAAC;YACRkS,IAAI,EAAEuF,QAAQ,CAACvF,IAAI;YACnBnH,QAAQ,EAAE,IAAI,CAAC6D,iBAAiB,CAACiD,OAAO;WACzC,CAAC;QACJ;QAEA;QACA,MAAMyH,YAAY,GAAG,IAAI,CAACzE,mBAAmB,CAACuE,SAAS,EAAElH,IAAI,CAAC;QAE9D;QACA,IAAIoH,YAAY,CAACvT,MAAM,KAAK,CAAC,EAAE;UAC7B,MAAMwT,aAAa,GAAG;YAAEpC,KAAK,EAAE,CAAC;YAAE3S,SAAS,EAAE;UAAC,CAAE;UAChD,IAAI,CAACoK,iBAAiB,CAACiD,OAAO,CAAC,GAAG0H,aAAa;UAC/C,OAAOvZ,EAAE,CAAC;YACRkS,IAAI,EAAEuF,QAAQ,CAACvF,IAAI;YACnBnH,QAAQ,EAAEwO;WACX,CAAC;QACJ;QAEA;QACA,OAAO,IAAI,CAAClL,YAAY,CAAC+G,uBAAuB,CAAC,IAAI,CAACtJ,MAAO,EAAEoG,IAAI,CAAC,CAACjG,IAAI,CACvE/L,IAAI,CAAC,CAAC,CAAC,EACPH,GAAG,CAACyZ,YAAY,IAAG;UACjB;UACA,MAAMC,QAAQ,GAAGH,YAAY,CAACvZ,GAAG,CAAC8X,CAAC,IAAIA,CAAC,CAAC3K,EAAE,CAAC;UAC5C,MAAMwM,gBAAgB,GAAGF,YAAY,CAAC5B,MAAM,CAAC+B,CAAC,IAAIF,QAAQ,CAAChR,QAAQ,CAACkR,CAAC,CAACpE,QAAQ,CAAC,CAAC;UAChF,MAAMyB,eAAe,GAAG0C,gBAAgB,CAAC9B,MAAM,CAAC+B,CAAC,IAAIA,CAAC,CAACnV,SAAS,CAAC,CAACuB,MAAM;UACxE,MAAMgR,WAAW,GAAGuC,YAAY,CAACvT,MAAM;UAEvC;UACA,MAAMgF,QAAQ,GAAG;YACfoM,KAAK,EAAEJ,WAAW;YAClBvS,SAAS,EAAEwS;WACZ;UAED;UACA,IAAI,CAACpI,iBAAiB,CAACiD,OAAO,CAAC,GAAG9G,QAAQ;UAE1C,OAAO;YACLmH,IAAI,EAAEuF,QAAQ,CAACvF,IAAI;YACnBnH;WACD;QACH,CAAC,CAAC,CACH;MACH,CAAC,CAAC;MAEJ;MACAjL,QAAQ,CAACuZ,eAAe,CAAC,CAACnN,SAAS,CAAC0N,OAAO,IAAG;QAC5C;QACAA,OAAO,CAAC9I,OAAO,CAAC8E,MAAM,IAAG;UACvB,MAAM8B,KAAK,GAAG,IAAI,CAACrK,SAAS,CAACwM,SAAS,CAACC,EAAE,IAAIA,EAAE,CAAC5H,IAAI,KAAK0D,MAAM,CAAC1D,IAAI,CAAC;UACrE,IAAIwF,KAAK,IAAI,CAAC,EAAE;YACd,IAAI,CAACrK,SAAS,CAACqK,KAAK,CAAC,CAACH,YAAY,GAAG3B,MAAM,CAAC7K,QAAQ,CAACoM,KAAK;YAC1D,IAAI,CAAC9J,SAAS,CAACqK,KAAK,CAAC,CAACF,gBAAgB,GAAG5B,MAAM,CAAC7K,QAAQ,CAACvG,SAAS;YAClE,IAAI,CAAC6I,SAAS,CAACqK,KAAK,CAAC,CAACzW,qBAAqB,GAAG2U,MAAM,CAAC7K,QAAQ,CAACoM,KAAK,GAAG,CAAC,GACnEC,IAAI,CAACC,KAAK,CAAEzB,MAAM,CAAC7K,QAAQ,CAACvG,SAAS,GAAGoR,MAAM,CAAC7K,QAAQ,CAACoM,KAAK,GAAI,GAAG,CAAC,GACrE,CAAC;UACP;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAxH,gBAAgBA,CAAA;IACd,MAAMnE,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,IAAI,IAAI,CAAC5G,SAAS,CAAC,IAAI,CAACC,YAAY,EAAE0G,KAAK,CAAC,EAAE;MAC5C,IAAI,CAAC+B,UAAU,GAAG,OAAO;IAC3B,CAAC,MAAM,IAAI,IAAI,CAAC1I,SAAS,CAAC,IAAI,CAACC,YAAY,EAAE,IAAI2G,IAAI,CAACD,KAAK,CAACiL,OAAO,CAACjL,KAAK,CAACkL,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1F,IAAI,CAACnJ,UAAU,GAAG,WAAW;IAC/B,CAAC,MAAM,IAAI,IAAI,CAAC1I,SAAS,CAAC,IAAI,CAACC,YAAY,EAAE,IAAI2G,IAAI,CAACD,KAAK,CAACiL,OAAO,CAACjL,KAAK,CAACkL,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1F,IAAI,CAACnJ,UAAU,GAAG,UAAU;IAC9B,CAAC,MAAM;MACL;MACA,IAAI,CAACA,UAAU,GAAG,IAAI,CAACzI,YAAY,CAACiV,kBAAkB,CAAC,OAAO,EAAE;QAC9DC,OAAO,EAAE,OAAO;QAChB1C,GAAG,EAAE,SAAS;QACd2C,KAAK,EAAE;OACR,CAAC;IACJ;EACF;EAKM7V,WAAWA,CAACiQ,KAAmB;IAAA,IAAA6F,MAAA;IAAA,OAAA7I,iBAAA;MACnC,IAAI,CAAC6I,MAAI,CAACpO,MAAM,IAAI,CAACuI,KAAK,CAACnH,EAAE,EAAE;MAE/B;MACA,IAAIgN,MAAI,CAACpL,gBAAgB,CAACuF,KAAK,CAACnH,EAAE,CAAC,EAAE;QACnCmC,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,qDAAqD,CAAC;QAC7G;MACF;MAEA;MACAuV,MAAI,CAACpL,gBAAgB,CAACuF,KAAK,CAACnH,EAAE,CAAC,GAAG,IAAI;MACtCmC,OAAO,CAACC,GAAG,CAAC,wCAAwC+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,GAAG,CAAC;MAE/E,IAAI;QACF;QACA;QACA;QAEA;QACA0K,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,qCAAqC0P,KAAK,CAACtR,cAAc,EAAE,CAAC;QAEnH;QACA;QACA,OAAOmX,MAAI,CAACpL,gBAAgB,CAACuF,KAAK,CAACnH,EAAE,CAAC;QACtC;MACF,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdsC,OAAO,CAACtC,KAAK,CAAC,uCAAuCsH,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,IAAI,EAAEoI,KAAK,CAAC;MAC1F,CAAC,SAAS;QACR;QACA,OAAOmN,MAAI,CAACpL,gBAAgB,CAACuF,KAAK,CAACnH,EAAE,CAAC;QACtCmC,OAAO,CAACC,GAAG,CAAC,wCAAwC+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,GAAG,CAAC;MACjF;IAAC;EACH;EAKM1B,mBAAmBA,CAACoR,KAAmB,EAAE8F,KAAW;IAAA,IAAAC,MAAA;IAAA,OAAA/I,iBAAA;MACxD,IAAI,CAAC+I,MAAI,CAACtO,MAAM,IAAI,CAACuI,KAAK,CAACnH,EAAE,EAAE;MAE/B;MACA,IAAIkN,MAAI,CAACrL,gBAAgB,CAACsF,KAAK,CAACnH,EAAE,CAAC,EAAE;QACnC;MACF;MAEA;MACAkN,MAAI,CAACrL,gBAAgB,CAACsF,KAAK,CAACnH,EAAE,CAAC,GAAG,IAAI;MAEtC,IAAI;QACF;QACA,MAAMmN,kBAAkB,GAAGhG,KAAK,CAAC7P,SAAS;QAC1C6K,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,+BAA+B0V,kBAAkB,EAAE,CAAC;QAE3G;QACA,IAAIF,KAAK,EAAE;UACT;UACA,MAAM3H,MAAM,GAAG2H,KAAK,CAAChX,MAAM,KAAKgX,KAAK,CAACG,MAAM,GAAGH,KAAK,CAACG,MAAM,CAAC/R,KAAK,GAAG,IAAI,CAAC;UACzE6R,MAAI,CAAChX,sBAAsB,CAACoP,MAAM,CAAC;UAEnC;UACA,MAAME,aAAa,GAAGF,MAAM,YAAYa,WAAW,GAAGb,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI;UACjG,IAAID,aAAa,IAAIA,aAAa,KAAK2B,KAAK,CAACnH,EAAE,EAAE;YAC/C,OAAOkN,MAAI,CAACrL,gBAAgB,CAACsF,KAAK,CAACnH,EAAE,CAAC;YACtC;UACF;UAEA;UACA,IAAI0F,WAAW,GAAG,CAAC;UACnB,IAAIuH,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACG,MAAM,CAAC/R,KAAK,KAAKgH,SAAS,EAAE;YACpD;YACAqD,WAAW,GAAGuH,KAAK,CAACG,MAAM,CAAC/R,KAAK;UAClC,CAAC,MAAM,IAAIiK,MAAM,YAAYC,gBAAgB,EAAE;YAC7C;YACAG,WAAW,GAAGpD,QAAQ,CAACgD,MAAM,CAACjK,KAAK,CAAC;UACtC,CAAC,MAAM,IAAIiK,MAAM,YAAYa,WAAW,IAAIb,MAAM,CAACc,OAAO,KAAK,WAAW,EAAE;YAC1E;YACA,MAAMC,SAAS,GAAGf,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;YACrDC,WAAW,GAAGpD,QAAQ,CAAC+D,SAAS,CAAC;UACnC;UAEA;UACAc,KAAK,CAACtR,cAAc,GAAG6P,WAAW;UAElC;UACA;UACA,IAAIyB,KAAK,CAAC9N,UAAU,KAAK,OAAO,EAAE;YAChC;YACA8N,KAAK,CAAC7P,SAAS,GAAGoO,WAAW,IAAIyB,KAAK,CAAC/Q,UAAU;UACnD,CAAC,MAAM;YAAE;YACP;YACA+Q,KAAK,CAAC7P,SAAS,GAAGoO,WAAW,GAAGyB,KAAK,CAAC/Q,UAAU;UAClD;UAEA+L,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,0BAA0B0P,KAAK,CAAC7P,SAAS,EAAE,CAAC;QACrG;QAEA;QACA,MAAM+V,SAAS,GAAG;UAAE,GAAGlG;QAAK,CAAE;QAE9B;QACA,MAAMuB,MAAM,SAASwE,MAAI,CAAC/L,YAAY,CAACmM,qBAAqB,CAC1DJ,MAAI,CAACtO,MAAM,EACXuI,KAAK,CAACnH,EAAE,EACRkN,MAAI,CAACtV,YAAY,EACjBuP,KAAK,CAACtR,cAAc,EACpBwX,SAAS,CACV;QAED;QACAlG,KAAK,CAAC7P,SAAS,GAAGoR,MAAM,CAACpR,SAAS;QAClC6P,KAAK,CAACtR,cAAc,GAAG6S,MAAM,CAAC7S,cAAc;QAE5C;QACA,MAAMyI,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1B,MAAM5G,YAAY,GAAG,IAAI2G,IAAI,CAAC2O,MAAI,CAACtV,YAAY,CAAC;QAChDA,YAAY,CAAC4G,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC,MAAMC,eAAe,GAAG7G,YAAY,CAAC8G,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;QAElE;QACA,IAAID,eAAe,EAAE;UACnB;UACA;UAEA;UACA,IAAI1H,MAAM,GAAG2R,MAAM,CAAC3R,MAAM;UAE1B;UACA,MAAMwW,cAAc,GAAGpG,KAAK,CAAC7P,SAAS;UAEtC6K,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,4BAA4B0V,kBAAkB,SAASI,cAAc,EAAE,CAAC;UAE/H;UACA,IAAIJ,kBAAkB,KAAKI,cAAc,EAAE;YACzC,IAAIA,cAAc,EAAE;cAClB;cACAxW,MAAM,EAAE;cACRoL,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,8DAA8DV,MAAM,EAAE,CAAC;YAChI,CAAC,MAAM;cACL;cACAA,MAAM,GAAGmT,IAAI,CAACpE,GAAG,CAAC,CAAC,EAAE/O,MAAM,GAAG,CAAC,CAAC;cAChCoL,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,8DAA8DV,MAAM,EAAE,CAAC;YAChI;YAEA;YACAmW,MAAI,CAAC/L,YAAY,CAACsH,iBAAiB,CAACtB,KAAK,CAACnH,EAAG,EAAEjJ,MAAM,CAAC,CAACiI,SAAS,CAAC;cAC/DC,IAAI,EAAEA,CAAA,KAAK;gBACTkD,OAAO,CAACC,GAAG,CAAC,oDAAoD+E,KAAK,CAACnH,EAAE,OAAOjJ,MAAM,EAAE,CAAC;gBAExF;gBACA,MAAM4N,OAAO,GAAGuI,MAAI,CAAC/O,UAAU,CAAC+O,MAAI,CAACtV,YAAY,CAAC;gBAClD,IAAIsV,MAAI,CAACzM,UAAU,CAACkE,OAAO,CAAC,EAAE;kBAC5B,MAAM6I,gBAAgB,GAAGN,MAAI,CAACzM,UAAU,CAACkE,OAAO,CAAC,CAACgI,SAAS,CAAChC,CAAC,IAAIA,CAAC,CAAC3K,EAAE,KAAKmH,KAAK,CAACnH,EAAE,CAAC;kBACnF,IAAIwN,gBAAgB,IAAI,CAAC,EAAE;oBACzBN,MAAI,CAACzM,UAAU,CAACkE,OAAO,CAAC,CAAC6I,gBAAgB,CAAC,CAACzW,MAAM,GAAGA,MAAM;kBAC5D;gBACF;cACF,CAAC;cACD8I,KAAK,EAAGA,KAAU,IAAKsC,OAAO,CAACtC,KAAK,CAAC,8CAA8CsH,KAAK,CAACnH,EAAE,GAAG,EAAEH,KAAK;aACtG,CAAC;UACJ,CAAC,MAAM;YACLsC,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,KAAKmH,KAAK,CAAC1P,IAAI,yDAAyDV,MAAM,EAAE,CAAC;UAC3H;QACF,CAAC,MAAM;UACL;UACAoL,OAAO,CAACC,GAAG,CAAC,6CAA6C8K,MAAI,CAAC/O,UAAU,CAAC+O,MAAI,CAACtV,YAAY,CAAC,mCAAmC,CAAC;UAE/H;UACAsV,MAAI,CAAC/L,YAAY,CAACsM,QAAQ,CAACtG,KAAK,CAACnH,EAAG,CAAC,CAAChB,SAAS,CAACc,YAAY,IAAG;YAC7D,IAAI,CAACA,YAAY,EAAE;cACjBqC,OAAO,CAACtC,KAAK,CAAC,8CAA8CsH,KAAK,CAACnH,EAAE,EAAE,CAAC;cACvE;YACF;YAEA;YACAkN,MAAI,CAAC1L,gBAAgB,CAACkM,eAAe,CAACR,MAAI,CAACtO,MAAO,EAAEuI,KAAK,CAACnH,EAAG,CAAC,CAC3DL,IAAI,CAAC6I,gBAAgB,IAAG;cACvBrG,OAAO,CAACC,GAAG,CAAC,4CAA4C+E,KAAK,CAACnH,EAAE,eAAewI,gBAAgB,EAAE,CAAC;cAElG;cACA0E,MAAI,CAAC/L,YAAY,CAACsH,iBAAiB,CAACtB,KAAK,CAACnH,EAAG,EAAEwI,gBAAgB,CAAC,CAACxJ,SAAS,CAAC;gBACzEC,IAAI,EAAEA,CAAA,KAAK;kBACTkD,OAAO,CAACC,GAAG,CAAC,oDAAoD+E,KAAK,CAACnH,EAAE,OAAOwI,gBAAgB,EAAE,CAAC;kBAElG;kBACA,MAAMmF,WAAW,GAAGT,MAAI,CAAC/O,UAAU,CAACG,KAAK,CAAC;kBAC1C6D,OAAO,CAACC,GAAG,CAAC,gGAAgG,CAAC;kBAC7G,OAAO8K,MAAI,CAACzM,UAAU,CAACkN,WAAW,CAAC;kBAEnC;kBACA,MAAMC,UAAU,GAAGV,MAAI,CAAC/M,SAAS,CAACwM,SAAS,CAACC,EAAE,IAAIA,EAAE,CAAC5H,IAAI,KAAK2I,WAAW,CAAC;kBAC1E,IAAIC,UAAU,IAAI,CAAC,EAAE;oBACnB,OAAOV,MAAI,CAACxL,iBAAiB,CAACiM,WAAW,CAAC;oBAC1CT,MAAI,CAACW,yBAAyB,CAACF,WAAW,CAAC;kBAC7C;gBACF,CAAC;gBACD9N,KAAK,EAAGA,KAAU,IAAKsC,OAAO,CAACtC,KAAK,CAAC,8CAA8CsH,KAAK,CAACnH,EAAE,GAAG,EAAEH,KAAK;eACtG,CAAC;YACJ,CAAC,CAAC,CACD+I,KAAK,CAAC/I,KAAK,IAAG;cACbsC,OAAO,CAACtC,KAAK,CAAC,iDAAiDsH,KAAK,CAACnH,EAAE,GAAG,EAAEH,KAAK,CAAC;YACpF,CAAC,CAAC;UACN,CAAC,CAAC;QACJ;QAEA;QACAqN,MAAI,CAACY,aAAa,CAAC3G,KAAK,CAAC;QAEzB;QACA,MAAMxC,OAAO,GAAGuI,MAAI,CAAC/O,UAAU,CAAC+O,MAAI,CAACtV,YAAY,CAAC;QAClD,IAAIsV,MAAI,CAACzM,UAAU,CAACkE,OAAO,CAAC,EAAE;UAC5B;UACA,MAAM6I,gBAAgB,GAAGN,MAAI,CAACzM,UAAU,CAACkE,OAAO,CAAC,CAACgI,SAAS,CAAChC,CAAC,IAAIA,CAAC,CAAC3K,EAAE,KAAKmH,KAAK,CAACnH,EAAE,CAAC;UACnF,IAAIwN,gBAAgB,IAAI,CAAC,EAAE;YACzBN,MAAI,CAACzM,UAAU,CAACkE,OAAO,CAAC,CAAC6I,gBAAgB,CAAC,GAAG;cAAE,GAAGrG;YAAK,CAAE;UAC3D;QACF;QAEA;QACA,OAAO+F,MAAI,CAACxL,iBAAiB,CAACiD,OAAO,CAAC;QAEtC;QACAuI,MAAI,CAACW,yBAAyB,CAAClJ,OAAO,CAAC;QAEvC;QACAuI,MAAI,CAACjE,uBAAuB,CAACiE,MAAI,CAAC3M,MAAM,CAAC;MAC3C,CAAC,CAAC,OAAOV,KAAK,EAAE;QACdsC,OAAO,CAACtC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACnE,CAAC,SAAS;QACR;QACA,OAAOqN,MAAI,CAACrL,gBAAgB,CAACsF,KAAK,CAACnH,EAAE,CAAC;MACxC;IAAC;EACH;EAEA;EACQ6N,yBAAyBA,CAAClJ,OAAe;IAC/C;IACA,MAAM6F,KAAK,GAAG,IAAI,CAACrK,SAAS,CAACwM,SAAS,CAACC,EAAE,IAAIA,EAAE,CAAC5H,IAAI,KAAKL,OAAO,CAAC;IACjE,IAAI6F,KAAK,GAAG,CAAC,EAAE;IAEf;IACA,IAAI,IAAI,CAAC/J,UAAU,CAACkE,OAAO,CAAC,EAAE;MAC5B,MAAM8F,YAAY,GAAG,IAAI,CAAChK,UAAU,CAACkE,OAAO,CAAC;MAC7C,MAAMkF,WAAW,GAAGY,YAAY,CAAC5R,MAAM;MACvC,MAAMiR,eAAe,GAAGW,YAAY,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrT,SAAS,CAAC,CAACuB,MAAM;MAEpE;MACA,IAAI,CAAC6I,iBAAiB,CAACiD,OAAO,CAAC,GAAG;QAChCsF,KAAK,EAAEJ,WAAW;QAClBvS,SAAS,EAAEwS;OACZ;MAED;MACA,IAAI,CAAC3J,SAAS,CAACqK,KAAK,CAAC,CAACH,YAAY,GAAGR,WAAW;MAChD,IAAI,CAAC1J,SAAS,CAACqK,KAAK,CAAC,CAACF,gBAAgB,GAAGR,eAAe;MACxD,IAAI,CAAC3J,SAAS,CAACqK,KAAK,CAAC,CAACzW,qBAAqB,GAAG8V,WAAW,GAAG,CAAC,GACzDK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;MAEL;IACF;IAEA;IACA,IAAI,IAAI,CAACjL,MAAM,EAAE;MACf,MAAMoG,IAAI,GAAG,IAAIzG,IAAI,CAACoG,OAAO,CAAC;MAE9B,IAAI,CAACxD,YAAY,CAAC+G,uBAAuB,CAAC,IAAI,CAACtJ,MAAM,EAAEoG,IAAI,CAAC,CAACjG,IAAI,CAC/D/L,IAAI,CAAC,CAAC,CAAC,CACR,CAACgM,SAAS,CAACsN,YAAY,IAAG;QACzB,IAAI,CAACnL,YAAY,CAAC8F,SAAS,CAAC,IAAI,CAACrI,MAAO,CAAC,CAACG,IAAI,CAC5C/L,IAAI,CAAC,CAAC,CAAC,CACR,CAACgM,SAAS,CAACuB,MAAM,IAAG;UACnB;UACA,MAAM6L,YAAY,GAAG,IAAI,CAACzE,mBAAmB,CAACpH,MAAM,EAAEyE,IAAI,CAAC;UAE3D;UACA,MAAMuH,QAAQ,GAAGH,YAAY,CAACvZ,GAAG,CAAC8X,CAAC,IAAIA,CAAC,CAAC3K,EAAE,CAAC;UAC5C,MAAMwM,gBAAgB,GAAGF,YAAY,CAAC5B,MAAM,CAAC+B,CAAC,IAAIF,QAAQ,CAAChR,QAAQ,CAACkR,CAAC,CAACpE,QAAQ,CAAC,CAAC;UAChF,MAAMyB,eAAe,GAAG0C,gBAAgB,CAAC9B,MAAM,CAAC+B,CAAC,IAAIA,CAAC,CAACnV,SAAS,CAAC,CAACuB,MAAM;UACxE,MAAMgR,WAAW,GAAGuC,YAAY,CAACvT,MAAM;UAEvC;UACA,IAAI,CAAC6I,iBAAiB,CAACiD,OAAO,CAAC,GAAG;YAChCsF,KAAK,EAAEJ,WAAW;YAClBvS,SAAS,EAAEwS;WACZ;UAED;UACA,IAAI,CAAC3J,SAAS,CAACqK,KAAK,CAAC,CAACH,YAAY,GAAGR,WAAW;UAChD,IAAI,CAAC1J,SAAS,CAACqK,KAAK,CAAC,CAACF,gBAAgB,GAAGR,eAAe;UACxD,IAAI,CAAC3J,SAAS,CAACqK,KAAK,CAAC,CAACzW,qBAAqB,GAAG8V,WAAW,GAAG,CAAC,GACzDK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;QACP,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;EAEA;EACQiE,aAAaA,CAAC3G,KAAmB;IACvC;IACA,MAAM4G,YAAY,GAAG3I,QAAQ,CAAC4I,aAAa,CAAC,mBAAmB7G,KAAK,CAACnH,EAAE,IAAI,CAAC;IAC5E,IAAI,CAAC+N,YAAY,EAAE;MACjB5L,OAAO,CAACtC,KAAK,CAAC,mDAAmDsH,KAAK,CAACnH,EAAE,EAAE,CAAC;MAC5E;IACF;IAEA;IACA,IAAImH,KAAK,CAAC7P,SAAS,EAAE;MACnByW,YAAY,CAACE,SAAS,CAAC9K,GAAG,CAAC,WAAW,CAAC;IACzC,CAAC,MAAM;MACL4K,YAAY,CAACE,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;IAC5C;IAEA;IACA,MAAMC,cAAc,GAAGJ,YAAY,CAAC1I,gBAAgB,CAAC,eAAe,CAAC;IACrE,IAAI8I,cAAc,IAAIA,cAAc,CAACtV,MAAM,GAAG,CAAC,EAAE;MAC/C;MACA,MAAMyF,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAM5G,YAAY,GAAG,IAAI2G,IAAI,CAAC,IAAI,CAAC3G,YAAY,CAAC;MAChDA,YAAY,CAAC4G,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,MAAMC,eAAe,GAAG7G,YAAY,CAAC8G,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;MAElE;MACA,IAAID,eAAe,EAAE;QACnB,MAAM2P,WAAW,GAAGjH,KAAK,CAACpQ,MAAM,IAAI,CAAC;QACrCoL,OAAO,CAACC,GAAG,CAAC,oBAAoB+E,KAAK,CAACnH,EAAE,gBAAgBmH,KAAK,CAAC7P,SAAS,aAAa8W,WAAW,EAAE,CAAC;QAElG;QACAD,cAAc,CAACvK,OAAO,CAACyK,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACpE;YACCA,OAAuB,CAACrI,KAAK,CAACwI,OAAO,GAAG,OAAO;YAChDH,OAAO,CAACI,WAAW,GAAG,KAAKL,WAAW,GAAG;UAC3C;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAD,cAAc,CAACvK,OAAO,CAACyK,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACnEA,OAAuB,CAACrI,KAAK,CAACwI,OAAO,GAAG,MAAM;YAC/CH,OAAO,CAACI,WAAW,GAAG,EAAE;UAC1B;QACF,CAAC,CAAC;MACJ;IACF;IAEA;IACA,MAAMC,YAAY,GAAGX,YAAY,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACjE,IAAIU,YAAY,EAAE;MAAA,IAAAC,qBAAA;MAChB,MAAMC,UAAU,IAAAD,qBAAA,GAAGD,YAAY,CAACJ,aAAa,cAAAK,qBAAA,uBAA1BA,qBAAA,CAA4BV,SAAS,CAACM,QAAQ,CAAC,eAAe,CAAC;MAClF,MAAMM,UAAU,GAAGD,UAAU,GAAG,GAAG,GAAG,EAAE;MACxC,MAAME,cAAc,GAAG3H,KAAK,CAAC5Q,SAAS,KAAK,OAAO,IAAI,CAACqY,UAAU,GAAG,IAAIzH,KAAK,CAAC5Q,SAAS,EAAE,GAAG,EAAE;MAE9FmY,YAAY,CAACD,WAAW,GAAG,GAAGtH,KAAK,CAACtR,cAAc,GAAGgZ,UAAU,IAAI1H,KAAK,CAAC/Q,UAAU,GAAGyY,UAAU,GAAGC,cAAc,EAAE;IACrH;IAEA3M,OAAO,CAACC,GAAG,CAAC,mCAAmC+E,KAAK,CAACnH,EAAE,EAAE,CAAC;EAC5D;EAEA;EACA9J,sBAAsBA,CAACoP,MAA6C;IAClE,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA;IACA,IAAIyJ,aAA0B;IAC9B,IAAIrJ,WAAW,GAAG,CAAC;IACnB,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIE,QAAQ,GAAG,GAAG;IAClB,IAAIL,aAAa,GAAG,EAAE;IAEtB,IAAIF,MAAM,YAAYC,gBAAgB,EAAE;MACtC;MACAwJ,aAAa,GAAGzJ,MAAM;MACtBE,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE;MAC1DC,WAAW,GAAGpD,QAAQ,CAACgD,MAAM,CAACjK,KAAK,CAAC;MACpCsK,QAAQ,GAAGrD,QAAQ,CAACgD,MAAM,CAACM,GAAG,CAAC;MAC/BC,QAAQ,GAAGvD,QAAQ,CAACgD,MAAM,CAACQ,GAAG,CAAC;IACjC,CAAC,MAAM,IAAIR,MAAM,YAAYa,WAAW,IAAIb,MAAM,CAACc,OAAO,KAAK,WAAW,EAAE;MAC1E;MACA2I,aAAa,GAAGzJ,MAAM;MACtBE,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE;MAE1D;MACA,MAAMY,SAAS,GAAGf,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;MACrD,MAAMa,OAAO,GAAGhB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG;MACjD,MAAMc,OAAO,GAAGjB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;MAEnDC,WAAW,GAAGpD,QAAQ,CAAC+D,SAAS,CAAC;MACjCV,QAAQ,GAAGrD,QAAQ,CAACgE,OAAO,CAAC;MAC5BT,QAAQ,GAAGvD,QAAQ,CAACiE,OAAO,CAAC;IAC9B,CAAC,MAAM;MACL;IACF;IAEA,IAAI,CAACf,aAAa,EAAE;MAClB;IACF;IAEA;IACA,MAAMO,UAAU,GAAGF,QAAQ,GAAGF,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKE,QAAQ,GAAGF,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;IAE9D;IACA,IAAIoJ,aAAa,CAAC3I,OAAO,KAAK,WAAW,EAAE;MACzC2I,aAAa,CAAC/I,KAAK,CAACQ,WAAW,CAAC,kBAAkB,EAAE,GAAGT,UAAU,GAAG,CAAC;IACvE,CAAC,MAAM;MACL;MACAgJ,aAAa,CAAC/I,KAAK,CAACC,UAAU,GAC5B,iDAAiDF,UAAU,cAAcA,UAAU,kBAAkB;IACzG;IAEA;IACAgJ,aAAa,CAAC7I,YAAY,CAAC,oBAAoB,EAAER,WAAW,CAACe,QAAQ,EAAE,CAAC;EAC1E;EAKA;;;;;EAKMxO,eAAeA,CAACkH,SAAqB;IAAA,IAAA6P,MAAA;IAAA,OAAA7K,iBAAA;MACzC,IAAI,CAAC6K,MAAI,CAACpQ,MAAM,IAAI,CAACO,SAAS,CAACa,EAAE,EAAE;MAEnC;MACA,IAAIgP,MAAI,CAAClN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC,EAAE;QAC3CmC,OAAO,CAACC,GAAG,CAAC,yBAAyBjD,SAAS,CAACa,EAAE,oDAAoD,CAAC;QACtG;MACF;MAEA;MACAgP,MAAI,CAAClN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC,GAAG,IAAI;MAC9CmC,OAAO,CAACC,GAAG,CAAC,6CAA6CjD,SAAS,CAACa,EAAE,EAAE,CAAC;MAExE,IAAI;QACF;QACA;QACAmC,OAAO,CAACC,GAAG,CAAC,wCAAwCjD,SAAS,CAACa,EAAE,EAAE,CAAC;QAEnE;QACA,MAAMiP,QAAQ,GAAG9P,SAAS,CAACtJ,cAAc,KAAK,CAAC,GAAGsJ,SAAS,CAAChH,aAAa,CAAC/B,UAAU,GAAG,CAAC;QACxF,MAAM8Y,iBAAiB,GAAGD,QAAQ,KAAK9P,SAAS,CAAChH,aAAa,CAAC/B,UAAU;QAEzE;QACA+I,SAAS,CAACtJ,cAAc,GAAGoZ,QAAQ;QACnC9P,SAAS,CAAC7H,SAAS,GAAG4X,iBAAiB;QAEvC/M,OAAO,CAACC,GAAG,CAAC,iCAAiCjD,SAAS,CAACa,EAAE,aAAab,SAAS,CAACtJ,cAAc,gBAAgBsJ,SAAS,CAAC7H,SAAS,EAAE,CAAC;QAEpI;QACA,MAAMgH,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1B,MAAM5G,YAAY,GAAG,IAAI2G,IAAI,CAACyQ,MAAI,CAACpX,YAAY,CAAC;QAChDA,YAAY,CAAC4G,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC,MAAMkL,OAAO,GAAG9R,YAAY,CAAC8G,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;QAE1D;QACA,IAAI,CAACgL,OAAO,EAAE;UACZvH,OAAO,CAACC,GAAG,CAAC,sDAAsD4M,MAAI,CAAC7Q,UAAU,CAAC6Q,MAAI,CAACpX,YAAY,CAAC,EAAE,CAAC;UACvG,OAAOoX,MAAI,CAAClN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC;UAC9C;QACF;QAEA;QACAgP,MAAI,CAACG,iBAAiB,CAAChQ,SAAS,CAAC;QAEjC,IAAI;UACF,MAAMuJ,MAAM,SAASsG,MAAI,CAACnQ,gBAAgB,CAACuQ,yBAAyB,CAClEjQ,SAAS,CAACa,EAAE,EACZgP,MAAI,CAACpQ,MAAM,EACXoQ,MAAI,CAACpX,YAAY,CAAC;WACnB;UAEDuK,OAAO,CAACC,GAAG,CAAC,8CAA8CjD,SAAS,CAACa,EAAE,EAAE,CAAC;UACzEmC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEsG,MAAM,CAAC;UAEjD;UACAvJ,SAAS,CAAC7H,SAAS,GAAGoR,MAAM,CAACpR,SAAS;UACtC6H,SAAS,CAACtJ,cAAc,GAAG6S,MAAM,CAAC7S,cAAc;UAChDsJ,SAAS,CAACpI,MAAM,GAAG2R,MAAM,CAAC3R,MAAM;UAEhC;UACAiY,MAAI,CAACG,iBAAiB,CAAChQ,SAAS,CAAC;UAEjC;UACA;UACA,MAAMwF,OAAO,GAAGqK,MAAI,CAAC7Q,UAAU,CAAC6Q,MAAI,CAACpX,YAAY,CAAC;UAClD,OAAOoX,MAAI,CAACtN,iBAAiB,CAACiD,OAAO,CAAC;UAEtC;UACAqK,MAAI,CAACnB,yBAAyB,CAAClJ,OAAO,CAAC;UAEvC;UACAqK,MAAI,CAAC/F,uBAAuB,CAAC+F,MAAI,CAACzO,MAAM,CAAC;UAEzC;UACA,OAAOyO,MAAI,CAAClN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC;UAC9CmC,OAAO,CAACC,GAAG,CAAC,6CAA6CjD,SAAS,CAACa,EAAE,EAAE,CAAC;QAC1E,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdsC,OAAO,CAACtC,KAAK,CAAC,wCAAwCV,SAAS,CAACa,EAAE,GAAG,EAAEH,KAAK,CAAC;UAE7E;UACAV,SAAS,CAACtJ,cAAc,GAAGsJ,SAAS,CAACtJ,cAAc,KAAK,CAAC,GAAGsJ,SAAS,CAAChH,aAAa,CAAC/B,UAAU,GAAG,CAAC;UAClG+I,SAAS,CAAC7H,SAAS,GAAG6H,SAAS,CAACtJ,cAAc,KAAKsJ,SAAS,CAAChH,aAAa,CAAC/B,UAAU;UACrF4Y,MAAI,CAACG,iBAAiB,CAAChQ,SAAS,CAAC;UAEjC,OAAO6P,MAAI,CAAClN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC;QAChD;MACF,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdsC,OAAO,CAACtC,KAAK,CAAC,2CAA2CV,SAAS,CAACa,EAAE,GAAG,EAAEH,KAAK,CAAC;QAChF,OAAOmP,MAAI,CAAClN,oBAAoB,CAAC3C,SAAS,CAACa,EAAE,CAAC;MAChD;IAAC;EACH;EAEA;EACQmP,iBAAiBA,CAAChQ,SAAqB;IAC7C;IACA,MAAM4O,YAAY,GAAG3I,QAAQ,CAAC4I,aAAa,CAAC,qCAAqC7O,SAAS,CAACa,EAAE,IAAI,CAAC;IAClG,IAAI,CAAC+N,YAAY,EAAE;MACjB5L,OAAO,CAACtC,KAAK,CAAC,wDAAwDV,SAAS,CAACa,EAAE,EAAE,CAAC;MACrF;IACF;IAEA;IACA,IAAIb,SAAS,CAAC7H,SAAS,EAAE;MACvByW,YAAY,CAACE,SAAS,CAAC9K,GAAG,CAAC,WAAW,CAAC;IACzC,CAAC,MAAM;MACL4K,YAAY,CAACE,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;IAC5C;IAEA;IACA,MAAMC,cAAc,GAAGJ,YAAY,CAAC1I,gBAAgB,CAAC,eAAe,CAAC;IACrE,IAAI8I,cAAc,IAAIA,cAAc,CAACtV,MAAM,GAAG,CAAC,EAAE;MAC/C;MACA,MAAMyF,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAM5G,YAAY,GAAG,IAAI2G,IAAI,CAAC,IAAI,CAAC3G,YAAY,CAAC;MAChDA,YAAY,CAAC4G,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,MAAMC,eAAe,GAAG7G,YAAY,CAAC8G,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;MAElE;MACA,IAAID,eAAe,EAAE;QACnB,MAAM2P,WAAW,GAAGjP,SAAS,CAACpI,MAAM,IAAI,CAAC;QACzCoL,OAAO,CAACC,GAAG,CAAC,yBAAyBjD,SAAS,CAACa,EAAE,gBAAgBb,SAAS,CAAC7H,SAAS,aAAa8W,WAAW,EAAE,CAAC;QAE/G;QACAD,cAAc,CAACvK,OAAO,CAACyK,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACpE;YACCA,OAAuB,CAACrI,KAAK,CAACwI,OAAO,GAAG,OAAO;YAChDH,OAAO,CAACI,WAAW,GAAG,KAAKL,WAAW,GAAG;UAC3C;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAD,cAAc,CAACvK,OAAO,CAACyK,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACnEA,OAAuB,CAACrI,KAAK,CAACwI,OAAO,GAAG,MAAM;YAC/CH,OAAO,CAACI,WAAW,GAAG,EAAE;UAC1B;QACF,CAAC,CAAC;MACJ;IACF;IAEA;IACA,MAAMC,YAAY,GAAGX,YAAY,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACjE,IAAIU,YAAY,EAAE;MAChB,MAAMW,QAAQ,GAAGlQ,SAAS,CAAChH,aAAa,CAAC5B,SAAS,KAAK,OAAO,GAAG,IAAI4I,SAAS,CAAChH,aAAa,CAAC5B,SAAS,EAAE,GAAG,EAAE;MAC7GmY,YAAY,CAACD,WAAW,GAAG,GAAGtP,SAAS,CAACtJ,cAAc,IAAIsJ,SAAS,CAAChH,aAAa,CAAC/B,UAAU,GAAGiZ,QAAQ,EAAE;IAC3G;IAEA;IACAhM,UAAU,CAAC,MAAK;MACd,IAAI0K,YAAY,CAACO,aAAa,EAAE;QAC9B,MAAME,OAAO,GAAGT,YAAY,CAACO,aAAa,CAACtI,KAAK,CAACwI,OAAO;QACxDT,YAAY,CAACO,aAAa,CAACtI,KAAK,CAACwI,OAAO,GAAG,MAAM;QACjD;QACA,KAAKT,YAAY,CAACO,aAAa,CAACgB,YAAY;QAC5CvB,YAAY,CAACO,aAAa,CAACtI,KAAK,CAACwI,OAAO,GAAGA,OAAO;MACpD;IACF,CAAC,EAAE,CAAC,CAAC;IAELrM,OAAO,CAACC,GAAG,CAAC,wCAAwCjD,SAAS,CAACa,EAAE,EAAE,CAAC;EACrE;EAEAuP,iBAAiBA,CAACtC,KAAY;IAC5BA,KAAK,CAACuC,cAAc,EAAE;IACtB,IAAI,CAAC7O,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACpI,QAAQ,GAAG,IAAI,CAACqI,aAAa,EAAE;IACpC,IAAI,CAACtF,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC4F,mBAAmB,GAAG,EAAE;IAE7B;IACA,IAAI,CAAC1G,oBAAoB,GAAG,KAAK;IAEjC;IACA,IAAI,CAACiV,oBAAoB,EAAE;IAE3B;IACApM,UAAU,CAAC,MAAK;MACdlB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAClJ,iBAAiB,GAAG,IAAI;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAuW,oBAAoBA,CAAA;IAClB,IAAI,CAACvW,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACQ,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACe,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACd,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACO,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACU,YAAY,GAAG,KAAK;EAC3B;EAEAkC,kBAAkBA,CAAA;IAChB,IAAI,CAACyD,iBAAiB,GAAG,KAAK;IAC9B;IACA,IAAI,CAACpI,QAAQ,GAAG,IAAI,CAACqI,aAAa,EAAE;IACpC,IAAI,CAACtF,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC4F,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC1G,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACiV,oBAAoB,EAAE;EAC7B;EAEA;EACAzW,eAAeA,CAAC0W,IAAsB;IACpC,IAAI,CAACtW,iBAAiB,GAAGsW,IAAI;IAC7B,IAAI,CAACvW,kBAAkB,GAAG,IAAI;IAE9B;IACAkK,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9K,QAAQ,CAACc,UAAU,GAAGqW,IAAW;MAEtC;MACArM,UAAU,CAAC,MAAK;QACd,IAAI,CAACzJ,gBAAgB,GAAG,IAAI;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEAJ,cAAcA,CAACO,QAAgB;IAC7B,IAAI,CAACD,gBAAgB,GAAGC,QAAQ;IAChC,IAAI,CAACF,iBAAiB,GAAG,IAAI;IAE7B;IACAwJ,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9K,QAAQ,CAACwB,QAAQ,GAAGA,QAAe;MACxC,IAAI,CAACa,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC+U,qBAAqB,CAAC;QAAEvC,MAAM,EAAE;UAAE/R,KAAK,EAAEtB;QAAQ;MAAE,CAAE,CAAC;MAE3D;MACAsJ,UAAU,CAAC,MAAK;QACd,IAAI,CAAChJ,gBAAgB,GAAG,IAAI;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEAH,cAAcA,CAACK,QAA0B;IACvC,IAAIA,QAAQ,KAAK,MAAM,IAAI,IAAI,CAACC,oBAAoB,EAAE;MACpD,OAAO,CAAC;IACV;IAEA,IAAI,CAACF,iBAAiB,GAAG,IAAI;IAE7B+I,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9K,QAAQ,CAACgC,QAAQ,GAAGA,QAAe;IAC1C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAwC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACe,WAAW,GAAG,IAAI,CAAC0C,UAAU,EAAE;MACtC,IAAI,CAAC1C,WAAW,EAAE;MAElB;MACA,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1BuF,UAAU,CAAC,MAAK;UACd,IAAI,CAACrI,YAAY,GAAG,IAAI;QAC1B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;EACF;EAEA4B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACkB,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA8R,cAAcA,CAAA;IACZvM,UAAU,CAAC,MAAK;MACd,IAAI,CAACwM,UAAU,CAACC,eAAe,EAAE,CAACnQ,IAAI,CAAEoQ,KAAuB,IAAI;QACjE,MAAMC,GAAG,GAAGD,KAAK,CAAC1U,KAAK,CAACxC,MAAM;QAC9BkX,KAAK,CAACE,iBAAiB,CAACD,GAAG,EAAEA,GAAG,CAAC;QACjCD,KAAK,CAACG,UAAU,GAAGH,KAAK,CAACI,WAAW;MACtC,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA,IAAItS,QAAQA,CAAA;IACV,OAAQ,IAAI,CAACC,WAAW,GAAK,IAAI,CAAC0C,UAAW;EAC/C;EAGMpD,WAAWA,CAAA;IAAA,IAAAgT,MAAA;IAAA,OAAAjM,iBAAA;MACf,IAAI,CAACiM,MAAI,CAACxR,MAAM,IAAI,CAACwR,MAAI,CAAC7X,QAAQ,CAACd,IAAI,IAAI,CAAC2Y,MAAI,CAAC7X,QAAQ,CAACf,KAAK,IAAI,CAAC4Y,MAAI,CAAC7X,QAAQ,CAACc,UAAU,IAC1F,CAAC+W,MAAI,CAAC7X,QAAQ,CAACwB,QAAQ,IAAI,CAACqW,MAAI,CAAC7X,QAAQ,CAACnC,UAAU,IAAI,CAACga,MAAI,CAAC7X,QAAQ,CAAChC,SAAS,IAAI,CAAC6Z,MAAI,CAAC7X,QAAQ,CAAC4D,WAAW,EAAE;QAChHgG,OAAO,CAACtC,KAAK,CAAC,0DAA0D,CAAC;QACzE;MACF;MAEA,IAAI;QACF,IAAIuQ,MAAI,CAAC7X,QAAQ,CAAC4D,WAAW,KAAK,MAAM,IAAIiU,MAAI,CAAC9U,kBAAkB,CAACzC,MAAM,GAAG,CAAC,EAAE;UAC9EuX,MAAI,CAAC7X,QAAQ,CAACyS,iBAAiB,GAAGoF,MAAI,CAAC9U,kBAAkB,CAAC+U,IAAI,CAAC,GAAG,CAAC;QACrE,CAAC,MAAM,IAAID,MAAI,CAAC7X,QAAQ,CAAC4D,WAAW,KAAK,OAAO,IAAIiU,MAAI,CAAClP,mBAAmB,CAACrI,MAAM,GAAG,CAAC,EAAE;UACvFuX,MAAI,CAAC7X,QAAQ,CAAC0S,kBAAkB,GAAGmF,MAAI,CAAClP,mBAAmB,CAACmP,IAAI,CAAC,GAAG,CAAC;QACvE;QAEA,MAAM;UAAEtQ,IAAI,EAAEuQ,WAAW;UAAEzQ,KAAK,EAAE0Q;QAAS,CAAE,SAASH,MAAI,CAAChR,eAAe,CAACC,SAAS,EAAE,CACnFC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,IAAI,EAAE4Q,MAAI,CAACxR,MAAM,CAAC,CACrBc,MAAM,EAAE;QAEX,IAAI6Q,SAAS,IAAI,CAACD,WAAW,EAAE;UAC7BnO,OAAO,CAACtC,KAAK,CAAC,oCAAoC,EAAE0Q,SAAS,IAAI,kBAAkB,CAAC;UACpF,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;QAC7E;QAEA,MAAMC,aAAa,GAAgD;UACjEhZ,IAAI,EAAE2Y,MAAI,CAAC7X,QAAQ,CAACd,IAAI,IAAI,EAAE;UAC9BC,WAAW,EAAE0Y,MAAI,CAAC7X,QAAQ,CAACb,WAAW,IAAI,EAAE;UAC5C2B,UAAU,EAAE+W,MAAI,CAAC7X,QAAQ,CAACc,UAAU,IAAI,OAAO;UAC/CjD,UAAU,EAAEga,MAAI,CAAC7X,QAAQ,CAACnC,UAAU,IAAI,CAAC;UACzCG,SAAS,EAAE6Z,MAAI,CAAC7X,QAAQ,CAAChC,SAAS,IAAI,OAAO;UAC7C4F,WAAW,EAAEiU,MAAI,CAAC7X,QAAQ,CAAC4D,WAAW,IAAI,KAAK;UAC/C5B,QAAQ,EAAE6V,MAAI,CAAC7X,QAAQ,CAACgC,QAAQ,IAAI,OAAO;UAC3CR,QAAQ,EAAEqW,MAAI,CAAC7X,QAAQ,CAACwB,QAAQ,IAAI,UAAU;UAC9CvC,KAAK,EAAE4Y,MAAI,CAAC7X,QAAQ,CAACf,KAAK,IAAI,IAAI;UAClCwT,iBAAiB,EAAEoF,MAAI,CAAC7X,QAAQ,CAACyS,iBAAiB,IAAI,EAAE;UACxDC,kBAAkB,EAAEmF,MAAI,CAAC7X,QAAQ,CAAC0S,kBAAkB,IAAI,EAAE;UAC1DyF,OAAO,EAAEN,MAAI,CAACxR,MAAM;UACpBsM,MAAM,EAAE;SACT;QAED,IAAI;UACF,MAAMyF,OAAO,SAASP,MAAI,CAACjP,YAAY,CAAC/D,WAAW,CAACqT,aAAa,CAAC;UAElE,IAAIL,MAAI,CAAC7X,QAAQ,CAACc,UAAU,KAAK,MAAM,EAAE;YAEvC,MAAM+W,MAAI,CAACjP,YAAY,CAACmM,qBAAqB,CAC3C8C,MAAI,CAACxR,MAAM,EACX+R,OAAO,EACP,IAAIpS,IAAI,EAAE,EACV,CAAC,EACD;cAAE,GAAGkS,aAAa;cAAEzQ,EAAE,EAAE2Q;YAAO,CAAW,CAC3C;UACH;UAEA,MAAMhM,OAAO,GAAGyL,MAAI,CAACjS,UAAU,CAACiS,MAAI,CAACxY,YAAY,CAAC;UAClD,OAAOwY,MAAI,CAAC3P,UAAU,CAACkE,OAAO,CAAC;UAC/B,OAAOyL,MAAI,CAAC1O,iBAAiB,CAACiD,OAAO,CAAC;UAEtCyL,MAAI,CAAClT,kBAAkB,EAAE;UACzBkT,MAAI,CAAC1N,QAAQ,EAAE;QACjB,CAAC,CAAC,OAAOkO,UAAe,EAAE;UACxBzO,OAAO,CAACtC,KAAK,CAAC,kCAAkC,EAAE+Q,UAAU,CAAC;UAE7D,IAAIA,UAAU,CAACC,OAAO,IAAID,UAAU,CAACC,OAAO,CAACtV,QAAQ,CAAC,wBAAwB,CAAC,EAAE;YAC/EuV,KAAK,CAAC,uJAAuJ,CAAC;UAChK,CAAC,MAAM,IAAIF,UAAU,CAACC,OAAO,IAAID,UAAU,CAACC,OAAO,CAACtV,QAAQ,CAAC,2BAA2B,CAAC,EAAE;YACzFuV,KAAK,CAACF,UAAU,CAACC,OAAO,CAAC;UAC3B,CAAC,MAAM;YACLC,KAAK,CAAC,yBAAyBF,UAAU,CAACC,OAAO,EAAE,CAAC;UACtD;QACF;MACF,CAAC,CAAC,OAAOhR,KAAU,EAAE;QACnBsC,OAAO,CAACtC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDiR,KAAK,CAAC,UAAUjR,KAAK,CAACgR,OAAO,IAAI,wBAAwB,EAAE,CAAC;MAC9D;IAAC;EACH;EAEAzV,gBAAgBA,CAACgP,GAAW;IAC1B,MAAMI,KAAK,GAAG,IAAI,CAAClP,kBAAkB,CAACyV,OAAO,CAAC3G,GAAG,CAAC;IAElD,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAClP,kBAAkB,CAAC0V,MAAM,CAACxG,KAAK,EAAE,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAAClP,kBAAkB,CAACqI,IAAI,CAACyG,GAAG,CAAC;IACnC;EACF;EAEAtO,iBAAiBA,CAACmR,KAAU,EAAE7C,GAAW;IACvC;IACA,IAAI6G,SAAS,GAAG,KAAK;IAErB,IAAIhE,KAAK,CAACG,MAAM,KAAK/K,SAAS,EAAE;MAC9B;MACA4O,SAAS,GAAGhE,KAAK,CAACG,MAAM,CAAC8D,OAAO;IAClC,CAAC,MAAM,IAAIjE,KAAK,CAAChX,MAAM,YAAYsP,gBAAgB,EAAE;MACnD;MACA0L,SAAS,GAAGhE,KAAK,CAAChX,MAAM,CAACib,OAAO;IAClC;IAEA,IAAID,SAAS,EAAE;MACb,IAAI,CAAC/P,mBAAmB,CAACyC,IAAI,CAACyG,GAAG,CAAC;IACpC,CAAC,MAAM;MACL,MAAMI,KAAK,GAAG,IAAI,CAACtJ,mBAAmB,CAAC6P,OAAO,CAAC3G,GAAG,CAAC;MACnD,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAACtJ,mBAAmB,CAAC8P,MAAM,CAACxG,KAAK,EAAE,CAAC,CAAC;MAC3C;IACF;IAEArI,OAAO,CAACC,GAAG,CAAC,qCAAqC,IAAI,CAAClB,mBAAmB,CAACmP,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACzF;EAEAhU,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACf,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC4F,mBAAmB,GAAG,EAAE;IAC7BiB,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAAC7J,QAAQ,CAAC4D,WAAW,oBAAoB,CAAC;EAC5F;EAEAwT,qBAAqBA,CAAC1C,KAAW;IAC/B,IAAI,CAAC,IAAI,CAACrO,MAAM,IAAI,CAAC,IAAI,CAACrG,QAAQ,CAACwB,QAAQ,EAAE;IAE7C;IACA,IAAIkT,KAAK,IAAIA,KAAK,CAACG,MAAM,EAAE;MACzB,IAAI,CAAC7U,QAAQ,CAACwB,QAAQ,GAAGkT,KAAK,CAACG,MAAM,CAAC/R,KAAK;MAC3C8G,OAAO,CAACC,GAAG,CAAC,kCAAkC,IAAI,CAAC7J,QAAQ,CAACwB,QAAQ,kBAAkB,CAAC;IACzF;IAEA;IACA,IAAI,CAACoH,YAAY,CAAC8F,SAAS,CAAC,IAAI,CAACrI,MAAM,CAAC,CAACG,IAAI,CAC3C/L,IAAI,CAAC,CAAC,CAAC,EACPH,GAAG,CAAC0N,MAAM,IAAG;MACX,OAAOA,MAAM,CAAC4Q,IAAI,CAACxG,CAAC,IAClBA,CAAC,CAAC5Q,QAAQ,KAAK,IAAI,CAACxB,QAAQ,CAACwB,QAAQ,IACrC4Q,CAAC,CAACpQ,QAAQ,KAAK,MAAM,IACrBoQ,CAAC,CAACO,MAAM,CACT;IACH,CAAC,CAAC,CACH,CAAClM,SAAS,CAAC;MACVC,IAAI,EAAGmS,eAAe,IAAI;QACxB,IAAI,CAAC5W,oBAAoB,GAAG4W,eAAe;QAE3C;QACA,IAAIA,eAAe,EAAE;UACnB,IAAI,CAAC7Y,QAAQ,CAACgC,QAAQ,GAAG,OAAO;QAClC;QAEA4H,OAAO,CAACC,GAAG,CAAC,uBAAuB,IAAI,CAAC7J,QAAQ,CAACwB,QAAQ,6BAA6BqX,eAAe,EAAE,CAAC;MAC1G;KACD,CAAC;EACJ;EAEA;;;EAGAnI,uBAAuBA,CAAC1I,MAAsB;IAC5C;IACA,MAAMjC,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,MAAM5G,YAAY,GAAG,IAAI2G,IAAI,CAAC,IAAI,CAAC3G,YAAY,CAAC;IAChDA,YAAY,CAAC4G,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACjC,MAAMC,eAAe,GAAG7G,YAAY,CAAC8G,OAAO,EAAE,KAAKJ,KAAK,CAACI,OAAO,EAAE;IAClE,MAAM6E,QAAQ,GAAG,IAAI,CAACpF,UAAU,CAACG,KAAK,CAAC;IAEvC,IAAI,CAACG,eAAe,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;MACzC;IACF;IAEA;IACA,MAAMmT,gBAAgB,GAAG5N,YAAY,CAACO,OAAO,CAAC,qBAAqBT,QAAQ,EAAE,CAAC;IAC9E,IAAI8N,gBAAgB,EAAE;MACpBlP,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEmB,QAAQ,CAAC;MACxE;IACF;IAEA;IACA,MAAM+N,kBAAkB,GAAG/Q,MAAM,CAAC1H,MAAM,GAAG,CAAC,IAAI0H,MAAM,CAACgR,KAAK,CAACpK,KAAK,IAAIA,KAAK,CAAC7P,SAAS,CAAC;IAEtF;IACA,MAAMka,kBAAkB,GAAG,CAAC,IAAI,CAAC7S,cAAc,IAAI,CAAC,IAAI,CAAC7G,UAAU,IAAI,IAAI,CAACA,UAAU,CAACR,SAAS;IAEhG;IAEA,IAAIga,kBAAkB,IAAIE,kBAAkB,IAAI,IAAI,CAACtT,WAAW,CAACuT,gBAAgB,EAAE;MACjF;MACA,IAAI,CAACrQ,WAAW,CAAC0B,WAAW,CAAC,IAAI,CAAClE,MAAO,CAAC,CAACI,SAAS,CAAC+D,QAAQ,IAAG;QAC9D,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAAC7E,WAAW,GAAG6E,QAAQ;QAC7B;QAEA;QACA,IAAI,CAAClC,eAAe,GAAG,IAAI;QAE3B;QACA4C,YAAY,CAACiO,OAAO,CAAC,qBAAqBnO,QAAQ,EAAE,EAAE,MAAM,CAAC;QAE7D;QACA,IAAI,CAAC,IAAI,CAACzC,qBAAqB,CAACvF,QAAQ,CAACgI,QAAQ,CAAC,EAAE;UAClD,IAAI,CAACzC,qBAAqB,CAAC6C,IAAI,CAACJ,QAAQ,CAAC;QAC3C;MACF,CAAC,CAAC;IACJ;EACF;EAEA;;;EAGAtF,gBAAgBA,CAAA;IACd,IAAI,CAAC4C,eAAe,GAAG,KAAK;EAC9B;EAIA;EACA1C,UAAUA,CAAC6G,IAAU;IACnB,MAAM2M,IAAI,GAAG3M,IAAI,CAAC4M,WAAW,EAAE;IAC/B,MAAM7E,KAAK,GAAG8E,MAAM,CAAC7M,IAAI,CAAC8M,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAM3H,GAAG,GAAGyH,MAAM,CAAC7M,IAAI,CAACwE,OAAO,EAAE,CAAC,CAACuI,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGJ,IAAI,IAAI5E,KAAK,IAAI3C,GAAG,EAAE;EAClC;EAEAzS,SAASA,CAACqa,KAAW,EAAEC,KAAW;IAChC,OAAOD,KAAK,CAACJ,WAAW,EAAE,KAAKK,KAAK,CAACL,WAAW,EAAE,IAChDI,KAAK,CAACF,QAAQ,EAAE,KAAKG,KAAK,CAACH,QAAQ,EAAE,IACrCE,KAAK,CAACxI,OAAO,EAAE,KAAKyI,KAAK,CAACzI,OAAO,EAAE;EACvC;EAEA3R,QAAQA,CAAA;IACN,MAAMyG,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,OAAOF,KAAK;EACd;EAEA;EACQmN,eAAeA,CAACyG,cAAsB;IAC5C;IACA,MAAMC,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzD,OAAOA,MAAM,CAACD,cAAc,CAAC;EAC/B;EAEA;EACQvG,cAAcA,CAACuG,cAAsB;IAC3C;IACA,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAChE,OAAOA,MAAM,CAACD,cAAc,CAAC;EAC/B;EAEQtR,aAAaA,CAAA;IACnB,OAAO;MACLnJ,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACf2B,UAAU,EAAE,EAAS;MAAE;MACvBjD,UAAU,EAAE,CAAC;MACbG,SAAS,EAAE,OAAwB;MACnC4F,WAAW,EAAE,KAAoB;MACjC5B,QAAQ,EAAE,OAAwB;MAAE;MACpCR,QAAQ,EAAE,EAAmB;MAC7BvC,KAAK,EAAE;KACR;EACH;;aAp+DW4G,SAAS;;mCAATA,UAAS;AAAA;;QAATA,UAAS;EAAAgU,SAAA;EAAAC,SAAA,WAAAC,gBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;MC1DpB5e,EADF,CAAAK,cAAA,qBAAqD,aACjB;MAEhCL,EADA,CAAAC,SAAA,aAA+B,aACG;MACpCD,EAAA,CAAAO,YAAA,EAAM;MAEJP,EADF,CAAAK,cAAA,oBAAkC,kBACnB;MACXL,EAAA,CAAAC,SAAA,oBAAmD;MACnDD,EAAA,CAAAK,cAAA,iBAA0C;MACxCL,EAAA,CAAAkB,UAAA,IAAA4d,wBAAA,kBAAyE;MAmB/E9e,EAFI,CAAAO,YAAA,EAAU,EACE,EACH;MAEXP,EADF,CAAAK,cAAA,eAAU,kBACoC;MAC1CL,EAAA,CAAAC,SAAA,eAAkD;MACpDD,EAAA,CAAAO,YAAA,EAAU;MAGNP,EAFJ,CAAAK,cAAA,mBAA2B,eAChB,UACH;MAAAL,EAAA,CAAAM,MAAA,cAAM;MACZN,EADY,CAAAO,YAAA,EAAK,EACP;MAERP,EADF,CAAAK,cAAA,eAAS,sBAC+F;MAApCL,EAAA,CAAAQ,UAAA,mBAAAue,gDAAAjd,MAAA;QAAA,OAAS+c,GAAA,CAAAjD,iBAAA,CAAA9Z,MAAA,CAAyB;MAAA,EAAC;MACnG9B,EAAA,CAAAM,MAAA,qBACF;MAEJN,EAFI,CAAAO,YAAA,EAAa,EACL,EACF;MAERP,EADF,CAAAK,cAAA,mBAAwB,eACb;MAgBPL,EAfA,CAAAkB,UAAA,KAAA8d,8BAAA,wBAA4E,KAAAC,8BAAA,yBAgBoB;MAiDpGjf,EADE,CAAAO,YAAA,EAAU,EACF;MAGNP,EAFJ,CAAAK,cAAA,mBAAiC,eACtB,UACH;MAAAL,EAAA,CAAAM,MAAA,wBAAgB;MAExBN,EAFwB,CAAAO,YAAA,EAAK,EACjB,EACF;MAERP,EADF,CAAAK,cAAA,mBAAwB,eACb;MAyBPL,EAxBA,CAAAkB,UAAA,KAAAge,8BAAA,wBAEwC,KAAAC,8BAAA,uBAsBS;MAavDnf,EAFI,CAAAO,YAAA,EAAU,EACF,EACD;MAIXP,EAAA,CAAAK,cAAA,qBAC2B;MADwBL,EAAA,CAAAQ,UAAA,gCAAA4e,4DAAA;QAAA,OAAsBP,GAAA,CAAAtV,kBAAA,EAAoB;MAAA,EAAC;MAE5FvJ,EAAA,CAAAkB,UAAA,KAAAme,iCAAA,uBAAa;MAoUfrf,EAAA,CAAAO,YAAA,EAAY;MAEZP,EAAA,CAAAkB,UAAA,KAAAoe,qCAAA,8BAC+B;MAEjCtf,EAAA,CAAAO,YAAA,EAAc;MACdP,EAAA,CAAAC,SAAA,sBAAiC;;;MA3eAD,EAAA,CAAA2B,UAAA,oBAAmB;MAOlC3B,EAAA,CAAAoB,SAAA,GAAyB;MAAzBpB,EAAA,CAAA2B,UAAA,eAAAkd,GAAA,CAAAnS,UAAA,CAAyB;MAES1M,EAAA,CAAAoB,SAAA,GAAc;MAAdpB,EAAA,CAAA2B,UAAA,YAAAkd,GAAA,CAAArS,SAAA,CAAc;MAoC/CxM,EAAA,CAAAoB,SAAA,IAAyB;MAAzBpB,EAAA,CAAA2B,UAAA,SAAAkd,GAAA,CAAAjS,MAAA,CAAA1H,MAAA,OAAyB;MAeRlF,EAAA,CAAAoB,SAAA,EAAS;MAATpB,EAAA,CAAA2B,UAAA,YAAAkd,GAAA,CAAAjS,MAAA,CAAS;MA0D1B5M,EAAA,CAAAoB,SAAA,GAA4C;MAA5CpB,EAAA,CAAA2B,UAAA,SAAAkd,GAAA,CAAA1a,UAAA,IAAA0a,GAAA,CAAA1a,UAAA,CAAAK,aAAA,CAA4C;MAwBzBxE,EAAA,CAAAoB,SAAA,EAAiB;MAAjBpB,EAAA,CAAA2B,UAAA,UAAAkd,GAAA,CAAA1a,UAAA,CAAiB;MAiB1CnE,EAAA,CAAAoB,SAAA,EAAe;MACxBpB,EADS,CAAA2B,UAAA,gBAAe,yBACA;MAuUR3B,EAAA,CAAAoB,SAAA,GAAqB;MAArBpB,EAAA,CAAA2B,UAAA,SAAAkd,GAAA,CAAA3R,eAAA,CAAqB;;;iBD9a7BxO,WAAW,EAAA6gB,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,cAAA,EAAAH,EAAA,CAAAI,aAAA,EAAAJ,EAAA,CAAAK,YAAA,EAAAL,EAAA,CAAAM,WAAA,EAAAN,EAAA,CAAAO,MAAA,EAAAP,EAAA,CAAAQ,UAAA,EAAAR,EAAA,CAAAS,OAAA,EAAAT,EAAA,CAAAU,SAAA,EAAAV,EAAA,CAAAW,OAAA,EAAAX,EAAA,CAAAY,QAAA,EAAAZ,EAAA,CAAAa,cAAA,EAAAb,EAAA,CAAAc,QAAA,EAAAd,EAAA,CAAAe,MAAA,EAAAf,EAAA,CAAAgB,SAAA,EAAAhB,EAAA,CAAAiB,eAAA,EAAAjB,EAAA,CAAAkB,OAAA,EAAAlB,EAAA,CAAAmB,WAAA,EAAAnB,EAAA,CAAAoB,UAAA,EAAApB,EAAA,CAAAqB,QAAA,EAAArB,EAAA,CAAAsB,oBAAA,EAAAtB,EAAA,CAAAuB,oBAAA,EAAAvB,EAAA,CAAAwB,mBAAA,EAAAxB,EAAA,CAAAyB,iBAAA,EAAAzB,EAAA,CAAA0B,eAAA,EAAEziB,YAAY,EAAA0iB,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE3iB,WAAW,EAAA4iB,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,OAAA,EAAAN,EAAA,CAAAO,MAAA,EAAEriB,mBAAmB,EAAEC,oBAAoB,EAAEI,mBAAmB,EAAEE,eAAe,EAAEC,eAAe;EAAA8hB,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}