import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { User } from '../../models/user.model';

interface Particle {
  style: {
    left: string;
    top: string;
    width: string;
    height: string;
    backgroundColor: string;
    animation: string;
    '--tx': string;
    '--ty': string;
  };
}

interface Hexagon {
  style: {
    left: string;
    top: string;
    animation?: string; // Made optional
  };
}

@Component({
  selector: 'app-celebration',
  templateUrl: './celebration.component.html',
  styleUrls: ['./celebration.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class CelebrationComponent implements OnInit {
  @Input() user: User | null = null;
  @Input() date: string = '';
  @Output() close = new EventEmitter<void>();

  particles: Particle[] = [];
  hexagons: Hexagon[] = [];

  constructor() { }

  ngOnInit() {
    // Create initial particles
    this.createParticles();

    // Create hexagon grid
    this.createHexagonGrid();

    // Play achievement sound
    this.playAchievementSound();

    // Auto-close after 15 seconds
    setTimeout(() => {
      this.closeModal();
    }, 15000);
  }

  createParticles() {
    const particleCount = 150; // Increased particle count for more visual impact
    const colors = ['#4d7bff', '#3a68e0', '#5ac8fa', '#34c759', '#ffcc00'];

    // Create initial particles
    for (let i = 0; i < particleCount; i++) {
      setTimeout(() => {
        this.addParticle(colors);
      }, Math.random() * 2000);
    }

    // Continue creating particles
    let particleInterval = setInterval(() => {
      for (let i = 0; i < 8; i++) { // Increased batch size
        this.addParticle(colors);
      }
    }, 250); // Faster interval

    // Stop creating particles after 8 seconds
    setTimeout(() => {
      clearInterval(particleInterval);
    }, 8000);
  }

  addParticle(colors: string[]) {
    // Random properties
    const angle = Math.random() * Math.PI * 2;
    const distance = 50 + Math.random() * 350; // Increased max distance
    const tx = Math.cos(angle) * distance;
    const ty = Math.sin(angle) * distance;
    const size = 3 + Math.random() * 5; // Slightly larger particles
    const color = colors[Math.floor(Math.random() * colors.length)];
    const duration = 1.2 + Math.random() * 2.5; // Slightly longer duration

    const particle: Particle = {
      style: {
        left: '50%',
        top: '50%',
        width: `${size}px`,
        height: `${size}px`,
        backgroundColor: color,
        animation: `particle-move ${duration}s ease-out forwards`,
        '--tx': `${tx}px`,
        '--ty': `${ty}px`
      }
    };

    this.particles.push(particle);

    // Remove particle after animation
    setTimeout(() => {
      const index = this.particles.indexOf(particle);
      if (index !== -1) {
        this.particles.splice(index, 1);
      }
    }, duration * 1000 + 100);
  }

  createHexagonGrid() {
    const gridSize = 15;
    const hexSize = 40;
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;

    // Clear existing hexagons
    this.hexagons = [];

    for (let row = -gridSize; row <= gridSize; row++) {
      for (let col = -gridSize; col <= gridSize; col++) {
        // Calculate position (hexagonal grid offset)
        const x = centerX + col * hexSize * 1.5;
        const y = centerY + (row * hexSize * 1.732) + (col % 2) * (hexSize * 0.866);

        // Skip hexagons that are too far from center
        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
        if (distance > 500) continue;

        // Calculate delay based on distance from center
        const delay = distance / 500; // 0-1 range

        const hex: Hexagon = {
          style: {
            left: `${x}px`,
            top: `${y}px`,
            // We'll use CSS animations instead of inline animations
          }
        };

        this.hexagons.push(hex);
      }
    }

  }

  playAchievementSound() {
    try {
      // Use Web Audio API for better compatibility
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      if (!AudioContext) return;

      const audioCtx = new AudioContext();

      // Create a master gain node for overall volume control
      const masterGain = audioCtx.createGain();
      masterGain.gain.value = 0.3; // Set overall volume
      masterGain.connect(audioCtx.destination);

      // Create a compressor for better sound quality
      const compressor = audioCtx.createDynamicsCompressor();
      compressor.threshold.value = -24;
      compressor.knee.value = 30;
      compressor.ratio.value = 12;
      compressor.attack.value = 0.003;
      compressor.release.value = 0.25;
      compressor.connect(masterGain);

      // Create a simulated reverb effect using a gain node
      const reverbGain = audioCtx.createGain();
      reverbGain.gain.value = 0.2; // Subtle reverb
      reverbGain.connect(masterGain);

      // Create a success melody (pleasant ascending pattern)
      const notes = [
        { note: 'G4', time: 0.0, duration: 0.15 },
        { note: 'C5', time: 0.15, duration: 0.15 },
        { note: 'E5', time: 0.3, duration: 0.15 },
        { note: 'G5', time: 0.45, duration: 0.4 }
      ];

      // Frequency mapping for notes
      const noteFrequency: {[key: string]: number} = {
        'G4': 392.00,
        'C5': 523.25,
        'E5': 659.25,
        'G5': 783.99
      };

      // Play each note in the melody
      notes.forEach(note => {
        // Create oscillators for each note with different waveforms for rich sound
        const mainOsc = audioCtx.createOscillator();
        const subOsc = audioCtx.createOscillator();

        // Create individual gain node for this note
        const noteGain = audioCtx.createGain();
        noteGain.gain.value = 0;

        // Connect the oscillators
        mainOsc.connect(noteGain);
        subOsc.connect(noteGain);
        noteGain.connect(compressor);

        // Also send to reverb for a richer sound
        noteGain.connect(reverbGain);

        // Set oscillator types and frequencies
        mainOsc.type = 'sine';
        subOsc.type = 'triangle';

        const freq = noteFrequency[note.note];
        mainOsc.frequency.value = freq;
        subOsc.frequency.value = freq;

        // Create volume envelope for natural sound
        const startTime = audioCtx.currentTime + note.time;
        const stopTime = startTime + note.duration;

        // Attack
        noteGain.gain.setValueAtTime(0, startTime);
        noteGain.gain.linearRampToValueAtTime(0.7, startTime + 0.02);

        // Decay to sustain
        noteGain.gain.linearRampToValueAtTime(0.5, startTime + 0.05);

        // Sustain
        noteGain.gain.setValueAtTime(0.5, stopTime - 0.05);

        // Release
        noteGain.gain.linearRampToValueAtTime(0, stopTime);

        // Start and stop oscillators
        mainOsc.start(startTime);
        subOsc.start(startTime);
        mainOsc.stop(stopTime + 0.1); // Small buffer for release
        subOsc.stop(stopTime + 0.1);
      });

      // Add a subtle chord at the end for resolution
      const chordTime = audioCtx.currentTime + 0.85;
      const chordDuration = 0.8;

      // Chord notes
      const chordNotes = [
        { freq: 392.00, type: 'sine' },     // G4
        { freq: 493.88, type: 'sine' },     // B4
        { freq: 587.33, type: 'triangle' }, // D5
        { freq: 783.99, type: 'triangle' }  // G5
      ];

      // Create and play chord
      const chordGain = audioCtx.createGain();
      chordGain.gain.setValueAtTime(0, chordTime);
      chordGain.gain.linearRampToValueAtTime(0.2, chordTime + 0.1);
      chordGain.gain.setValueAtTime(0.2, chordTime + chordDuration - 0.3);
      chordGain.gain.linearRampToValueAtTime(0, chordTime + chordDuration);
      chordGain.connect(compressor);
      chordGain.connect(reverbGain);

      chordNotes.forEach(noteData => {
        const osc = audioCtx.createOscillator();
        osc.type = noteData.type as OscillatorType;
        osc.frequency.value = noteData.freq;
        osc.connect(chordGain);
        osc.start(chordTime);
        osc.stop(chordTime + chordDuration);
      });
    } catch (e) {
      console.log('Web Audio API not supported:', e);
    }
  }

  closeModal() {
    this.close.emit();
  }
}
