import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';

import { GroupSettingsPage } from './group-settings.page';
import { NavigationComponent } from '../../../components/navigation/navigation.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild([
      {
        path: '',
        component: GroupSettingsPage
      }
    ]),
    NavigationComponent
  ],
  declarations: [GroupSettingsPage]
})
export class GroupSettingsPageModule {}
