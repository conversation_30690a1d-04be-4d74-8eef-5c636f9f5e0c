import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';

@Component({
  selector: 'app-calculating',
  templateUrl: './calculating.page.html',
  styleUrls: ['./calculating.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule]
})
export class CalculatingPage implements OnInit {
  progress = 0;
  subtitle = 'Learning relapse triggers';

  constructor(private router: Router) {}

  ngOnInit() {
    this.startProgress();
  }

  private startProgress() {
    const interval = setInterval(() => {
      this.progress += 1;
      if (this.progress >= 100) {
        clearInterval(interval);
        this.router.navigate(['/results']); // Navigate to results when done
      }
    }, 50);
  }
} 