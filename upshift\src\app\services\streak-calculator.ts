import { Injectable } from '@angular/core';
import { SupabaseService } from './supabase.service';
import { Quest, QuestProgress } from '../models/quest.model';

@Injectable({
  providedIn: 'root'
})
export class StreakCalculatorService {
  constructor(private supabaseService: SupabaseService) {}

  /**
   * Calculate streaks for quests based on the exact Django logic
   * This doesn't use the streak value stored in Supabase
   */
  async calculateStreaks(userId: string,  quests: Quest[]): Promise<{ [questId: string]: number }> {
    // Day name mapping for database format
    const dayNameMapping: { [key: string]: string } = {
      'Monday': 'Mon',
      'Tuesday': 'Tue',
      'Wednesday': 'Wed',
      'Thursday': 'Thu',
      'Friday': 'Fri',
      'Saturday': 'Sat',
      'Sunday': 'Sun'
    };

    const today = new Date();
    const dateString = today.toLocaleDateString('en-CA');

    // Get yesterday's date
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const yesterdayString = yesterday.toISOString().split('T')[0];

    // Get all progress for the user
    const { data: allProgress, error: progressError } = await this.supabaseService.getClient()
      .from('quest_progress')
      .select('*')
      .eq('user_id', userId);

    if (progressError) {
      console.error('StreakCalculator: Error getting progress:', progressError);
      return {};
    }

    if (!allProgress || allProgress.length === 0) {
      return {};
    }

    // Create lookup dictionaries for quick access
    const progressByDate: { [date: string]: { [questId: string]: QuestProgress } } = {};

    allProgress.forEach(progress => {
      if (!progressByDate[progress.date]) {
        progressByDate[progress.date] = {};
      }
      progressByDate[progress.date][progress.quest_id] = progress;
    });

    // Calculate streaks for each quest
    const streaks: { [questId: string]: number } = {};

    for (const quest of quests) {
      if (!quest.id) {
        continue;
      }

      // Get progress for the selected date
      const questId = quest.id;
      const dateProgress = progressByDate[dateString] && progressByDate[dateString][questId]
        ? progressByDate[dateString][questId]
        : null;

      // Get progress for yesterday
      const yesterdayProgress = progressByDate[yesterdayString] && progressByDate[yesterdayString][questId]
        ? progressByDate[yesterdayString][questId]
        : null;

      // Get the date when the quest was created
      const createdDate = new Date(quest.created_at);
      const createdDateString = createdDate.toISOString().split('T')[0];

      // Calculate display streak based on completion status
      let displayStreak = 0;

      // First, get the streak from previous days (not including today)
      let currentDate = new Date(yesterday);
      let streakCount = 0;

      // Use the already defined createdDate
      // Count consecutive completed days from yesterday backwards
      while (true) {
        // IMPORTANT: Stop if we reach a date before the quest was created
        if (currentDate < createdDate) {
          break;
        }

        let shouldCount = true;

        if (quest.goal_period === 'week' && quest.task_days_of_week) {
          // Weekly quest logic
          const dayName = currentDate.toLocaleDateString('en-US', { weekday: 'long' });
          const currentDay = dayNameMapping[dayName];
          const taskDays = quest.task_days_of_week.split(',').map(day => day.trim());

          if (!taskDays.includes(currentDay)) {
            shouldCount = false;
          }
        } else if (quest.goal_period === 'month' && quest.task_days_of_month) {
          // Monthly quest logic
          const taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));

          if (!taskDays.includes(currentDate.getDate())) {
            shouldCount = false;
          }
        }

        if (shouldCount) {
          const currentDateString = currentDate.toISOString().split('T')[0];

          // DOUBLE CHECK: Make sure the date is not before the creation date
          if (currentDate < createdDate) {
            console.error(`StreakCalculator: ERROR! Trying to count streak for date ${currentDateString} which is before creation date ${createdDateString}. This should never happen!`);
            break;
          }

          const progress = progressByDate[currentDateString] && progressByDate[currentDateString][questId]
            ? progressByDate[currentDateString][questId]
            : null;

          if (!progress || !progress.completed) {
            // No completed progress for this date, break the streak
            break;
          }

          // Add to streak count
          streakCount++;
        } else {
          // Not a scheduled day, skip
        }

        // Move to the previous day
        currentDate.setDate(currentDate.getDate() - 1);
      }

      // Calculate final streak based on today's completion
      if (quest.goal_period === 'week' && quest.task_days_of_week) {
        // Weekly quest logic
        const dayName = today.toLocaleDateString('en-US', { weekday: 'long' });
        const currentDay = dayNameMapping[dayName];
        const taskDays = quest.task_days_of_week.split(',').map(day => day.trim());

        if (taskDays.includes(currentDay)) {
          if (dateProgress?.completed) {
            displayStreak = streakCount + 1;
          } else {
            displayStreak = streakCount;
          }
        } else {
          displayStreak = streakCount;
        }
      } else if (quest.goal_period === 'month' && quest.task_days_of_month) {
        // Monthly quest logic
        const taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));

        if (taskDays.includes(today.getDate())) {
          if (dateProgress?.completed) {
            displayStreak = streakCount + 1;
          } else {
            displayStreak = streakCount;
          }
        } else {
          displayStreak = streakCount;
        }
      } else {
        // Daily quest logic
        if (yesterdayProgress && yesterdayProgress.completed) {
          displayStreak = streakCount;

          if (dateProgress?.completed) {
            displayStreak += 1;
          }
        } else {
          if (dateProgress?.completed) {
            displayStreak = 1;
          } else {
            displayStreak = 0;
          }
        }
      }

      streaks[questId] = displayStreak;
    }

    return streaks;
  }

  /**
   * Calculate streak for a single quest
   */
  async calculateStreak(userId: string, questId: string): Promise<number> {
    // Get the quest details
    const { data: quest, error: questError } = await this.supabaseService.getClient()
      .from('quests')
      .select('*')
      .eq('id', questId)
      .maybeSingle();

    if (questError || !quest) {
      console.error('StreakCalculator: Error getting quest:', questError);
      return 0;
    }

    const streaks = await this.calculateStreaks(userId, [quest]);
    return streaks[questId] || 0;
  }
}
