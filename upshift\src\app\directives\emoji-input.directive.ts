import { Directive, ElementRef, HostListener, Optional } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[appEmojiInput]',
  standalone: true
})
export class EmojiInputDirective {
  constructor(
    private el: ElementRef,
    @Optional() private ngControl: NgControl
  ) {}

  private isProcessing = false;

  @HostListener('input', ['$event'])
  onInput(event: Event) {
    // Prevent infinite recursion
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      const input = event.target as HTMLInputElement;
      const value = input.value;

      // Check if the last character is an emoji
      const emojiRegex = /\p{Extended_Pictographic}/u;
      const graphemes = [...value];

      // Find the last emoji in the input
      const lastEmoji = graphemes.reverse().find(char => emojiRegex.test(char));

      // Get the new value (either the last emoji or empty string)
      const newValue = lastEmoji ? lastEmoji : '';

      // Update the input value
      input.value = newValue;

      // Update the model value if NgControl is available
      if (this.ngControl && this.ngControl.control) {
        // Update the form control value
        this.ngControl.control.setValue(newValue, { emitEvent: false });

        // Mark as touched and dirty
        this.ngControl.control.markAsDirty();
        this.ngControl.control.markAsTouched();

        // Trigger change detection
        this.ngControl.control.updateValueAndValidity();
      }

      // We no longer need to dispatch a new input event as it causes infinite recursion
      // The model is already updated above
    } finally {
      this.isProcessing = false;
    }
  }
}
