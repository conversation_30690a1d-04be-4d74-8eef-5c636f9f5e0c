# Upshift Supabase Edge Functions

This directory contains Supabase Edge Functions for the Upshift application.

## Functions

### createCheckoutSession

Creates a Stripe checkout session for subscription payments.

**Parameters:**
- `priceId`: The Stripe price ID
- `planType`: The type of plan
- `uid`: The user ID

**Returns:**
- `sessionId`: The Stripe checkout session ID

### createUser

Creates a new user in Supabase Auth and the profiles table. Only admin users can call this function.

**Parameters:**
- `email`: The user's email
- `password`: The user's password
- `userData`: Additional user data to store in the profiles table

**Returns:**
- `uid`: The created user's UID

## Deployment

To deploy these functions to your Supabase project, you need to have the Supabase CLI installed.

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to your Supabase project
supabase link --project-ref YOUR_PROJECT_REF

# Deploy all functions
supabase functions deploy

# Or deploy a specific function
supabase functions deploy createCheckoutSession
supabase functions deploy createUser
```

## Environment Variables

These functions require the following environment variables to be set in your Supabase project:

- `STRIPE_SECRET_KEY`: Your Stripe secret key
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (for admin operations)

You can set these variables using the Supabase CLI:

```bash
supabase secrets set STRIPE_SECRET_KEY=sk_test_your_key
supabase secrets set SUPABASE_URL=https://your-project.supabase.co
supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

Or through the Supabase dashboard under Settings > API > Edge Functions.
