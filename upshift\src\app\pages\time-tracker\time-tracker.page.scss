:host {
    --background-color: #0C0C0F;
    --text-color: #FFFFFF;
    --secondary-text: #8E8E93;
    --accent-color: #4169E1;
    --quest-bg: #1C1C1E;
    --quest-border: #2C2C2E;
    --active-date: #4169E1;
    --inactive-date: #2C2C2E;
}


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}


/* Time Tracker Styles */
.time-tracking {
    margin-top: 32px;
}


.activity-input {
    margin-bottom: 24px;
}


.activity-input select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--quest-border);
    border-radius: 8px;
    background-color: var(--quest-bg);
    color: var(--text-color);
    font-size: 14px;
    cursor: pointer;
}


.time-input-container,
#customActivityForm {
    margin-top: 12px;
    display: none;
    gap: 12px;
    align-items: center;
}


.time-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--quest-bg);
    border: 1px solid var(--quest-border);
    border-radius: 8px;
    padding: 8px 12px;
}


.time-inputs input {
    width: 50px;
    padding: 4px;
    border: none;
    background: none;
    color: var(--text-color);
    font-size: 14px;
    text-align: center;
}


.time-inputs span {
    color: var(--secondary-text);
}


#customActivityForm {
    flex-wrap: wrap;
}


#customActivityForm input[type="text"] {
    /* min-width: 200px; */
    padding: 12px;
    border: 1px solid var(--quest-border);
    border-radius: 8px;
    background-color: var(--quest-bg);
    color: var(--text-color);
}


#customActivityForm select {
    padding: 12px;
    border: 1px solid var(--quest-border);
    border-radius: 8px;
    background-color: var(--quest-bg);
    color: var(--text-color);
}


.activity-input button,
#customActivityForm button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    background-color: var(--accent-color);
    color: var(--text-color);
    font-weight: 600;
    cursor: pointer;
    transition: opacity 0.2s;
}


.activity-input button:hover,
#customActivityForm button:hover {
    opacity: 0.9;
}


.time-visualization {
    margin: 32px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    position: relative;
}


#timeChart {
    width: 240px !important;
    height: 240px !important;
    margin: 0 auto;
}


.time-summary {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: var(--quest-bg);
    border: 1px solid var(--quest-border);
    border-radius: 12px;
    margin-top: 16px;
}


.time-summary div {
    font-size: 14px;
    color: var(--secondary-text);
}


.time-summary span {
    color: var(--text-color);
    font-weight: 600;
    margin-left: 4px;
}


.activity-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 24px;
}


.activity-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background-color: var(--quest-bg);
    border: 1px solid var(--quest-border);
    border-radius: 12px;
    justify-content: space-between;
}
.activity-name-emoji {
    display: flex;
    align-items: center;
    gap: 15px;
}
.activity-name-emoji h3 {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
}
.activity-icon {
    font-size: 24px;
    min-width: 32px;
    text-align: center;
}


.activity-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}


.activity-info h3 {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
    color: var(--text-color);
}


.delete-activity {
    background: none;
    border: none;
    color: var(--secondary-text);
    font-size: 24px;
    cursor: pointer;
    padding: 0 4px;
    transition: color 0.2s;
    opacity: 0.5;
    position: relative;   
    top: -2px;
}


.delete-activity:hover {
    color: var(--text-color);
    opacity: 1;
}


.time-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--quest-bg);
    border: 1px solid var(--quest-border);
    border-radius: 8px;
    padding: 8px 12px;
}


.time-inputs input {
    width: 50px;
    padding: 4px;
    border: none;
    background: none;
    color: var(--text-color);
    font-size: 14px;
    text-align: center;
}


.time-inputs span {
    color: var(--secondary-text);
}
#customActivityName {
    width: 100%;
}

.activity-input select,
#customActivityForm input[type="text"],
#customActivityForm select {
    /* padding: 16px; */
    border: 1px solid var(--quest-border);
    border-radius: 12px;
    background-color: var(--quest-bg);
    color: var(--text-color);
    font-size: 16px;
}


.activity-input button,
#customActivityForm button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    background-color: var(--accent-color);
    color: var(--text-color);
    font-weight: 600;
    cursor: pointer;
    transition: opacity 0.2s;
}


/* Style disabled state */
.activity-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}


.activity-item.disabled input {
    pointer-events: none;
}


body.dark-theme {
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
}


.container {
    width: 480px;
    margin: 0 auto;
    padding: 20px;
    overflow-y: auto;
    padding-bottom: 74px;
    scrollbar-width: none;
}
.container::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}


.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}


.logo img {
    height: 24px;
}


.logo span {
    font-size: 20px;
    font-weight: 600;
}


h1 {
    font-size: 20px;
    font-weight: 600;
}


.week-calendar {
    margin-bottom: 32px;
}


.days, .dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    gap: 8px;
}


.days {
    margin-bottom: 8px;
}


.day-name {
    color: var(--secondary-text);
    font-size: 14px;
}


.date {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    font-size: 14px;
    background-color: var(--inactive-date);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}


.date.active {
    background-color: var(--active-date);
    color: white;
}


.date.selected {
    background-color: var(--accent-color);
    color: white;
}


.date.selected::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: var(--accent-color);
    border-radius: 50%;
}


.date:hover:not(.disabled) {
    background-color: rgba(255, 255, 255, 0.1);
}


.date.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}


h2 {
    font-size: 20px;
    margin-bottom: 16px;
}


.side-quests::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    height: 1px;
    background: linear-gradient(to right, transparent, #4B0082, transparent);
}


.calendar {
    margin: 20px 0;
    padding: 10px;
    background: var(--bg-secondary);
    border-radius: 8px;
}


.calendar-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}


.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    text-align: center;
}


.day-name {
    color: var(--secondary-text);
    font-size: 14px;
}


.day-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    text-decoration: none;
    color: var(--text-primary);
    margin: 0 auto;
}


.day-number:hover {
    background: var(--bg-hover);
}


.day-number.selected {
    background: var(--primary-color);
    color: white;
}


.day-number.today {
    border: 2px solid var(--primary-color);
}


.nav-arrow {
    padding: 8px 12px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
}


.nav-arrow:hover {
    background: var(--bg-hover);
}


.time-display {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    margin-right: 16px;
}


/* Make number input spinners less prominent */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    opacity: 0.3;
}
.custom-inline-form {
    display: none;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    margin-top: 12px;
}

.custom-inline-form input[type="text"] {
    padding: 12px;
    border: 1px solid var(--quest-border);
    border-radius: 8px;
    background-color: var(--quest-bg);
    color: var(--text-color);
    /* min-width: 140px; */
}

.emoji-select {
    padding: 8px 12px;
    font-size: 18px;
    border-radius: 8px;
    background-color: var(--quest-bg);
    border: 1px solid var(--quest-border);
    color: var(--text-color);
}
.custom-row {
    display: flex;
    gap: 12px;
    width: 100%;
}

/* .custom-row input[type="text"] {
    flex: 1;
} */

.custom-row select {
    width: 80px;
    min-width: 60px;
    text-align: center;
}

.activity-item .time-input input {
    width: 60px;
    padding: 8px;
    border: none;
    border-radius: 8px;
    background-color: var(--quest-bg);
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    box-shadow: inset 0 0 0 1px var(--quest-border);
    transition: all 0.2s;
}

.activity-item .time-input input:focus {
    outline: none;
    box-shadow: inset 0 0 0 1px white;
}

.activity-item .time-input span {
    color: var(--secondary-text);
    margin: 0 4px;
}

#emoji {
    width: 50px;
}