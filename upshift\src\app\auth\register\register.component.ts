// @ts-nocheck - Disable TypeScript checking for this file
import { Component, inject, EnvironmentInjector, runInInjectionContext } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Preferences } from '@capacitor/preferences';
import { SupabaseService } from '../../services/supabase.service';
import { UserService } from '../../services/user.service';

@Component({
  selector: 'app-register',
  standalone: true,
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss'],
  imports: [CommonModule, IonicModule, ReactiveFormsModule, RouterModule],
})
export class RegisterComponent {
  form: FormGroup;
  segment: 'login' | 'register' = 'login';
  private fb = inject(FormBuilder);
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);
  private injector = inject(EnvironmentInjector);

  constructor() {
    this.form = this.fb.group({
      email: [''],
      password: [''],
      confirmPassword: [''],
    });
  }

  onSegmentChange(event: CustomEvent) {
    const value = event.detail.value;
    if (value === 'login' || value === 'register') {
      this.segment = value;
    }
  }

  async login() {
    console.log('Register: Login method called');
    const { email, password } = this.form.value;
    console.log('Register: Email:', email, 'Password:', password ? '******' : 'empty');

    if (!email || !password) {
      alert('Please enter both email and password');
      return;
    }

    try {
      console.log('Register: Attempting to sign in with email and password');
      const { data, error } = await this.supabaseService.getClient().auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        console.error('Register: Login error:', error);
        alert('Login error: ' + error.message);
        return;
      }

      console.log('Register: Sign in successful, user ID:', data.user.id);
      await this.handlePostLogin(data.user.id);
    } catch (error: any) {
      console.error('Register: Login error:', error);
      alert('Login error: ' + error.message);
    }
  }

  async register() {
    console.log('Register: Registration method called');
    const { email, password, confirmPassword } = this.form.value;
    console.log('Register: Email:', email);

    if (password !== confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    try {
      console.log('Register: Attempting to sign up with email and password');
      const { data, error } = await this.supabaseService.getClient().auth.signUp({
        email,
        password
      });

      if (error) {
        console.error('Register: Registration error:', error);
        alert('Registration error: ' + error.message);
        return;
      }

      if (data?.user) {
        console.log('Register: Sign up successful, user ID:', data.user.id);
        await this.handlePostRegister(data.user.id);
      } else {
        console.error('Register: Sign up successful but no user data returned');
        alert('Registration successful but no user data returned');
      }
    } catch (error: any) {
      console.error('Register: Registration error:', error);
      alert('Registration error: ' + error.message);
    }
  }

  async signInWithGoogle() {
    console.log('Register: Attempting to sign in with Google');
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: window.location.origin + '/'
        }
      });

      if (error) {
        console.error('Register: Google sign in error:', error);
        alert('Error signing in with Google: ' + error.message);
        return;
      }

      console.log('Register: Google sign in initiated:', data);
    } catch (error: any) {
      console.error('Register: Google sign in exception:', error);
      alert('Error signing in with Google');
    }
  }

  async signInWithApple() {
    console.log('Register: Attempting to sign in with Apple');
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: window.location.origin + '/'
        }
      });

      if (error) {
        console.error('Register: Apple sign in error:', error);
        alert('Error signing in with Apple: ' + error.message);
        return;
      }

      console.log('Register: Apple sign in initiated:', data);
    } catch (error: any) {
      console.error('Register: Apple sign in exception:', error);
      alert('Error signing in with Apple');
    }
  }

  private async createUserRecord(userId: string, userData: any) {
    console.log('Register: Creating user record for:', userId);
    try {
      const newUser = {
        id: userId,
        email: userData.email || '',
        username: '',
        name: userData.user_metadata?.full_name || '',
        profile_picture: userData.user_metadata?.avatar_url || '',
        registration_date: new Date(),
        last_login: new Date(),
        active: true,
        xp: null,
        level: null,
        title: null,
        plan: 'none',
        auto_renew: true,
        start_of_current_plan: null,
        end_of_current_plan: null,
        start_of_sick_days: null,
        end_of_sick_days: null,
        subscription_status: 'email marketing',
        affiliate_code_used: null,
        // Initialize XP fields
        strength_xp: 0,
        money_xp: 0,
        health_xp: 0,
        knowledge_xp: 0,
      };

      const { error } = await this.supabaseService.getClient()
        .from('profiles')
        .insert(newUser);

      if (error) {
        console.error('Register: Error creating user record:', error);
        throw error;
      }

      console.log('Register: User record created successfully');
    } catch (error) {
      console.error('Register: Error in createUserRecord:', error);
      throw error;
    }
  }

  private async handlePostLogin(uid: string) {
    console.log('Register: handlePostLogin called with uid:', uid);
    try {
      await Preferences.set({ key: 'uid', value: uid });
      console.log('Register: Preferences set, checking user data');

      // Check if user has a record in the profiles table
      const { data: userData, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('*')
        .eq('id', uid)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Register: Error checking user data:', error);
      }

      if (!userData) {
        console.log('Register: No user record found, creating one');
        const authUser = this.supabaseService._currentUser.value;
        if (authUser) {
          await this.createUserRecord(uid, authUser);
        }
      } else {
        console.log('Register: User record found, updating last login');
        const { error: updateError } = await this.supabaseService.getClient()
          .from('profiles')
          .update({ last_login: new Date() })
          .eq('id', uid);

        if (updateError) {
          console.error('Register: Error updating last login:', updateError);
        }
      }

      console.log('Register: Navigating to /today');
      this.router.navigateByUrl('/today');
      console.log('Register: Navigation completed');
    } catch (error) {
      console.error('Register: Error in handlePostLogin:', error);
    }
  }

  private async handlePostRegister(uid: string) {
    console.log('Register: handlePostRegister called with uid:', uid);
    try {
      await Preferences.set({ key: 'uid', value: uid });
      console.log('Register: Preferences set, creating user record');

      const authUser = this.supabaseService._currentUser.value;
      if (authUser) {
        await this.createUserRecord(uid, authUser);
      }

      console.log('Register: Navigating to /onboarding');
      this.router.navigateByUrl('/onboarding');
      console.log('Register: Navigation completed');
    } catch (error) {
      console.error('Register: Error in handlePostRegister:', error);
    }
  }
}