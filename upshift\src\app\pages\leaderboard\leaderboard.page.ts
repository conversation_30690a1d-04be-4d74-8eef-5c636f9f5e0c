import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { UserService } from '../../services/user.service';
import { GroupService } from '../../services/group.service';
import { FriendService } from '../../services/friend.service';
import { User } from '../../models/user.model';
import { Group } from '../../models/group.model';
import { Subscription, of, switchMap, take, map } from 'rxjs';
import { NavigationComponent } from '../../components/navigation/navigation.component';
import { SupabaseService } from '../../services/supabase.service';

@Component({
  selector: 'app-leaderboard',
  templateUrl: './leaderboard.page.html',
  styleUrls: ['./leaderboard.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, RouterModule, NavigationComponent]
})
export class LeaderboardPage implements OnInit {
  // Active tab - default to groups tab as in Django
  activeTab: 'groups' | 'users' = 'groups';

  // Leaderboard data
  topGroups: Group[] = [];
  topUsers: User[] = [];
  currentUserId: string | null = null;

  // Subscriptions
  groupsSubscription: Subscription | null = null;
  usersSubscription: Subscription | null = null;

  // Services
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private groupService = inject(GroupService);
  private friendService = inject(FriendService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  constructor() {}

  ngOnInit() {
    console.log('LeaderboardPage: ngOnInit');

    // Get current user ID
    this.supabaseService.currentUser$.subscribe(user => {
      if (user) {
        this.currentUserId = user.id;
        console.log('LeaderboardPage: Current user ID:', this.currentUserId);
      }
    });

    // Check the current route to determine which tab to show
    const url = this.router.url;
    console.log('LeaderboardPage: Current URL:', url);

    if (url.includes('/leaderboard/users')) {
      this.activeTab = 'users';
    } else {
      this.activeTab = 'groups';
    }

    console.log('LeaderboardPage: Active tab set to:', this.activeTab);

    // Load data for both tabs
    this.loadTopGroups();
    this.loadTopUsers();
  }

  ngOnDestroy() {
    if (this.groupsSubscription) {
      this.groupsSubscription.unsubscribe();
    }

    if (this.usersSubscription) {
      this.usersSubscription.unsubscribe();
    }
  }

  setActiveTab(tab: 'groups' | 'users') {
    console.log('LeaderboardPage: Setting active tab to:', tab);
    this.activeTab = tab;

    // Navigate to the appropriate route using window.location for more direct navigation
    window.location.href = `/leaderboard/${tab}`;
  }

  loadTopGroups() {
    console.log('LeaderboardPage: Loading top groups...');
    this.groupsSubscription = this.groupService.getTopGroups(10).subscribe({
      next: (groups) => {
        console.log('LeaderboardPage: Loaded top groups:', groups);
        this.topGroups = groups;
      },
      error: (error) => {
        console.error('LeaderboardPage: Error loading top groups:', error);
      },
      complete: () => {
        console.log('LeaderboardPage: Top groups loading complete');
      }
    });
  }

  loadTopUsers() {
    console.log('LeaderboardPage: Loading top users...');
    this.usersSubscription = this.userService.getTopUsers(10).subscribe({
      next: (users) => {
        console.log('LeaderboardPage: Loaded top users:', users);
        this.topUsers = users;
      },
      error: (error) => {
        console.error('LeaderboardPage: Error loading top users:', error);
      },
      complete: () => {
        console.log('LeaderboardPage: Top users loading complete');
      }
    });
  }

  viewUserProfile(userId: string | undefined) {
    if (!userId) return;

    // Don't navigate if it's the current user
    if (userId === this.currentUserId) {
      return;
    }

    // Check if this user is a friend
    this.friendService.getFriends(this.currentUserId || '').pipe(
      take(1),
      map(friends => {
        // Check if the user is in the friends list
        const isFriend = friends.some(friend =>
          (friend.user_id === this.currentUserId && friend.friend_id === userId) ||
          (friend.user_id === userId && friend.friend_id === this.currentUserId)
        );

        if (isFriend) {
          // Navigate to friend profile if they are a friend
          this.router.navigate(['/friends', userId]);
        } else {
          // Navigate to user profile if they are not a friend
          this.router.navigate(['/user-profile', userId]);
        }
      })
    ).subscribe();
  }

  goBackToGroups() {
    console.log('Navigating back to groups using window.location...');
    window.location.href = '/groups';
  }
}
