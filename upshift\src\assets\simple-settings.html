<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Group Settings</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #0C0C0F;
            color: #FFFFFF;
            padding: 20px;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 20px;
            margin: 20px 0 10px;
        }
        .back-link {
            display: inline-block;
            color: #8E8E93;
            text-decoration: none;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .back-link:hover {
            color: #FFFFFF;
        }
        .section {
            background-color: #1C1C1E;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .section-icon {
            font-size: 20px;
            margin-right: 10px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        .progress-bar {
            height: 8px;
            background-color: #2C2C2E;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #4169E1;
            border-radius: 4px;
        }
        .level-info {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #8E8E93;
        }
        .member-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .member-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #2C2C2E;
        }
        .member-item:last-child {
            border-bottom: none;
        }
        .member-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #4169E1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .member-info {
            flex-grow: 1;
        }
        .member-name {
            font-weight: 600;
            margin: 0;
        }
        .member-role {
            font-size: 12px;
            color: #8E8E93;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #2C2C2E;
            transition: .4s;
            border-radius: 24px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .toggle-slider {
            background-color: #4169E1;
        }
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
        .setting-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .setting-label {
            font-weight: 500;
        }
        .button {
            background-color: #4169E1;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .button:hover {
            background-color: #3A68E0;
        }
        .danger-button {
            background-color: #FF3B30;
        }
        .danger-button:hover {
            background-color: #E0352B;
        }
        #groupInfo {
            margin-top: 10px;
            font-size: 14px;
            color: #8E8E93;
        }
    </style>
</head>
<body>
    <a href="#" class="back-link" id="backToGroup">← Back to group</a>
    <h1>Group Settings</h1>
    <div id="groupInfo">Loading group information...</div>
    
    <div class="section">
        <div class="section-header">
            <span class="section-icon">📊</span>
            <h2 class="section-title">Group XP</h2>
        </div>
        <div class="level-info">
            <span>Level <span id="currentLevel">1</span></span>
            <span><span id="currentXP">0</span> / <span id="requiredXP">1000</span> XP</span>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" id="xpProgress" style="width: 0%"></div>
        </div>
    </div>
    
    <div class="section">
        <div class="section-header">
            <span class="section-icon">👥</span>
            <h2 class="section-title">Members</h2>
        </div>
        <ul class="member-list" id="memberList">
            <li class="member-item">
                <div class="member-avatar">U</div>
                <div class="member-info">
                    <p class="member-name">Loading members...</p>
                    <p class="member-role">Please wait</p>
                </div>
            </li>
        </ul>
    </div>
    
    <div class="section">
        <div class="section-header">
            <span class="section-icon">⚙️</span>
            <h2 class="section-title">Settings</h2>
        </div>
        <div class="setting-row">
            <span class="setting-label">Enable Daily Side Quests</span>
            <label class="toggle-switch">
                <input type="checkbox" id="enableSideQuests" checked>
                <span class="toggle-slider"></span>
            </label>
        </div>
    </div>
    
    <div class="section">
        <div class="section-header">
            <span class="section-icon">🔗</span>
            <h2 class="section-title">Invite</h2>
        </div>
        <p>Invite code: <strong id="inviteCode">Loading...</strong></p>
        <button class="button" id="copyInviteCode">Copy Code</button>
    </div>
    
    <div class="section">
        <div class="section-header">
            <span class="section-icon">⚠️</span>
            <h2 class="section-title">Danger Zone</h2>
        </div>
        <p>These actions cannot be undone.</p>
        <button class="button danger-button" id="leaveGroup">Leave Group</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the group ID from the URL
            const urlParams = new URLSearchParams(window.location.search);
            const groupId = urlParams.get('id');
            
            if (groupId) {
                document.getElementById('groupInfo').textContent = `Group ID: ${groupId}`;
                document.getElementById('backToGroup').href = `/groups/${groupId}`;
                
                // Simulate loading group data
                setTimeout(() => {
                    // Simulate group data
                    const groupData = {
                        name: 'Example Group',
                        level: 5,
                        currentXP: 2500,
                        requiredXP: 5000,
                        enableSideQuests: true,
                        inviteCode: 'GROUP-' + Math.random().toString(36).substring(2, 8).toUpperCase(),
                        members: [
                            { name: 'User 1', role: 'Admin', avatar: 'U1' },
                            { name: 'User 2', role: 'Member', avatar: 'U2' },
                            { name: 'User 3', role: 'Member', avatar: 'U3' }
                        ]
                    };
                    
                    // Update UI with group data
                    document.getElementById('currentLevel').textContent = groupData.level;
                    document.getElementById('currentXP').textContent = groupData.currentXP;
                    document.getElementById('requiredXP').textContent = groupData.requiredXP;
                    document.getElementById('xpProgress').style.width = `${(groupData.currentXP / groupData.requiredXP) * 100}%`;
                    document.getElementById('enableSideQuests').checked = groupData.enableSideQuests;
                    document.getElementById('inviteCode').textContent = groupData.inviteCode;
                    
                    // Update member list
                    const memberList = document.getElementById('memberList');
                    memberList.innerHTML = '';
                    
                    groupData.members.forEach(member => {
                        memberList.innerHTML += `
                            <li class="member-item">
                                <div class="member-avatar">${member.avatar}</div>
                                <div class="member-info">
                                    <p class="member-name">${member.name}</p>
                                    <p class="member-role">${member.role}</p>
                                </div>
                            </li>
                        `;
                    });
                }, 1000);
                
                // Set up event listeners
                document.getElementById('copyInviteCode').addEventListener('click', function() {
                    const inviteCode = document.getElementById('inviteCode').textContent;
                    navigator.clipboard.writeText(inviteCode)
                        .then(() => alert('Invite code copied to clipboard!'))
                        .catch(err => console.error('Failed to copy: ', err));
                });
                
                document.getElementById('leaveGroup').addEventListener('click', function() {
                    if (confirm('Are you sure you want to leave this group? This action cannot be undone.')) {
                        alert('You have left the group.');
                        window.location.href = '/groups';
                    }
                });
                
                document.getElementById('enableSideQuests').addEventListener('change', function() {
                    console.log('Side quests enabled:', this.checked);
                });
            } else {
                document.getElementById('groupInfo').textContent = 'No group ID provided. Please go back and try again.';
            }
        });
    </script>
</body>
</html>
