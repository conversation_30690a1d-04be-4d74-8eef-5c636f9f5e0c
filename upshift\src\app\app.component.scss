ion-app {
  background: var(--bg);
}

ion-content {
  --background: var(--bg);
  --padding-start: 16px;
  --padding-end: 16px;
}

// Base card styling
ion-card {
  margin: 16px 0;
  padding: 16px;
  background: var(--quest-card-bg);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 12px;
    background: linear-gradient(45deg, var(--accent-glow), transparent);
    opacity: 0.05;
    pointer-events: none;
  }
}

// Header styling
ion-header {
  ion-toolbar {
    --background: var(--surface);
    --border-color: var(--border);
    
    ion-title {
      color: var(--text);
      font-weight: 600;
    }
  }
}

// List styling
ion-list {
  background: transparent;
  
  ion-item {
    --background: var(--surface);
    --background-hover: var(--surface-alt);
    --border-color: var(--border);
    --color: var(--text);
    
    &::part(native) {
      border-radius: 8px;
      margin: 4px 0;
    }
  }
}

// Input styling
ion-input, ion-textarea {
  --background: var(--surface);
  --color: var(--text);
  --placeholder-color: var(--text-muted);
  border: 1px solid var(--border);
  border-radius: 8px;
  margin: 8px 0;
  
  &:focus-within {
    border-color: var(--accent);
    box-shadow: 0 0 0 2px var(--accent-glow);
  }
}

// Button styling enhancement
ion-button {
  --border-radius: 8px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  
  &.button-solid {
    --background: var(--accent);
    --background-hover: var(--accent-hover);
    --box-shadow: 0 0 15px var(--accent-glow);
    
    &:hover {
      --box-shadow: 0 0 20px var(--accent-glow);
    }
  }
  
  &.button-outline {
    --border-color: var(--accent);
    --color: var(--accent);
    
    &:hover {
      --background: var(--accent-glow);
    }
  }
}

// Progress bar enhancement
.progress-container {
  background: var(--progress-bg);
  border-radius: 6px;
  overflow: hidden;
  height: 8px;
  margin: 8px 0;
  
  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent), var(--accent-hover));
    box-shadow: 0 0 15px var(--accent-glow);
    transition: width 0.3s ease;
  }
}

// Badge styling
ion-badge {
  background: var(--accent-glow);
  color: var(--accent);
  border: 1px solid var(--accent);
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}
