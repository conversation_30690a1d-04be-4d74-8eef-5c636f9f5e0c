{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { QuestService } from '../../services/quest.service';\nimport { SideQuestService } from '../../services/sidequest.service';\nimport { UserService } from '../../services/user.service';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { Quest } from '../../models/quest.model';\nimport { Subscription, forkJoin, map, of, switchMap, take, firstValueFrom } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\nimport { CelebrationComponent } from '../../components/celebration/celebration.component';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { PreferencesService } from '../../services/preferences.service';\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\nimport { StreakCalculatorService } from '../../services/streak-calculator';\nimport { HeaderComponent } from 'src/app/components/header/header.component';\nimport { AuroraComponent } from 'src/app/components/aurora/aurora.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"emojiInput\"];\nconst _c1 = () => [\"M\", \"T\", \"W\", \"T\", \"F\", \"S\", \"S\"];\nfunction TodayPage_div_8__svg_circle_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"circle\", 27);\n  }\n  if (rf & 2) {\n    const date_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"low\", date_r2.completion_percentage < 50);\n    i0.ɵɵattribute(\"stroke-dasharray\", date_r2.completion_percentage * 81.68 / 100 + \", 81.68\")(\"data-date\", date_r2.date);\n  }\n}\nfunction TodayPage_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"ion-text\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function TodayPage_div_8_Template_div_click_3_listener() {\n      const date_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(!date_r2.is_future && ctx_r2.selectDate(date_r2));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 24);\n    i0.ɵɵelement(5, \"circle\", 25);\n    i0.ɵɵtemplate(6, TodayPage_div_8__svg_circle_6_Template, 1, 4, \"circle\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const date_r2 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", date_r2.is_today)(\"selected\", date_r2.is_selected && !date_r2.is_today)(\"unselected\", !date_r2.is_selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpureFunction0(12, _c1)[i_r4], \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", date_r2.is_selected)(\"disabled\", date_r2.is_future);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !date_r2.is_future);\n  }\n}\nfunction TodayPage_ion_card_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-card\", 28)(1, \"ion-card-header\")(2, \"h2\");\n    i0.ɵɵtext(3, \"No quests found..\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-card-content\")(5, \"ion-row\")(6, \"ion-col\", 29)(7, \"ion-text\");\n    i0.ɵɵtext(8, \"No quests found. Try adding a quest ;)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"ion-col\", 30);\n    i0.ɵɵelement(10, \"ion-icon\", 31);\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction TodayPage_ion_card_22_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"ion-range\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ion_card_22_div_11_Template_ion_range_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(quest_r6.value_achieved, $event) || (quest_r6.value_achieved = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ion_card_22_div_11_Template_ion_range_ionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updateQuestProgress(quest_r6, $event));\n    })(\"ionInput\", function TodayPage_ion_card_22_div_11_Template_ion_range_ionInput_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView($event.target && ctx_r2.updateSliderBackground($event.target));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"ion-text\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMapInterpolate1(\"--progress-value: \", quest_r6.value_achieved / quest_r6.goal_value * 100, \"%\");\n    i0.ɵɵproperty(\"max\", quest_r6.goal_value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", quest_r6.value_achieved);\n    i0.ɵɵproperty(\"step\", 1);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r6.id)(\"data-quest-type\", quest_r6.quest_type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate4(\" \", quest_r6.value_achieved, \"\", quest_r6.goal_unit === \"min\" ? \"m\" : quest_r6.goal_unit === \"hr\" ? \"h\" : \"s\", \"/\", quest_r6.goal_value, \"\", quest_r6.goal_unit === \"min\" ? \"m\" : quest_r6.goal_unit === \"hr\" ? \"h\" : \"s\", \" \");\n  }\n}\nfunction TodayPage_ion_card_22_div_12_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", quest_r6.goal_unit, \" \");\n  }\n}\nfunction TodayPage_ion_card_22_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"ion-range\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ion_card_22_div_12_Template_ion_range_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(quest_r6.value_achieved, $event) || (quest_r6.value_achieved = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function TodayPage_ion_card_22_div_12_Template_ion_range_ionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const quest_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updateQuestProgress(quest_r6, $event));\n    })(\"ionInput\", function TodayPage_ion_card_22_div_12_Template_ion_range_ionInput_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView($event.target && ctx_r2.updateSliderBackground($event.target));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"ion-text\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, TodayPage_ion_card_22_div_12_ng_container_4_Template, 2, 1, \"ng-container\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMapInterpolate1(\"--progress-value: \", quest_r6.value_achieved / quest_r6.goal_value * 100, \"%\");\n    i0.ɵɵproperty(\"max\", quest_r6.goal_value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", quest_r6.value_achieved);\n    i0.ɵɵproperty(\"step\", 1);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r6.id)(\"data-quest-type\", quest_r6.quest_type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", quest_r6.value_achieved, \"/\", quest_r6.goal_value, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", quest_r6.goal_unit !== \"count\");\n  }\n}\nfunction TodayPage_ion_card_22_ion_text_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-text\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const quest_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\uD83D\\uDD25\", quest_r6.streak, \"d \");\n  }\n}\nfunction TodayPage_ion_card_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-card\", 32);\n    i0.ɵɵlistener(\"click\", function TodayPage_ion_card_22_Template_ion_card_click_0_listener() {\n      const quest_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleQuest(quest_r6));\n    });\n    i0.ɵɵelementStart(1, \"ion-row\")(2, \"ion-col\", 33)(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-col\", 35)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-text\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 36);\n    i0.ɵɵtemplate(11, TodayPage_ion_card_22_div_11_Template, 4, 12, \"div\", 37)(12, TodayPage_ion_card_22_div_12_Template, 5, 11, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"ion-col\", 33);\n    i0.ɵɵtemplate(14, TodayPage_ion_card_22_ion_text_14_Template, 2, 1, \"ion-text\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const quest_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"completed\", quest_r6.completed);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r6.id)(\"data-regular-quest\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(quest_r6.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(quest_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r6.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", quest_r6.goal_unit === \"time\" || quest_r6.goal_unit === \"min\" || quest_r6.goal_unit === \"hr\" || quest_r6.goal_unit === \"sec\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", quest_r6.goal_unit !== \"time\" && quest_r6.goal_unit !== \"min\" && quest_r6.goal_unit !== \"hr\" && quest_r6.goal_unit !== \"sec\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSameDay(ctx_r2.selectedDate, ctx_r2.getToday()));\n  }\n}\nfunction TodayPage_ion_card_29_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\uD83D\\uDD25\", ctx_r2.dailyQuest.streak, \"d \");\n  }\n}\nfunction TodayPage_ion_card_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-card\", 46);\n    i0.ɵɵlistener(\"click\", function TodayPage_ion_card_29_Template_ion_card_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSideQuest(ctx_r2.dailyQuest));\n    });\n    i0.ɵɵelementStart(1, \"ion-row\")(2, \"ion-col\", 33)(3, \"div\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ion-col\", 35)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-text\", 47);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"ion-col\", 33);\n    i0.ɵɵtemplate(11, TodayPage_ion_card_29_div_11_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"completed\", ctx_r2.dailyQuest.completed);\n    i0.ɵɵattribute(\"data-quest-id\", ctx_r2.dailyQuest.id)(\"data-side-quest\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.dailyQuest.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.dailyQuest.current_quest.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.dailyQuest.current_quest.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSameDay(ctx_r2.selectedDate, ctx_r2.getToday()));\n  }\n}\nfunction TodayPage_ion_card_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-card\", 48)(1, \"ion-card-header\")(2, \"ion-card-title\");\n    i0.ɵɵtext(3, \" Daily Side Quest \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-card-content\")(5, \"ion-text\");\n    i0.ɵɵtext(6, \" No daily side quests are currently available. Please check back later or contact an administrator. \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TodayPage_ng_template_32_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"ion-row\")(2, \"ion-col\", 61)(3, \"ion-card\", 62);\n    i0.ɵɵelement(4, \"ion-icon\", 63);\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6, \"Explore\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ion-text\");\n    i0.ɵɵtext(8, \"Our upshift qusts!\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"ion-col\", 61)(10, \"ion-card\", 62);\n    i0.ɵɵelement(11, \"ion-icon\", 64);\n    i0.ɵɵelementStart(12, \"h2\");\n    i0.ɵɵtext(13, \"Create\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ion-text\");\n    i0.ɵɵtext(15, \"Create your own!\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction TodayPage_ng_template_32_div_12_ion_col_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-col\", 74)(1, \"ion-text\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const emoji_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(emoji_r12);\n  }\n}\nfunction TodayPage_ng_template_32_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"ion-row\", 66)(2, \"ion-col\")(3, \"h2\");\n    i0.ɵɵtext(4, \"Let's choose the \");\n    i0.ɵɵelementStart(5, \"span\", 67);\n    i0.ɵɵtext(6, \"emoji!!\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"ion-row\")(8, \"ion-col\")(9, \"ion-text\", 68);\n    i0.ɵɵtext(10, \"Choose an EMOJI that you think represents this quest!\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"ion-row\", 69)(12, \"ion-col\")(13, \"ion-input\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_12_Template_ion_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.emoji, $event) || (ctx_r2.newQuest.emoji = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"ion-row\", 71);\n    i0.ɵɵtemplate(15, TodayPage_ng_template_32_div_12_ion_col_15_Template, 3, 1, \"ion-col\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ion-button\", 73);\n    i0.ɵɵtext(17, \"Custom\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.emojis);\n  }\n}\nfunction TodayPage_ng_template_32_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"ion-row\", 76)(2, \"ion-col\", 77)(3, \"div\", 78);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6, \"Tell us about your \");\n    i0.ɵɵelementStart(7, \"span\", 67);\n    i0.ɵɵtext(8, \"quest!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"ion-text\", 68);\n    i0.ɵɵtext(10, \"Give your quest a name and describe what you want to achieve\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"ion-row\", 79)(12, \"ion-col\")(13, \"div\", 80)(14, \"div\", 81);\n    i0.ɵɵtext(15, \"\\u270F\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ion-input\", 82);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_13_Template_ion_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.name, $event) || (ctx_r2.newQuest.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 83);\n    i0.ɵɵtext(18, \"Quest Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"div\", 84);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"ion-row\", 79)(21, \"ion-col\")(22, \"div\", 85)(23, \"div\", 81);\n    i0.ɵɵtext(24, \"\\uD83D\\uDCDD\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"ion-textarea\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_13_Template_ion_textarea_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.description, $event) || (ctx_r2.newQuest.description = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"label\", 87);\n    i0.ɵɵtext(27, \"Quest Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"div\", 84);\n    i0.ɵɵelementStart(29, \"div\", 88)(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.emoji ? ctx_r2.newQuest.emoji : \"\\uD83C\\uDFAF\");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.name);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"warning\", ((ctx_r2.newQuest.description == null ? null : ctx_r2.newQuest.description.length) || 0) > 120);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.newQuest.description == null ? null : ctx_r2.newQuest.description.length) || 0, \"/150 \");\n  }\n}\nfunction TodayPage_ng_template_32_div_14_ion_row_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-row\", 98)(1, \"ion-col\")(2, \"div\", 99);\n    i0.ɵɵelement(3, \"ion-icon\", 100);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Quest Type\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 101)(7, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_12_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectQuestType(\"build\"));\n    });\n    i0.ɵɵelementStart(8, \"div\", 103);\n    i0.ɵɵtext(9, \"\\uD83C\\uDFD7\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 104)(11, \"h4\");\n    i0.ɵɵtext(12, \"Build Habit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14, \"Create a new positive habit\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_12_Template_div_click_15_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectQuestType(\"quit\"));\n    });\n    i0.ɵɵelementStart(16, \"div\", 103);\n    i0.ɵɵtext(17, \"\\uD83D\\uDEAB\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 104)(19, \"h4\");\n    i0.ɵɵtext(20, \"Quit Habit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22, \"Break a negative habit\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"slide-in-from-right\", ctx_r2.questTypeAnimated)(\"slide-out-left\", ctx_r2.questTypeAnimating && ctx_r2.selectedQuestType);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"active\", ctx_r2.newQuest.quest_type === \"build\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"active\", ctx_r2.newQuest.quest_type === \"quit\");\n  }\n}\nfunction TodayPage_ng_template_32_div_14_ion_row_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-row\", 105)(1, \"ion-col\")(2, \"div\", 99);\n    i0.ɵɵelement(3, \"ion-icon\", 106);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 107)(7, \"div\", 108);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectCategory(\"strength\"));\n    });\n    i0.ɵɵelementStart(8, \"div\", 109);\n    i0.ɵɵtext(9, \"\\uD83D\\uDCAA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Strength\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 108);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_12_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectCategory(\"money\"));\n    });\n    i0.ɵɵelementStart(13, \"div\", 109);\n    i0.ɵɵtext(14, \"\\uD83D\\uDCB0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Money\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 108);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_17_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectCategory(\"health\"));\n    });\n    i0.ɵɵelementStart(18, \"div\", 109);\n    i0.ɵɵtext(19, \"\\uD83C\\uDFE5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \"Health\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 108);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_13_Template_div_click_22_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectCategory(\"knowledge\"));\n    });\n    i0.ɵɵelementStart(23, \"div\", 109);\n    i0.ɵɵtext(24, \"\\uD83E\\uDDE0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"Knowledge\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"slide-in-from-right\", ctx_r2.categoryAnimated)(\"slide-out-left\", ctx_r2.categoryAnimating && ctx_r2.selectedCategory);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.category === \"strength\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.category === \"money\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.category === \"health\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.category === \"knowledge\");\n  }\n}\nfunction TodayPage_ng_template_32_div_14_ion_row_14_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵelement(1, \"ion-icon\", 118);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"You already have a high priority quest in this category\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TodayPage_ng_template_32_div_14_ion_row_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-row\", 110)(1, \"ion-col\")(2, \"div\", 99);\n    i0.ɵɵelement(3, \"ion-icon\", 111);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Priority\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 112)(7, \"div\", 113);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_14_Template_div_click_7_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectPriority(\"basic\"));\n    });\n    i0.ɵɵelement(8, \"div\", 114);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"Basic Priority\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 113);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_14_ion_row_14_Template_div_click_11_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectPriority(\"high\"));\n    });\n    i0.ɵɵelement(12, \"div\", 115);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"High Priority\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(15, TodayPage_ng_template_32_div_14_ion_row_14_div_15_Template, 4, 0, \"div\", 116);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"slide-in-from-right\", ctx_r2.priorityAnimated)(\"slide-out-left\", ctx_r2.priorityAnimating);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"active\", ctx_r2.newQuest.priority === \"basic\")(\"disabled\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"active\", ctx_r2.newQuest.priority === \"high\")(\"disabled\", ctx_r2.hasHighPriorityQuest);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasHighPriorityQuest);\n  }\n}\nfunction TodayPage_ng_template_32_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"ion-row\", 90)(2, \"ion-col\", 77)(3, \"div\", 91)(4, \"div\", 92);\n    i0.ɵɵtext(5, \"\\u2699\\uFE0F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"h2\", 93);\n    i0.ɵɵtext(7, \"Configure your \");\n    i0.ɵɵelementStart(8, \"span\", 67);\n    i0.ɵɵtext(9, \"quest!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p\", 94);\n    i0.ɵɵtext(11, \"Choose the type, category and set your goal\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, TodayPage_ng_template_32_div_14_ion_row_12_Template, 23, 8, \"ion-row\", 95)(13, TodayPage_ng_template_32_div_14_ion_row_13_Template, 27, 12, \"ion-row\", 96)(14, TodayPage_ng_template_32_div_14_ion_row_14_Template, 16, 13, \"ion-row\", 97);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.newQuest.quest_type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.quest_type && !ctx_r2.categorySelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.category);\n  }\n}\nfunction TodayPage_ng_template_32_div_15_div_118_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 195);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_15_div_118_div_4_Template_div_click_0_listener() {\n      const day_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.updateDaysOfWeek(day_r19.value));\n    });\n    i0.ɵɵelementStart(1, \"span\", 196);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 197);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r19 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.selectedDaysOfWeek.includes(day_r19.value));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r19.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r19.fullName);\n  }\n}\nfunction TodayPage_ng_template_32_div_15_div_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 192)(1, \"h5\");\n    i0.ɵɵtext(2, \"Select Days of Week\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 193);\n    i0.ɵɵtemplate(4, TodayPage_ng_template_32_div_15_div_118_div_4_Template, 5, 4, \"div\", 194);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.weekDays);\n  }\n}\nfunction TodayPage_ng_template_32_div_15_div_119_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 201);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_15_div_119_div_4_Template_div_click_0_listener() {\n      const day_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.toggleMonthDay(day_r21));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r21 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.selectedDaysOfMonth.includes(day_r21));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", day_r21, \" \");\n  }\n}\nfunction TodayPage_ng_template_32_div_15_div_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 198)(1, \"h5\");\n    i0.ɵɵtext(2, \"Select Days of Month\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 199);\n    i0.ɵɵtemplate(4, TodayPage_ng_template_32_div_15_div_119_div_4_Template, 2, 3, \"div\", 200);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.monthDays);\n  }\n}\nfunction TodayPage_ng_template_32_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"ion-row\", 90)(2, \"ion-col\", 77)(3, \"div\", 91)(4, \"div\", 92);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"h2\", 93);\n    i0.ɵɵtext(7, \"Finalize your \");\n    i0.ɵɵelementStart(8, \"span\", 67);\n    i0.ɵɵtext(9, \"quest!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p\", 94);\n    i0.ɵɵtext(11, \"Set your goal and frequency to complete your quest\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"ion-row\", 120)(13, \"ion-col\")(14, \"div\", 99);\n    i0.ɵɵelement(15, \"ion-icon\", 121);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Goal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 122)(19, \"div\", 123)(20, \"div\", 124);\n    i0.ɵɵtext(21, \"\\uD83C\\uDFAF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 125);\n    i0.ɵɵtext(23, \"Set Your Target\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 126)(25, \"div\", 127)(26, \"div\", 81);\n    i0.ɵɵtext(27, \"\\uD83D\\uDD22\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"ion-input\", 128);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_15_Template_ion_input_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.goal_value, $event) || (ctx_r2.newQuest.goal_value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"label\", 129);\n    i0.ɵɵtext(30, \"Target Value\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"div\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 130)(33, \"div\", 81);\n    i0.ɵɵtext(34, \"\\uD83D\\uDCCF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"ion-select\", 131);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TodayPage_ng_template_32_div_15_Template_ion_select_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newQuest.goal_unit, $event) || (ctx_r2.newQuest.goal_unit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(36, \"ion-select-option\", 132);\n    i0.ɵɵtext(37, \"times\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"ion-select-option\", 133);\n    i0.ɵɵtext(39, \"steps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"ion-select-option\", 134);\n    i0.ɵɵtext(41, \"meters\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"ion-select-option\", 135);\n    i0.ɵɵtext(43, \"kilometers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"ion-select-option\", 136);\n    i0.ɵɵtext(45, \"seconds\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"ion-select-option\", 137);\n    i0.ɵɵtext(47, \"minutes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"ion-select-option\", 138);\n    i0.ɵɵtext(49, \"hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"ion-select-option\", 139);\n    i0.ɵɵtext(51, \"calories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"ion-select-option\", 140);\n    i0.ɵɵtext(53, \"grams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"ion-select-option\", 141);\n    i0.ɵɵtext(55, \"milligrams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"ion-select-option\", 142);\n    i0.ɵɵtext(57, \"liters\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"ion-select-option\", 143);\n    i0.ɵɵtext(59, \"drinks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"ion-select-option\", 144);\n    i0.ɵɵtext(61, \"pages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"ion-select-option\", 145);\n    i0.ɵɵtext(63, \"books\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"ion-select-option\", 146);\n    i0.ɵɵtext(65, \"percent\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"ion-select-option\", 147);\n    i0.ɵɵtext(67, \"euros\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"ion-select-option\", 148);\n    i0.ɵɵtext(69, \"dollars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"ion-select-option\", 149);\n    i0.ɵɵtext(71, \"pounds\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(72, \"label\", 150);\n    i0.ɵɵtext(73, \"Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(74, \"div\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 151)(76, \"span\", 152);\n    i0.ɵɵtext(77, \"Goal:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"span\", 153);\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(80, \"ion-row\", 154)(81, \"ion-col\")(82, \"div\", 99);\n    i0.ɵɵelement(83, \"ion-icon\", 155);\n    i0.ɵɵelementStart(84, \"span\");\n    i0.ɵɵtext(85, \"Frequency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(86, \"div\", 156)(87, \"div\", 157)(88, \"div\", 158);\n    i0.ɵɵtext(89, \"\\uD83D\\uDCC5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(90, \"div\", 159);\n    i0.ɵɵtext(91, \"Choose Frequency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(92, \"div\", 160)(93, \"div\", 161);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_15_Template_div_click_93_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectFrequency(\"day\"));\n    });\n    i0.ɵɵelementStart(94, \"div\", 162);\n    i0.ɵɵtext(95, \"\\uD83C\\uDF05\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"div\", 163)(97, \"h4\");\n    i0.ɵɵtext(98, \"Every Day\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"p\");\n    i0.ɵɵtext(100, \"Daily commitment\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(101, \"div\", 161);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_15_Template_div_click_101_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectFrequency(\"week\"));\n    });\n    i0.ɵɵelementStart(102, \"div\", 162);\n    i0.ɵɵtext(103, \"\\uD83D\\uDCC6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"div\", 163)(105, \"h4\");\n    i0.ɵɵtext(106, \"Weekly\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(107, \"p\");\n    i0.ɵɵtext(108, \"Choose specific days\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(109, \"div\", 161);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_div_15_Template_div_click_109_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectFrequency(\"month\"));\n    });\n    i0.ɵɵelementStart(110, \"div\", 162);\n    i0.ɵɵtext(111, \"\\uD83D\\uDDD3\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(112, \"div\", 163)(113, \"h4\");\n    i0.ɵɵtext(114, \"Monthly\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"p\");\n    i0.ɵɵtext(116, \"Select days of month\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(117, \"div\", 164);\n    i0.ɵɵtemplate(118, TodayPage_ng_template_32_div_15_div_118_Template, 5, 1, \"div\", 165)(119, TodayPage_ng_template_32_div_15_div_119_Template, 5, 1, \"div\", 166);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(120, \"ion-row\", 167)(121, \"ion-col\")(122, \"div\", 99);\n    i0.ɵɵelement(123, \"ion-icon\", 168);\n    i0.ɵɵelementStart(124, \"span\");\n    i0.ɵɵtext(125, \"Preview\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(126, \"div\", 169)(127, \"div\", 170);\n    i0.ɵɵtext(128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(129, \"div\", 171)(130, \"div\", 172)(131, \"h3\", 173);\n    i0.ɵɵtext(132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(133, \"p\", 174);\n    i0.ɵɵtext(134);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(135, \"div\", 175)(136, \"div\", 176)(137, \"div\", 177);\n    i0.ɵɵtext(138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(139, \"div\", 178)(140, \"span\", 179);\n    i0.ɵɵtext(141, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(142, \"span\", 180);\n    i0.ɵɵtext(143);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(144, \"div\", 176)(145, \"div\", 177);\n    i0.ɵɵtext(146);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(147, \"div\", 178)(148, \"span\", 179);\n    i0.ɵɵtext(149, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(150, \"span\", 180);\n    i0.ɵɵtext(151);\n    i0.ɵɵpipe(152, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(153, \"div\", 176)(154, \"div\", 177);\n    i0.ɵɵtext(155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(156, \"div\", 178)(157, \"span\", 179);\n    i0.ɵɵtext(158, \"Priority\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(159, \"span\", 180);\n    i0.ɵɵtext(160);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(161, \"div\", 181)(162, \"div\", 182)(163, \"span\", 183);\n    i0.ɵɵtext(164);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(165, \"span\", 184);\n    i0.ɵɵtext(166);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(167, \"div\", 185)(168, \"div\", 186)(169, \"span\", 187);\n    i0.ɵɵtext(170, \"Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(171, \"span\", 188);\n    i0.ɵɵtext(172, \"0%\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(173, \"div\", 189);\n    i0.ɵɵelement(174, \"div\", 190);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(175, \"div\", 191)(176, \"span\");\n    i0.ɵɵtext(177);\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.emoji ? ctx_r2.newQuest.emoji : \"\\uD83C\\uDFAF\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"slide-in-from-right\", ctx_r2.goalAnimated);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.goal_value);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newQuest.goal_unit);\n    i0.ɵɵadvance(44);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.newQuest.goal_value || 1, \" \", ctx_r2.newQuest.goal_unit || \"times\", \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"slide-in-from-right\", ctx_r2.frequencyAnimated);\n    i0.ɵɵadvance(13);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.goal_period === \"day\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.goal_period === \"week\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.newQuest.goal_period === \"month\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"slide-in\", ctx_r2.frequencyOptionsAnimated);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.goal_period === \"week\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.newQuest.goal_period === \"month\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"slide-in-from-right\", ctx_r2.previewAnimated);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.emoji ? ctx_r2.newQuest.emoji : \"\\uD83C\\uDFAF\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.name ? ctx_r2.newQuest.name : \"Your Quest Name\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.description ? ctx_r2.newQuest.description : \"Add a description to make your quest more meaningful\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.quest_type === \"build\" ? \"\\uD83C\\uDFD7\\uFE0F\" : \"\\uD83D\\uDEAB\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.quest_type === \"build\" ? \"Build Habit\" : \"Quit Habit\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getCategoryIcon(ctx_r2.newQuest.category ? ctx_r2.newQuest.category : \"\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.category ? i0.ɵɵpipeBind1(152, 36, ctx_r2.newQuest.category) : \"Not selected\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.priority === \"high\" ? \"\\u2B50\" : \"\\uD83D\\uDCCC\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.priority === \"high\" ? \"High Priority\" : \"Basic Priority\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.newQuest.goal_value ? ctx_r2.newQuest.goal_value : 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.newQuest.goal_unit ? ctx_r2.newQuest.goal_unit : \"times\", \" \", ctx_r2.getFrequencyText(), \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleProp(\"width\", 0, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"0 / \", ctx_r2.newQuest.goal_value ? ctx_r2.newQuest.goal_value : 1, \" completed\");\n  }\n}\nfunction TodayPage_ng_template_32_ion_col_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-col\")(1, \"ion-button\", 202);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_ion_col_17_Template_ion_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.prevStep());\n    });\n    i0.ɵɵtext(2, \"Back\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TodayPage_ng_template_32_ion_col_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-col\")(1, \"ion-button\", 203);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_ion_col_18_Template_ion_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.nextStep());\n    });\n    i0.ɵɵtext(2, \"Next\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TodayPage_ng_template_32_ion_col_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-col\")(1, \"ion-button\", 204);\n    i0.ɵɵtext(2, \"Create Quest\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TodayPage_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-content\", 49);\n    i0.ɵɵelement(1, \"app-aurora\");\n    i0.ɵɵelementStart(2, \"ion-grid\")(3, \"ion-row\", 50)(4, \"ion-col\")(5, \"h2\");\n    i0.ɵɵtext(6, \"Add New Quest\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"ion-progress-bar\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-icon\", 52);\n    i0.ɵɵlistener(\"click\", function TodayPage_ng_template_32_Template_ion_icon_click_8_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeAddQuestModal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"form\", 53, 0);\n    i0.ɵɵlistener(\"ngSubmit\", function TodayPage_ng_template_32_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.createQuest());\n    });\n    i0.ɵɵtemplate(11, TodayPage_ng_template_32_div_11_Template, 16, 0, \"div\", 54)(12, TodayPage_ng_template_32_div_12_Template, 18, 2, \"div\", 55)(13, TodayPage_ng_template_32_div_13_Template, 32, 6, \"div\", 56)(14, TodayPage_ng_template_32_div_14_Template, 15, 3, \"div\", 57)(15, TodayPage_ng_template_32_div_15_Template, 178, 38, \"div\", 58);\n    i0.ɵɵelementStart(16, \"ion-row\", 59);\n    i0.ɵɵtemplate(17, TodayPage_ng_template_32_ion_col_17_Template, 3, 0, \"ion-col\", 44)(18, TodayPage_ng_template_32_ion_col_18_Template, 3, 0, \"ion-col\", 44)(19, TodayPage_ng_template_32_ion_col_19_Template, 3, 0, \"ion-col\", 44);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r2.progress);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep !== 5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep !== 5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 5);\n  }\n}\nfunction TodayPage_app_celebration_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-celebration\", 205);\n    i0.ɵɵlistener(\"close\", function TodayPage_app_celebration_33_Template_app_celebration_close_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeCelebration());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"user\", ctx_r2.currentUser)(\"date\", ctx_r2.formatDate(ctx_r2.selectedDate));\n  }\n}\nexport let TodayPage = /*#__PURE__*/(() => {\n  var _TodayPage;\n  class TodayPage {\n    // Method to load daily side quest\n    loadDailySideQuest() {\n      // Only load for today's date\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(this.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      // Reset dailyQuest if not today's date\n      if (!isTodaySelected) {\n        this.dailyQuest = null;\n        return;\n      }\n      if (this.showSidequests && isTodaySelected && this.userId) {\n        // Use the ensureUserHasDailySideQuests method\n        this.sideQuestService.ensureUserHasDailySideQuests(this.userId).pipe(take(1)).subscribe({\n          next: sideQuests => {\n            if (sideQuests && sideQuests.length > 0) {\n              const sideQuest = sideQuests[0];\n              // Get the quest details from the pool\n              this.supabaseService.getClient().from('daily_sidequest_pool').select('*').eq('id', sideQuest.current_quest_id).single().then(response => {\n                if (response.error) {\n                  return;\n                }\n                const questDetails = response.data;\n                // Create the daily quest object\n                this.dailyQuest = {\n                  id: sideQuest.id,\n                  current_quest: {\n                    id: sideQuest.current_quest_id,\n                    name: questDetails.name || 'Daily Side Quest',\n                    description: questDetails.description || 'Complete this daily side quest',\n                    goal_value: questDetails.goal_value || 1,\n                    goal_unit: questDetails.goal_unit || 'count'\n                  },\n                  streak: sideQuest.streak || 0,\n                  completed: sideQuest.completed || false,\n                  value_achieved: sideQuest.value_achieved || 0,\n                  emoji: questDetails.emoji || '🎯'\n                };\n              });\n            } else {\n              this.dailyQuest = null;\n            }\n          },\n          error: () => {\n            this.dailyQuest = null;\n          }\n        });\n      }\n    }\n    constructor() {\n      // User data\n      this.user$ = of(null);\n      this.userId = null;\n      this.showSidequests = true;\n      // Date and calendar\n      this.selectedDate = new Date();\n      this.weekDates = [];\n      this.dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];\n      this.headerText = 'Today';\n      this.weekOffset = 0;\n      // Quests\n      this.quests = [];\n      this.dailyQuest = null;\n      this.currentStep = 5;\n      this.totalSteps = 5;\n      this.emojis = ['🚀', '🪐', '⏳', '💊', '⚔️', '🧠', '🦷', '👨‍🍳', '🏃', '🥬', '🏆', '🎮', '🎯', '💻', '🚴‍♂️', '🏋️‍♂️', '💰', '💸', '🪬', '🧪', '😴', '📈', '📚', '❌', '🎓', '💪', '🧘‍♂️', '📵', '🚭', '💧'];\n      // Cache for quest data to improve performance\n      this.questCache = {};\n      // Flag to track if we're currently loading data\n      this.isLoadingData = false;\n      // Add Quest Modal\n      this.showAddQuestModal = false;\n      this.newQuest = this.getEmptyQuest();\n      this.hasHighPriorityQuest = false;\n      // Animation states\n      this.questTypeAnimated = false;\n      this.questTypeAnimating = false;\n      this.selectedQuestType = '';\n      this.categoryAnimated = false;\n      this.categoryAnimating = false;\n      this.categorySelected = false;\n      this.selectedCategory = '';\n      this.priorityAnimated = false;\n      this.priorityAnimating = false;\n      this.goalAnimated = false;\n      this.questDetailsAnimated = false;\n      this.frequencyAnimated = false;\n      this.frequencyOptionsAnimated = false;\n      this.previewAnimated = false;\n      // Celebration Modal\n      this.showCelebration = false;\n      this.currentUser = null;\n      this.celebrationShownDates = [];\n      // Days selection for new quest\n      this.weekDays = [{\n        value: 'Sun',\n        label: 'Su',\n        fullName: 'Sunday'\n      }, {\n        value: 'Mon',\n        label: 'Mo',\n        fullName: 'Monday'\n      }, {\n        value: 'Tue',\n        label: 'Tu',\n        fullName: 'Tuesday'\n      }, {\n        value: 'Wed',\n        label: 'We',\n        fullName: 'Wednesday'\n      }, {\n        value: 'Thu',\n        label: 'Th',\n        fullName: 'Thursday'\n      }, {\n        value: 'Fri',\n        label: 'Fr',\n        fullName: 'Friday'\n      }, {\n        value: 'Sat',\n        label: 'Sa',\n        fullName: 'Saturday'\n      }];\n      this.monthDays = Array.from({\n        length: 31\n      }, (_, i) => i + 1);\n      this.selectedDaysOfWeek = [];\n      this.selectedDaysOfMonth = [];\n      // Use inject instead of constructor injection\n      this.questService = inject(QuestService);\n      this.sideQuestService = inject(SideQuestService);\n      this.userService = inject(UserService);\n      this.supabaseService = inject(SupabaseService);\n      this.route = inject(ActivatedRoute);\n      this.router = inject(Router);\n      this.preferencesService = inject(PreferencesService);\n      this.streakCalculator = inject(StreakCalculatorService);\n      this.isRedirecting = false; // Flag to prevent multiple redirects\n      // Cache for week date progress\n      this.weekProgressCache = {};\n      // Flag to track if we're currently changing weeks\n      this.isChangingWeek = false;\n      // Map to track which quests are currently being toggled\n      this.togglingQuestIds = {};\n      // Map to track which quests are currently being updated\n      this.updatingQuestIds = {};\n      // Map to track which side quests are currently being toggled\n      this.togglingSideQuestIds = {};\n      // Subscribe to query params to get date and week_offset from URL\n      this.route.queryParams.subscribe(params => {\n        const dateParam = params['date'];\n        const weekOffsetParam = params['week_offset'];\n        console.log('TodayPage: Date param from URL query:', dateParam);\n        console.log('TodayPage: Week offset param from URL query:', weekOffsetParam);\n        // Process week offset parameter\n        if (weekOffsetParam !== undefined) {\n          try {\n            this.weekOffset = parseInt(weekOffsetParam);\n            console.log('TodayPage: Week offset set to:', this.weekOffset);\n          } catch (error) {\n            console.error('TodayPage: Error parsing week offset:', error);\n            this.weekOffset = 0;\n          }\n        } else {\n          this.weekOffset = 0;\n        }\n        // Process date parameter\n        if (dateParam) {\n          try {\n            // Validate date format (YYYY-MM-DD)\n            if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateParam)) {\n              this.selectedDate = new Date(dateParam);\n              console.log('TodayPage: Selected date from URL query:', this.selectedDate);\n            } else {\n              console.error('TodayPage: Invalid date format in URL query:', dateParam);\n              this.selectedDate = new Date(); // Default to today\n            }\n          } catch (error) {\n            console.error('TodayPage: Error parsing date from URL query:', error);\n            this.selectedDate = new Date(); // Default to today\n          }\n        } else {\n          this.selectedDate = new Date(); // Default to today\n        }\n        // Initialize week dates based on selected date and week offset\n        this.generateWeekDates();\n        // Update header text and load data\n        this.updateHeaderText();\n        // Only load data if we have a userId\n        if (this.userId) {\n          this.loadData();\n        }\n      });\n      // Subscribe to auth state changes\n      this.userSubscription = this.supabaseService.currentUser$.subscribe(authUser => {\n        if (!authUser) {\n          console.log('TodayPage: No authenticated user, but not redirecting');\n          // Removed redirect to allow direct access\n          return;\n        }\n        // User is authenticated, get user data\n        this.userId = authUser.id;\n        // Get user data from Supabase\n        this.userService.getUserById(authUser.id).subscribe(userData => {\n          if (!userData) {\n            console.log('TodayPage: No user data found, but not redirecting');\n            // Removed redirect to allow direct access\n            return;\n          }\n          console.log('TodayPage: User data loaded:', userData);\n          this.loadData();\n        });\n      });\n      // Set up user$ observable for template binding\n      this.user$ = this.supabaseService.currentUser$.pipe(switchMap(authUser => {\n        if (!authUser) {\n          return of(null);\n        }\n        return this.userService.getUserById(authUser.id);\n      }));\n      // Subscribe to user$ to get user preferences\n      const userDataSubscription = this.user$.subscribe({\n        next: user => {\n          if (user) {\n            this.showSidequests = user.sidequests_switch;\n            this.currentUser = user;\n          }\n        }\n      });\n      // Add the subscription to be cleaned up\n      this.userSubscription = new Subscription();\n      this.userSubscription.add(userDataSubscription);\n    }\n    ngOnInit() {\n      // Generate week dates and preload data for all days\n      this.generateWeekDates();\n      // Preload data for all days in the week\n      setTimeout(() => {\n        this.preloadWeekData();\n      }, 0);\n      // Load celebration shown dates from localStorage and clean up old ones\n      try {\n        // Get today's date\n        const today = new Date();\n        const todayStr = this.formatDate(today);\n        // First, collect all localStorage keys\n        const allKeys = [];\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          if (key) {\n            allKeys.push(key);\n          }\n        }\n        // Find and remove all celebration_shown keys except today's\n        allKeys.forEach(key => {\n          if (key.startsWith('celebration_shown_') && key !== `celebration_shown_${todayStr}`) {\n            localStorage.removeItem(key);\n          }\n        });\n        // Check if we have a celebration shown for today\n        const todayCelebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\n        // Add to our tracking array if found\n        this.celebrationShownDates = [];\n        if (todayCelebrationShown) {\n          this.celebrationShownDates.push(todayStr);\n        }\n      } catch (error) {\n        this.celebrationShownDates = [];\n      }\n    }\n    ionViewWillEnter() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        // Authentication is now handled by the AuthGuard\n        // Just get the current user and load data\n        const authUser = _this.supabaseService._currentUser.value;\n        if (!authUser) {\n          console.log('TodayPage: No authenticated user, but not redirecting');\n          return;\n        }\n        // Use the UserService to get or create the user document\n        _this.userService.ensureUserExists(authUser).subscribe(userData => {\n          if (!userData) {\n            _this.router.navigateByUrl('/signup');\n            return;\n          }\n          // Get end date\n          let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;\n          const currentDate = new Date();\n          // Compare dates properly\n          let isValidPlan = false;\n          if (endDate instanceof Date) {\n            isValidPlan = endDate > currentDate;\n          }\n          if (!isValidPlan) {\n            // Prevent multiple redirects\n            if (_this.isRedirecting) return;\n            _this.isRedirecting = true;\n            setTimeout(() => {\n              _this.router.navigateByUrl('/pricing');\n              setTimeout(() => {\n                _this.isRedirecting = false;\n              }, 2000);\n            }, 500);\n            return;\n          }\n          // Check if we have cached data for this date\n          const dateKey = _this.formatDate(_this.selectedDate);\n          if (_this.questCache[dateKey]) {\n            // Use cached data\n            _this.quests = _this.questCache[dateKey];\n            // Initialize slider backgrounds immediately\n            requestAnimationFrame(() => {\n              _this.initializeSliderBackgrounds();\n            });\n            // Load daily side quest if needed\n            _this.loadDailySideQuest();\n          } else {\n            // Load data with the current selected date\n            _this.loadData();\n          }\n        });\n        // Make sure the URL reflects the selected date and week offset\n        const route = _this.router.url;\n        const dateParam = _this.formatDate(_this.selectedDate);\n        if (route === '/today') {\n          // If we're on the base route, update to include the date and week_offset as query parameters\n          _this.router.navigate(['/today'], {\n            queryParams: {\n              date: dateParam,\n              week_offset: _this.weekOffset !== 0 ? _this.weekOffset : null\n            },\n            replaceUrl: true\n          });\n        }\n      })();\n    }\n    // Initialize all slider backgrounds\n    initializeSliderBackgrounds() {\n      // Use requestAnimationFrame for better performance\n      requestAnimationFrame(() => {\n        const sliders = document.querySelectorAll('.progress-slider');\n        if (sliders.length === 0) {\n          return;\n        }\n        sliders.forEach(slider => {\n          if (slider instanceof HTMLInputElement) {\n            // Get the slider's quest ID for debugging\n            const sliderQuestId = slider.getAttribute('data-quest-id');\n            if (!sliderQuestId) {\n              return;\n            }\n            // Get the exact value from the slider (no rounding)\n            const sliderValue = parseInt(slider.value);\n            const minValue = parseInt(slider.min);\n            const maxValue = parseInt(slider.max);\n            // Calculate the percentage value\n            const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n            // Set the background directly with hardcoded colors\n            slider.style.background = `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\n            // Add a data attribute to track the current value\n            slider.setAttribute('data-current-value', slider.value);\n          } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n            // Get the slider's quest ID for debugging\n            const sliderQuestId = slider.getAttribute('data-quest-id');\n            if (!sliderQuestId) {\n              return;\n            }\n            // Get the value from the element's properties or attributes\n            const valueAttr = slider.getAttribute('value') || '0';\n            const minAttr = slider.getAttribute('min') || '0';\n            const maxAttr = slider.getAttribute('max') || '100';\n            const sliderValue = parseInt(valueAttr);\n            const minValue = parseInt(minAttr);\n            const maxValue = parseInt(maxAttr);\n            // Calculate the percentage value\n            const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n            // Set the CSS variable for the progress\n            slider.style.setProperty('--progress-value', `${percentage}%`);\n            // Add a data attribute to track the current value\n            slider.setAttribute('data-current-value', sliderValue.toString());\n          }\n        });\n      });\n    }\n    ionViewWillLeave() {\n      console.log('TodayPage: ionViewWillLeave called');\n    }\n    ngOnDestroy() {\n      console.log('TodayPage: ngOnDestroy called');\n      if (this.userSubscription) {\n        this.userSubscription.unsubscribe();\n      }\n    }\n    loadData() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this2.userId) {\n          return;\n        }\n        // Update header text\n        _this2.updateHeaderText();\n        // Check if we have cached data for this date\n        const dateKey = _this2.formatDate(_this2.selectedDate);\n        if (_this2.questCache[dateKey]) {\n          // Use cached data\n          _this2.quests = _this2.questCache[dateKey];\n          // Initialize slider backgrounds immediately\n          requestAnimationFrame(() => {\n            _this2.initializeSliderBackgrounds();\n          });\n          // Load daily side quest if needed\n          _this2.loadDailySideQuest();\n          return;\n        }\n        // Set up date variables\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = new Date(_this2.selectedDate);\n        selectedDate.setHours(0, 0, 0, 0);\n        const isTodaySelected = selectedDate.getTime() === today.getTime();\n        console.log('TodayPage: Loading data for date:', _this2.formatDate(_this2.selectedDate));\n        if (isTodaySelected) {\n          // Check if we've already calculated streaks for today\n          const todayDateString = _this2.formatDate(today);\n          try {\n            const {\n              value: lastStreakCalculation\n            } = yield _this2.preferencesService.get('last_streak_calculation');\n            if (lastStreakCalculation !== todayDateString) {\n              console.log('TodayPage: First time loading today, calculating streaks');\n              // Najprv spracujeme všetky questy pomocou checkMissedDays\n              yield firstValueFrom(_this2.questService.getQuests(_this2.userId).pipe(take(1), switchMap(/*#__PURE__*/function () {\n                var _ref = _asyncToGenerator(function* (quests) {\n                  // Check missed days for each quest\n                  for (const quest of quests) {\n                    if (quest.id) {\n                      yield _this2.questService.checkMissedDays(quest.id);\n                    }\n                  }\n                  // Potom vytvoríme progress záznamy pre quit questy\n                  yield _this2.questService.createQuitQuestProgressForToday();\n                  // NEBUDEME tu nastavovať last_streak_calculation, aby sa mohli vypočítať streaky v ďalšej časti kódu\n                  return quests;\n                });\n                return function (_x) {\n                  return _ref.apply(this, arguments);\n                };\n              }())));\n              // Streaky sa vypočítajú v ďalšej časti kódu\n            } else {\n              console.log('TodayPage: Streaks already calculated for today');\n            }\n          } catch (error) {\n            console.error('TodayPage: Error checking last streak calculation:', error);\n            // Ak nastane chyba, nenastavujeme last_streak_calculation, aby sa mohli vypočítať streaky\n          }\n          // Recalculate streak for the daily side quest only for today\n          if (_this2.showSidequests) {\n            _this2.sideQuestService.recalculateSideQuestStreak(_this2.userId, _this2.selectedDate).subscribe({\n              error: error => {\n                console.error('Error recalculating side quest streak:', error);\n              }\n            });\n          }\n        }\n        // Load quests\n        _this2.questService.getQuests(_this2.userId).pipe(take(1), switchMap(quests => {\n          // Filter active quests for the selected date\n          const filteredQuests = _this2.filterQuestsForDate(quests, _this2.selectedDate);\n          if (filteredQuests.length === 0) {\n            return of([]);\n          }\n          // Sort filtered quests by creation date (newest first) or ID\n          const sortedFilteredQuests = [...filteredQuests].sort((a, b) => {\n            if (a.created_at && b.created_at) {\n              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n            }\n            return a.id && b.id ? a.id.localeCompare(b.id) : 0;\n          });\n          // Get all progress for all quests at once\n          return _this2.questService.getQuestProgressForDate(_this2.userId, _this2.selectedDate).pipe(take(1), switchMap(allProgress => {\n            // Create a lookup for quick access\n            const progressLookup = {};\n            allProgress.forEach(progress => {\n              progressLookup[progress.quest_id] = progress;\n            });\n            // For today's view, calculate streaks once per day\n            // For other days, just use the streak from the database\n            if (isTodaySelected) {\n              // Check if we've already calculated streaks for today\n              const todayDateString = _this2.formatDate(today);\n              return _this2.preferencesService.get('last_streak_calculation').then(({\n                value: lastStreakCalculation\n              }) => {\n                if (lastStreakCalculation !== todayDateString) {\n                  console.log('TodayPage: First time loading today, calculating streaks');\n                  // Calculate streaks using our streak calculator\n                  return _this2.streakCalculator.calculateStreaks(_this2.userId, sortedFilteredQuests).then(streaks => {\n                    // Map quests with progress and calculated streaks\n                    return sortedFilteredQuests.map(quest => {\n                      const progress = progressLookup[quest.id];\n                      const calculatedStreak = streaks[quest.id] || 0;\n                      // Update the streak in the database\n                      _this2.questService.updateQuestStreak(quest.id, calculatedStreak).subscribe();\n                      return {\n                        ...quest,\n                        completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                        value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                        streak: calculatedStreak\n                      };\n                    });\n                  }).then(result => {\n                    // Po výpočte streakov nastavíme last_streak_calculation\n                    _this2.preferencesService.set('last_streak_calculation', todayDateString);\n                    return result;\n                  });\n                } else {\n                  console.log('TodayPage: Streaks already calculated for today, using database values');\n                  // Just use the streak from the database\n                  return sortedFilteredQuests.map(quest => {\n                    const progress = progressLookup[quest.id];\n                    return {\n                      ...quest,\n                      completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                      value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                      streak: quest.streak || 0\n                    };\n                  });\n                }\n              }).catch(error => {\n                console.error('TodayPage: Error checking last streak calculation:', error);\n                // If there's an error, just use the streak from the database\n                return sortedFilteredQuests.map(quest => {\n                  const progress = progressLookup[quest.id];\n                  return {\n                    ...quest,\n                    completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                    value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                    streak: quest.streak || 0\n                  };\n                });\n              });\n            } else {\n              // For previous days, just use the streak from the database but set it to 0 for display\n              return Promise.resolve(sortedFilteredQuests.map(quest => {\n                const progress = progressLookup[quest.id];\n                return {\n                  ...quest,\n                  completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                  value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                  streak: 0 // Don't show streak for previous days\n                };\n              }));\n            }\n          }));\n        })).subscribe({\n          next: questsWithProgress => {\n            // Sort quests by creation date (newest first) or ID\n            const sortedQuests = [...questsWithProgress].sort((a, b) => {\n              if (a.created_at && b.created_at) {\n                return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n              }\n              return a.id && b.id ? a.id.localeCompare(b.id) : 0;\n            });\n            // Check if all quests are completed for today\n            _this2.checkAllQuestsCompleted(sortedQuests);\n            // Update the quests array\n            _this2.quests = sortedQuests;\n            // Cache the quests for this date\n            const dateKey = _this2.formatDate(_this2.selectedDate);\n            _this2.questCache[dateKey] = sortedQuests;\n            // Update the week date progress\n            _this2.updateWeekDateProgress();\n            // Initialize slider backgrounds\n            requestAnimationFrame(() => {\n              _this2.initializeSliderBackgrounds();\n            });\n            // Load daily side quest if needed\n            _this2.loadDailySideQuest();\n          },\n          error: error => {\n            console.error('Error loading quests:', error);\n          }\n        });\n      })();\n    }\n    generateWeekDates() {\n      const today = new Date();\n      // Calculate the start of the week based on week offset\n      // This starts on Monday (1) instead of Sunday (0)\n      const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\n      const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // Convert to Monday-based (0 = Monday)\n      const startOfWeek = new Date(today);\n      startOfWeek.setDate(today.getDate() - daysFromMonday + 7 * this.weekOffset);\n      this.weekDates = [];\n      for (let i = 0; i < 7; i++) {\n        const date = new Date(startOfWeek);\n        date.setDate(startOfWeek.getDate() + i);\n        const dateString = this.formatDate(date);\n        const isToday = this.isSameDay(date, today);\n        const isSelected = this.isSameDay(date, this.selectedDate);\n        const isFuture = date > today;\n        // Check if we have cached progress for this date\n        const dateKey = dateString;\n        let totalQuests = 0;\n        let completedQuests = 0;\n        let completionPercentage = 0;\n        if (this.weekProgressCache[dateKey]) {\n          const cached = this.weekProgressCache[dateKey];\n          totalQuests = cached.total;\n          completedQuests = cached.completed;\n          completionPercentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n        }\n        this.weekDates.push({\n          date: dateString,\n          day: date.getDate(),\n          is_today: isToday,\n          is_selected: isSelected,\n          is_future: isFuture,\n          total_quests: totalQuests,\n          completed_quests: completedQuests,\n          completion_percentage: completionPercentage\n        });\n      }\n      // Preload data for all days in the week\n      if (this.userId) {\n        // Use setTimeout to allow the UI to render first\n        setTimeout(() => {\n          this.preloadWeekData();\n        }, 0);\n      }\n    }\n    updateWeekDateProgress() {\n      if (!this.userId) return;\n      // For each date in the week, update the progress\n      this.weekDates.forEach((weekDate, index) => {\n        if (weekDate.is_future) return;\n        const date = new Date(weekDate.date);\n        const dateKey = this.formatDate(date);\n        // Check if we have cached progress for this date\n        if (this.weekProgressCache[dateKey]) {\n          const cached = this.weekProgressCache[dateKey];\n          this.weekDates[index].total_quests = cached.total;\n          this.weekDates[index].completed_quests = cached.completed;\n          this.weekDates[index].completion_percentage = cached.total > 0 ? Math.round(cached.completed / cached.total * 100) : 0;\n          return;\n        }\n        // If we have cached quests for this date, use them to calculate progress\n        if (this.questCache[dateKey]) {\n          const cachedQuests = this.questCache[dateKey];\n          const totalQuests = cachedQuests.length;\n          const completedQuests = cachedQuests.filter(q => q.completed).length;\n          // Cache the progress\n          this.weekProgressCache[dateKey] = {\n            total: totalQuests,\n            completed: completedQuests\n          };\n          // Update the week date\n          this.weekDates[index].total_quests = totalQuests;\n          this.weekDates[index].completed_quests = completedQuests;\n          this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n          return;\n        }\n      });\n      // Preload data for all days in the week\n      this.preloadWeekData();\n    }\n    // Helper method to filter quests for a specific date\n    filterQuestsForDate(quests, date) {\n      const dateObj = new Date(date);\n      const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.\n      // Django uses Monday=0, Sunday=6 format, so we need to convert\n      const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format\n      const dayOfMonth = dateObj.getDate(); // 1-31\n      console.log(`TodayPage: Filtering quests for date ${this.formatDate(date)}, day of week: ${dayOfWeek} (Django: ${djangoDayOfWeek}), day of month: ${dayOfMonth}`);\n      const filteredQuests = quests.filter(quest => {\n        console.log(`TodayPage: Checking quest ${quest.id} (${quest.name}), type: ${quest.quest_type}, period: ${quest.goal_period}, task_days_of_week: ${quest.task_days_of_week}, task_days_of_month: ${quest.task_days_of_month}`);\n        if (!quest.active) {\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is not active, filtering out`);\n          return false;\n        }\n        // Only show quests from the date they were created\n        if (quest.created_at) {\n          const createdDate = new Date(quest.created_at);\n          createdDate.setHours(0, 0, 0, 0);\n          dateObj.setHours(0, 0, 0, 0);\n          // If the selected date is before the quest was created, don't show it\n          if (dateObj < createdDate) {\n            return false;\n          }\n        }\n        // Daily quests are always shown\n        if (quest.goal_period === 'day') {\n          return true;\n        }\n        // Weekly quests are shown on specific days\n        if (quest.goal_period === 'week') {\n          if (!quest.task_days_of_week) {\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has no task_days_of_week specified, showing every day`);\n            return true; // If no days specified, show every day\n          }\n          // Parse task_days_of_week\n          let taskDays = [];\n          if (typeof quest.task_days_of_week === 'string') {\n            taskDays = quest.task_days_of_week.split(',').map(day => day.trim());\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as string: ${quest.task_days_of_week}, parsed to:`, taskDays);\n          } else if (Array.isArray(quest.task_days_of_week)) {\n            taskDays = quest.task_days_of_week;\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as array:`, taskDays);\n          }\n          // Check if current day is in task days\n          // Convert current day to different formats for comparison\n          const dayNameShort = this.getDayNameShort(djangoDayOfWeek);\n          const dayNameFull = this.getDayNameFull(djangoDayOfWeek);\n          console.log(`TodayPage: Checking if day ${dayNameFull} (${dayNameShort}, ${djangoDayOfWeek}) is in task days:`, taskDays);\n          const isIncluded = taskDays.includes(djangoDayOfWeek) || taskDays.includes(djangoDayOfWeek.toString()) || taskDays.includes(dayNameShort) || taskDays.includes(dayNameFull);\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) should be shown on ${dayNameFull}? ${isIncluded}`);\n          return isIncluded;\n        }\n        // Monthly quests are shown on specific days of month\n        if (quest.goal_period === 'month') {\n          if (!quest.task_days_of_month) return true; // If no days specified, show every day\n          // Parse task_days_of_month\n          let taskDays = [];\n          if (typeof quest.task_days_of_month === 'string') {\n            taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));\n          } else if (Array.isArray(quest.task_days_of_month)) {\n            taskDays = quest.task_days_of_month;\n          }\n          // Check if current day is in task days\n          return taskDays.includes(dayOfMonth) || taskDays.includes(dayOfMonth.toString());\n        }\n        return false;\n      });\n      console.log(`TodayPage: Filtered ${quests.length} quests to ${filteredQuests.length} for date ${this.formatDate(date)}`);\n      return filteredQuests;\n    }\n    selectDate(dateObj) {\n      if (this.isLoadingData) {\n        return;\n      }\n      const dateData = dateObj.date;\n      this.isLoadingData = true;\n      this.selectedDateData = dateObj;\n      const date = new Date(dateData);\n      this.selectedDate = date;\n      this.weekDates.forEach(weekDate => {\n        weekDate.is_selected = weekDate.date === dateData;\n      });\n      const formattedDate = this.formatDate(date);\n      this.router.navigate(['/today'], {\n        queryParams: {\n          date: formattedDate,\n          week_offset: this.weekOffset !== 0 ? this.weekOffset : null\n        },\n        replaceUrl: true\n      });\n      this.updateHeaderText();\n      setTimeout(() => {\n        this.loadData();\n        this.isLoadingData = false;\n      }, 10);\n    }\n    changeWeek(direction) {\n      // Prevent multiple rapid week changes\n      if (this.isChangingWeek) {\n        return;\n      }\n      this.isChangingWeek = true;\n      // Update the week offset\n      this.weekOffset += direction;\n      // Generate new week dates with the updated offset\n      this.generateWeekDates();\n      // Preload quest data for all days in the week\n      this.preloadWeekData();\n      // Update the URL with the new week offset while preserving the selected date\n      const dateParam = this.formatDate(this.selectedDate);\n      this.router.navigate(['/today'], {\n        queryParams: {\n          date: dateParam,\n          week_offset: this.weekOffset\n        },\n        replaceUrl: true\n      });\n      // Reset the flag after a short delay\n      setTimeout(() => {\n        this.isChangingWeek = false;\n      }, 300);\n    }\n    // Preload data for all days in the current week\n    preloadWeekData() {\n      if (!this.userId) return;\n      // Get all quests once to avoid multiple API calls\n      this.questService.getQuests(this.userId).pipe(take(1)).subscribe(allQuests => {\n        // Create an array of observables for each date\n        const dateObservables = this.weekDates.filter(weekDate => !weekDate.is_future).map(weekDate => {\n          const date = new Date(weekDate.date);\n          const dateKey = this.formatDate(date);\n          // Skip if we already have cached data\n          if (this.weekProgressCache[dateKey]) {\n            return of({\n              date: weekDate.date,\n              progress: this.weekProgressCache[dateKey]\n            });\n          }\n          // Filter active quests for this date\n          const activeQuests = this.filterQuestsForDate(allQuests, date);\n          // If no active quests, return empty progress\n          if (activeQuests.length === 0) {\n            const emptyProgress = {\n              total: 0,\n              completed: 0\n            };\n            this.weekProgressCache[dateKey] = emptyProgress;\n            return of({\n              date: weekDate.date,\n              progress: emptyProgress\n            });\n          }\n          // Get progress for this date\n          return this.questService.getQuestProgressForDate(this.userId, date).pipe(take(1), map(progressList => {\n            // Count completed quests\n            const questIds = activeQuests.map(q => q.id);\n            const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\n            const completedQuests = relevantProgress.filter(p => p.completed).length;\n            const totalQuests = activeQuests.length;\n            // Create progress object\n            const progress = {\n              total: totalQuests,\n              completed: completedQuests\n            };\n            // Cache the progress\n            this.weekProgressCache[dateKey] = progress;\n            return {\n              date: weekDate.date,\n              progress\n            };\n          }));\n        });\n        // Process all date observables in parallel\n        forkJoin(dateObservables).subscribe(results => {\n          // Update the week dates with the progress\n          results.forEach(result => {\n            const index = this.weekDates.findIndex(wd => wd.date === result.date);\n            if (index >= 0) {\n              this.weekDates[index].total_quests = result.progress.total;\n              this.weekDates[index].completed_quests = result.progress.completed;\n              this.weekDates[index].completion_percentage = result.progress.total > 0 ? Math.round(result.progress.completed / result.progress.total * 100) : 0;\n            }\n          });\n        });\n      });\n    }\n    updateHeaderText() {\n      const today = new Date();\n      if (this.isSameDay(this.selectedDate, today)) {\n        this.headerText = 'Today';\n      } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() - 1)))) {\n        this.headerText = 'Yesterday';\n      } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() + 2)))) {\n        this.headerText = 'Tomorrow';\n      } else {\n        // Format as \"Mon, 15 Jan\"\n        this.headerText = this.selectedDate.toLocaleDateString('en-US', {\n          weekday: 'short',\n          day: 'numeric',\n          month: 'short'\n        });\n      }\n    }\n    toggleQuest(quest) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this3.userId || !quest.id) return;\n        // Check if this specific quest is already being toggled\n        if (_this3.togglingQuestIds[quest.id]) {\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is already being toggled, ignoring duplicate call`);\n          return;\n        }\n        // Set flag for this specific quest\n        _this3.togglingQuestIds[quest.id] = true;\n        console.log(`TodayPage: Starting toggle for quest ${quest.id} (${quest.name})`);\n        try {\n          // For normal quests, we don't want to toggle the value when clicking on the quest\n          // Instead, we want to keep the current value from the slider\n          // This is different from the original behavior where clicking would toggle between 0 and goal_value\n          // We'll just log that the quest was clicked but not change any values\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) clicked, keeping current value: ${quest.value_achieved}`);\n          // No need to update the database since we're not changing any values\n          // Just release the flag and return\n          delete _this3.togglingQuestIds[quest.id];\n          return;\n        } catch (error) {\n          console.error(`TodayPage: Error in toggleQuest for ${quest.id} (${quest.name}):`, error);\n        } finally {\n          // Reset flag for this specific quest\n          delete _this3.togglingQuestIds[quest.id];\n          console.log(`TodayPage: Finished toggle for quest ${quest.id} (${quest.name})`);\n        }\n      })();\n    }\n    updateQuestProgress(quest, event) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this4.userId || !quest.id) return;\n        // Check if this specific quest is already being updated\n        if (_this4.updatingQuestIds[quest.id]) {\n          return;\n        }\n        // Set flag for this specific quest\n        _this4.updatingQuestIds[quest.id] = true;\n        try {\n          // Store the original completed state before any changes\n          const wasCompletedBefore = quest.completed;\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) original completed state: ${wasCompletedBefore}`);\n          // Update the slider background if an event is provided\n          if (event) {\n            // Handle both standard Event and Ionic's CustomEvent\n            const slider = event.target || (event.detail ? event.detail.value : null);\n            _this4.updateSliderBackground(slider);\n            // Verify that the slider is for the correct quest\n            const sliderQuestId = slider instanceof HTMLElement ? slider.getAttribute('data-quest-id') : null;\n            if (sliderQuestId && sliderQuestId !== quest.id) {\n              delete _this4.updatingQuestIds[quest.id];\n              return;\n            }\n            // Get the value from the slider\n            let sliderValue = 0;\n            if (event.detail && event.detail.value !== undefined) {\n              // Ionic range event\n              sliderValue = event.detail.value;\n            } else if (slider instanceof HTMLInputElement) {\n              // Standard input event\n              sliderValue = parseInt(slider.value);\n            } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n              // Ionic range element\n              const valueAttr = slider.getAttribute('value') || '0';\n              sliderValue = parseInt(valueAttr);\n            }\n            // Update the quest's value_achieved with the slider value\n            quest.value_achieved = sliderValue;\n            // Update completed status based on quest type and value\n            // This exactly matches the Django implementation in toggle_quest view\n            if (quest.quest_type === 'build') {\n              // For build quests, completed when value >= goal\n              quest.completed = sliderValue >= quest.goal_value;\n            } else {\n              // 'quit' type\n              // For quit quests, completed when value < goal (opposite of build)\n              quest.completed = sliderValue < quest.goal_value;\n            }\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) new completed state: ${quest.completed}`);\n          }\n          // Make a deep copy of the quest to avoid reference issues\n          const questCopy = {\n            ...quest\n          };\n          // Call the service and get the updated values\n          const result = yield _this4.questService.toggleQuestCompletion(_this4.userId, quest.id, _this4.selectedDate, quest.value_achieved, questCopy);\n          // Update the quest in the UI with the returned values\n          quest.completed = result.completed;\n          quest.value_achieved = result.value_achieved;\n          // Get today's date for comparison\n          const today = new Date();\n          today.setHours(0, 0, 0, 0);\n          const selectedDate = new Date(_this4.selectedDate);\n          selectedDate.setHours(0, 0, 0, 0);\n          const isTodaySelected = selectedDate.getTime() === today.getTime();\n          // Handle streak calculation differently based on whether we're in today's view or a previous day\n          if (isTodaySelected) {\n            // For today's view, manually calculate the streak by going backward from today\n            // until we find a non-completed progress entry\n            // Use the streak from the result (from Supabase)\n            let streak = result.streak;\n            // Get the current completed state after the update\n            const isCompletedNow = quest.completed;\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status: was ${wasCompletedBefore}, now ${isCompletedNow}`);\n            // Only update streak if the completion status has changed\n            if (wasCompletedBefore !== isCompletedNow) {\n              if (isCompletedNow) {\n                // Changed from incomplete to complete\n                streak++;\n                console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from incomplete to complete, streak increased to ${streak}`);\n              } else {\n                // Changed from complete to incomplete\n                streak = Math.max(0, streak - 1);\n                console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from complete to incomplete, streak decreased to ${streak}`);\n              }\n              // Update the streak in the database\n              _this4.questService.updateQuestStreak(quest.id, streak).subscribe({\n                next: () => {\n                  console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${streak}`);\n                  // Update the quest in the cache\n                  const dateKey = _this4.formatDate(_this4.selectedDate);\n                  if (_this4.questCache[dateKey]) {\n                    const cachedQuestIndex = _this4.questCache[dateKey].findIndex(q => q.id === quest.id);\n                    if (cachedQuestIndex >= 0) {\n                      _this4.questCache[dateKey][cachedQuestIndex].streak = streak;\n                    }\n                  }\n                },\n                error: error => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\n              });\n            } else {\n              console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status did not change, keeping streak at ${streak}`);\n            }\n          } else {\n            // For previous days, recalculate streak for today\n            console.log(`TodayPage: Quest toggled in previous day (${_this4.formatDate(_this4.selectedDate)}), recalculating streak for today`);\n            // Get the quest details\n            _this4.questService.getQuest(quest.id).subscribe(questDetails => {\n              if (!questDetails) {\n                console.error(`TodayPage: Could not get quest details for ${quest.id}`);\n                return;\n              }\n              // Calculate the streak for today using our streak calculator\n              _this4.streakCalculator.calculateStreak(_this4.userId, quest.id).then(calculatedStreak => {\n                console.log(`TodayPage: Recalculated streak for quest ${quest.id} for today: ${calculatedStreak}`);\n                // Update the streak in the database\n                _this4.questService.updateQuestStreak(quest.id, calculatedStreak).subscribe({\n                  next: () => {\n                    console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${calculatedStreak}`);\n                    // Clear today's cache for next time\n                    const todayString = _this4.formatDate(today);\n                    console.log('TodayPage: Clearing today\\'s cache to force reload of updated streak next time today is viewed');\n                    delete _this4.questCache[todayString];\n                    // If we have today's date in the week view, update its progress\n                    const todayIndex = _this4.weekDates.findIndex(wd => wd.date === todayString);\n                    if (todayIndex >= 0) {\n                      delete _this4.weekProgressCache[todayString];\n                      _this4.updateProgressRingForDate(todayString);\n                    }\n                  },\n                  error: error => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\n                });\n              }).catch(error => {\n                console.error(`TodayPage: Error calculating streak for quest ${quest.id}:`, error);\n              });\n            });\n          }\n          // Update the UI element for this quest\n          _this4.updateQuestUI(quest);\n          // Cache the updated quest data and update progress ring\n          const dateKey = _this4.formatDate(_this4.selectedDate);\n          if (_this4.questCache[dateKey]) {\n            // Find and update the quest in the cache\n            const cachedQuestIndex = _this4.questCache[dateKey].findIndex(q => q.id === quest.id);\n            if (cachedQuestIndex >= 0) {\n              _this4.questCache[dateKey][cachedQuestIndex] = {\n                ...quest\n              };\n            }\n          }\n          // Clear the cache for this date to force a refresh\n          delete _this4.weekProgressCache[dateKey];\n          // Update the progress ring for this date\n          _this4.updateProgressRingForDate(dateKey);\n          // Check if all quests are completed\n          _this4.checkAllQuestsCompleted(_this4.quests);\n        } catch (error) {\n          console.error(`TodayPage: Error updating quest progress:`, error);\n        } finally {\n          // Reset flag for this specific quest\n          delete _this4.updatingQuestIds[quest.id];\n        }\n      })();\n    }\n    // Helper method to update the progress ring for a specific date\n    updateProgressRingForDate(dateKey) {\n      // Find the index of this date in weekDates\n      const index = this.weekDates.findIndex(wd => wd.date === dateKey);\n      if (index < 0) return;\n      // If we have cached quests for this date, use them to calculate progress\n      if (this.questCache[dateKey]) {\n        const cachedQuests = this.questCache[dateKey];\n        const totalQuests = cachedQuests.length;\n        const completedQuests = cachedQuests.filter(q => q.completed).length;\n        // Cache the progress\n        this.weekProgressCache[dateKey] = {\n          total: totalQuests,\n          completed: completedQuests\n        };\n        // Update the week date\n        this.weekDates[index].total_quests = totalQuests;\n        this.weekDates[index].completed_quests = completedQuests;\n        this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n        return;\n      }\n      // If no cached quests, fetch from server\n      if (this.userId) {\n        const date = new Date(dateKey);\n        this.questService.getQuestProgressForDate(this.userId, date).pipe(take(1)).subscribe(progressList => {\n          this.questService.getQuests(this.userId).pipe(take(1)).subscribe(quests => {\n            // Filter active quests for this date\n            const activeQuests = this.filterQuestsForDate(quests, date);\n            // Count completed quests\n            const questIds = activeQuests.map(q => q.id);\n            const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\n            const completedQuests = relevantProgress.filter(p => p.completed).length;\n            const totalQuests = activeQuests.length;\n            // Cache the progress\n            this.weekProgressCache[dateKey] = {\n              total: totalQuests,\n              completed: completedQuests\n            };\n            // Update the week date\n            this.weekDates[index].total_quests = totalQuests;\n            this.weekDates[index].completed_quests = completedQuests;\n            this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n          });\n        });\n      }\n    }\n    // Helper method to update the UI for a specific quest\n    updateQuestUI(quest) {\n      // Find the quest element in the DOM\n      const questElement = document.querySelector(`[data-quest-id=\"${quest.id}\"]`);\n      if (!questElement) {\n        console.error(`TodayPage: Could not find quest element for ID: ${quest.id}`);\n        return;\n      }\n      // Update the completed class\n      if (quest.completed) {\n        questElement.classList.add('completed');\n      } else {\n        questElement.classList.remove('completed');\n      }\n      // Update the streak display - only show streak for today\n      const streakElements = questElement.querySelectorAll('.quest-streak');\n      if (streakElements && streakElements.length > 0) {\n        // Get today's date for comparison\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = new Date(this.selectedDate);\n        selectedDate.setHours(0, 0, 0, 0);\n        const isTodaySelected = selectedDate.getTime() === today.getTime();\n        // Only show streak for today's view\n        if (isTodaySelected) {\n          const streakValue = quest.streak || 0;\n          console.log(`TodayPage: Quest ${quest.id}, completed: ${quest.completed}, streak: ${streakValue}`);\n          // Update all streak elements (there might be multiple due to ngIf)\n          streakElements.forEach(element => {\n            if (element.parentElement && element.parentElement.contains(element)) {\n              // Make sure the streak is visible\n              element.style.display = 'block';\n              element.textContent = `🔥${streakValue}d`;\n            }\n          });\n        } else {\n          // Hide streak for previous days\n          streakElements.forEach(element => {\n            if (element.parentElement && element.parentElement.contains(element)) {\n              element.style.display = 'none';\n              element.textContent = '';\n            }\n          });\n        }\n      }\n      // Update the progress text\n      const progressText = questElement.querySelector('.progress-text');\n      if (progressText) {\n        var _progressText$parentE;\n        const isTimeUnit = (_progressText$parentE = progressText.parentElement) === null || _progressText$parentE === void 0 ? void 0 : _progressText$parentE.classList.contains('progress-time');\n        const unitSuffix = isTimeUnit ? 'm' : '';\n        const goalUnitSuffix = quest.goal_unit !== 'count' && !isTimeUnit ? ` ${quest.goal_unit}` : '';\n        progressText.textContent = `${quest.value_achieved}${unitSuffix}/${quest.goal_value}${unitSuffix}${goalUnitSuffix}`;\n      }\n      console.log(`TodayPage: Updated UI for quest ${quest.id}`);\n    }\n    // Update slider background based on value\n    updateSliderBackground(slider) {\n      if (!slider) {\n        return;\n      }\n      // Handle different types of slider elements\n      let sliderElement;\n      let sliderValue = 0;\n      let minValue = 0;\n      let maxValue = 100;\n      let sliderQuestId = '';\n      if (slider instanceof HTMLInputElement) {\n        // Standard HTML input range\n        sliderElement = slider;\n        sliderQuestId = slider.getAttribute('data-quest-id') || '';\n        sliderValue = parseInt(slider.value);\n        minValue = parseInt(slider.min);\n        maxValue = parseInt(slider.max);\n      } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n        // Ionic range element\n        sliderElement = slider;\n        sliderQuestId = slider.getAttribute('data-quest-id') || '';\n        // Get the value from the element's properties or attributes\n        const valueAttr = slider.getAttribute('value') || '0';\n        const minAttr = slider.getAttribute('min') || '0';\n        const maxAttr = slider.getAttribute('max') || '100';\n        sliderValue = parseInt(valueAttr);\n        minValue = parseInt(minAttr);\n        maxValue = parseInt(maxAttr);\n      } else {\n        return;\n      }\n      if (!sliderQuestId) {\n        return;\n      }\n      // Calculate the percentage value\n      const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n      // For Ionic range, we need to set the CSS variable\n      if (sliderElement.tagName === 'ION-RANGE') {\n        sliderElement.style.setProperty('--progress-value', `${percentage}%`);\n      } else {\n        // Set the background directly with hardcoded colors for standard HTML input\n        sliderElement.style.background = `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\n      }\n      // Add a data attribute to track the current value\n      sliderElement.setAttribute('data-current-value', sliderValue.toString());\n    }\n    /**\n     * Toggle side quest completion\n     * This matches the Django implementation in toggle_daily_side_quest view\n     * Side quests are always toggled between 0 and goal value\n     */\n    toggleSideQuest(sideQuest) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this5.userId || !sideQuest.id) return;\n        // Check if this specific side quest is already being toggled\n        if (_this5.togglingSideQuestIds[sideQuest.id]) {\n          console.log(`TodayPage: Side quest ${sideQuest.id} is already being toggled, ignoring duplicate call`);\n          return;\n        }\n        // Set flag for this specific side quest\n        _this5.togglingSideQuestIds[sideQuest.id] = true;\n        console.log(`TodayPage: Starting toggle for side quest ${sideQuest.id}`);\n        try {\n          // For side quests, we always toggle between 0 and goal value\n          // This matches the Django implementation where side quests are either completed or not\n          console.log(`TodayPage: Click event on side quest ${sideQuest.id}`);\n          // Toggle the value immediately for better UI feedback\n          const newValue = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\n          const newCompletedState = newValue === sideQuest.current_quest.goal_value;\n          // Update local state first for immediate feedback\n          sideQuest.value_achieved = newValue;\n          sideQuest.completed = newCompletedState;\n          console.log(`TodayPage: Updated side quest ${sideQuest.id} value to ${sideQuest.value_achieved}, completed: ${sideQuest.completed}`);\n          // Get today's date for comparison\n          const today = new Date();\n          today.setHours(0, 0, 0, 0);\n          const selectedDate = new Date(_this5.selectedDate);\n          selectedDate.setHours(0, 0, 0, 0);\n          const isToday = selectedDate.getTime() === today.getTime();\n          // Only allow toggling side quests for today\n          if (!isToday) {\n            console.log(`TodayPage: Cannot toggle side quest for past date: ${_this5.formatDate(_this5.selectedDate)}`);\n            delete _this5.togglingSideQuestIds[sideQuest.id];\n            return;\n          }\n          // Update the UI element immediately for better feedback\n          _this5.updateSideQuestUI(sideQuest);\n          try {\n            const result = yield _this5.sideQuestService.toggleSideQuestCompletion(sideQuest.id, _this5.userId, _this5.selectedDate // Pass the selected date\n            );\n            console.log(`TodayPage: Successfully toggled side quest ${sideQuest.id}`);\n            console.log(`TodayPage: Updated values:`, result);\n            // Update the side quest in the UI with the returned values from the server\n            sideQuest.completed = result.completed;\n            sideQuest.value_achieved = result.value_achieved;\n            sideQuest.streak = result.streak;\n            // Update the UI element with the updated streak\n            _this5.updateSideQuestUI(sideQuest);\n            // Update the week date progress for the selected date\n            // Clear the cache for this date to force a refresh\n            const dateKey = _this5.formatDate(_this5.selectedDate);\n            delete _this5.weekProgressCache[dateKey];\n            // Update the progress ring for this date\n            _this5.updateProgressRingForDate(dateKey);\n            // Check if all quests are completed\n            _this5.checkAllQuestsCompleted(_this5.quests);\n            // Reset flag for this specific side quest\n            delete _this5.togglingSideQuestIds[sideQuest.id];\n            console.log(`TodayPage: Finished toggle for side quest ${sideQuest.id}`);\n          } catch (error) {\n            console.error(`TodayPage: Error toggling side quest ${sideQuest.id}:`, error);\n            // Revert the local state if the server update failed\n            sideQuest.value_achieved = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\n            sideQuest.completed = sideQuest.value_achieved === sideQuest.current_quest.goal_value;\n            _this5.updateSideQuestUI(sideQuest);\n            delete _this5.togglingSideQuestIds[sideQuest.id];\n          }\n        } catch (error) {\n          console.error(`TodayPage: Error in toggleSideQuest for ${sideQuest.id}:`, error);\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n        }\n      })();\n    }\n    // Helper method to update the UI for a specific side quest\n    updateSideQuestUI(sideQuest) {\n      // Find the side quest element in the DOM\n      const questElement = document.querySelector(`.daily-side-quest [data-quest-id=\"${sideQuest.id}\"]`);\n      if (!questElement) {\n        console.error(`TodayPage: Could not find side quest element for ID: ${sideQuest.id}`);\n        return;\n      }\n      // Update the completed class\n      if (sideQuest.completed) {\n        questElement.classList.add('completed');\n      } else {\n        questElement.classList.remove('completed');\n      }\n      // Update the streak display - only show streak for today\n      const streakElements = questElement.querySelectorAll('.quest-streak');\n      if (streakElements && streakElements.length > 0) {\n        // Get today's date for comparison\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = new Date(this.selectedDate);\n        selectedDate.setHours(0, 0, 0, 0);\n        const isTodaySelected = selectedDate.getTime() === today.getTime();\n        // Only show streak for today's view\n        if (isTodaySelected) {\n          const streakValue = sideQuest.streak || 0;\n          console.log(`TodayPage: Side quest ${sideQuest.id}, completed: ${sideQuest.completed}, streak: ${streakValue}`);\n          // Update all streak elements (there might be multiple due to ngIf)\n          streakElements.forEach(element => {\n            if (element.parentElement && element.parentElement.contains(element)) {\n              // Make sure the streak is visible\n              element.style.display = 'block';\n              element.textContent = `🔥${streakValue}d`;\n            }\n          });\n        } else {\n          // Hide streak for previous days\n          streakElements.forEach(element => {\n            if (element.parentElement && element.parentElement.contains(element)) {\n              element.style.display = 'none';\n              element.textContent = '';\n            }\n          });\n        }\n      }\n      // Update the progress text\n      const progressText = questElement.querySelector('.progress-text');\n      if (progressText) {\n        const goalUnit = sideQuest.current_quest.goal_unit !== 'count' ? ` ${sideQuest.current_quest.goal_unit}` : '';\n        progressText.textContent = `${sideQuest.value_achieved}/${sideQuest.current_quest.goal_value}${goalUnit}`;\n      }\n      // Force a repaint to ensure the UI updates\n      setTimeout(() => {\n        if (questElement.parentElement) {\n          const display = questElement.parentElement.style.display;\n          questElement.parentElement.style.display = 'none';\n          // Force a reflow\n          void questElement.parentElement.offsetHeight;\n          questElement.parentElement.style.display = display;\n        }\n      }, 0);\n      console.log(`TodayPage: Updated UI for side quest ${sideQuest.id}`);\n    }\n    openAddQuestModal(event) {\n      event.preventDefault();\n      this.showAddQuestModal = true;\n      this.newQuest = this.getEmptyQuest();\n      this.selectedDaysOfWeek = [];\n      this.selectedDaysOfMonth = [];\n      // Reset hasHighPriorityQuest flag\n      this.hasHighPriorityQuest = false;\n      // Reset animation states\n      this.resetAnimationStates();\n      // Start quest type animation after modal opens\n      setTimeout(() => {\n        console.log('Setting questTypeAnimated to true');\n        this.questTypeAnimated = true;\n      }, 300);\n    }\n    resetAnimationStates() {\n      this.questTypeAnimated = false;\n      this.questTypeAnimating = false;\n      this.selectedQuestType = '';\n      this.categoryAnimated = false;\n      this.categoryAnimating = false;\n      this.categorySelected = false;\n      this.selectedCategory = '';\n      this.priorityAnimated = false;\n      this.priorityAnimating = false;\n      this.goalAnimated = false;\n      this.questDetailsAnimated = false;\n      this.frequencyAnimated = false;\n      this.frequencyOptionsAnimated = false;\n      this.previewAnimated = false;\n    }\n    closeAddQuestModal() {\n      this.showAddQuestModal = false;\n      // Reset form state\n      this.newQuest = this.getEmptyQuest();\n      this.selectedDaysOfWeek = [];\n      this.selectedDaysOfMonth = [];\n      this.hasHighPriorityQuest = false;\n      this.resetAnimationStates();\n    }\n    // Animation methods\n    selectQuestType(type) {\n      this.selectedQuestType = type;\n      this.questTypeAnimating = true;\n      // Start slide out animation\n      setTimeout(() => {\n        this.newQuest.quest_type = type;\n        // After quest type is set, trigger category animation\n        setTimeout(() => {\n          this.categoryAnimated = true;\n        }, 100);\n      }, 300); // Half of the animation duration\n    }\n    selectCategory(category) {\n      this.selectedCategory = category;\n      this.categoryAnimating = true;\n      // Start slide out animation based on category\n      setTimeout(() => {\n        this.newQuest.category = category;\n        this.categorySelected = true;\n        this.checkCategoryPriority({\n          detail: {\n            value: category\n          }\n        });\n        // After category is set, trigger priority animation\n        setTimeout(() => {\n          this.priorityAnimated = true;\n        }, 100);\n      }, 300); // Half of the animation duration\n    }\n    selectPriority(priority) {\n      if (priority === 'high' && this.hasHighPriorityQuest) {\n        return; // Don't allow high priority if already exists\n      }\n      this.priorityAnimating = true;\n      setTimeout(() => {\n        this.newQuest.priority = priority;\n      }, 300);\n    }\n    nextStep() {\n      if (this.currentStep < this.totalSteps) {\n        this.currentStep++;\n        // If moving to step 5, trigger sequential animations\n        if (this.currentStep === 5) {\n          setTimeout(() => {\n            this.questDetailsAnimated = true;\n          }, 100);\n          setTimeout(() => {\n            this.goalAnimated = true;\n          }, 300);\n          setTimeout(() => {\n            this.frequencyAnimated = true;\n          }, 500);\n          setTimeout(() => {\n            this.previewAnimated = true;\n          }, 700);\n        }\n      }\n    }\n    prevStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    }\n    moveCaretToEnd() {\n      setTimeout(() => {\n        this.emojiInput.getInputElement().then(input => {\n          const pos = input.value.length;\n          input.setSelectionRange(pos, pos);\n          input.scrollLeft = input.scrollWidth;\n        });\n      }, 100);\n    }\n    get progress() {\n      return this.currentStep / this.totalSteps;\n    }\n    createQuest() {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this6.userId || !_this6.newQuest.name || !_this6.newQuest.emoji || !_this6.newQuest.quest_type || !_this6.newQuest.category || !_this6.newQuest.goal_value || !_this6.newQuest.goal_unit || !_this6.newQuest.goal_period) {\n          console.error('TodayPage: Cannot create quest - missing required fields');\n          return;\n        }\n        try {\n          if (_this6.newQuest.goal_period === 'week' && _this6.selectedDaysOfWeek.length > 0) {\n            _this6.newQuest.task_days_of_week = _this6.selectedDaysOfWeek.join(',');\n          } else if (_this6.newQuest.goal_period === 'month' && _this6.selectedDaysOfMonth.length > 0) {\n            _this6.newQuest.task_days_of_month = _this6.selectedDaysOfMonth.join(',');\n          }\n          const {\n            data: userProfile,\n            error: userError\n          } = yield _this6.supabaseService.getClient().from('profiles').select('id').eq('id', _this6.userId).single();\n          if (userError || !userProfile) {\n            console.error('TodayPage: User profile not found:', userError || 'No profile found');\n            throw new Error('User profile not found. Please ensure you are logged in.');\n          }\n          const questToCreate = {\n            name: _this6.newQuest.name || '',\n            description: _this6.newQuest.description || '',\n            quest_type: _this6.newQuest.quest_type || 'build',\n            goal_value: _this6.newQuest.goal_value || 1,\n            goal_unit: _this6.newQuest.goal_unit || 'count',\n            goal_period: _this6.newQuest.goal_period || 'day',\n            priority: _this6.newQuest.priority || 'basic',\n            category: _this6.newQuest.category || 'strength',\n            emoji: _this6.newQuest.emoji || '🎯',\n            task_days_of_week: _this6.newQuest.task_days_of_week || '',\n            task_days_of_month: _this6.newQuest.task_days_of_month || '',\n            user_id: _this6.userId,\n            active: true\n          };\n          try {\n            const questId = yield _this6.questService.createQuest(questToCreate);\n            if (_this6.newQuest.quest_type === 'quit') {\n              yield _this6.questService.toggleQuestCompletion(_this6.userId, questId, new Date(), 0, {\n                ...questToCreate,\n                id: questId\n              });\n            }\n            const dateKey = _this6.formatDate(_this6.selectedDate);\n            delete _this6.questCache[dateKey];\n            delete _this6.weekProgressCache[dateKey];\n            _this6.closeAddQuestModal();\n            _this6.loadData();\n          } catch (questError) {\n            console.error('TodayPage: Error creating quest:', questError);\n            if (questError.message && questError.message.includes('foreign key constraint')) {\n              alert('Database configuration issue detected. Please run the fix_quest_constraints.sql script in the Supabase SQL Editor to fix the foreign key constraints.');\n            } else if (questError.message && questError.message.includes('fix_quest_constraints.sql')) {\n              alert(questError.message);\n            } else {\n              alert(`Error creating quest: ${questError.message}`);\n            }\n          }\n        } catch (error) {\n          console.error('TodayPage: Error in createQuest:', error);\n          alert(`Error: ${error.message || 'Unknown error occurred'}`);\n        }\n      })();\n    }\n    updateDaysOfWeek(day) {\n      const index = this.selectedDaysOfWeek.indexOf(day);\n      if (index !== -1) {\n        this.selectedDaysOfWeek.splice(index, 1);\n      } else {\n        this.selectedDaysOfWeek.push(day);\n      }\n    }\n    updateDaysOfMonth(event, day) {\n      // Handle both standard Event and Ionic's CustomEvent\n      let isChecked = false;\n      if (event.detail !== undefined) {\n        // Ionic checkbox event\n        isChecked = event.detail.checked;\n      } else if (event.target instanceof HTMLInputElement) {\n        // Standard checkbox event\n        isChecked = event.target.checked;\n      }\n      if (isChecked) {\n        this.selectedDaysOfMonth.push(day);\n      } else {\n        const index = this.selectedDaysOfMonth.indexOf(day);\n        if (index !== -1) {\n          this.selectedDaysOfMonth.splice(index, 1);\n        }\n      }\n      console.log(`TodayPage: Updated days of month: ${this.selectedDaysOfMonth.join(', ')}`);\n    }\n    updatePeriodDisplay() {\n      // Reset selections when period changes\n      this.selectedDaysOfWeek = [];\n      this.selectedDaysOfMonth = [];\n      console.log(`TodayPage: Period changed to ${this.newQuest.goal_period}, reset selections`);\n      // Trigger frequency options animation\n      setTimeout(() => {\n        this.frequencyOptionsAnimated = true;\n      }, 200);\n    }\n    toggleMonthDay(day) {\n      const index = this.selectedDaysOfMonth.indexOf(day);\n      if (index !== -1) {\n        this.selectedDaysOfMonth.splice(index, 1);\n      } else {\n        this.selectedDaysOfMonth.push(day);\n      }\n    }\n    getCategoryIcon(category) {\n      const icons = {\n        'strength': '💪',\n        'money': '💰',\n        'health': '🏥',\n        'knowledge': '🧠'\n      };\n      return icons[category] || '📌';\n    }\n    selectFrequency(period) {\n      this.newQuest.goal_period = period;\n      this.updatePeriodDisplay();\n    }\n    getFrequencyText() {\n      if (this.newQuest.goal_period === 'day') {\n        return 'daily';\n      } else if (this.newQuest.goal_period === 'week') {\n        if (this.selectedDaysOfWeek.length === 0) {\n          return 'weekly';\n        } else if (this.selectedDaysOfWeek.length === 7) {\n          return 'daily';\n        } else {\n          return `${this.selectedDaysOfWeek.length}x per week`;\n        }\n      } else if (this.newQuest.goal_period === 'month') {\n        if (this.selectedDaysOfMonth.length === 0) {\n          return 'monthly';\n        } else {\n          return `${this.selectedDaysOfMonth.length}x per month`;\n        }\n      }\n      return '';\n    }\n    checkCategoryPriority(event) {\n      if (!this.userId || !this.newQuest.category) return;\n      // If this is an Ionic event, make sure we have the latest category value\n      if (event && event.detail) {\n        this.newQuest.category = event.detail.value;\n        console.log(`TodayPage: Category changed to ${this.newQuest.category} via Ionic event`);\n      }\n      // Check if user already has a high priority quest in this category\n      this.questService.getQuests(this.userId).pipe(take(1), map(quests => {\n        return quests.some(q => q.category === this.newQuest.category && q.priority === 'high' && q.active);\n      })).subscribe({\n        next: hasHighPriority => {\n          this.hasHighPriorityQuest = hasHighPriority;\n          // If user already has a high priority quest, set this one to basic\n          if (hasHighPriority) {\n            this.newQuest.priority = 'basic';\n          }\n          console.log(`TodayPage: Category ${this.newQuest.category} has high priority quest: ${hasHighPriority}`);\n        }\n      });\n    }\n    /**\n     * Check if all quests are completed for today and show celebration if enabled\n     */\n    checkAllQuestsCompleted(quests) {\n      // Only check for today's date\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(this.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      const todayStr = this.formatDate(today);\n      if (!isTodaySelected || !this.currentUser) {\n        return;\n      }\n      // Check if celebration has already been shown for today\n      const celebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\n      if (celebrationShown) {\n        console.log('TodayPage: Celebration already shown for today:', todayStr);\n        return;\n      }\n      // Check if all quests are completed\n      const allQuestsCompleted = quests.length > 0 && quests.every(quest => quest.completed);\n      // Check if side quest is completed (if enabled)\n      const sideQuestCompleted = !this.showSidequests || !this.dailyQuest || this.dailyQuest.completed;\n      // Show celebration if all quests and side quests are completed and celebration is enabled\n      if (allQuestsCompleted && sideQuestCompleted && this.currentUser.show_celebration) {\n        // Make sure we have the latest user data\n        this.userService.getUserById(this.userId).subscribe(userData => {\n          if (userData) {\n            this.currentUser = userData;\n          }\n          // Show the celebration\n          this.showCelebration = true;\n          // Save today's date to localStorage\n          localStorage.setItem(`celebration_shown_${todayStr}`, 'true');\n          // Update our tracking array\n          if (!this.celebrationShownDates.includes(todayStr)) {\n            this.celebrationShownDates.push(todayStr);\n          }\n        });\n      }\n    }\n    /**\n     * Close the celebration modal\n     */\n    closeCelebration() {\n      this.showCelebration = false;\n    }\n    // Helper methods\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    }\n    isSameDay(date1, date2) {\n      return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n    }\n    getToday() {\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      return today;\n    }\n    // Convert Django day index (0=Monday, 6=Sunday) to short day name\n    getDayNameShort(djangoDayIndex) {\n      // Map Django day index to day name\n      const dayMap = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];\n      return dayMap[djangoDayIndex];\n    }\n    // Convert Django day index (0=Monday, 6=Sunday) to full day name\n    getDayNameFull(djangoDayIndex) {\n      // Map Django day index to full day name\n      const dayMap = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n      return dayMap[djangoDayIndex];\n    }\n    getEmptyQuest() {\n      return {\n        name: '',\n        description: '',\n        quest_type: '',\n        // Empty by default, user must select\n        goal_value: 1,\n        goal_unit: 'count',\n        goal_period: 'day',\n        priority: 'basic',\n        // Default to basic priority\n        category: '',\n        emoji: '🎯'\n      };\n    }\n  }\n  _TodayPage = TodayPage;\n  _TodayPage.ɵfac = function TodayPage_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _TodayPage)();\n  };\n  _TodayPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: _TodayPage,\n    selectors: [[\"app-today\"]],\n    viewQuery: function TodayPage_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.emojiInput = _t.first);\n      }\n    },\n    decls: 35,\n    vars: 10,\n    consts: [[\"questForm\", \"ngForm\"], [1, \"ion-padding\", 3, \"fullscreen\"], [1, \"background-container\"], [1, \"gradient-bg\"], [1, \"celestial-body\"], [1, \"ion-no-border\"], [3, \"headerText\"], [1, \"week-row\", \"ion-padding-top\"], [\"class\", \"day-container\", 4, \"ngFor\", \"ngForOf\"], [1, \"ion-justify-content-center\"], [1, \"heartbeat-circle\", \"gradient-text\"], [1, \"add-quest\"], [\"fill\", \"clear\", \"id\", \"add-quest-btn\", 1, \"add-quest-btn\", 3, \"click\"], [1, \"quests\"], [\"class\", \"ion-text-center no-quest-card\", 4, \"ngif\"], [\"class\", \"quest-item ion-margin-bottom\", 3, \"completed\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"ion-padding-top\"], [\"class\", \"quest-item\", 3, \"completed\", \"click\", 4, \"ngIf\"], [\"class\", \"quest-item\", 4, \"ngIf\"], [1, \"add-quest-modal\", 3, \"ionModalDidDismiss\", \"isOpen\", \"backdropDismiss\"], [3, \"user\", \"date\", \"close\", 4, \"ngIf\"], [1, \"day-container\"], [1, \"day-name\"], [1, \"date\", 3, \"click\"], [\"viewBox\", \"0 0 36 36\", 1, \"date-progress\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"13\", \"stroke-dasharray\", \"81.68, 81.68\", 1, \"background-circle\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"13\", \"class\", \"progress-circle\", 3, \"low\", 4, \"ngIf\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"13\", 1, \"progress-circle\"], [1, \"ion-text-center\", \"no-quest-card\"], [\"size\", \"8\"], [\"size\", \"4\"], [\"name\", \"warning\"], [1, \"quest-item\", \"ion-margin-bottom\", 3, \"click\"], [\"size\", \"2\"], [1, \"quest-icon\"], [\"size\", \"8\", 1, \"quest-info\"], [1, \"progress-container\"], [\"class\", \"progress-time\", 4, \"ngIf\"], [\"class\", \"progress\", 4, \"ngIf\"], [\"class\", \"quest-streak\", 4, \"ngIf\"], [1, \"progress-time\"], [\"min\", \"0\", \"snaps\", \"true\", \"ticks\", \"false\", \"snaps-per-step\", \"true\", 1, \"progress-slider\", 3, \"ngModelChange\", \"ionChange\", \"ionInput\", \"max\", \"ngModel\", \"step\"], [1, \"progress-text\"], [1, \"progress\"], [4, \"ngIf\"], [1, \"quest-streak\"], [1, \"quest-item\", 3, \"click\"], [1, \"quest-description\"], [1, \"quest-item\"], [1, \"ion-padding\"], [1, \"modal-header\", \"ion-padding-top\", \"ion-text-center\"], [\"color\", \"success\", 3, \"value\"], [\"name\", \"close\", 3, \"click\"], [3, \"ngSubmit\"], [\"class\", \"choose-quest\", 4, \"ngIf\"], [\"class\", \"first-step\", 4, \"ngIf\"], [\"class\", \"step-three-container\", 4, \"ngIf\"], [\"class\", \"step-four-container\", 4, \"ngIf\"], [\"class\", \"step-five-container\", 4, \"ngIf\"], [1, \"ion-padding\", \"create-quest-row\"], [1, \"choose-quest\"], [\"size\", \"6\"], [1, \"ion-padding\", \"ion-no-margin\", \"ion-text-center\"], [\"name\", \"earth-outline\", 1, \"ion-margin-bottom\"], [\"name\", \"create-outline\", 1, \"ion-margin-bottom\"], [1, \"first-step\"], [1, \"ion-margin-top\"], [1, \"gradient-text\"], [1, \"dark-text\"], [1, \"preview-emoji\"], [\"type\", \"text\", \"id\", \"emoji\", \"name\", \"emoji\", \"value\", \"\\uD83C\\uDFAF\", \"appEmojiInput\", \"\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"emoji-row\", \"ion-margin-top\", \"ion-margin-bottom\"], [\"size\", \"2\", \"class\", \"ion-justify-content-center ion-align-items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"social-button\"], [\"size\", \"2\", 1, \"ion-justify-content-center\", \"ion-align-items-center\"], [1, \"step-three-container\"], [1, \"step-header\", \"ion-margin-top\"], [1, \"ion-text-center\"], [1, \"floating-emoji\", \"ion-margin-bottom\"], [1, \"input-section\"], [1, \"floating-input-container\"], [1, \"input-icon\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \" \", \"required\", \"\", 1, \"dark-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"name\", 1, \"floating-label\"], [1, \"input-border\"], [1, \"floating-textarea-container\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \" \", \"rows\", \"3\", \"maxlength\", \"150\", 1, \"dark-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"description\", 1, \"floating-label\"], [1, \"character-counter\"], [1, \"step-four-container\"], [1, \"step-header\"], [1, \"step-icon-container\"], [1, \"floating-emoji\"], [1, \"step-title\"], [1, \"step-subtitle\"], [\"class\", \"quest-type-section\", 3, \"slide-in-from-right\", \"slide-out-left\", 4, \"ngIf\"], [\"class\", \"category-section\", 3, \"slide-in-from-right\", \"slide-out-left\", 4, \"ngIf\"], [\"class\", \"priority-section\", 3, \"slide-in-from-right\", \"slide-out-left\", 4, \"ngIf\"], [1, \"quest-type-section\"], [1, \"section-label\"], [\"name\", \"trending-up-outline\"], [1, \"toggle-container\"], [1, \"toggle-option\", 3, \"click\"], [1, \"toggle-icon\"], [1, \"toggle-text\"], [1, \"category-section\"], [\"name\", \"grid-outline\"], [1, \"category-grid\"], [1, \"category-card\", 3, \"click\"], [1, \"category-icon\"], [1, \"priority-section\"], [\"name\", \"flag-outline\"], [1, \"priority-container\"], [1, \"priority-option\", 3, \"click\"], [1, \"priority-indicator\", \"basic\"], [1, \"priority-indicator\", \"high\"], [\"class\", \"priority-warning\", 4, \"ngIf\"], [1, \"priority-warning\"], [\"name\", \"warning-outline\"], [1, \"step-five-container\"], [1, \"goal-section\"], [\"name\", \"target-outline\"], [1, \"goal-card\"], [1, \"goal-header\"], [1, \"goal-icon\"], [1, \"goal-title\"], [1, \"goal-inputs\"], [1, \"floating-input-container\", \"goal-value-container\"], [\"type\", \"number\", \"id\", \"goal_value\", \"name\", \"goal_value\", \"placeholder\", \" \", \"min\", \"1\", \"required\", \"\", 1, \"dark-input\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"goal_value\", 1, \"floating-label\"], [1, \"floating-select-container\"], [\"id\", \"goal_unit\", \"name\", \"goal_unit\", \"interface\", \"popover\", \"required\", \"\", 1, \"dark-input\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"count\"], [\"value\", \"steps\"], [\"value\", \"m\"], [\"value\", \"km\"], [\"value\", \"sec\"], [\"value\", \"min\"], [\"value\", \"hr\"], [\"value\", \"Cal\"], [\"value\", \"g\"], [\"value\", \"mg\"], [\"value\", \"l\"], [\"value\", \"drink\"], [\"value\", \"pages\"], [\"value\", \"books\"], [\"value\", \"%\"], [\"value\", \"\\u20AC\"], [\"value\", \"$\"], [\"value\", \"\\u00A3\"], [1, \"floating-label\"], [1, \"goal-preview\"], [1, \"preview-label\"], [1, \"preview-value\"], [1, \"frequency-section\"], [\"name\", \"calendar-outline\"], [1, \"frequency-card\"], [1, \"frequency-header\"], [1, \"frequency-icon\"], [1, \"frequency-title\"], [1, \"frequency-options-container\"], [1, \"frequency-option\", 3, \"click\"], [1, \"option-icon\"], [1, \"option-content\"], [1, \"frequency-details\"], [\"class\", \"week-selector\", 4, \"ngIf\"], [\"class\", \"month-selector\", 4, \"ngIf\"], [1, \"preview-section\"], [\"name\", \"eye-outline\"], [1, \"quest-preview-card\"], [1, \"preview-floating-emoji\"], [1, \"preview-content\"], [1, \"preview-title-section\"], [1, \"quest-preview-title\"], [1, \"quest-preview-description\"], [1, \"preview-stats\"], [1, \"stat-item\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-label\"], [1, \"stat-value\"], [1, \"preview-goal-section\"], [1, \"goal-badge\"], [1, \"goal-number\"], [1, \"goal-text\"], [1, \"preview-progress-section\"], [1, \"progress-header\"], [1, \"progress-label\"], [1, \"progress-percentage\"], [1, \"progress-track\"], [1, \"progress-fill\"], [1, \"progress-footer\"], [1, \"week-selector\"], [1, \"week-days-grid\"], [\"class\", \"day-chip\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"day-chip\", 3, \"click\"], [1, \"day-short\"], [1, \"day-full\"], [1, \"month-selector\"], [1, \"month-days-grid\"], [\"class\", \"month-day-chip\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"month-day-chip\", 3, \"click\"], [1, \"social-button\", 3, \"click\"], [1, \"blue-button\", 3, \"click\"], [\"type\", \"submit\", 1, \"blue-button\", \"create-quest\"], [3, \"close\", \"user\", \"date\"]],\n    template: function TodayPage_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"ion-content\", 1)(1, \"div\", 2);\n        i0.ɵɵelement(2, \"div\", 3)(3, \"div\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"ion-header\", 5)(5, \"ion-toolbar\");\n        i0.ɵɵelement(6, \"app-header\", 6);\n        i0.ɵɵelementStart(7, \"ion-row\", 7);\n        i0.ɵɵtemplate(8, TodayPage_div_8_Template, 7, 13, \"div\", 8);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"ion-grid\")(10, \"ion-row\", 9);\n        i0.ɵɵelement(11, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"ion-row\", 11)(13, \"ion-col\")(14, \"h2\");\n        i0.ɵɵtext(15, \"Quests\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"ion-col\")(17, \"ion-button\", 12);\n        i0.ɵɵlistener(\"click\", function TodayPage_Template_ion_button_click_17_listener($event) {\n          return ctx.openAddQuestModal($event);\n        });\n        i0.ɵɵtext(18, \" + Add Quest \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(19, \"ion-row\", 13)(20, \"ion-col\");\n        i0.ɵɵtemplate(21, TodayPage_ion_card_21_Template, 11, 0, \"ion-card\", 14)(22, TodayPage_ion_card_22_Template, 15, 10, \"ion-card\", 15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"ion-row\", 16)(24, \"ion-col\")(25, \"h2\");\n        i0.ɵɵtext(26, \"Daily Side Quest\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(27, \"ion-row\", 13)(28, \"ion-col\");\n        i0.ɵɵtemplate(29, TodayPage_ion_card_29_Template, 12, 8, \"ion-card\", 17)(30, TodayPage_ion_card_30_Template, 7, 0, \"ion-card\", 18);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(31, \"ion-modal\", 19);\n        i0.ɵɵlistener(\"ionModalDidDismiss\", function TodayPage_Template_ion_modal_ionModalDidDismiss_31_listener() {\n          return ctx.closeAddQuestModal();\n        });\n        i0.ɵɵtemplate(32, TodayPage_ng_template_32_Template, 20, 9, \"ng-template\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(33, TodayPage_app_celebration_33_Template, 1, 2, \"app-celebration\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(34, \"app-navigation\");\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"fullscreen\", true);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"headerText\", ctx.headerText);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.weekDates);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngif\", ctx.quests.length === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.quests);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.dailyQuest && ctx.dailyQuest.current_quest);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.dailyQuest);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"isOpen\", true)(\"backdropDismiss\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showCelebration);\n      }\n    },\n    dependencies: [IonicModule, i1.IonButton, i1.IonCard, i1.IonCardContent, i1.IonCardHeader, i1.IonCardTitle, i1.IonCol, i1.IonContent, i1.IonGrid, i1.IonHeader, i1.IonIcon, i1.IonInput, i1.IonProgressBar, i1.IonRange, i1.IonRow, i1.IonSelect, i1.IonSelectOption, i1.IonText, i1.IonTextarea, i1.IonToolbar, i1.IonModal, i1.NumericValueAccessor, i1.SelectValueAccessor, i1.TextValueAccessor, i1.IonMinValidator, CommonModule, i2.NgForOf, i2.NgIf, i2.TitleCasePipe, FormsModule, i3.ɵNgNoValidate, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, NavigationComponent, CelebrationComponent, EmojiInputDirective, HeaderComponent, AuroraComponent],\n    styles: [\"ion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{width:40px;height:40px;display:flex;align-items:center;justify-content:center;border-radius:50%;margin:0 auto;position:relative}ion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;border-radius:50%;pointer-events:none;z-index:0;transform:rotate(-90deg)}ion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%]{fill:transparent;stroke-width:5;stroke-linecap:round;transform-origin:center;transition:stroke-dasharray .5s ease}ion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .background-circle[_ngcontent-%COMP%]{stroke:#ffffff1a;stroke-width:5}ion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%]{stroke:#4169e1;stroke-opacity:1;stroke-width:5}ion-content[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle.low[_ngcontent-%COMP%]{stroke:#ff9500!important}ion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]{display:flex;justify-content:space-around}ion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;width:40px}ion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .day-name[_ngcontent-%COMP%]{padding:5px;font-size:12px;color:#fff;margin-bottom:8px;font-weight:lighter;width:22px;height:22px;border-radius:50%;display:flex;justify-content:center;align-items:center}ion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .day-name.active[_ngcontent-%COMP%]{background-color:var(--accent)}ion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .day-name.selected[_ngcontent-%COMP%]{background-color:var(--text-muted)}ion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%]{stroke:#4169e1;stroke-opacity:.9}ion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .date.disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}ion-content[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   .week-row[_ngcontent-%COMP%]   .day-container[_ngcontent-%COMP%]   .date.unselected[_ngcontent-%COMP%]{color:var(--text-muted)}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{padding:0}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .heartbeat-circle[_ngcontent-%COMP%]{margin:32px;width:100px;height:100px;background:linear-gradient(220deg,#4169e1,#6b85e8,#95a5ef,#bfc5f6,#e7e9fd,#fff);background-size:300% 100%;border-radius:50%;position:relative;animation:_ngcontent-%COMP%_heartbeat 1.2s infinite,_ngcontent-%COMP%_gradient 2s ease-in-out infinite alternate}@keyframes _ngcontent-%COMP%_heartbeat{0%{transform:scale(1);opacity:1}25%{transform:scale(1.03)}50%{transform:scale(1.05)}75%{transform:scale(1.03)}to{transform:scale(1)}}@keyframes _ngcontent-%COMP%_gradient{0%{background-position:100% 0%}to{background-position:0% 0%}}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .big-date[_ngcontent-%COMP%]{min-width:200px;max-width:450px;min-height:200px;max-height:450px}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .big-date[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%]{stroke-width:3}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .add-quest[_ngcontent-%COMP%]{display:flex;align-items:center}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .add-quest[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{display:flex;align-items:center}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .add-quest[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]{color:var(--text);margin:16px 0;border:1px solid var(--error)}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:20px}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:left}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:5rem;color:var(--error)}ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .quests[_ngcontent-%COMP%]   .no-quest-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-top:16px;width:80%}.quest-item[_ngcontent-%COMP%]{padding:5px 0;margin:16px 0;display:flex;flex-direction:column}.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{width:100%}.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]{align-items:flex-start}.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;color:var(--accent)}.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:14px;margin-top:5px;color:var(--text-secondary)}.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center}.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .quest-icon[_ngcontent-%COMP%]{font-size:2rem}.quest-item[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   .quest-streak[_ngcontent-%COMP%]{font-size:1rem;color:var(--text-secondary)}.quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]{margin-top:10px;width:100%;display:flex;justify-content:center}.quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-time[_ngcontent-%COMP%], .quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column}.quest-item[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{margin:5px auto;color:var(--text-secondary);text-align:right}.add-quest-btn[_ngcontent-%COMP%]{--background: rgba(65, 105, 225, .1);--color: #4169E1;--border-radius: 6px;--padding-start: 12px;--padding-end: 12px;font-size:14px;font-weight:500;height:32px;margin:0}.add-quest-btn[_ngcontent-%COMP%]:hover{--background: rgba(65, 105, 225, .2)}.quest-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.quest-item[_ngcontent-%COMP%]{background-color:#1c1c1e;border:1px solid #2C2C2E;border-radius:8px;padding:12px;display:flex;align-items:center;gap:12px;cursor:pointer;transition:all .2s ease;position:relative;overflow:hidden}.quest-item[_ngcontent-%COMP%]:active{transform:scale(.98)}.quest-item.completed[_ngcontent-%COMP%]{border-color:#4169e1}.quest-item.completed[_ngcontent-%COMP%]   .quest-title[_ngcontent-%COMP%]{color:#4169e1}.quest-item.completed[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background-color:#4169e10d;pointer-events:none}.quest-icon[_ngcontent-%COMP%]{font-size:20px;min-width:24px;text-align:center}.quest-info[_ngcontent-%COMP%]{flex-grow:1}.quest-title[_ngcontent-%COMP%]{font-size:14px;font-weight:600;margin-bottom:2px;color:#4169e1}.quest-description[_ngcontent-%COMP%]{color:#8e8e93;font-size:12px;margin-bottom:4px}.progress[_ngcontent-%COMP%], .progress-time[_ngcontent-%COMP%]{color:var(--secondary-text);font-size:12px}.side-quests[_ngcontent-%COMP%]{position:relative;padding-top:32px}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.quests[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .daily-side-quest[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:20px;font-weight:600;margin:0}.progress-slider[_ngcontent-%COMP%]{-webkit-appearance:none;-moz-appearance:none;appearance:none;width:100%;height:4px;background:#2c2c2e;outline:none;position:relative}.progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb{-webkit-appearance:none;-moz-appearance:none;appearance:none;width:12px;height:12px;border-radius:50%;background:#4169e1;cursor:pointer;position:relative;margin-top:-4px}.progress-slider[_ngcontent-%COMP%]::-moz-range-thumb{width:12px;height:12px;border-radius:50%;background:#4169e1;cursor:pointer;border:none;position:relative;margin-top:0}.progress-slider[_ngcontent-%COMP%]::-webkit-slider-runnable-track{height:4px;border-radius:2px}.progress-slider[_ngcontent-%COMP%]::-moz-range-track{height:4px;border-radius:2px}.progress-slider[_ngcontent-%COMP%]{background:var(--inactive-date)}input[type=range].progress-slider[_ngcontent-%COMP%]{-webkit-appearance:none;-moz-appearance:none;appearance:none;height:4px;border-radius:2px;outline:none;position:relative;z-index:1}input[type=range].progress-slider[_ngcontent-%COMP%]::-webkit-slider-runnable-track{height:4px;border-radius:2px;background:transparent}input[type=range].progress-slider[_ngcontent-%COMP%]::-moz-range-track{height:4px;border-radius:2px;background:transparent}input[type=range].progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb{-webkit-appearance:none;-moz-appearance:none;appearance:none;width:12px;height:12px;border-radius:50%;background:#4169e1;cursor:pointer;margin-top:-4px;position:relative;z-index:2}input[type=range].progress-slider[_ngcontent-%COMP%]::-moz-range-thumb{width:12px;height:12px;border-radius:50%;background:#4169e1;cursor:pointer;border:none;position:relative;margin-top:0;z-index:2}.progress-text[_ngcontent-%COMP%]{font-size:12px;color:var(--secondary-text);margin-top:2px}.container[_ngcontent-%COMP%]{width:480px;margin:0 auto;padding:20px 20px 74px;overflow-y:auto;scrollbar-width:none;height:100%}.container[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.header-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.logo[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:24px}.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .page-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600}.week-calendar[_ngcontent-%COMP%]{margin-bottom:32px}.days[_ngcontent-%COMP%], .dates[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr);text-align:center;gap:8px}.day-name[_ngcontent-%COMP%]{color:var(--secondary-text);font-size:14px}.date-content[_ngcontent-%COMP%]{position:relative;z-index:1}h2[_ngcontent-%COMP%]{font-size:20px;margin-bottom:16px}.side-quests[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:50%;transform:translate(-50%);width:90%;height:1px;background:linear-gradient(to right,transparent,#4B0082,transparent)}.calendar[_ngcontent-%COMP%]{margin:20px 0;padding:10px;background:var(--bg-secondary);border-radius:8px}.calendar-nav[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:10px}.calendar-days[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr);gap:5px;text-align:center}.day-name[_ngcontent-%COMP%]{color:#8e8e93;font-size:12px;font-weight:500}.day-number[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:30px;height:30px;border-radius:50%;cursor:pointer;text-decoration:none;color:var(--text-primary);margin:0 auto}.day-number[_ngcontent-%COMP%]:hover{background:var(--bg-hover)}.day-number.selected[_ngcontent-%COMP%]{background:var(--primary-color);color:#fff}.day-number.today[_ngcontent-%COMP%]{border:2px solid var(--primary-color)}.nav-arrow[_ngcontent-%COMP%]{--background: transparent;--color: #FFFFFF;--border-radius: 50%;--padding-start: 0;--padding-end: 0;width:32px;height:32px;margin:0;font-size:18px;cursor:pointer}.nav-arrow[_ngcontent-%COMP%]:hover{--background: rgba(255, 255, 255, .1)}.time-display[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--text-color);margin-right:16px}input[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, input[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{opacity:.3}ion-range.progress-slider[_ngcontent-%COMP%]{--bar-height: 6px;--bar-border-radius: 3px;--knob-size: 16px;--bar-background: #2C2C2E;--bar-background-active: #4169E1;--knob-background: #4169E1;--pin-background: #4169E1;--pin-color: #FFFFFF;--step: 1;--tick-height: 0;--tick-width: 0;--tick-background: transparent;--tick-background-active: transparent;margin:0;padding:0}.progress-slider[_ngcontent-%COMP%]{-webkit-appearance:none;-moz-appearance:none;appearance:none;width:100%;height:6px;border-radius:3px;outline:none;background:linear-gradient(to right,#4169E1 0%,#4169E1 var(--progress-value),#2C2C2E var(--progress-value),#2C2C2E 100%)}ion-range.progress-slider[_ngcontent-%COMP%]::part(tick){display:none!important}ion-range.progress-slider[_ngcontent-%COMP%]::part(tick-active){display:none!important}.progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb{-webkit-appearance:none;-moz-appearance:none;appearance:none;width:16px;height:16px;border-radius:50%;background:#4169e1;cursor:pointer;margin-top:-5px}.progress-slider[_ngcontent-%COMP%]::-moz-range-thumb{width:16px;height:16px;border-radius:50%;background:#4169e1;cursor:pointer;margin-top:0;border:none}.daily-side-quest[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{padding-bottom:20px}.add-quest-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]{--background: var(--bg);position:relative;overflow:hidden}.add-quest-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   .create-quest-row[_ngcontent-%COMP%]{width:100%;position:absolute;bottom:0;left:0}.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-progress-bar[_ngcontent-%COMP%]{--background: var(--progress-bg);height:8px;border-radius:4px}.add-quest-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{position:absolute;right:15px;top:15px;font-size:30px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .choose-quest[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:90%}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .choose-quest[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{height:150px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .choose-quest[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--accent);font-size:32px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .choose-quest[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .first-step[_ngcontent-%COMP%]{text-align:center}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .first-step[_ngcontent-%COMP%]   .preview-emoji[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{display:flex;justify-content:center}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .first-step[_ngcontent-%COMP%]   .preview-emoji[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{height:100%;font-size:8em;max-width:60%}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .first-step[_ngcontent-%COMP%]   .emoji-row[_ngcontent-%COMP%]   ion-text[_ngcontent-%COMP%]{font-size:1em}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]{margin-bottom:30px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .floating-emoji[_ngcontent-%COMP%]{font-size:4em;display:inline-block;animation:_ngcontent-%COMP%_float-emoji 3s ease-in-out infinite;filter:drop-shadow(0 0 20px rgba(65,105,225,.3))}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .input-section[_ngcontent-%COMP%]{margin-top:32px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%]{position:absolute;left:18px;top:50%;transform:translateY(-50%);font-size:20px;z-index:3;transition:all .4s cubic-bezier(.4,0,.2,1);filter:drop-shadow(0 2px 4px rgba(0,0,0,.3));opacity:.8}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]{--padding-start: 55px;--padding-end: 20px;--padding-top: 24px;--padding-bottom: 24px;border-radius:16px;transition:all .4s cubic-bezier(.4,0,.2,1);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);position:relative;overflow:hidden;box-shadow:0 8px 32px #0000004d,inset 0 1px #ffffff1a}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:focus-within, .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:focus-within{border-color:var(--accent);box-shadow:0 0 0 1px var(--accent),0 0 30px #4169e166,0 12px 40px #0006,inset 0 1px #fff3}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .floating-label[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .floating-label[_ngcontent-%COMP%]{position:absolute;left:20%;top:-10px;color:var(--text-secondary);font-size:16px;font-weight:600;padding:4px 12px;border-radius:8px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.1);box-shadow:0 2px 8px #0003;z-index:4}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .character-counter[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .character-counter[_ngcontent-%COMP%]{position:absolute;right:20px;font-size:11px;font-weight:600;color:var(--text-muted);background:#16171ccc;padding:4px 8px;border-radius:6px;border:1px solid rgba(255,255,255,.05);transition:all .3s ease}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .character-counter[_ngcontent-%COMP%]   span.warning[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .character-counter[_ngcontent-%COMP%]   span.warning[_ngcontent-%COMP%]{color:var(--warning);text-shadow:0 0 8px rgba(250,204,21,.3)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:focus + .floating-label[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-input-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:not(:placeholder-shown) + .floating-label[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:focus + .floating-label[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-three-container[_ngcontent-%COMP%]   .floating-textarea-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]:not(:placeholder-shown) + .floating-label[_ngcontent-%COMP%]{top:-12px;left:50px;font-size:12px;color:var(--accent);transform:translateY(0) scale(.9);background:linear-gradient(135deg,#4169e133,#5277e826);border-color:#4169e14d;box-shadow:0 4px 12px #4169e133,0 0 0 1px #4169e11a}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]{margin-top:10%}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]{margin-bottom:30px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-icon-container[_ngcontent-%COMP%]{position:relative;margin-bottom:20px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-icon-container[_ngcontent-%COMP%]   .floating-emoji[_ngcontent-%COMP%]{font-size:4em;display:inline-block;animation:_ngcontent-%COMP%_float-emoji 3s ease-in-out infinite;filter:drop-shadow(0 0 20px rgba(65,105,225,.3))}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:28px;font-weight:700;margin-bottom:10px;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-subtitle[_ngcontent-%COMP%]{font-size:16px;color:var(--text-secondary);margin:0;opacity:.8}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .section-label[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:15px;font-size:18px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .section-label[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px;font-size:20px;color:var(--accent)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]{margin-bottom:30px;position:relative;overflow:hidden;transition:all .6s cubic-bezier(.4,0,.2,1);transform:translate(0);opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section.slide-in-from-right[_ngcontent-%COMP%]{transform:translate(0);opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section.slide-out-left[_ngcontent-%COMP%]{transform:translate(-100%);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]{display:flex;gap:15px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]{flex:1;background:linear-gradient(135deg,#1e1f25f2,#16171ce6);border:2px solid var(--border);border-radius:16px;padding:20px;cursor:pointer;transition:all .4s cubic-bezier(.4,0,.2,1);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);position:relative;overflow:hidden}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,#4169e11a,#5277e80d);opacity:0;transition:opacity .4s ease;pointer-events:none}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]   .toggle-icon[_ngcontent-%COMP%]{font-size:2.5em;margin-bottom:10px;filter:drop-shadow(0 2px 4px rgba(0,0,0,.3))}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 5px;font-size:16px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:12px;color:var(--text-secondary);opacity:.8}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #0000004d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option.active[_ngcontent-%COMP%]{border-color:var(--accent);box-shadow:0 0 0 1px var(--accent),0 0 20px #4169e14d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option.active[_ngcontent-%COMP%]:before{opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .quest-type-section[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-option.active[_ngcontent-%COMP%]   .toggle-icon[_ngcontent-%COMP%]{transform:scale(1.1)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]{margin-bottom:30px;position:relative;overflow:hidden;transition:all .6s cubic-bezier(.4,0,.2,1);transform:translate(100%);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section.slide-in-from-right[_ngcontent-%COMP%]{transform:translate(0);opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section.slide-out-left[_ngcontent-%COMP%]{transform:translate(-100%);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:12px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1e1f25f2,#16171ce6);border:2px solid var(--border);border-radius:16px;padding:20px;text-align:center;cursor:pointer;transition:all .4s cubic-bezier(.4,0,.2,1);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);position:relative;overflow:hidden}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,#4169e11a,#5277e80d);opacity:0;transition:opacity .4s ease;pointer-events:none}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]{font-size:2.5em;margin-bottom:8px;filter:drop-shadow(0 2px 4px rgba(0,0,0,.3));transition:transform .3s ease}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #0000004d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]:hover   .category-icon[_ngcontent-%COMP%]{transform:scale(1.1)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card.selected[_ngcontent-%COMP%]{border-color:var(--accent);box-shadow:0 0 0 1px var(--accent),0 0 20px #4169e14d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card.selected[_ngcontent-%COMP%]:before{opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-grid[_ngcontent-%COMP%]   .category-card.selected[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]{transform:scale(1.1)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]{margin-bottom:30px;position:relative;overflow:hidden;transition:all .6s cubic-bezier(.4,0,.2,1);transform:translate(100%);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section.slide-in-from-right[_ngcontent-%COMP%]{transform:translate(0);opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section.slide-out-left[_ngcontent-%COMP%]{transform:translate(-100%);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]{display:flex;gap:15px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]{flex:1;background:linear-gradient(135deg,#1e1f25f2,#16171ce6);border:2px solid var(--border);border-radius:16px;padding:15px 20px;cursor:pointer;transition:all .4s cubic-bezier(.4,0,.2,1);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);display:flex;align-items:center;gap:12px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]   .priority-indicator[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%;transition:all .3s ease}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]   .priority-indicator.basic[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4ade80,#22c55e);box-shadow:0 0 10px #4ade804d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]   .priority-indicator.high[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f59e0b,#d97706);box-shadow:0 0 10px #f59e0b4d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option[_ngcontent-%COMP%]:hover:not(.disabled){transform:translateY(-1px);box-shadow:0 6px 20px #0000004d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option.active[_ngcontent-%COMP%]{border-color:var(--accent);box-shadow:0 0 0 1px var(--accent),0 0 15px #4169e14d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option.active[_ngcontent-%COMP%]   .priority-indicator[_ngcontent-%COMP%]{transform:scale(1.2)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-container[_ngcontent-%COMP%]   .priority-option.disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-warning[_ngcontent-%COMP%]{margin-top:10px;padding:10px 15px;background:#f59e0b1a;border:1px solid rgba(245,158,11,.3);border-radius:8px;display:flex;align-items:center;gap:8px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-warning[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--warning);font-size:16px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .priority-section[_ngcontent-%COMP%]   .priority-warning[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;color:var(--warning)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]{margin-bottom:30px;text-align:center}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-icon-container[_ngcontent-%COMP%]{margin-bottom:20px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-icon-container[_ngcontent-%COMP%]   .floating-emoji[_ngcontent-%COMP%]{font-size:4em;animation:float 3s ease-in-out infinite;filter:drop-shadow(0 4px 8px rgba(0,0,0,.3))}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:28px;font-weight:700;margin-bottom:10px;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]   .gradient-text[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--accent),#5277e8);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .step-five-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   .step-subtitle[_ngcontent-%COMP%]{font-size:16px;color:var(--text-secondary);opacity:.8;margin:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]{margin-bottom:30px;position:relative;overflow:hidden;transition:all .6s cubic-bezier(.4,0,.2,1);transform:translate(100%);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section.slide-in-from-right[_ngcontent-%COMP%]{transform:translate(0);opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1e1f25f2,#16171ce6);border:2px solid var(--border);border-radius:20px;padding:25px;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);position:relative;overflow:hidden}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,#4169e10d,#5277e805);pointer-events:none}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px;margin-bottom:25px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-header[_ngcontent-%COMP%]   .goal-icon[_ngcontent-%COMP%]{font-size:2em;filter:drop-shadow(0 2px 4px rgba(0,0,0,.3))}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-header[_ngcontent-%COMP%]   .goal-title[_ngcontent-%COMP%]{font-size:20px;font-weight:700;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-inputs[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1.5fr;gap:20px;margin-bottom:20px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-inputs[_ngcontent-%COMP%]   .goal-value-container[_ngcontent-%COMP%]   .dark-input[_ngcontent-%COMP%]{text-align:center;font-size:18px;font-weight:600}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-preview[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4169e126,#5277e814);border:1px solid rgba(65,105,225,.3);border-radius:12px;padding:15px;text-align:center}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-preview[_ngcontent-%COMP%]   .preview-label[_ngcontent-%COMP%]{font-size:14px;color:var(--text-secondary);margin-right:10px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .step-four-container[_ngcontent-%COMP%]   .goal-section[_ngcontent-%COMP%]   .goal-card[_ngcontent-%COMP%]   .goal-preview[_ngcontent-%COMP%]   .preview-value[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:var(--accent)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]{margin-bottom:30px;position:relative;overflow:hidden;transition:all .6s cubic-bezier(.4,0,.2,1);transform:translate(100%);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section.slide-in-from-right[_ngcontent-%COMP%]{transform:translate(0);opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1e1f25f2,#16171ce6);border:2px solid var(--border);border-radius:20px;padding:25px;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);position:relative;overflow:hidden}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,#4169e10d,#5277e805);pointer-events:none}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px;margin-bottom:25px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-header[_ngcontent-%COMP%]   .frequency-icon[_ngcontent-%COMP%]{font-size:2em;filter:drop-shadow(0 2px 4px rgba(0,0,0,.3))}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-header[_ngcontent-%COMP%]   .frequency-title[_ngcontent-%COMP%]{font-size:20px;font-weight:700;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-options-container[_ngcontent-%COMP%]{display:grid;gap:15px;margin-bottom:25px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-options-container[_ngcontent-%COMP%]   .frequency-option[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28292ff2,#202126e6);border:2px solid var(--border);border-radius:16px;padding:20px;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:15px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-options-container[_ngcontent-%COMP%]   .frequency-option[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #0000004d;border-color:#4169e180}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-options-container[_ngcontent-%COMP%]   .frequency-option.selected[_ngcontent-%COMP%]{border-color:var(--accent);background:linear-gradient(135deg,#4169e126,#5277e814);box-shadow:0 0 20px #4169e14d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-options-container[_ngcontent-%COMP%]   .frequency-option[_ngcontent-%COMP%]   .option-icon[_ngcontent-%COMP%]{font-size:1.8em;filter:drop-shadow(0 2px 4px rgba(0,0,0,.3))}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-options-container[_ngcontent-%COMP%]   .frequency-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]{flex:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-options-container[_ngcontent-%COMP%]   .frequency-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 5px;font-size:16px;font-weight:700;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-options-container[_ngcontent-%COMP%]   .frequency-option[_ngcontent-%COMP%]   .option-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--text-secondary);opacity:.8}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]{transition:all .4s cubic-bezier(.4,0,.2,1);transform:translateY(20px);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details.slide-in[_ngcontent-%COMP%]{transform:translateY(0);opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .week-selector[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .month-selector[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 15px;font-size:16px;font-weight:600;color:var(--text);text-align:center}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr);gap:10px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-chip[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28292ff2,#202126e6);border:2px solid var(--border);border-radius:12px;padding:12px 8px;text-align:center;cursor:pointer;transition:all .3s ease}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-chip[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #0000004d;border-color:#4169e180}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-chip.selected[_ngcontent-%COMP%]{border-color:var(--accent);background:linear-gradient(135deg,#4169e133,#5277e81a);box-shadow:0 0 15px #4169e14d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-chip[_ngcontent-%COMP%]   .day-short[_ngcontent-%COMP%]{display:block;font-size:12px;font-weight:700;color:var(--text);margin-bottom:2px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-chip[_ngcontent-%COMP%]   .day-full[_ngcontent-%COMP%]{display:block;font-size:10px;color:var(--text-secondary);opacity:.8}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr);gap:8px;max-height:200px;overflow-y:auto}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-chip[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28292ff2,#202126e6);border:2px solid var(--border);border-radius:10px;padding:10px 6px;text-align:center;cursor:pointer;transition:all .3s ease;font-size:12px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-chip[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 10px #0000004d;border-color:#4169e180}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-card[_ngcontent-%COMP%]   .frequency-details[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-chip.selected[_ngcontent-%COMP%]{border-color:var(--accent);background:linear-gradient(135deg,#4169e133,#5277e81a);box-shadow:0 0 10px #4169e14d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]{margin-top:15px;transition:all .4s cubic-bezier(.4,0,.2,1);transform:translateY(20px);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options.slide-in[_ngcontent-%COMP%]{transform:translateY(0);opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr);gap:8px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-option[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1e1f25f2,#16171ce6);border:2px solid var(--border);border-radius:12px;padding:12px 8px;text-align:center;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-option[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #0000004d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .week-days-grid[_ngcontent-%COMP%]   .day-option.selected[_ngcontent-%COMP%]{border-color:var(--accent);background:linear-gradient(135deg,#4169e133,#5277e81a);box-shadow:0 0 15px #4169e14d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(7,1fr);gap:6px;max-height:200px;overflow-y:auto}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-option[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1e1f25f2,#16171ce6);border:2px solid var(--border);border-radius:8px;padding:8px 4px;text-align:center;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:11px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-option[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 10px #0000004d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .frequency-section[_ngcontent-%COMP%]   .frequency-options[_ngcontent-%COMP%]   .month-days-grid[_ngcontent-%COMP%]   .month-day-option.selected[_ngcontent-%COMP%]{border-color:var(--accent);background:linear-gradient(135deg,#4169e133,#5277e81a);box-shadow:0 0 10px #4169e14d}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]{margin-bottom:30px;position:relative;overflow:hidden;transition:all .6s cubic-bezier(.4,0,.2,1);transform:translate(100%);opacity:0}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section.slide-in-from-right[_ngcontent-%COMP%]{transform:translate(0);opacity:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1e1f25f2,#16171ce6);border:2px solid var(--border);border-radius:24px;padding:30px;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);position:relative;overflow:hidden}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,#4169e114,#5277e80a);pointer-events:none}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-floating-emoji[_ngcontent-%COMP%]{position:absolute;top:-10px;right:20px;font-size:3em;animation:float 3s ease-in-out infinite;filter:drop-shadow(0 4px 8px rgba(0,0,0,.3));z-index:2}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]{position:relative;z-index:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-title-section[_ngcontent-%COMP%]{margin-bottom:25px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-title-section[_ngcontent-%COMP%]   .quest-preview-title[_ngcontent-%COMP%]{margin:0 0 8px;font-size:24px;font-weight:700;color:var(--text);background:linear-gradient(135deg,var(--text),rgba(255,255,255,.8));-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-title-section[_ngcontent-%COMP%]   .quest-preview-description[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--text-secondary);opacity:.9;line-height:1.5}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-stats[_ngcontent-%COMP%]{display:grid;gap:15px;margin-bottom:25px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:15px;background:linear-gradient(135deg,#28292f99,#20212666);border:1px solid rgba(65,105,225,.2);border-radius:12px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{font-size:1.5em;filter:drop-shadow(0 2px 4px rgba(0,0,0,.3))}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]{flex:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{display:block;font-size:12px;color:var(--text-secondary);opacity:.8;margin-bottom:2px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-goal-section[_ngcontent-%COMP%]{margin-bottom:25px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-goal-section[_ngcontent-%COMP%]   .goal-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4169e133,#5277e81a);border:2px solid rgba(65,105,225,.4);border-radius:16px;padding:20px;text-align:center}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-goal-section[_ngcontent-%COMP%]   .goal-badge[_ngcontent-%COMP%]   .goal-number[_ngcontent-%COMP%]{display:block;font-size:32px;font-weight:700;color:var(--accent);margin-bottom:5px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-goal-section[_ngcontent-%COMP%]   .goal-badge[_ngcontent-%COMP%]   .goal-text[_ngcontent-%COMP%]{display:block;font-size:16px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-progress-section[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-progress-section[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-progress-section[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%]{font-size:14px;font-weight:700;color:var(--accent)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-progress-section[_ngcontent-%COMP%]   .progress-track[_ngcontent-%COMP%]{height:12px;background:#6b72804d;border-radius:6px;overflow:hidden;margin-bottom:8px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-progress-section[_ngcontent-%COMP%]   .progress-track[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,var(--accent),#5277e8);border-radius:6px;transition:width .3s ease}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-progress-section[_ngcontent-%COMP%]   .progress-footer[_ngcontent-%COMP%]{text-align:center}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .preview-progress-section[_ngcontent-%COMP%]   .progress-footer[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;color:var(--text-secondary);opacity:.8}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:15px;margin-bottom:15px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-emoji[_ngcontent-%COMP%]{font-size:2.5em;filter:drop-shadow(0 2px 4px rgba(0,0,0,.3))}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-info[_ngcontent-%COMP%]{flex:1}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-info[_ngcontent-%COMP%]   .preview-title[_ngcontent-%COMP%]{margin:0 0 5px;font-size:18px;font-weight:700;color:var(--text)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-info[_ngcontent-%COMP%]   .preview-description[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--text-secondary);opacity:.8}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-badges[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:5px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-badges[_ngcontent-%COMP%]   .type-badge[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-badges[_ngcontent-%COMP%]   .category-badge[_ngcontent-%COMP%], .add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-badges[_ngcontent-%COMP%]   .priority-badge[_ngcontent-%COMP%]{padding:4px 8px;border-radius:8px;font-size:10px;font-weight:600;text-align:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-badges[_ngcontent-%COMP%]   .type-badge.build[_ngcontent-%COMP%]{background:#22c55e33;color:#22c55e;border:1px solid rgba(34,197,94,.3)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-badges[_ngcontent-%COMP%]   .type-badge.quit[_ngcontent-%COMP%]{background:#ef444433;color:#ef4444;border:1px solid rgba(239,68,68,.3)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-badges[_ngcontent-%COMP%]   .category-badge[_ngcontent-%COMP%]{background:#4169e133;color:var(--accent);border:1px solid rgba(65,105,225,.3)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-badges[_ngcontent-%COMP%]   .priority-badge.high[_ngcontent-%COMP%]{background:#f59e0b33;color:#f59e0b;border:1px solid rgba(245,158,11,.3)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-badges[_ngcontent-%COMP%]   .priority-badge.basic[_ngcontent-%COMP%]{background:#6b728033;color:#9ca3af;border:1px solid rgba(107,114,128,.3)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-goal[_ngcontent-%COMP%]{margin-bottom:15px;padding:15px;background:linear-gradient(135deg,#4169e11a,#5277e80d);border:1px solid rgba(65,105,225,.2);border-radius:12px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-goal[_ngcontent-%COMP%]   .goal-display[_ngcontent-%COMP%]{text-align:center}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-goal[_ngcontent-%COMP%]   .goal-display[_ngcontent-%COMP%]   .goal-value[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:var(--accent)}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-goal[_ngcontent-%COMP%]   .goal-display[_ngcontent-%COMP%]   .goal-unit[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--text);margin-left:5px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-goal[_ngcontent-%COMP%]   .goal-display[_ngcontent-%COMP%]   .goal-period[_ngcontent-%COMP%]{font-size:14px;color:var(--text-secondary);margin-left:10px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-progress[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-progress[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{flex:1;height:8px;background:#6b72804d;border-radius:4px;overflow:hidden}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-progress[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,var(--accent),#5277e8);border-radius:4px;transition:width .3s ease}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .preview-section[_ngcontent-%COMP%]   .quest-preview-card[_ngcontent-%COMP%]   .preview-progress[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:var(--text-secondary);min-width:60px;text-align:right}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .select-days[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly}.add-quest-modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .select-days[_ngcontent-%COMP%]   .day-checked[_ngcontent-%COMP%]{width:100%;flex:1;--background: var(--surface);--color: var(--text);--border-color: var(--border);--border-style: solid;--border-width: 1px}@keyframes _ngcontent-%COMP%_aurora-curve{0%{transform:translate(-10px) translateY(5px) scale(1);opacity:.8}50%{transform:translate(15px) translateY(-8px) scale(1.05);opacity:1}to{transform:translate(-5px) translateY(3px) scale(.98);opacity:.9}}@keyframes _ngcontent-%COMP%_aurora-flow{0%{transform:translate(-15px) translateY(-5px) skew(1deg);opacity:.6}50%{transform:translate(20px) translateY(10px) skew(-1.5deg);opacity:.8}to{transform:translate(-8px) translateY(-3px) skew(.5deg);opacity:.7}}@keyframes _ngcontent-%COMP%_aurora-glow{0%{transform:scale(1) rotate(0);opacity:.4}50%{transform:scale(1.1) rotate(1deg);opacity:.6}to{transform:scale(.95) rotate(-.5deg);opacity:.5}}@keyframes _ngcontent-%COMP%_float-emoji{0%,to{transform:translateY(0) rotate(0)}33%{transform:translateY(-10px) rotate(2deg)}66%{transform:translateY(-5px) rotate(-2deg)}}\"]\n  });\n  return TodayPage;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}