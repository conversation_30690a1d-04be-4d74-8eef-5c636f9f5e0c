# Supabase Access Guide for Upshift

Tento dokument obsahuje inštrukcie na riešenie problémov s prístupovými právami v Supabase pre aplikáciu Upshift, najmä pre stránky ako Friends, Groups, Profile Settings a User Badges.

## Problém s prístupovými právami

Ak máte problémy s prístupom k stránkam ako Friends, Groups, Profile Settings, User Badges, alebo nemôžete pridávať nové polo<PERSON>ky (quests, goals, atď.), je to spôsobené chýbajúcimi alebo nesprávne nastavenými Row Level Security (RLS) politikami v Supabase.

## Riešenie pre konkrétne stránky

### Friends Page - Potrebné politiky

Pre správne fungovanie stránky Friends potrebujete tieto politiky:

1. **profiles** tabuľka:
   ```sql
   -- <PERSON><PERSON><PERSON><PERSON><PERSON> použ<PERSON>vateľom vidieť profily iných pou<PERSON> (pre zobrazenie priateľov)
   CREATE POLICY "Users can view all profiles"
   ON profiles FOR SELECT USING (true);
   ```

2. **friends** tabuľka:
   ```sql
   -- Umožní používateľom vidieť svojich priateľov
   CREATE POLICY "Users can view their own friends"
   ON friends FOR SELECT
   USING (auth.uid() = user_id OR auth.uid() = friend_id);

   -- Umožní používateľom pridávať priateľov
   CREATE POLICY "Users can insert their own friends"
   ON friends FOR INSERT
   WITH CHECK (auth.uid() = user_id);

   -- Umožní používateľom odstrániť priateľov
   CREATE POLICY "Users can delete their own friends"
   ON friends FOR DELETE
   USING (auth.uid() = user_id);
   ```

### Groups Page - Potrebné politiky

Pre správne fungovanie stránky Groups potrebujete tieto politiky:

1. **groups** tabuľka:
   ```sql
   -- Umožní používateľom vidieť skupiny, ktorých sú členmi
   CREATE POLICY "Users can view groups they are members of"
   ON groups FOR SELECT
   USING (EXISTS (SELECT 1 FROM group_members WHERE group_id = id AND user_id = auth.uid()));

   -- Umožní používateľom vytvárať skupiny
   CREATE POLICY "Users can insert groups"
   ON groups FOR INSERT
   WITH CHECK (auth.uid() = admin_id);

   -- Umožní administrátorom skupín upravovať ich skupiny
   CREATE POLICY "Group admins can update their groups"
   ON groups FOR UPDATE
   USING (auth.uid() = admin_id);
   ```

2. **group_members** tabuľka:
   ```sql
   -- Umožní používateľom vidieť členov skupín, ktorých sú členmi
   CREATE POLICY "Users can view group members of groups they are in"
   ON group_members FOR SELECT
   USING (EXISTS (SELECT 1 FROM group_members gm WHERE gm.group_id = group_id AND gm.user_id = auth.uid()));

   -- Umožní používateľom pridať sa do skupiny
   CREATE POLICY "Users can insert themselves as group members"
   ON group_members FOR INSERT
   WITH CHECK (auth.uid() = user_id OR
               EXISTS (SELECT 1 FROM groups WHERE id = group_id AND admin_id = auth.uid()));
   ```

### Profile Settings Page - Potrebné politiky

Pre správne fungovanie stránky Profile Settings potrebujete tieto politiky:

1. **profiles** tabuľka:
   ```sql
   -- Umožní používateľom vidieť svoj vlastný profil
   CREATE POLICY "Users can view their own profile"
   ON profiles FOR SELECT
   USING (auth.uid() = id);

   -- Umožní používateľom upravovať svoj vlastný profil
   CREATE POLICY "Users can update their own profile"
   ON profiles FOR UPDATE
   USING (auth.uid() = id);
   ```

### User Badges Page - Potrebné politiky

Pre správne fungovanie stránky User Badges potrebujete tieto politiky:

1. **user_badges** tabuľka:
   ```sql
   -- Umožní používateľom vidieť svoje vlastné odznaky
   CREATE POLICY "Users can view their own badges"
   ON user_badges FOR SELECT
   USING (auth.uid() = user_id);

   -- Umožní používateľom aktualizovať svoje vlastné odznaky
   CREATE POLICY "Users can update their own badges"
   ON user_badges FOR UPDATE
   USING (auth.uid() = user_id);

   -- Umožní používateľom vytvoriť svoje vlastné odznaky
   CREATE POLICY "Users can insert their own badges"
   ON user_badges FOR INSERT
   WITH CHECK (auth.uid() = user_id);
   ```

### Time Tracker Page - Potrebné politiky

Pre správne fungovanie stránky Time Tracker potrebujete tieto politiky:

1. **activities** tabuľka:
   ```sql
   -- Umožní používateľom vidieť svoje vlastné aktivity
   CREATE POLICY "Users can view their own activities"
   ON activities FOR SELECT
   USING (auth.uid() = user_id);

   -- Umožní používateľom pridávať svoje vlastné aktivity
   CREATE POLICY "Users can insert their own activities"
   ON activities FOR INSERT
   WITH CHECK (auth.uid() = user_id);

   -- Umožní používateľom upravovať svoje vlastné aktivity
   CREATE POLICY "Users can update their own activities"
   ON activities FOR UPDATE
   USING (auth.uid() = user_id);
   ```

2. **activity_types** tabuľka:
   ```sql
   -- Umožní všetkým používateľom vidieť typy aktivít
   CREATE POLICY "Everyone can view activity types"
   ON activity_types FOR SELECT
   USING (true);
   ```

### Goals Page - Potrebné politiky

Pre správne fungovanie stránky Goals potrebujete tieto politiky:

1. **goals** tabuľka:
   ```sql
   -- Umožní používateľom vidieť svoje vlastné ciele
   CREATE POLICY "Users can view their own goals"
   ON goals FOR SELECT
   USING (auth.uid() = user_id);

   -- Umožní používateľom pridávať svoje vlastné ciele
   CREATE POLICY "Users can insert their own goals"
   ON goals FOR INSERT
   WITH CHECK (auth.uid() = user_id);

   -- Umožní používateľom upravovať svoje vlastné ciele
   CREATE POLICY "Users can update their own goals"
   ON goals FOR UPDATE
   USING (auth.uid() = user_id);
   ```

2. **goal_microgoals** tabuľka:
   ```sql
   -- Umožní používateľom vidieť svoje vlastné mikrociele
   CREATE POLICY "Users can view their own microgoals"
   ON goal_microgoals FOR SELECT
   USING (auth.uid() = (SELECT user_id FROM goals WHERE id = goal_id));

   -- Umožní používateľom pridávať svoje vlastné mikrociele
   CREATE POLICY "Users can insert their own microgoals"
   ON goal_microgoals FOR INSERT
   WITH CHECK (auth.uid() = (SELECT user_id FROM goals WHERE id = goal_id));
   ```

## Kompletné nastavenie RLS politík

Pre nastavenie všetkých potrebných politík naraz:

1. Prihláste sa do [Supabase Dashboard](https://app.supabase.com)
2. Vyberte váš projekt
3. Prejdite na "SQL Editor"
4. Otvorte súbor `supabase-rls-policies.sql` z tohto projektu
5. Skopírujte obsah súboru do SQL editora
6. Spustite SQL príkazy kliknutím na "Run"

Tieto príkazy nastavia správne RLS politiky pre všetky tabuľky vo vašej databáze, čo umožní používateľom pristupovať k svojim vlastným dátam.

### 2. Vytvorenie testovacieho používateľa s platným plánom

Ak potrebujete vytvoriť testovacieho používateľa s platným plánom:

1. Otvorte súbor `create-test-user.js`
2. Nahraďte `YOUR_SUPABASE_SERVICE_ROLE_KEY` vaším skutočným Service Role kľúčom zo Supabase
   - Tento kľúč nájdete v Supabase Dashboard > Settings > API
   - Použite "service_role" kľúč, nie "anon" kľúč
3. Spustite skript pomocou Node.js:
   ```
   node create-test-user.js
   ```

Tento skript vytvorí testovacieho používateľa s platným plánom, ktorý vyprší o jeden mesiac.

### 3. Kontrola existujúcich politík

Ak chcete skontrolovať existujúce RLS politiky:

1. Prihláste sa do Supabase Dashboard
2. Prejdite na "Authentication" > "Policies"
3. Tu uvidíte zoznam všetkých tabuliek a ich politík

### 4. Kontrola chýb v konzole

Pri ladení problémov s prístupom:

1. Otvorte vývojársku konzolu v prehliadači (F12)
2. Prejdite na záložku "Console"
3. Skontrolujte chybové hlásenia, ktoré môžu obsahovať informácie o chýbajúcich oprávneniach

### 5. Kontrola štruktúry tabuliek

Ak máte problémy s konkrétnou tabuľkou:

1. Prejdite na "Table Editor" v Supabase Dashboard
2. Skontrolujte, či tabuľka existuje a má správnu štruktúru
3. Skontrolujte, či sú správne nastavené cudzie kľúče

## Ďalšie kroky

Ak problémy pretrvávajú aj po nastavení RLS politík:

1. Skontrolujte, či používateľ má platný plán (end_of_current_plan > aktuálny dátum)
2. Skontrolujte, či používateľ má nastavené používateľské meno (username)
3. Skontrolujte, či sú správne nastavené cudzie kľúče v databáze
4. Skontrolujte, či sú správne implementované metódy v službách (services)

## Užitočné odkazy

- [Supabase Row Level Security dokumentácia](https://supabase.com/docs/guides/auth/row-level-security)
- [Supabase Auth dokumentácia](https://supabase.com/docs/guides/auth)
- [Supabase JavaScript klient dokumentácia](https://supabase.com/docs/reference/javascript/introduction)
