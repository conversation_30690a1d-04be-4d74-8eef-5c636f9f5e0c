import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'filterSideQuests',
  standalone: true
})
export class FilterSideQuestsPipe implements PipeTransform {
  transform(quests: any[], isSideQuest: boolean): any[] {
    if (!quests) return [];

    // In our Firebase implementation, we don't have is_side_quest property
    // Side quests are handled separately in the dailyQuest property
    // So we just return all quests when isSideQuest is false
    return quests;
  }
}
