import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { NavigationComponent } from '../../components/navigation/navigation.component';

// Define the WeekDate interface for calendar
interface WeekDate {
  date: string; // YYYY-MM-DD format
  day: number;
  is_today: boolean;
  is_selected: boolean;
  is_future: boolean;
  total_quests: number;
  completed_quests: number;
  completion_percentage: number;
}

@Component({
  selector: 'app-focus',
  templateUrl: './focus.page.html',
  styleUrls: ['./focus.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]
})
export class FocusPage implements OnInit, OnDestroy {
  // Calendar and date
  selectedDate: Date = new Date();
  weekDates: WeekDate[] = [];
  dayNames: string[] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  headerText: string = 'Focus';

  // Timer settings
  focusTime = 25;
  breakTime = 5;
  longBreakTime = 15;
  sessionsBeforeLongBreak = 4;

  // Timer state
  isRunning = false;
  isPaused = false;
  isBreak = false;
  isLongBreak = false;
  currentSession = 1;

  // Timer variables
  timerMinutes = 25;
  timerSeconds = 0;
  timerInterval: any = null;

  // Stats
  completedSessions = 0;
  totalFocusTime = 0;
  currentStreak = 0;

  constructor() {}

  ngOnInit() {
    // Load settings from localStorage
    this.loadSettings();

    // Load stats from localStorage
    this.loadStats();

    // Set initial timer display
    this.resetTimer();

    // Generate calendar dates
    this.generateWeekDates();
  }

  ngOnDestroy() {
    // Clear timer interval when component is destroyed
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }

  loadSettings() {
    const settings = localStorage.getItem('focusSettings');
    if (settings) {
      const parsedSettings = JSON.parse(settings);
      this.focusTime = parsedSettings.focusTime || 25;
      this.breakTime = parsedSettings.breakTime || 5;
      this.longBreakTime = parsedSettings.longBreakTime || 15;
      this.sessionsBeforeLongBreak = parsedSettings.sessionsBeforeLongBreak || 4;
    }
  }

  saveSettings() {
    const settings = {
      focusTime: this.focusTime,
      breakTime: this.breakTime,
      longBreakTime: this.longBreakTime,
      sessionsBeforeLongBreak: this.sessionsBeforeLongBreak
    };

    localStorage.setItem('focusSettings', JSON.stringify(settings));

    // Reset timer with new settings
    this.resetTimer();
  }

  loadStats() {
    const stats = localStorage.getItem('focusStats');
    if (stats) {
      const parsedStats = JSON.parse(stats);
      this.completedSessions = parsedStats.completedSessions || 0;
      this.totalFocusTime = parsedStats.totalFocusTime || 0;
      this.currentStreak = parsedStats.currentStreak || 0;
    }
  }

  saveStats() {
    const stats = {
      completedSessions: this.completedSessions,
      totalFocusTime: this.totalFocusTime,
      currentStreak: this.currentStreak,
      lastSessionDate: new Date().toISOString().split('T')[0]
    };

    localStorage.setItem('focusStats', JSON.stringify(stats));
  }

  startTimer() {
    if (this.isRunning && !this.isPaused) return;

    if (this.isPaused) {
      this.isPaused = false;
    } else {
      this.isRunning = true;
    }

    this.timerInterval = setInterval(() => {
      if (this.timerSeconds === 0) {
        if (this.timerMinutes === 0) {
          // Timer completed
          this.timerCompleted();
        } else {
          // Decrement minutes
          this.timerMinutes--;
          this.timerSeconds = 59;
        }
      } else {
        // Decrement seconds
        this.timerSeconds--;
      }
    }, 1000);
  }

  pauseTimer() {
    if (!this.isRunning) return;

    this.isPaused = true;
    clearInterval(this.timerInterval);
  }

  resetTimer() {
    // Clear existing timer
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    // Reset timer state
    this.isRunning = false;
    this.isPaused = false;

    // Set timer based on current mode
    if (this.isBreak) {
      if (this.isLongBreak) {
        this.timerMinutes = this.longBreakTime;
      } else {
        this.timerMinutes = this.breakTime;
      }
    } else {
      this.timerMinutes = this.focusTime;
    }

    this.timerSeconds = 0;
  }

  timerCompleted() {
    // Clear timer interval
    clearInterval(this.timerInterval);

    // Play notification sound
    this.playNotificationSound();

    if (this.isBreak) {
      // Break completed, start focus session
      this.isBreak = false;
      this.timerMinutes = this.focusTime;
    } else {
      // Focus session completed
      this.completedSessions++;
      this.totalFocusTime += this.focusTime;

      // Update streak
      this.updateStreak();

      // Save stats
      this.saveStats();

      // Determine if next break should be a long break
      if (this.currentSession % this.sessionsBeforeLongBreak === 0) {
        this.isLongBreak = true;
        this.timerMinutes = this.longBreakTime;
      } else {
        this.isLongBreak = false;
        this.timerMinutes = this.breakTime;
      }

      this.isBreak = true;
      this.currentSession++;
    }

    // Reset timer
    this.timerSeconds = 0;
    this.isRunning = false;
  }

  playNotificationSound() {
    // Play a notification sound
    const audio = new Audio('assets/sounds/notification.mp3');
    audio.play().catch(error => {
      console.error('Error playing notification sound:', error);
    });
  }

  updateStreak() {
    const stats = localStorage.getItem('focusStats');
    if (stats) {
      const parsedStats = JSON.parse(stats);
      const lastSessionDate = parsedStats.lastSessionDate;
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];

      if (lastSessionDate === yesterday) {
        // Consecutive day, increment streak
        this.currentStreak++;
      } else if (lastSessionDate !== today) {
        // Not consecutive, reset streak
        this.currentStreak = 1;
      }
    } else {
      // First session, start streak
      this.currentStreak = 1;
    }
  }

  // Calendar methods
  generateWeekDates() {
    const today = new Date();
    const currentDay = today.getDay() || 7; // Convert Sunday (0) to 7
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - currentDay + 1); // Start from Monday

    this.weekDates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      const dateString = this.formatDate(date);
      const isToday = this.isSameDay(date, today);
      const isSelected = this.isSameDay(date, this.selectedDate);
      const isFuture = date > today;

      this.weekDates.push({
        date: dateString,
        day: date.getDate(),
        is_today: isToday,
        is_selected: isSelected,
        is_future: isFuture,
        total_quests: 0,
        completed_quests: 0,
        completion_percentage: 0
      });
    }
  }

  selectDate(dateString: string) {
    const date = new Date(dateString);
    this.selectedDate = date;

    // Update selected state in weekDates
    this.weekDates.forEach(weekDate => {
      weekDate.is_selected = weekDate.date === dateString;
    });

    this.updateHeaderText();
  }

  changeWeek(direction: number) {
    const firstDate = new Date(this.weekDates[0].date);
    firstDate.setDate(firstDate.getDate() + (direction * 7));

    const currentDay = firstDate.getDay() || 7; // Convert Sunday (0) to 7
    const startDate = new Date(firstDate);
    startDate.setDate(firstDate.getDate() - currentDay + 1); // Start from Monday

    this.weekDates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      const dateString = this.formatDate(date);
      const today = new Date();
      const isToday = this.isSameDay(date, today);
      const isSelected = this.isSameDay(date, this.selectedDate);
      const isFuture = date > today;

      this.weekDates.push({
        date: dateString,
        day: date.getDate(),
        is_today: isToday,
        is_selected: isSelected,
        is_future: isFuture,
        total_quests: 0,
        completed_quests: 0,
        completion_percentage: 0
      });
    }
  }

  updateHeaderText() {
    const today = new Date();
    if (this.isSameDay(this.selectedDate, today)) {
      this.headerText = 'Focus';
    } else {
      // Format as "Mon, 15 Jan"
      this.headerText = this.selectedDate.toLocaleDateString('en-US', {
        weekday: 'short',
        day: 'numeric',
        month: 'short'
      }) + ' - Focus';
    }
  }

  // Helper methods for date handling
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }
}
