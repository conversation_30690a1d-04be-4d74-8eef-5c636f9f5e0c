-- Fix for the group sidequest trigger
CREATE OR REPLACE FUNCTION public.update_group_sidequest_streak()
 RET<PERSON><PERSON> trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    today_date DATE := CURRENT_DATE;
    yesterday_date DATE := CURRENT_DATE - INTERVAL '1 day';
    old_streak INTEGER;
    was_completed BOOLEAN;
    last_completed_date DATE;
    eligible_members_count INTEGER;
    completed_members_count INTEGER;
    quest_group_id UUID;
BEGIN
    -- Get the group ID
    SELECT gs.group_id INTO quest_group_id
    FROM group_sidequests gs
    WHERE gs.id = NEW.group_quest_id;

    -- Get the current streak, completion status, and last completed date
    SELECT streak, completed, last_completed_date INTO old_streak, was_completed, last_completed_date
    FROM group_sidequests
    WHERE id = NEW.group_quest_id;

    -- Count eligible members (joined at least one day before)
    SELECT COUNT(*) INTO eligible_members_count
    FROM group_members
    WHERE group_members.group_id = quest_group_id
    AND joined_date < CURRENT_DATE;

    -- Count completed members
    SELECT COUNT(*) INTO completed_members_count
    FROM group_sidequest_member_status
    WHERE group_quest_id = NEW.group_quest_id
    AND completed = TRUE;

    RAISE NOTICE 'TRIGGER DEBUG: Status changed for quest %, eligible members: %, completed members: %, was_completed: %, old_streak: %, last_completed_date: %',
        NEW.group_quest_id, eligible_members_count, completed_members_count, was_completed, old_streak, last_completed_date;

    -- Check if all eligible members have completed the quest
    IF completed_members_count = eligible_members_count AND eligible_members_count > 0 THEN
        -- All members have completed the quest

        -- Update the quest as completed
        UPDATE group_sidequests
        SET
            completed = TRUE,
            value_achieved = (
                SELECT goal_value
                FROM group_sidequest_pool gsp
                JOIN group_sidequests gs ON gs.current_quest_id = gsp.id
                WHERE gs.id = NEW.group_quest_id
            ),
            last_completed_date = today_date
        WHERE id = NEW.group_quest_id;

        -- Update streak if it wasn't completed before
        IF NOT was_completed THEN
            -- Check if yesterday was completed to determine streak behavior
            IF last_completed_date = yesterday_date THEN
                -- Yesterday was completed, increment streak
                UPDATE group_sidequests
                SET streak = streak + 1
                WHERE id = NEW.group_quest_id;

                RAISE NOTICE 'TRIGGER DEBUG: Yesterday was completed, incrementing streak from % to %', old_streak, old_streak + 1;
            ELSE
                -- Yesterday was not completed, start new streak
                UPDATE group_sidequests
                SET streak = 1
                WHERE id = NEW.group_quest_id;

                RAISE NOTICE 'TRIGGER DEBUG: Yesterday was not completed, starting new streak';
            END IF;
        ELSE
            RAISE NOTICE 'TRIGGER DEBUG: Quest was already completed, not changing streak';
        END IF;
    ELSE
        -- Not all members have completed the quest

        -- If it was completed before but now it's not, handle streak change
        IF was_completed THEN
            -- Check if last_completed_date is today
            IF last_completed_date = today_date THEN
                -- If uncompleting today's quest, revert to previous day's state
                IF old_streak > 1 THEN
                    -- Decrement streak and set last_completed_date to yesterday
                    UPDATE group_sidequests
                    SET
                        streak = old_streak - 1,
                        completed = FALSE,
                        last_completed_date = yesterday_date
                    WHERE id = NEW.group_quest_id;

                    RAISE NOTICE 'TRIGGER DEBUG: Uncompleting today, reverting to streak: % and setting last_completed_date to yesterday', old_streak - 1;
                ELSE
                    -- Reset streak to 0 and set last_completed_date to yesterday
                    UPDATE group_sidequests
                    SET
                        streak = 0,
                        completed = FALSE,
                        last_completed_date = yesterday_date
                    WHERE id = NEW.group_quest_id;

                    RAISE NOTICE 'TRIGGER DEBUG: Uncompleting today, resetting streak to 0';
                END IF;
            ELSE
                -- Just mark as not completed without changing the streak
                UPDATE group_sidequests
                SET completed = FALSE
                WHERE id = NEW.group_quest_id;

                RAISE NOTICE 'TRIGGER DEBUG: Last completed date is not today, keeping streak at: %', old_streak;
            END IF;
        ELSE
            -- Just mark as not completed without changing the streak
            UPDATE group_sidequests
            SET completed = FALSE
            WHERE id = NEW.group_quest_id;

            RAISE NOTICE 'TRIGGER DEBUG: Marked quest as not completed without changing streak';
        END IF;
    END IF;

    RETURN NEW;
END;
$function$;

-- Drop the trigger if it exists and recreate it
DROP TRIGGER IF EXISTS update_group_sidequest_streak_trigger ON group_sidequest_member_status;

-- Create the trigger on the group_sidequest_member_status table
CREATE TRIGGER update_group_sidequest_streak_trigger
AFTER INSERT OR UPDATE
ON group_sidequest_member_status
FOR EACH ROW
EXECUTE FUNCTION update_group_sidequest_streak();

-- Function to check for missed days and reset streak if needed
CREATE OR REPLACE FUNCTION public.check_missed_days_group_sidequest(sidequest_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    today_date DATE := CURRENT_DATE;
    yesterday_date DATE := today_date - INTERVAL '1 day';
    sidequest_record RECORD;
BEGIN
    -- Get the sidequest
    SELECT * INTO sidequest_record
    FROM group_sidequests
    WHERE id = sidequest_id;

    IF sidequest_record IS NULL THEN
        RAISE EXCEPTION 'Sidequest record not found';
    END IF;

    RAISE NOTICE 'CHECK_MISSED_DAYS: Checking sidequest % with last_completed_date %',
        sidequest_id, sidequest_record.last_completed_date;

    -- Check if a day was missed (last_completed_date is not yesterday or today)
    -- This matches the individual sidequest logic in sidequest-supabase.service.ts
    IF sidequest_record.last_completed_date IS NULL OR
       (sidequest_record.last_completed_date::date != yesterday_date::date AND
        sidequest_record.last_completed_date::date != today_date::date) THEN

        -- Reset streak to 0
        UPDATE group_sidequests
        SET
            streak = 0,
            completed = FALSE,
            last_completed_date = yesterday_date  -- Set to yesterday instead of NULL
        WHERE id = sidequest_id;

        RAISE NOTICE 'CHECK_MISSED_DAYS: Reset streak for sidequest % due to missed days', sidequest_id;
    ELSE
        RAISE NOTICE 'CHECK_MISSED_DAYS: No need to reset streak for sidequest %', sidequest_id;
    END IF;
END;
$function$;

-- Function to reset member statuses for a new day
CREATE OR REPLACE FUNCTION public.reset_group_sidequest_member_statuses(sidequest_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    today_date DATE := CURRENT_DATE;
BEGIN
    -- Reset all member statuses for the new day
    UPDATE group_sidequest_member_status
    SET
        completed = FALSE,
        value_achieved = 0,
        last_updated = today_date
    WHERE group_quest_id = sidequest_id;

    RAISE NOTICE 'RESET_MEMBER_STATUSES: Reset all member statuses for sidequest %', sidequest_id;
END;
$function$;

-- Function to check and reset group sidequest if needed (called when page loads)
CREATE OR REPLACE FUNCTION public.check_and_reset_group_sidequest(sidequest_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    sidequest_record RECORD;
    today DATE := CURRENT_DATE;
    yesterday DATE := today - INTERVAL '1 day';
    quest_pool_record RECORD;
    new_quest_id UUID;
BEGIN
    -- Get the sidequest
    SELECT * INTO sidequest_record
    FROM group_sidequests
    WHERE id = sidequest_id;

    IF sidequest_record IS NULL THEN
        RAISE EXCEPTION 'Sidequest record not found';
    END IF;

    -- First check for missed days
    PERFORM check_missed_days_group_sidequest(sidequest_id);

    -- Refresh the sidequest record after checking for missed days
    SELECT * INTO sidequest_record
    FROM group_sidequests
    WHERE id = sidequest_id;

    -- Check if the quest needs to be reset for a new day
    IF sidequest_record.date_assigned::date != today::date THEN
        -- It's a new day, assign a new quest
        RAISE NOTICE 'CHECK_AND_RESET: Sidequest % needs to be reset for a new day', sidequest_id;

        -- Select a new quest from the pool
        SELECT id INTO new_quest_id
        FROM group_sidequest_pool
        WHERE active = TRUE
        AND id != sidequest_record.current_quest_id
        ORDER BY RANDOM()
        LIMIT 1;

        -- If no other quest is available, keep the current one
        IF new_quest_id IS NULL THEN
            new_quest_id := sidequest_record.current_quest_id;
        END IF;

        -- Get the new quest details
        SELECT * INTO quest_pool_record
        FROM group_sidequest_pool
        WHERE id = new_quest_id;

        -- Update the group sidequest
        UPDATE group_sidequests
        SET
            date_assigned = today,
            current_quest_id = new_quest_id,
            completed = FALSE,
            value_achieved = 0,
            category = quest_pool_record.category
        WHERE id = sidequest_id;

        -- Reset all member statuses for the new day
        PERFORM reset_group_sidequest_member_statuses(sidequest_id);

        RAISE NOTICE 'CHECK_AND_RESET: Reset sidequest % for a new day with new quest %', sidequest_id, new_quest_id;
    END IF;
END;
$function$;

-- Drop the existing function first
DROP FUNCTION IF EXISTS public.toggle_group_sidequest_member_completion(status_id uuid);

-- Create or replace the toggle_group_sidequest_member_completion function
CREATE OR REPLACE FUNCTION public.toggle_group_sidequest_member_completion(status_id uuid)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    status_record RECORD;
    quest_record RECORD;
    quest_pool_record RECORD;
    was_completed BOOLEAN;
    new_completed BOOLEAN;
    new_value_achieved INTEGER;
    eligible_members_count INTEGER;
    completed_members_count INTEGER;
    result_json json;
    today_date DATE := CURRENT_DATE;
BEGIN
    -- Get the current status
    SELECT * INTO status_record
    FROM group_sidequest_member_status
    WHERE id = status_id;

    IF status_record IS NULL THEN
        RAISE EXCEPTION 'Status record not found';
    END IF;

    -- Store previous state
    was_completed := status_record.completed;

    -- Get the quest details
    SELECT * INTO quest_record
    FROM group_sidequests
    WHERE id = status_record.group_quest_id;

    IF quest_record IS NULL THEN
        RAISE EXCEPTION 'Quest record not found';
    END IF;

    -- Get the quest pool details
    SELECT * INTO quest_pool_record
    FROM group_sidequest_pool
    WHERE id = quest_record.current_quest_id;

    IF quest_pool_record IS NULL THEN
        RAISE EXCEPTION 'Quest pool record not found';
    END IF;

    -- Toggle completion
    new_completed := NOT status_record.completed;

    -- Set value achieved based on completion status
    IF new_completed THEN
        new_value_achieved := quest_pool_record.goal_value;
    ELSE
        new_value_achieved := 0;
    END IF;

    -- Update the status
    UPDATE group_sidequest_member_status
    SET
        completed = new_completed,
        value_achieved = new_value_achieved,
        last_updated = today_date
    WHERE id = status_id;

    -- Count eligible members (joined at least one day before)
    SELECT COUNT(*) INTO eligible_members_count
    FROM group_members
    WHERE group_id = quest_record.group_id
    AND joined_date < today_date;

    -- Count completed members
    SELECT COUNT(*) INTO completed_members_count
    FROM group_sidequest_member_status
    WHERE group_quest_id = status_record.group_quest_id
    AND completed = TRUE;

    -- Create a JSON result to return
    SELECT json_build_object(
        'id', status_id,
        'group_quest_id', status_record.group_quest_id,
        'member_id', status_record.member_id,
        'completed', new_completed,
        'value_achieved', new_value_achieved,
        'last_updated', today_date,
        'streak', quest_record.streak,
        'completed_members', completed_members_count,
        'total_members', eligible_members_count
    ) INTO result_json;

    -- Return the result
    RETURN result_json;

    -- The trigger will handle updating the group quest completion status
END;
$function$;
