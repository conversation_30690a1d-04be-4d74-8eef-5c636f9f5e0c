{"ast": null, "code": "export class AuthError extends Error {\n  constructor(message, status, code) {\n    super(message);\n    this.__isAuthError = true;\n    this.name = 'AuthError';\n    this.status = status;\n    this.code = code;\n  }\n}\nexport function isAuthError(error) {\n  return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nexport class AuthApiError extends AuthError {\n  constructor(message, status, code) {\n    super(message, status, code);\n    this.name = 'AuthApiError';\n    this.status = status;\n    this.code = code;\n  }\n}\nexport function isAuthApiError(error) {\n  return isAuthError(error) && error.name === 'AuthApiError';\n}\nexport class AuthUnknownError extends AuthError {\n  constructor(message, originalError) {\n    super(message);\n    this.name = 'AuthUnknownError';\n    this.originalError = originalError;\n  }\n}\nexport class CustomAuthError extends AuthError {\n  constructor(message, name, status, code) {\n    super(message, status, code);\n    this.name = name;\n    this.status = status;\n  }\n}\nexport class AuthSessionMissingError extends CustomAuthError {\n  constructor() {\n    super('Auth session missing!', 'AuthSessionMissingError', 400, undefined);\n  }\n}\nexport function isAuthSessionMissingError(error) {\n  return isAuthError(error) && error.name === 'AuthSessionMissingError';\n}\nexport class AuthInvalidTokenResponseError extends CustomAuthError {\n  constructor() {\n    super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined);\n  }\n}\nexport class AuthInvalidCredentialsError extends CustomAuthError {\n  constructor(message) {\n    super(message, 'AuthInvalidCredentialsError', 400, undefined);\n  }\n}\nexport class AuthImplicitGrantRedirectError extends CustomAuthError {\n  constructor(message, details = null) {\n    super(message, 'AuthImplicitGrantRedirectError', 500, undefined);\n    this.details = null;\n    this.details = details;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details\n    };\n  }\n}\nexport function isAuthImplicitGrantRedirectError(error) {\n  return isAuthError(error) && error.name === 'AuthImplicitGrantRedirectError';\n}\nexport class AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n  constructor(message, details = null) {\n    super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined);\n    this.details = null;\n    this.details = details;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details\n    };\n  }\n}\nexport class AuthRetryableFetchError extends CustomAuthError {\n  constructor(message, status) {\n    super(message, 'AuthRetryableFetchError', status, undefined);\n  }\n}\nexport function isAuthRetryableFetchError(error) {\n  return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nexport class AuthWeakPasswordError extends CustomAuthError {\n  constructor(message, status, reasons) {\n    super(message, 'AuthWeakPasswordError', status, 'weak_password');\n    this.reasons = reasons;\n  }\n}\nexport function isAuthWeakPasswordError(error) {\n  return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}\nexport class AuthInvalidJwtError extends CustomAuthError {\n  constructor(message) {\n    super(message, 'AuthInvalidJwtError', 400, 'invalid_jwt');\n  }\n}\n//# sourceMappingURL=errors.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}