{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { j as clamp } from './helpers-78efeec3.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { createGesture } from './index-39782642.js';\nimport './gesture-controller-314a54f6.js';\nconst createSwipeBackGesture = (el, canStartHand<PERSON>, onStartHandler, onMoveHandler, onEndHandler) => {\n  const win = el.ownerDocument.defaultView;\n  let rtl = isRTL(el);\n  /**\n   * Determine if a gesture is near the edge\n   * of the screen. If true, then the swipe\n   * to go back gesture should proceed.\n   */\n  const isAtEdge = detail => {\n    const threshold = 50;\n    const {\n      startX\n    } = detail;\n    if (rtl) {\n      return startX >= win.innerWidth - threshold;\n    }\n    return startX <= threshold;\n  };\n  const getDeltaX = detail => {\n    return rtl ? -detail.deltaX : detail.deltaX;\n  };\n  const getVelocityX = detail => {\n    return rtl ? -detail.velocityX : detail.velocityX;\n  };\n  const canStart = detail => {\n    /**\n     * The user's locale can change mid-session,\n     * so we need to check text direction at\n     * the beginning of every gesture.\n     */\n    rtl = isRTL(el);\n    return isAtEdge(detail) && canStartHandler();\n  };\n  const onMove = detail => {\n    // set the transition animation's progress\n    const delta = getDeltaX(detail);\n    const stepValue = delta / win.innerWidth;\n    onMoveHandler(stepValue);\n  };\n  const onEnd = detail => {\n    // the swipe back gesture has ended\n    const delta = getDeltaX(detail);\n    const width = win.innerWidth;\n    const stepValue = delta / width;\n    const velocity = getVelocityX(detail);\n    const z = width / 2.0;\n    const shouldComplete = velocity >= 0 && (velocity > 0.2 || delta > z);\n    const missing = shouldComplete ? 1 - stepValue : stepValue;\n    const missingDistance = missing * width;\n    let realDur = 0;\n    if (missingDistance > 5) {\n      const dur = missingDistance / Math.abs(velocity);\n      realDur = Math.min(dur, 540);\n    }\n    onEndHandler(shouldComplete, stepValue <= 0 ? 0.01 : clamp(0, stepValue, 0.9999), realDur);\n  };\n  return createGesture({\n    el,\n    gestureName: 'goback-swipe',\n    /**\n     * Swipe to go back should have priority over other horizontal swipe\n     * gestures. These gestures have a priority of 100 which is why 101 was chosen here.\n     */\n    gesturePriority: 101,\n    threshold: 10,\n    canStart,\n    onStart: onStartHandler,\n    onMove,\n    onEnd\n  });\n};\nexport { createSwipeBackGesture };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}