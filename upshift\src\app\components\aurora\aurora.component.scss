.aurora-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    z-index: -1;
    filter: blur(12px);
    background:
        radial-gradient(ellipse 40% 25% at 0% 80%, rgba(65, 105, 225, 0.25) 0%, transparent 70%),
        radial-gradient(ellipse 35% 20% at 15% 65%, rgba(135, 206, 250, 0.2) 0%, transparent 60%),
        radial-gradient(ellipse 45% 30% at 35% 45%, rgba(64, 224, 208, 0.18) 0%, transparent 65%),
        radial-gradient(ellipse 50% 25% at 60% 35%, rgba(82, 119, 232, 0.15) 0%, transparent 70%),
        radial-gradient(ellipse 40% 20% at 85% 30%, rgba(135, 206, 250, 0.12) 0%, transparent 60%);
    animation: aurora-curve 15s ease-in-out infinite alternate;
}

.aurora-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    filter: blur(20px);
    background:
        radial-gradient(ellipse 30% 15% at 5% 75%, rgba(64, 224, 208, 0.15) 0%, transparent 80%),
        radial-gradient(ellipse 35% 18% at 25% 55%, rgba(65, 105, 225, 0.12) 0%, transparent 75%),
        radial-gradient(ellipse 40% 22% at 50% 40%, rgba(135, 206, 250, 0.1) 0%, transparent 70%),
        radial-gradient(ellipse 35% 16% at 75% 32%, rgba(64, 224, 208, 0.08) 0%, transparent 65%);
    animation: aurora-flow 20s ease-in-out infinite alternate-reverse;
}

.aurora-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    filter: blur(30px);
    background:
        radial-gradient(ellipse 60% 35% at 10% 70%, rgba(135, 206, 250, 0.08) 0%, transparent 80%),
        radial-gradient(ellipse 55% 30% at 40% 45%, rgba(82, 119, 232, 0.06) 0%, transparent 75%),
        radial-gradient(ellipse 50% 25% at 70% 35%, rgba(64, 224, 208, 0.05) 0%, transparent 70%);
    animation: aurora-glow 25s ease-in-out infinite alternate;
}