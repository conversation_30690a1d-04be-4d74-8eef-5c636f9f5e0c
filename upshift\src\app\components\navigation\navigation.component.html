<nav class="main-navigation">
    <div class="nav-container">
        <a [routerLink]="['/today']" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" class="nav-item">
            <span class="nav-icon">📋</span>
            <span class="nav-text">Quests</span>
        </a>
        <a [routerLink]="['/groups']" routerLinkActive="active" class="nav-item">
            <span class="nav-icon">👥</span>
            <span class="nav-text">Groups</span>
        </a>
        <a [routerLink]="['/goals']" routerLinkActive="active" class="nav-item">
            <span class="nav-icon">🎯</span>
            <span class="nav-text">Goals</span>
        </a>
        <a [routerLink]="['/time-tracker']" routerLinkActive="active" class="nav-item">
            <span class="nav-icon">⏰</span>
            <span class="nav-text">Time</span>
        </a>
        <a [routerLink]="['/focus']" routerLinkActive="active" class="nav-item">
            <span class="nav-icon">🔥</span>
            <span class="nav-text">Focus</span>
        </a>
        <a [routerLink]="['/profile']" routerLinkActive="active" class="nav-item">
            <span class="nav-icon">👤</span>
            <span class="nav-text">Profile</span>
        </a>
        <a [routerLink]="['/friends']" routerLinkActive="active" class="nav-item">
            <span class="nav-icon">👥</span>
            <span class="nav-text">Friends</span>
        </a>
        <a *ngIf="isAdmin" [routerLink]="['/import-sidequests']" routerLinkActive="active" class="nav-item">
            <span class="nav-icon">📥</span>
            <span class="nav-text">Import</span>
        </a>
        <a *ngIf="isAdmin" [routerLink]="['/admin']" routerLinkActive="active" class="nav-item admin-link">
            <span class="nav-icon">⚙️</span>
            <span class="nav-text">Admin</span>
        </a>
    </div>
</nav>
