import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FriendService } from '../../../services/friend.service';
import { UserService } from '../../../services/user.service';
import { User } from '../../../models/user.model';
import { Observable, Subscription, map, of, switchMap, take, firstValueFrom } from 'rxjs';
import { NavigationComponent } from '../../../components/navigation/navigation.component';
import { SupabaseService } from '../../../services/supabase.service';
import { XpService, EntityType } from '../../../services/xp.service';

interface CategoryDisplay {
  name: string;
  icon: string;
  color: string;
  current_xp: number;
  required_xp: number;
  progress: number;
}

@Component({
  selector: 'app-friend-profile',
  templateUrl: './friend-profile.page.html',
  styleUrls: ['./friend-profile.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]
})
export class FriendProfilePage implements OnInit {
  // User data
  currentUserId: string | null = null;
  friend: User | null = null;
  friendId: string | null = null;

  // XP categories
  categories: CategoryDisplay[] = [];
  nextLevel = 0;

  private supabaseService = inject(SupabaseService);
  private friendService = inject(FriendService);
  private userService = inject(UserService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private xpService = inject(XpService);

  constructor() {}

  ngOnInit() {
    // Get the friend ID from the route
    this.route.paramMap.pipe(
      take(1),
      switchMap(params => {
        this.friendId = params.get('id');

        // Get the current user
        return this.supabaseService.currentUser$;
      }),
      switchMap(authUser => {
        if (authUser) {
          this.currentUserId = authUser.id;

          // Check if the friend ID is valid
          if (this.friendId) {
            // Get the friend's profile
            return this.userService.getUser(this.friendId);
          }
        }
        return of(null);
      })
    ).subscribe(friend => {
      if (friend) {
        this.friend = friend;
        this.calculateXpProgress();
      } else {
        // Friend not found, navigate back to friends list
        this.router.navigate(['/friends']);
      }
    });
  }

  calculateXpProgress() {
    if (!this.friend) return;

    // Use the XP service to calculate the progress
    this.xpService.calculateXpProgress(this.friend, EntityType.USER).subscribe(result => {
      if (result) {
        this.categories = result.categories;
        this.nextLevel = result.next_level;
        console.log('Friend XP progress calculated:', this.categories);
        console.log('Friend next level:', this.nextLevel);
      }
    });
  }

  async removeFriend() {
    if (!this.currentUserId || !this.friendId) return;

    if (confirm('Are you sure you want to remove this friend?')) {
      try {
        await this.friendService.removeFriend(this.currentUserId, this.friendId);
        // Navigate back to friends list
        this.router.navigate(['/friends']);
      } catch (error) {
        console.error('Error removing friend:', error);
        alert('Failed to remove friend. Please try again.');
      }
    }
  }
  goBack() {
    // Navigate back to the previous page
    window.history.back();
  }
}
