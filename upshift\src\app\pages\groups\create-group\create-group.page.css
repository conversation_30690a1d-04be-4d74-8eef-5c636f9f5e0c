@charset "UTF-8";
/* Exact CSS from Django template */
:root {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --input-bg: #1C1C1E;
  --input-border: #2C2C2E;
  --card-bg: #1C1C1E;
  --border-color: #2C2C2E;
  --danger-color: #FF3B30;
  --success-color: #30D158;
}

:host {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
  display: block;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.back-link {
  display: inline-block;
  margin-bottom: 20px;
  color: var(--secondary-text);
  text-decoration: none;
  font-size: 14px;
}

.back-link:hover {
  color: var(--text-color);
}

.messages {
  margin-bottom: 20px;
}

.message {
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 10px;
}

.message.error {
  background-color: rgba(255, 59, 48, 0.1);
  border: 1px solid var(--danger-color);
  color: var(--danger-color);
}

.group-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 10px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.input-group {
  display: flex;
  gap: 10px;
}

label {
  font-size: 14px;
  margin-bottom: 6px;
  color: var(--secondary-text);
}

input[type=text], select {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  padding: 10px;
  border-radius: 6px;
  color: var(--text-color);
  font-size: 14px;
  width: 100%;
  box-sizing: border-box;
}

/* Oprava pre select box a jeho options */
select {
  -webkit-appearance: auto;
     -moz-appearance: auto;
          appearance: auto; /* Použije natívny vzhľad select boxu */
}

select option {
  background-color: var(--input-bg);
  color: var(--text-color);
  padding: 8px;
}

.emoji input {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  padding: 10px;
  border-radius: 6px;
  color: var(--text-color);
  font-size: 14px;
  width: 40px;
  text-align: center;
}

input[type=text]::-moz-placeholder {
  color: var(--secondary-text);
}

input[type=text]::placeholder {
  color: var(--secondary-text);
}

.help-text {
  font-size: 12px;
  color: var(--secondary-text);
  margin-top: 4px;
}

.btn {
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
}

.btn.primary {
  background-color: var(--accent-color);
  color: white;
}

.btn.primary:hover {
  background-color: #3a5fcb;
}

.btn.primary:disabled {
  background-color: #2a3a6b;
  cursor: not-allowed;
}

.full-width {
  width: 100%;
}/*# sourceMappingURL=create-group.page.css.map */