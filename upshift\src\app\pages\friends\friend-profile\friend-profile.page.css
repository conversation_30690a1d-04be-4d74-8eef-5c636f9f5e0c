/* Exact CSS from Django template */
:root {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --quest-bg: #1C1C1E;
  --quest-border: #2C2C2E;
  --active-date: #4169E1;
  --inactive-date: #2C2C2E;
  --card-bg: #1C1C1E;
  --border-color: #2C2C2E;
  --bg-color: #0C0C0F;
  --danger-color: #FF3B30;
}

:host {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
  display: block;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.back-link {
  display: inline-block;
  color: var(--secondary-text);
  text-decoration: none;
  margin-bottom: 20px;
  font-size: 14px;
}

.back-link:hover {
  color: var(--text-color);
}

.profile-container {
  padding: 20px;
  margin-bottom: 80px;
}

.profile-header {
  display: flex;
  align-items: center;
}

.profile-picture {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin-right: 20px;
  overflow: hidden;
}

.profile-picture img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.profile-username {
  font-size: 16px;
  color: var(--secondary-text);
  margin-bottom: 5px;
}

.profile-level {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.level-badge {
  background-color: var(--accent-color);
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 14px;
  font-weight: 600;
  margin-right: 10px;
}

.profile-title {
  font-size: 16px;
  color: var(--accent-color);
}

.button-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 10px;
}

.back-button {
  display: inline-block;
  background-color: var(--card-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 16px;
  text-decoration: none;
  text-align: center;
}

.remove-button {
  display: inline-block;
  background-color: var(--danger-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 16px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
}

.xp-section {
  margin-top: 30px;
}

.xp-section h2 {
  font-size: 20px;
  margin-bottom: 20px;
}

.category-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.category-icon {
  font-size: 24px;
  margin-right: 10px;
}

.category-name {
  font-size: 18px;
  font-weight: 600;
}

.progress-container {
  height: 10px;
  background-color: var(--bg-color);
  border-radius: 5px;
  margin-bottom: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 5px;
}

.xp-text {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: var(--secondary-text);
}

.next-level-info {
  text-align: center;
  margin-top: 30px;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 12px;
}

.next-level-text {
  font-size: 16px;
  margin-bottom: 10px;
}

.next-level-requirements {
  font-size: 14px;
  color: var(--secondary-text);
}

.profile-bio {
  font-size: 14px;
  color: var(--secondary-text);
  margin-bottom: 5px;
  font-style: italic;
  max-width: 300px;
}

.profile-bio-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* Navigation Styles */
.main-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #121212;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  z-index: 1000;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #888;
  padding: 5px 0;
  transition: color 0.2s ease;
  width: 20%;
}

.nav-item:hover {
  color: #fff;
}

.nav-item.active {
  color: #4D7BFF;
  position: relative;
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #4D7BFF;
  border-radius: 50%;
}

.nav-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
}

/* Adjust container padding to account for navigation bar */
.container {
  padding-bottom: 120px !important;
}/*# sourceMappingURL=friend-profile.page.css.map */