import { Component, inject, runInInjectionContext, EnvironmentInjector } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { PreferencesService } from '../services/preferences.service';
import { SupabaseService } from '../services/supabase.service';

type PlanKey = 'monthly' | 'yearly' | 'discountYearly';

@Component({
  selector: 'app-plan',
  standalone: true,
  imports: [IonicModule, CommonModule, RouterModule],
  templateUrl: './pricing.page.html',
  styleUrls: ['./pricing.page.scss'],
})
export class PricingPage {
  private router = inject(Router);
  private injector = inject(EnvironmentInjector);
  private preferencesService = inject(PreferencesService);
  private supabaseService = inject(SupabaseService);
  private authSubscription: any;
  private isRedirecting = false; // Flag to prevent multiple redirects
  selectedPlan: PlanKey | null = null;

  // Track if affiliate code is used
  hasAffiliateDiscount = false;
  affiliateCode = '';
  discountPercentage = 5; // 5% discount

  PRICING: Record<PlanKey, {
    label: string;
    displayPrice: string;
    originalPrice: number;
    priceId: string;
    planType: string;
    badge?: boolean;
    badgeText?: string;
  }> = {
    monthly: {
      label: 'Monthly',
      displayPrice: '€9.99/mo',
      originalPrice: 9.99,
      priceId: 'price_1RDOm4EB7UFpNfHGQIndEtcK',
      planType: 'monthly',
      badge: false,
      badgeText: 'Best Value',
    },
    yearly: {
      label: 'Yearly',
      displayPrice: '€2.99/mo',
      originalPrice: 2.99,
      priceId: 'price_1RDOmhEB7UFpNfHGa4QL9P8g',
      planType: 'yearly',
      badge: true,
      badgeText: 'Best Value',
    },
    discountYearly: {
      label: 'Yearly Discount',
      displayPrice: '€1.99/mo',
      originalPrice: 1.99,
      priceId: 'price_1RDPIVEB7UFpNfHGNplAzv3l',
      planType: 'yearly',
      badge: true,
      badgeText: 'Best Value',
    }
  };

  public pageContent = [
    {
      icon: 'lock-open',
      title: 'Today',
      text:'Unlock all app features, like group challenges, side quests and more.'
    },
    {
      icon: 'barbell',
      title: 'In 1 month',
      text:'You will notice a difference in your productivity and focus.'
    },
    {
      icon: 'diamond',
      title: 'In 365 days',
      text:'You will be a different person, with consistent routine, better focus and more energy.'
    }
  ];

  constructor() {
    console.log('✅ Pricing page component loaded');
  }

  async ionViewWillEnter() {
    try {
      // Check if an affiliate code is used
      const { value: affiliateCode } = await this.preferencesService.get('affiliate_code_used');

      if (affiliateCode) {
        this.hasAffiliateDiscount = true;
        this.affiliateCode = affiliateCode;
        console.log('Pricing: Affiliate code used:', affiliateCode);

        // Apply discount to all plans
        this.applyAffiliateDiscount();
      } else {
        console.log('Pricing: No affiliate code used');
      }

      // Use our PreferencesService instead of direct Capacitor Preferences
      const { value: onboarding } = await this.preferencesService.get('onboarding_complete');

      runInInjectionContext(this.injector, () => {
        this.authSubscription = this.supabaseService.currentUser$.subscribe(async (user) => {
          if (!user) return;
          if (!onboarding) {
            this.router.navigateByUrl('/onboarding');
            return;
          }

          try {
            runInInjectionContext(this.injector, async () => {
              // Check if user has an ID
              if (!user.id) {
                console.error('Pricing: User ID is missing, cannot fetch user document');
                return;
              }

              console.log('Pricing: Looking up user document for ID:', user.id);

              // Get user data from Supabase
              const { data: userData, error } = await this.supabaseService.getClient()
                .from('profiles')
                .select('*')
                .eq('id', user.id)
                .single();

              if (error) {
                console.error('Pricing: Error fetching user data:', error);
                return;
              }

              if (!userData) {
                console.log('Pricing: No user document found with ID:', user.id);
                return;
              }

              console.log('Pricing: User data:', userData);

              // Get end date
              let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
              const username = userData.username;
              const name = userData.name;

              // Add detailed logging to debug the date comparison
              console.log('Pricing: End date:', endDate);
              console.log('Pricing: Current date:', new Date());
              console.log('Pricing: Username:', username);
              console.log('Pricing: Name:', name);

              // Compare dates properly
              let isValidPlan = false;
              if (endDate instanceof Date) {
                isValidPlan = endDate > new Date();
                console.log('Pricing: Plan valid until:', endDate);
                console.log('Pricing: Is plan still valid?', isValidPlan);
              } else {
                console.log('Pricing: No valid end date found');
              }

              if (isValidPlan) {
                // Prevent multiple redirects
                if (this.isRedirecting) {
                  console.log('Pricing: Already redirecting, skipping additional redirect');
                  return;
                }

                this.isRedirecting = true;

                if (username) {
                  console.log('Pricing: User has valid plan and username, redirecting to home');
                  // Add a small delay to prevent immediate redirection loops
                  setTimeout(() => {
                    this.router.navigateByUrl('/today');
                    // Reset the flag after a longer delay
                    setTimeout(() => {
                      this.isRedirecting = false;
                    }, 2000);
                  }, 500);
                } else {
                  console.log('Pricing: User has valid plan but no username, redirecting to signup-step3');
                  setTimeout(() => {
                    this.router.navigateByUrl('/signup-step3');
                    // Reset the flag after a longer delay
                    setTimeout(() => {
                      this.isRedirecting = false;
                    }, 2000);
                  }, 500);
                }
              } else {
                console.log('Pricing: User has no valid plan or plan has expired');
                // Stay on pricing page
              }
            });
          } catch (error) {
            console.error('Error checking user data:', error);
          }
        });
      });
    } catch (error) {
      console.error('Error getting preferences:', error);
      // Continue without preferences - user will need to complete onboarding again
    }
  }

  ionViewWillLeave() {
    // Clean up the auth subscription when leaving the page
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  /**
   * Apply affiliate discount to all pricing plans
   */
  private applyAffiliateDiscount() {
    // Calculate discounted prices for all plans
    for (const key of Object.keys(this.PRICING) as PlanKey[]) {
      const plan = this.PRICING[key];
      const originalPrice = plan.originalPrice;
      const discountedPrice = originalPrice * (1 - this.discountPercentage / 100);

      // Format the discounted price with 2 decimal places
      const formattedPrice = discountedPrice.toFixed(2);

      // Update the display price
      plan.displayPrice = `€${formattedPrice}/mo`;

      // Update the badge text to show the discount
      if (plan.badge) {
        plan.badgeText = `${this.discountPercentage}% Off`;
      } else {
        plan.badge = true;
        plan.badgeText = `${this.discountPercentage}% Off`;
      }

      console.log(`Pricing: Applied ${this.discountPercentage}% discount to ${key} plan: ${originalPrice} -> ${formattedPrice}`);
    }
  }

  getPricingValue(key: string) {
    return this.PRICING[key as PlanKey];
  }

  setSelectedPlan(key: string) {
    this.selectedPlan = key as PlanKey;
  }

  processPayment() {
    this.router.navigate(['/rating']);
  }
}
