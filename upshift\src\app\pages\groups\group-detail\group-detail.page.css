/* Exact CSS from Django template */
:root {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --quest-bg: #1C1C1E;
  --quest-border: #2C2C2E;
  --active-date: #4169E1;
  --inactive-date: #2C2C2E;
  --card-bg: #1C1C1E;
  --border-color: #2C2C2E;
  --bg-color: #0C0C0F;
  --danger-color: #FF3B30;
}

:host {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
  display: block;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.back-link {
  display: inline-block;
  color: var(--secondary-text);
  text-decoration: none;
  margin-bottom: 20px;
  font-size: 14px;
}

.back-link:hover {
  color: var(--text-color);
}

.group-info {
  margin-bottom: 80px;
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.group-icon {
  font-size: 32px;
  margin-right: 15px;
  width: 60px;
  height: 60px;
  background-color: var(--card-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.group-level {
  background-color: var(--accent-color);
  color: white;
  border-radius: 12px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 600;
}

.xp-section {
  margin-bottom: 30px;
}

h2 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
}

.category-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.category-icon {
  font-size: 24px;
  margin-right: 10px;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
}

.progress-container {
  height: 10px;
  background-color: var(--bg-color);
  border-radius: 5px;
  margin-bottom: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 5px;
}

.xp-text {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: var(--secondary-text);
}

.next-level-info {
  text-align: center;
  margin-top: 20px;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 12px;
}

.next-level-text {
  font-size: 16px;
  margin-bottom: 5px;
}

.next-level-requirements {
  font-size: 14px;
  color: var(--secondary-text);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.member-count {
  background-color: var(--card-bg);
  color: var(--text-color);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.members-section {
  margin-bottom: 30px;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.member-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 15px;
  overflow: hidden;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.member-info {
  flex-grow: 1;
}

.member-name {
  font-size: 16px;
  font-weight: 600;
}

.member-username {
  font-size: 14px;
  color: var(--secondary-text);
}

.member-role {
  font-size: 12px;
  color: var(--accent-color);
  font-weight: 600;
}

.quests-section {
  margin-bottom: 30px;
}

.add-quest-btn {
  background-color: var(--accent-color);
  color: white;
  border-radius: 20px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
}

.quests-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.quest-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: flex-start;
}

.quest-icon {
  font-size: 24px;
  margin-right: 15px;
  width: 40px;
  height: 40px;
  background-color: var(--bg-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.quest-info {
  flex-grow: 1;
}

.quest-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.quest-info p {
  font-size: 14px;
  color: var(--secondary-text);
  margin-bottom: 10px;
}

.quest-progress {
  margin-top: 10px;
}

.progress-text {
  font-size: 12px;
  color: var(--secondary-text);
  margin-top: 5px;
}

.quest-streak {
  font-size: 14px;
  color: var(--accent-color);
  font-weight: 600;
  margin-left: 10px;
}

.no-quests {
  text-align: center;
  padding: 20px;
  background-color: var(--card-bg);
  border-radius: 12px;
  color: var(--secondary-text);
}

.side-quest-section {
  margin-bottom: 30px;
}

.generate-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
}

.side-quest-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: flex-start;
}

.group-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.settings-btn, .invite-btn {
  background-color: var(--card-bg);
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  width: 100%;
}

.invitation-code {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  width: 100%;
}

.code-display {
  background-color: var(--bg-color);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 2px;
  margin-bottom: 10px;
}

.code-info {
  font-size: 14px;
  color: var(--secondary-text);
  text-align: center;
}

/* Navigation Styles */
.main-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #121212;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  z-index: 1000;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #888;
  padding: 5px 0;
  transition: color 0.2s ease;
  width: 20%;
}

.nav-item:hover {
  color: #fff;
}

.nav-item.active {
  color: #4D7BFF;
  position: relative;
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #4D7BFF;
  border-radius: 50%;
}

.nav-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
}

/* Adjust container padding to account for navigation bar */
.container {
  padding-bottom: 120px !important;
}/*# sourceMappingURL=group-detail.page.css.map */