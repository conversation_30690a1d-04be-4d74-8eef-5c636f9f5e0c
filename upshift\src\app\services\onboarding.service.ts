import { Injectable } from '@angular/core';
import { BehaviorSubject } from "rxjs";
import {Router} from "@angular/router";
import { Preferences } from '@capacitor/preferences';

export interface Answer {
  id: number;
  answer: string;
}

export interface Question {
  id: number;
  type: string;
  question: string;
  answers: Answer[];
}

@Injectable({
  providedIn: 'root'
})
export class OnboardingService {
  private questions: Question[] = [
    {
      id: 1,
      type: 'health',
      question: 'How often do you exercise or move your body intentionally?',
      answers: [
        { id: 0, answer: 'Daily' },
        { id: 1, answer: 'A few times a week' },
        { id: 2, answer: 'Rarely' },
        { id: 3, answer: 'Almost never' }
      ]
    },
    {
      id: 2,
      type: 'health',
      question: 'How healthy is your usual diet?',
      answers: [
        { id: 0, answer: 'Very healthy' },
        { id: 1, answer: 'Somewhat healthy' },
        { id: 2, answer: 'Mostly unhealthy' },
        { id: 3, answer: 'Very unhealthy' }
      ]
    },
    {
      id: 3,
      type: 'health',
      question: 'How good are your sleep habits?',
      answers: [
        { id: 0, answer: 'Consistently good' },
        { id: 1, answer: 'Okay but inconsistent' },
        { id: 2, answer: 'Usually poor' },
        { id: 3, answer: 'Very poor, sleep-deprived' }
      ]
    },
    {
      id: 4,
      type: 'money',
      question: 'How would you describe your relationship with saving money?',
      answers: [
        { id: 0, answer: 'I save regularly' },
        { id: 1, answer: 'I save sometimes' },
        { id: 2, answer: 'I struggle to save' },
        { id: 3, answer: 'I don’t save at all' }
      ]
    },
    {
      id: 5,
      type: 'money',
      question: 'Which best describes your income right now?',
      answers: [
        { id: 0, answer: 'Stable and growing' },
        { id: 1, answer: 'Stable but stagnant' },
        { id: 2, answer: 'Unstable or unpredictable' },
        { id: 3, answer: 'I have no current income' }
      ]
    },
    {
      id: 6,
      type: 'strength',
      question: 'How easily do you stay disciplined on your goals (like studying, fitness, business)?',
      answers: [
        { id: 0, answer: 'Very easily' },
        { id: 1, answer: 'I manage okay but get distracted' },
        { id: 2, answer: 'I struggle to stay disciplined' },
        { id: 3, answer: 'I quit goals very easily' }
      ]
    },
    {
      id: 7,
      type: 'strength',
      question: 'When you face setbacks, how do you react?',
      answers: [
        { id: 0, answer: 'I bounce back fast' },
        { id: 1, answer: 'I bounce back but need some time' },
        { id: 2, answer: 'It takes a long time to recover' },
        { id: 3, answer: 'I often give up completely' }
      ]
    },
    {
      id: 8,
      type: 'knowledge',
      question: 'How often do you actively learn new skills or knowledge?',
      answers: [
        { id: 0, answer: 'Daily' },
        { id: 1, answer: 'Several times a week' },
        { id: 2, answer: 'Rarely' },
        { id: 3, answer: 'Almost never' }
      ]
    },
    {
      id: 9,
      type: 'knowledge',
      question: 'How often do you practice mindfulness, meditation, or journaling?',
      answers: [
        { id: 0, answer: 'Daily' },
        { id: 1, answer: 'A few times a week' },
        { id: 2, answer: 'Rarely' },
        { id: 3, answer: 'Never' }
      ]
    },
    {
      id: 10,
      type: 'knowledge',
      question: 'How often do you reflect on your goals and personal growth?',
      answers: [
        { id: 0, answer: 'Very often' },
        { id: 1, answer: 'Sometimes' },
        { id: 2, answer: 'Rarely' },
        { id: 3, answer: 'Almost never' }
      ]
    },
    {
      id: 11,
      type: 'system',
      question: 'Do you want us to create quests for you based on this questionare?',
      answers: [
        { id: 0, answer: 'Yes, please' },
        { id: 1, answer: 'No, I will create my own' },
      ]
    },
    {
      id: 12,
      type: 'personal',
      question: 'What is your age range?',
      answers: [
        { id: 0, answer: 'Under 18' },
        { id: 1, answer: '18-24' },
        { id: 2, answer: '25-34' },
        { id: 3, answer: '35-44' },
        { id: 4, answer: '45-54' },
        { id: 5, answer: '55-64' },
        { id: 6, answer: '65 or older' }
      ]
    }
  ]


  private currentIndexSubject = new BehaviorSubject<number>(0);
  currentIndex$ = this.currentIndexSubject.asObservable();

  private userAnswersSubject = new BehaviorSubject<string[]>([]);
  userAnswers$ = this.userAnswersSubject.asObservable();

  private quizEndedSubject = new BehaviorSubject<boolean>(false);
  quizEnded$ = this.quizEndedSubject.asObservable();

  constructor(private router: Router) { }

  public getCurrentQuestion(): Question | null {
    const currentIndex = this.currentIndexSubject.getValue();
    if (currentIndex >= 0 && currentIndex < this.questions.length) {
      return this.questions[currentIndex];
    }
    return null;
  }

  public async submitAnswer(answer: string): Promise<void> {
    const answers = [...this.userAnswersSubject.getValue(), answer];
    this.userAnswersSubject.next(answers);

    const nextIndex = this.currentIndexSubject.getValue() + 1;
    if (nextIndex < this.questions.length) {
      this.currentIndexSubject.next(nextIndex);
    } else {
      this.quizEndedSubject.next(true);
      Preferences.set({ key: 'onboarding_complete', value: 'true' });
    }
  }

  public getProgress(): number {
    const totalQuestions = this.questions.length;

    if (this.quizEndedSubject.getValue()) {
      return 1;
    }
    const currentIndex = this.currentIndexSubject.getValue();
    return totalQuestions > 0 ? (currentIndex + 1) / totalQuestions : 0;
  }

  public goToPreviousQuestion(): boolean {
    if (this.quizEndedSubject.getValue()) {
      this.quizEndedSubject.next(false);
      this.getProgress()
      const lastQuestionIndex = this.questions.length - 1;
      this.currentIndexSubject.next(lastQuestionIndex);

      return true;
    }

    const currentIndex = this.currentIndexSubject.getValue();

    if (currentIndex > 0) {
      const answers = this.userAnswersSubject.getValue();
      if (answers.length > 0) {
        const newAnswers = answers.slice(0, -1);
        this.userAnswersSubject.next(newAnswers);
      }

      this.currentIndexSubject.next(currentIndex - 1);
      return true;
    }

    this.router.navigate(['/signup']);
    return false;
  }

  resetQuestionnaire(): void {
    this.currentIndexSubject.next(0);
    this.userAnswersSubject.next([]);
  }
}