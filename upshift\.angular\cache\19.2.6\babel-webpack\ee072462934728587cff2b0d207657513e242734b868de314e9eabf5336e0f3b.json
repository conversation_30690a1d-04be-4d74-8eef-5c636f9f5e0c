{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host } from './index-527b9e34.js';\nimport { b as getIonMode } from './ionic-global-ca86cf32.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nconst avatarIosCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}\";\nconst IonAvatarIosStyle0 = avatarIosCss;\nconst avatarMdCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}\";\nconst IonAvatarMdStyle0 = avatarMdCss;\nconst Avatar = /*#__PURE__*/(() => {\n  let Avatar = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n    }\n    render() {\n      return h(Host, {\n        key: '998217066084f966bf5d356fed85bcbd451f675a',\n        class: getIonMode(this)\n      }, h(\"slot\", {\n        key: '1a6f7c9d4dc6a875f86b5b3cda6d59cb39587f22'\n      }));\n    }\n  };\n  Avatar.style = {\n    ios: IonAvatarIosStyle0,\n    md: IonAvatarMdStyle0\n  };\n  return Avatar;\n})();\nconst badgeIosCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}\";\nconst IonBadgeIosStyle0 = badgeIosCss;\nconst badgeMdCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}\";\nconst IonBadgeMdStyle0 = badgeMdCss;\nconst Badge = /*#__PURE__*/(() => {\n  let Badge = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.color = undefined;\n    }\n    render() {\n      const mode = getIonMode(this);\n      return h(Host, {\n        key: '1a2d39c5deec771a2f2196447627b62a7d4c8389',\n        class: createColorClasses(this.color, {\n          [mode]: true\n        })\n      }, h(\"slot\", {\n        key: 'fc1b6587f1ed24715748eb6785e7fb7a57cdd5cd'\n      }));\n    }\n  };\n  Badge.style = {\n    ios: IonBadgeIosStyle0,\n    md: IonBadgeMdStyle0\n  };\n  return Badge;\n})();\nconst thumbnailCss = \":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}\";\nconst IonThumbnailStyle0 = thumbnailCss;\nconst Thumbnail = /*#__PURE__*/(() => {\n  let Thumbnail = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n    }\n    render() {\n      return h(Host, {\n        key: '7f5fd6c056da2d82feb2c3c33f3e6dec898787f5',\n        class: getIonMode(this)\n      }, h(\"slot\", {\n        key: 'd15fd2b6cdc03777edc1930be95ad838e1b376c8'\n      }));\n    }\n  };\n  Thumbnail.style = IonThumbnailStyle0;\n  return Thumbnail;\n})();\nexport { Avatar as ion_avatar, Badge as ion_badge, Thumbnail as ion_thumbnail };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}