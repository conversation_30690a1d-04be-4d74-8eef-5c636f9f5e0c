ion-content {
  &::part(scroll) {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
  }

  .back-button-container {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 10;

    .back-button {
      --padding-start: 8px;
      --padding-end: 8px;
      --padding-top: 8px;
      --padding-bottom: 8px;
      margin: 0;
      height: auto;

      ion-icon {
        font-size: 24px;
        color: var(--text);
      }
    }
  }

  .social-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
  }

  ion-input {
    --background: transparent !important;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: 8px;
    margin: 8px 0;

    &:focus-within {
      border-color: var(--accent);
      box-shadow: 0 0 0 1px var(--accent);
    }
  }
}

