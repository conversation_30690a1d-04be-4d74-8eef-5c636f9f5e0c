{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { a as addEventListener, c as componentOnReady } from './helpers-78efeec3.js';\nimport { a as printIonError } from './index-738d7504.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-ca86cf32.js';\nimport { s as parseDate, x as getToday, L as getHourCycle, N as getLocalizedDateTime, M as getLocalizedTime } from './data-174ad5e0.js';\nconst datetimeButtonIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:7px;padding-bottom:7px}:host button.ion-activated{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}\";\nconst IonDatetimeButtonIosStyle0 = datetimeButtonIosCss;\nconst datetimeButtonMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}\";\nconst IonDatetimeButtonMdStyle0 = datetimeButtonMdCss;\nconst DatetimeButton = /*#__PURE__*/(() => {\n  let DatetimeButton = class {\n    constructor(hostRef) {\n      var _this = this;\n      registerInstance(this, hostRef);\n      this.datetimeEl = null;\n      this.overlayEl = null;\n      /**\n       * Accepts one or more string values and converts\n       * them to DatetimeParts. This is done so datetime-button\n       * can work with an array internally and not need\n       * to keep checking if the datetime value is `string` or `string[]`.\n       */\n      this.getParsedDateValues = value => {\n        if (value === undefined || value === null) {\n          return [];\n        }\n        if (Array.isArray(value)) {\n          return value;\n        }\n        return [value];\n      };\n      /**\n       * Check the value property on the linked\n       * ion-datetime and then format it according\n       * to the locale specified on ion-datetime.\n       */\n      this.setDateTimeText = () => {\n        var _a, _b, _c, _d, _e;\n        const {\n          datetimeEl,\n          datetimePresentation\n        } = this;\n        if (!datetimeEl) {\n          return;\n        }\n        const {\n          value,\n          locale,\n          formatOptions,\n          hourCycle,\n          preferWheel,\n          multiple,\n          titleSelectedDatesFormatter\n        } = datetimeEl;\n        const parsedValues = this.getParsedDateValues(value);\n        /**\n         * Both ion-datetime and ion-datetime-button default\n         * to today's date and time if no value is set.\n         */\n        const parsedDatetimes = parseDate(parsedValues.length > 0 ? parsedValues : [getToday()]);\n        if (!parsedDatetimes) {\n          return;\n        }\n        /**\n         * If developers incorrectly use multiple=\"true\"\n         * with non \"date\" datetimes, then just select\n         * the first value so the interface does\n         * not appear broken. Datetime will provide a\n         * warning in the console.\n         */\n        const firstParsedDatetime = parsedDatetimes[0];\n        const computedHourCycle = getHourCycle(locale, hourCycle);\n        this.dateText = this.timeText = undefined;\n        switch (datetimePresentation) {\n          case 'date-time':\n          case 'time-date':\n            const dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : {\n              month: 'short',\n              day: 'numeric',\n              year: 'numeric'\n            });\n            const timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n            if (preferWheel) {\n              this.dateText = `${dateText} ${timeText}`;\n            } else {\n              this.dateText = dateText;\n              this.timeText = timeText;\n            }\n            break;\n          case 'date':\n            if (multiple && parsedValues.length !== 1) {\n              let headerText = `${parsedValues.length} days`; // default/fallback for multiple selection\n              if (titleSelectedDatesFormatter !== undefined) {\n                try {\n                  headerText = titleSelectedDatesFormatter(parsedValues);\n                } catch (e) {\n                  printIonError('Exception in provided `titleSelectedDatesFormatter`: ', e);\n                }\n              }\n              this.dateText = headerText;\n            } else {\n              this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _b !== void 0 ? _b : {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric'\n              });\n            }\n            break;\n          case 'time':\n            this.timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n            break;\n          case 'month-year':\n            this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _c !== void 0 ? _c : {\n              month: 'long',\n              year: 'numeric'\n            });\n            break;\n          case 'month':\n            this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _d !== void 0 ? _d : {\n              month: 'long'\n            });\n            break;\n          case 'year':\n            this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_e = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _e !== void 0 ? _e : {\n              year: 'numeric'\n            });\n            break;\n        }\n      };\n      /**\n       * Waits for the ion-datetime to re-render.\n       * This is needed in order to correctly position\n       * a popover relative to the trigger element.\n       */\n      this.waitForDatetimeChanges = /*#__PURE__*/_asyncToGenerator(function* () {\n        const {\n          datetimeEl\n        } = _this;\n        if (!datetimeEl) {\n          return Promise.resolve();\n        }\n        return new Promise(resolve => {\n          addEventListener(datetimeEl, 'ionRender', resolve, {\n            once: true\n          });\n        });\n      });\n      this.handleDateClick = /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (ev) {\n          const {\n            datetimeEl,\n            datetimePresentation\n          } = _this;\n          if (!datetimeEl) {\n            return;\n          }\n          let needsPresentationChange = false;\n          /**\n           * When clicking the date button,\n           * we need to make sure that only a date\n           * picker is displayed. For presentation styles\n           * that display content other than a date picker,\n           * we need to update the presentation style.\n           */\n          switch (datetimePresentation) {\n            case 'date-time':\n            case 'time-date':\n              const needsChange = datetimeEl.presentation !== 'date';\n              /**\n               * The date+time wheel picker\n               * shows date and time together,\n               * so do not adjust the presentation\n               * in that case.\n               */\n              if (!datetimeEl.preferWheel && needsChange) {\n                datetimeEl.presentation = 'date';\n                needsPresentationChange = true;\n              }\n              break;\n          }\n          /**\n           * Track which button was clicked\n           * so that it can have the correct\n           * activated styles applied when\n           * the modal/popover containing\n           * the datetime is opened.\n           */\n          _this.selectedButton = 'date';\n          _this.presentOverlay(ev, needsPresentationChange, _this.dateTargetEl);\n        });\n        return function (_x) {\n          return _ref2.apply(this, arguments);\n        };\n      }();\n      this.handleTimeClick = ev => {\n        const {\n          datetimeEl,\n          datetimePresentation\n        } = this;\n        if (!datetimeEl) {\n          return;\n        }\n        let needsPresentationChange = false;\n        /**\n         * When clicking the time button,\n         * we need to make sure that only a time\n         * picker is displayed. For presentation styles\n         * that display content other than a time picker,\n         * we need to update the presentation style.\n         */\n        switch (datetimePresentation) {\n          case 'date-time':\n          case 'time-date':\n            const needsChange = datetimeEl.presentation !== 'time';\n            if (needsChange) {\n              datetimeEl.presentation = 'time';\n              needsPresentationChange = true;\n            }\n            break;\n        }\n        /**\n         * Track which button was clicked\n         * so that it can have the correct\n         * activated styles applied when\n         * the modal/popover containing\n         * the datetime is opened.\n         */\n        this.selectedButton = 'time';\n        this.presentOverlay(ev, needsPresentationChange, this.timeTargetEl);\n      };\n      /**\n       * If the datetime is presented in an\n       * overlay, the datetime and overlay\n       * should be appropriately sized.\n       * These classes provide default sizing values\n       * that developers can customize.\n       * The goal is to provide an overlay that is\n       * reasonably sized with a datetime that\n       * fills the entire container.\n       */\n      this.presentOverlay = /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (ev, needsPresentationChange, triggerEl) {\n          const {\n            overlayEl\n          } = _this;\n          if (!overlayEl) {\n            return;\n          }\n          if (overlayEl.tagName === 'ION-POPOVER') {\n            /**\n             * When the presentation on datetime changes,\n             * we need to wait for the component to re-render\n             * otherwise the computed width/height of the\n             * popover content will be wrong, causing\n             * the popover to not align with the trigger element.\n             */\n            if (needsPresentationChange) {\n              yield _this.waitForDatetimeChanges();\n            }\n            /**\n             * We pass the trigger button element\n             * so that the popover aligns with the individual\n             * button that was clicked, not the component container.\n             */\n            overlayEl.present(Object.assign(Object.assign({}, ev), {\n              detail: {\n                ionShadowTarget: triggerEl\n              }\n            }));\n          } else {\n            overlayEl.present();\n          }\n        });\n        return function (_x2, _x3, _x4) {\n          return _ref3.apply(this, arguments);\n        };\n      }();\n      this.datetimePresentation = 'date-time';\n      this.dateText = undefined;\n      this.timeText = undefined;\n      this.datetimeActive = false;\n      this.selectedButton = undefined;\n      this.color = 'primary';\n      this.disabled = false;\n      this.datetime = undefined;\n    }\n    componentWillLoad() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const {\n          datetime\n        } = _this2;\n        if (!datetime) {\n          printIonError('An ID associated with an ion-datetime instance is required for ion-datetime-button to function properly.', _this2.el);\n          return;\n        }\n        const datetimeEl = _this2.datetimeEl = document.getElementById(datetime);\n        if (!datetimeEl) {\n          printIonError(`No ion-datetime instance found for ID '${datetime}'.`, _this2.el);\n          return;\n        }\n        /**\n         * The element reference must be an ion-datetime. Print an error\n         * if a non-datetime element was provided.\n         */\n        if (datetimeEl.tagName !== 'ION-DATETIME') {\n          printIonError(`Expected an ion-datetime instance for ID '${datetime}' but received '${datetimeEl.tagName.toLowerCase()}' instead.`, datetimeEl);\n          return;\n        }\n        /**\n         * Since the datetime can be used in any context (overlays, accordion, etc)\n         * we track when it is visible to determine when it is active.\n         * This informs which button is highlighted as well as the\n         * aria-expanded state.\n         */\n        const io = new IntersectionObserver(entries => {\n          const ev = entries[0];\n          _this2.datetimeActive = ev.isIntersecting;\n        }, {\n          threshold: 0.01\n        });\n        io.observe(datetimeEl);\n        /**\n         * Get a reference to any modal/popover\n         * the datetime is being used in so we can\n         * correctly size it when it is presented.\n         */\n        const overlayEl = _this2.overlayEl = datetimeEl.closest('ion-modal, ion-popover');\n        /**\n         * The .ion-datetime-button-overlay class contains\n         * styles that allow any modal/popover to be\n         * sized according to the dimensions of the datetime.\n         * If developers want a smaller/larger overlay all they need\n         * to do is change the width/height of the datetime.\n         * Additionally, this lets us avoid having to set\n         * explicit widths on each variant of datetime.\n         */\n        if (overlayEl) {\n          overlayEl.classList.add('ion-datetime-button-overlay');\n        }\n        componentOnReady(datetimeEl, () => {\n          const datetimePresentation = _this2.datetimePresentation = datetimeEl.presentation || 'date-time';\n          /**\n           * Set the initial display\n           * in the rendered buttons.\n           *\n           * From there, we need to listen\n           * for ionChange to be emitted\n           * from datetime so we know when\n           * to re-render the displayed\n           * text in the buttons.\n           */\n          _this2.setDateTimeText();\n          addEventListener(datetimeEl, 'ionValueChange', _this2.setDateTimeText);\n          /**\n           * Configure the initial selected button\n           * in the event that the datetime is displayed\n           * without clicking one of the datetime buttons.\n           * For example, a datetime could be expanded\n           * in an accordion. In this case users only\n           * need to click the accordion header to show\n           * the datetime.\n           */\n          switch (datetimePresentation) {\n            case 'date-time':\n            case 'date':\n            case 'month-year':\n            case 'month':\n            case 'year':\n              _this2.selectedButton = 'date';\n              break;\n            case 'time-date':\n            case 'time':\n              _this2.selectedButton = 'time';\n              break;\n          }\n        });\n      })();\n    }\n    render() {\n      const {\n        color,\n        dateText,\n        timeText,\n        selectedButton,\n        datetimeActive,\n        disabled\n      } = this;\n      const mode = getIonMode(this);\n      return h(Host, {\n        key: '26e606af6f067a5774db37ed41387ffebb941777',\n        class: createColorClasses(color, {\n          [mode]: true,\n          [`${selectedButton}-active`]: datetimeActive,\n          ['datetime-button-disabled']: disabled\n        })\n      }, dateText && h(\"button\", {\n        key: '6b7aa66a15b4a6d89d411e40586de28a2ac9f343',\n        class: \"ion-activatable\",\n        id: \"date-button\",\n        \"aria-expanded\": datetimeActive ? 'true' : 'false',\n        onClick: this.handleDateClick,\n        disabled: disabled,\n        part: \"native\",\n        ref: el => this.dateTargetEl = el\n      }, h(\"slot\", {\n        key: 'd42f34fd167be34386319e7ea788c2ab03c90b87',\n        name: \"date-target\"\n      }, dateText), mode === 'md' && h(\"ion-ripple-effect\", {\n        key: '47dd34f3c2799064cac7a5fe25440ecc043950f0'\n      })), timeText && h(\"button\", {\n        key: 'd77424a20fae320654774c7bfc8a8e2369d3afe3',\n        class: \"ion-activatable\",\n        id: \"time-button\",\n        \"aria-expanded\": datetimeActive ? 'true' : 'false',\n        onClick: this.handleTimeClick,\n        disabled: disabled,\n        part: \"native\",\n        ref: el => this.timeTargetEl = el\n      }, h(\"slot\", {\n        key: 'ac088a78141bb53f2efd48dd7745f8954c92378b',\n        name: \"time-target\"\n      }, timeText), mode === 'md' && h(\"ion-ripple-effect\", {\n        key: 'b3a58ddfd28b9396e2518ffd62a045ec13d8b9d0'\n      })));\n    }\n    get el() {\n      return getElement(this);\n    }\n  };\n  DatetimeButton.style = {\n    ios: IonDatetimeButtonIosStyle0,\n    md: IonDatetimeButtonMdStyle0\n  };\n  return DatetimeButton;\n})();\nexport { DatetimeButton as ion_datetime_button };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}