import { QuestCategory, QuestGoalUnit } from './quest.model';

/**
 * Group Sidequest Pool model
 * Represents a pool of possible group daily side quests
 */
export interface GroupSideQuestPool {
  id: string;                      // PK
  name: string;                    // Quest name
  description?: string;            // Quest description
  goal_value: number;              // Default 1
  category: QuestCategory;         // 'money', 'health', 'strength', 'knowledge'
  goal_unit: QuestGoalUnit;        // 'count', 'steps', 'm', 'km', 'sec', 'min', 'hr', 'Cal', 'g', 'mg', 'drink'
  active: boolean;                 // Default true
  emoji: string;                   // Default '🎯'
  created_at?: string;             // Timestamp
}

/**
 * Group Sidequest model
 * Tracks the group's current active side quest
 */
export interface GroupSideQuest {
  id: string;                      // PK
  group_id: string;                // FK to groups.id
  current_quest_id: string;        // FK to group_sidequest_pool.id
  streak: number;                  // Default 0
  last_completed_date?: string | null; // Date in YYYY-MM-DD format, nullable
  date_assigned: string;           // Date in YYYY-MM-DD format
  completed: boolean;              // Default false
  value_achieved: number;          // Default 0
  category: QuestCategory;         // 'money', 'health', 'strength', 'knowledge'
  created_at?: string;             // Timestamp
  
  // Joined fields
  current_quest?: GroupSideQuestPool; // Joined from group_sidequest_pool
}

/**
 * Group Sidequest Member Status model
 * Tracks each member's daily side quest status
 */
export interface GroupSideQuestMemberStatus {
  id: string;                      // PK
  group_quest_id: string;          // FK to group_sidequests.id
  member_id: string;               // FK to profiles.id
  completed: boolean;              // Default false
  value_achieved: number;          // Default 0
  last_updated: string;            // Date in YYYY-MM-DD format
  created_at?: string;             // Timestamp
  
  // Joined fields
  member?: any;                    // Joined from profiles
  group_quest?: GroupSideQuest;    // Joined from group_sidequests
}

/**
 * Group Daily Quest model for UI
 * Combines data from GroupSideQuest and GroupSideQuestPool
 */
export interface GroupDailyQuest {
  id: string;                      // GroupSideQuest.id
  group_id: string;                // GroupSideQuest.group_id
  streak: number;                  // GroupSideQuest.streak
  completed: boolean;              // GroupSideQuest.completed
  value_achieved: number;          // GroupSideQuest.value_achieved
  date_assigned: string;           // GroupSideQuest.date_assigned
  last_completed_date?: string | null; // GroupSideQuest.last_completed_date
  category: QuestCategory;         // GroupSideQuest.category
  
  // From GroupSideQuestPool
  current_quest: {
    id: string;                    // GroupSideQuestPool.id
    name: string;                  // GroupSideQuestPool.name
    description?: string;          // GroupSideQuestPool.description
    goal_value: number;            // GroupSideQuestPool.goal_value
    goal_unit: QuestGoalUnit;      // GroupSideQuestPool.goal_unit
    emoji: string;                 // GroupSideQuestPool.emoji
  };
  
  // Member status
  member_status?: {
    id: string;                    // GroupSideQuestMemberStatus.id
    completed: boolean;            // GroupSideQuestMemberStatus.completed
    value_achieved: number;        // GroupSideQuestMemberStatus.value_achieved
    last_updated: string;          // GroupSideQuestMemberStatus.last_updated
  };
  
  // Additional UI fields
  eligible_members_count?: number; // Count of eligible members
  completed_members_count?: number; // Count of members who completed
}
