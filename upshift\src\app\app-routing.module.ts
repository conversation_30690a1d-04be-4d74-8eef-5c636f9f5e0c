import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';
import { AdminGuard } from './guards/admin.guard';
import { TransitionComponent } from './transition/transition.component';
import { MainRedirectComponent } from './main-redirect/main-redirect.component';
export const routes: Routes = [
    {
        path: 'rating',
        loadComponent: () => import('./rating/rating.page').then(m => m.RatingPage)
    },
    {
        path: '',
        component: MainRedirectComponent,
        pathMatch: 'full',
    },
    {
        path: 'home',
        loadComponent: () => import('./home/<USER>').then(m => m.HomePage),
        canActivate: [AuthGuard]
    },
    {
        path: 'signup',
        loadComponent: () => import('./auth/signup/signup.component').then(m => m.SignupComponent)
    },
    {
        path: 'register',
        loadComponent: () => import('./auth/register/register.component').then(m => m.RegisterComponent)
    },
    {
        path: 'login',
        loadComponent: () => import('./auth/login/login.component').then(m => m.LoginComponent)
    },
    {
        path: 'transition',
        component: TransitionComponent
    },
    {
        path: 'pricing',
        loadComponent: () => import('./pricing/pricing.page').then(m => m.PricingPage)
    },
    {
        path: 'onboarding',
        loadComponent: () => import('./onboarding/onboarding.page').then(m => m.OnboardingPage)
    },
    {
        path: 'calculating',
        loadComponent: () => import('./calculating/calculating.page').then(m => m.CalculatingPage),
        canActivate: [AuthGuard]
    },
    // New pages from Django migration
    {
        path: 'today',
        loadComponent: () => import('./pages/today/today.page').then(m => m.TodayPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'goals',
        loadComponent: () => import('./pages/goals/goal-list/goal-list.page').then(m => m.GoalListPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'goals/:id',
        loadComponent: () => import('./pages/goals/goal-detail/goal-detail.page').then(m => m.GoalDetailPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'profile',
        loadComponent: () => import('./pages/profile/profile.page').then(m => m.ProfilePage),
        canActivate: [AuthGuard]
    },
    {
        path: 'profile-settings',
        loadComponent: () => import('./pages/profile-settings/profile-settings.page').then(m => m.ProfileSettingsPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'friends',
        loadComponent: () => import('./pages/friends/friends.page').then(m => m.FriendsPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'friends/:id',
        loadComponent: () => import('./pages/friends/friend-profile/friend-profile.page').then(m => m.FriendProfilePage),
        canActivate: [AuthGuard]
    },
    {
        path: 'affiliates',
        loadComponent: () => import('./pages/affiliates/affiliates.page').then(m => m.AffiliatesPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'groups',
        loadComponent: () => import('./pages/groups/group-list/group-list.page').then(m => m.GroupListPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'groups/:id',
        loadComponent: () => import('./pages/groups/group-detail/group-detail.page').then(m => m.GroupDetailPage),
        canActivate: [AuthGuard]
    },
    // Group settings page
    {
        path: 'groups/:id/settings',
        loadComponent: () => import('./pages/groups/group-settings/group-settings.page').then(m => m.GroupSettingsPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'create-group',
        loadComponent: () => import('./pages/groups/create-group/create-group.page').then(m => m.CreateGroupPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'group-requests',
        loadComponent: () => import('./pages/groups/group-requests/group-requests.page').then(m => m.GroupRequestsPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'time-tracker',
        loadComponent: () => import('./pages/time-tracker/time-tracker.page').then(m => m.TimeTrackerPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'leaderboard',
        redirectTo: 'leaderboard/groups',
        pathMatch: 'full'
    },
    {
        path: 'leaderboard/groups',
        loadComponent: () => import('./pages/leaderboard/leaderboard.page').then(m => m.LeaderboardPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'leaderboard/users',
        loadComponent: () => import('./pages/leaderboard/leaderboard.page').then(m => m.LeaderboardPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'badges',
        loadComponent: () => import('./pages/badges/user-badges.page').then(m => m.UserBadgesPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'badges/:id',
        loadComponent: () => import('./pages/badges/user-badges.page').then(m => m.UserBadgesPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'focus',
        loadComponent: () => import('./pages/focus/focus.page').then(m => m.FocusPage),
        canActivate: [AuthGuard]
    },

    {
        path: 'import-sidequests',
        loadComponent: () => import('./pages/import-sidequests/import-sidequests.page').then(m => m.ImportSideQuestsPage),
        canActivate: [AuthGuard]
    },
    {
        path: 'admin',
        loadComponent: () => import('./pages/admin/admin.page').then(m => m.AdminPage),
        canActivate: [AdminGuard]
    },
    {
        path: 'admin/collection/:name',
        loadComponent: () => import('./pages/admin/collection-detail/collection-detail.page').then(m => m.CollectionDetailPage),
        canActivate: [AdminGuard]
    },
    {
        path: 'user-profile/:id',
        loadComponent: () => import('./pages/user-profile/user-profile.page').then(m => m.UserProfilePage),
        canActivate: [AuthGuard]
    },
    {
        path: 'redirect',
        component: MainRedirectComponent
    },
    {
        path: '**',
        redirectTo: 'redirect'
    }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
