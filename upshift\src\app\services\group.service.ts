import { Injectable, inject } from '@angular/core';
import {
  Group,
  GroupJoinRequest,
  GroupMember,
  GroupQuest,
  GroupQuestProgress,
  GroupSideQuest,
  GroupSideQuestMemberStatus,
  GroupSideQuestPool
} from '../models/group.model';
import { Observable, map, of, switchMap, from, catchError, firstValueFrom, combineLatest } from 'rxjs';
import { User } from '../models/user.model';
import { SupabaseService } from './supabase.service';
import { XpService, EntityType } from './xp.service';

@Injectable({
  providedIn: 'root'
})
export class GroupService {
  private supabaseService = inject(SupabaseService);
  private xpService = inject(XpService);

  constructor() {}



  // Group CRUD operations
  // Alias for getGroups to match the method name used in group-list.page.ts
  getUserGroups(userId: string): Observable<Group[]> {
    return this.getGroups(userId);
  }

  getTopGroups(count: number = 10): Observable<Group[]> {
    console.log('GroupService: Getting top groups, limit:', count);

    return from(
      this.supabaseService.getClient()
        .from('groups')
        .select('*')
        .order('level', { ascending: false })
        .limit(count * 2) // Get more groups than needed to sort by XP
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting top groups:', response.error);
          return [];
        }

        console.log('GroupService: Found', response.data.length, 'groups in the database');

        // Sort groups by level (descending) and then by total XP (descending) when levels are equal
        const sortedGroups = response.data.sort((a, b) => {
          // First sort by level (descending)
          if (b.level !== a.level) {
            return b.level - a.level;
          }

          // If levels are equal, sort by total XP (descending)
          const aTotalXp = (a.strength_xp || 0) + (a.money_xp || 0) + (a.health_xp || 0) + (a.knowledge_xp || 0);
          const bTotalXp = (b.strength_xp || 0) + (b.money_xp || 0) + (b.health_xp || 0) + (b.knowledge_xp || 0);
          return bTotalXp - aTotalXp;
        });

        // Limit to the requested count after sorting
        const limitedGroups = sortedGroups.slice(0, count);

        console.log('GroupService: Sorted and limited to', limitedGroups.length, 'groups');
        console.log('GroupService: Sample group data:', limitedGroups.length > 0 ? limitedGroups[0] : 'No groups found');

        return limitedGroups as Group[];
      }),
      catchError(error => {
        console.error('GroupService: Error getting top groups:', error);
        return of([]);
      })
    );
  }

  getGroups(userId: string): Observable<Group[]> {
    console.log('GroupService: Getting groups for user:', userId);

    return from(
      this.supabaseService.getClient()
        .from('group_members')
        .select('group_id')
        .eq('user_id', userId)
    ).pipe(
      switchMap(memberResponse => {
        if (memberResponse.error) {
          console.error('GroupService: Error getting group memberships:', memberResponse.error);
          return of([]);
        }

        if (memberResponse.data.length === 0) {
          console.log('GroupService: User is not a member of any groups');
          return of([]);
        }

        const groupIds = memberResponse.data.map(membership => membership.group_id);
        console.log('GroupService: Found group IDs:', groupIds);

        // Get all groups in a single query
        return from(
          this.supabaseService.getClient()
            .from('groups')
            .select('*')
            .in('id', groupIds)
        ).pipe(
          map(groupResponse => {
            if (groupResponse.error) {
              console.error('GroupService: Error getting groups:', groupResponse.error);
              return [];
            }

            console.log('GroupService: Found groups:', groupResponse.data);
            return groupResponse.data as Group[];
          }),
          catchError(error => {
            console.error('GroupService: Error getting groups:', error);
            return of([]);
          })
        );
      })
    );
  }

  getGroup(groupId: string): Observable<Group | null> {
    console.log('GroupService: Getting group with ID:', groupId);

    return from(
      this.supabaseService.getClient()
        .from('groups')
        .select('*')
        .eq('id', groupId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting group:', response.error);
          return null;
        }

        console.log('GroupService: Found group:', response.data);
        return response.data as Group;
      }),
      catchError(error => {
        console.error('GroupService: Error getting group:', error);
        return of(null);
      })
    );
  }

  async createGroup(group: Omit<Group, 'id' | 'created'>): Promise<string> {
    console.log('GroupService: Creating new group:', group);

    // Convert Date objects to ISO strings for Supabase
    const newGroup = {
      ...group,
      created: new Date().toISOString()
    };

    console.log('GroupService: Prepared group for Supabase:', newGroup);

    // Insert the group
    const { data: groupData, error: groupError } = await this.supabaseService.getClient()
      .from('groups')
      .insert(newGroup)
      .select()
      .single();

    if (groupError) {
      console.error('GroupService: Error creating group:', groupError);
      throw new Error(groupError.message);
    }

    console.log('GroupService: Created group with ID:', groupData.id);

    // Get the creator's username to use as nickname
    let nickname: string = 'Admin'; // Default fallback

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('username')
        .eq('id', group.admin_id)
        .single();

      if (!error && data && data.username) {
        nickname = data.username;
        console.log('GroupService: Using username as nickname for admin:', nickname);
      }
    } catch (err) {
      console.error('GroupService: Error getting username for admin:', err);
    }

    // Add the creator as a member and admin
    const member = {
      group_id: groupData.id,
      user_id: group.admin_id,
      nickname: nickname,
      is_admin: true,
      joined_date: new Date().toISOString()
    };

    console.log('GroupService: Adding creator as member:', member);

    const { error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (memberError) {
      console.error('GroupService: Error adding group member:', memberError);
      throw new Error(memberError.message);
    }

    return groupData.id;
  }

  async updateGroup(groupId: string, data: Partial<Group>): Promise<void> {
    console.log('GroupService: Updating group:', groupId, data);

    // Check if name is being updated and if it's unique
    if (data.name) {
      const isNameAvailable = await this.checkGroupNameAvailability(data.name, groupId);
      if (!isNameAvailable) {
        throw new Error('A group with this name already exists. Please choose a different name.');
      }
    }

    const { error } = await this.supabaseService.getClient()
      .from('groups')
      .update(data)
      .eq('id', groupId);

    if (error) {
      console.error('GroupService: Error updating group:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Updated group successfully');
  }

  /**
   * Check if a group name is available
   * @param name The group name to check
   * @param excludeGroupId Optional group ID to exclude from the check (for updates)
   * @returns Promise<boolean> True if the name is available, false if it's taken
   */
  async checkGroupNameAvailability(name: string, excludeGroupId?: string): Promise<boolean> {
    console.log('GroupService: Checking if group name is available:', name, 'excluding group:', excludeGroupId);

    if (!name) {
      return false;
    }

    try {
      let query = this.supabaseService.getClient()
        .from('groups')
        .select('id')
        .eq('name', name)
        .limit(1);

      // If we're updating an existing group, exclude it from the check
      if (excludeGroupId) {
        query = query.neq('id', excludeGroupId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('GroupService: Error checking group name availability:', error);
        throw new Error(error.message);
      }

      const isAvailable = !data || data.length === 0;
      console.log('GroupService: Group name is available:', isAvailable);
      return isAvailable;
    } catch (error) {
      console.error('GroupService: Error checking group name availability:', error);
      throw error;
    }
  }

  async deleteGroup(groupId: string): Promise<void> {
    console.log('GroupService: Deleting group:', groupId);

    const { error } = await this.supabaseService.getClient()
      .from('groups')
      .delete()
      .eq('id', groupId);

    if (error) {
      console.error('GroupService: Error deleting group:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Deleted group successfully');
  }

  // Group Member operations
  getGroupMembers(groupId: string): Observable<GroupMember[]> {
    console.log('GroupService: Getting members for group:', groupId);

    return from(
      this.supabaseService.getClient()
        .from('group_members')
        .select('*')
        .eq('group_id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting group members:', response.error);
          return [];
        }

        console.log(`GroupService: Found ${response.data.length} members for group:`, groupId);
        return response.data as GroupMember[];
      }),
      catchError(error => {
        console.error('GroupService: Error getting group members:', error);
        return of([]);
      })
    );
  }

  getGroupMembersWithProfiles(groupId: string): Observable<any[]> {
    console.log('GroupService: Getting members with profiles for group:', groupId);

    return this.getGroupMembers(groupId).pipe(
      switchMap(members => {
        if (members.length === 0) {
          console.log('GroupService: No members found for group:', groupId);
          return of([]);
        }

        const memberIds = members.map(member => member.user_id);
        console.log('GroupService: Found member IDs:', memberIds);

        // Get user profiles for each member
        return from(
          this.supabaseService.getClient()
            .from('profiles')
            .select('*')
            .in('id', memberIds)
        ).pipe(
          map(response => {
            if (response.error) {
              console.error('GroupService: Error getting member profiles:', response.error);
              return [];
            }

            const profiles = response.data as User[];
            console.log('GroupService: Found profiles:', profiles);

            // Combine members with their profiles
            return members.map(member => {
              const profile = profiles.find(p => p.id === member.user_id);
              return {
                ...member,
                profile: profile || null
              };
            });
          }),
          catchError(error => {
            console.error('GroupService: Error getting member profiles:', error);
            return of([]);
          })
        );
      })
    );
  }

  async addGroupMemberWithOptionalNickname(groupId: string, userId: string, nickname?: string): Promise<void> {
    console.log('GroupService: Adding member to group with optional nickname:', groupId, userId, nickname);

    // If no nickname provided, get the user's username from profiles
    let memberNickname: string = nickname || '';

    if (!memberNickname) {
      try {
        const { data, error } = await this.supabaseService.getClient()
          .from('profiles')
          .select('username')
          .eq('id', userId)
          .single();

        if (error) {
          console.error('GroupService: Error getting username:', error);
          memberNickname = 'Member'; // Fallback
        } else if (data && data.username) {
          memberNickname = data.username;
          console.log('GroupService: Using username as nickname:', memberNickname);
        } else {
          memberNickname = 'Member'; // Fallback
        }
      } catch (err) {
        console.error('GroupService: Error getting username:', err);
        memberNickname = 'Member'; // Fallback
      }
    }

    // Create the member object
    const member: GroupMember = {
      group_id: groupId,
      user_id: userId,
      nickname: memberNickname,
      is_admin: false,
      joined_date: new Date()
    };

    // Insert the member directly
    const { error } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (error) {
      console.error('GroupService: Error adding member to group:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Added member to group successfully');
  }

  async addGroupMember(groupId: string, userId: string, nickname: string): Promise<void> {
    console.log('GroupService: Adding member to group:', groupId, userId, nickname);

    const member: GroupMember = {
      group_id: groupId,
      user_id: userId,
      nickname: nickname,
      is_admin: false,
      joined_date: new Date()
    };

    const { error } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (error) {
      console.error('GroupService: Error adding group member:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Added member to group successfully');
  }

  async removeGroupMember(groupId: string, userId: string): Promise<void> {
    console.log('GroupService: Removing member from group:', groupId, userId);

    const { data, error: findError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', userId)
      .single();

    if (findError || !data) {
      console.log('GroupService: Member not found in group');
      return;
    }

    const { error: deleteError } = await this.supabaseService.getClient()
      .from('group_members')
      .delete()
      .eq('id', data.id);

    if (deleteError) {
      console.error('GroupService: Error removing group member:', deleteError);
      throw new Error(deleteError.message);
    }

    console.log('GroupService: Removed member from group successfully');
  }

  async updateGroupMember(memberId: string, data: Partial<GroupMember>): Promise<void> {
    console.log('GroupService: Updating group member:', memberId, data);

    const { error } = await this.supabaseService.getClient()
      .from('group_members')
      .update(data)
      .eq('id', memberId);

    if (error) {
      console.error('GroupService: Error updating group member:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Updated group member successfully');
  }

  async removeGroupMemberById(groupId: string, memberId: string): Promise<void> {
    console.log('GroupService: Removing member from group by ID:', groupId, memberId);

    const { error } = await this.supabaseService.getClient()
      .from('group_members')
      .delete()
      .eq('id', memberId);

    if (error) {
      console.error('GroupService: Error removing group member:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Removed member from group successfully');
  }

  // This function is already defined above with a different signature
  // We'll keep the original implementation and update the group settings page instead

  async leaveGroup(groupId: string): Promise<void> {
    console.log('GroupService: User leaving group:', groupId);

    const userId = this.supabaseService.getCurrentUserId();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Check if the user is an admin
    const { data: memberData, error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id, is_admin')
      .eq('group_id', groupId)
      .eq('user_id', userId)
      .single();

    if (memberError || !memberData) {
      console.error('GroupService: Member not found in group');
      throw new Error('Member not found in group');
    }

    const isAdmin = memberData.is_admin;
    console.log('GroupService: User is admin:', isAdmin);

    // Get all members of the group
    const { data: allMembers, error: allMembersError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id, user_id, is_admin')
      .eq('group_id', groupId);

    if (allMembersError) {
      console.error('GroupService: Error getting all members:', allMembersError);
      throw new Error(allMembersError.message);
    }

    // If this is the only member, delete the group
    if (allMembers.length === 1) {
      console.log('GroupService: Last member leaving, deleting group');
      await this.deleteGroup(groupId);
      return;
    }

    // If the user is an admin and there are other members, transfer admin rights
    if (isAdmin) {
      // Find another member who is not the current user
      const otherMembers = allMembers.filter(m => m.user_id !== userId);
      if (otherMembers.length > 0) {
        // Pick a random member to be the new admin
        const newAdmin = otherMembers[Math.floor(Math.random() * otherMembers.length)];
        console.log('GroupService: Transferring admin rights to:', newAdmin.user_id);

        // Update the member to be an admin
        const { error: updateError } = await this.supabaseService.getClient()
          .from('group_members')
          .update({ is_admin: true })
          .eq('id', newAdmin.id);

        if (updateError) {
          console.error('GroupService: Error transferring admin rights:', updateError);
          throw new Error(updateError.message);
        }
      }
    }

    // Now remove the user from the group
    const { error: deleteError } = await this.supabaseService.getClient()
      .from('group_members')
      .delete()
      .eq('id', memberData.id);

    if (deleteError) {
      console.error('GroupService: Error leaving group:', deleteError);
      throw new Error(deleteError.message);
    }

    console.log('GroupService: Left group successfully');
  }

  // Group Join Request operations
  getGroupJoinRequests(groupId: string): Observable<GroupJoinRequest[]> {
    console.log('GroupService: Getting join requests for group:', groupId);

    return from(
      this.supabaseService.getClient()
        .from('group_join_requests')
        .select('*')
        .eq('group_id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting join requests:', response.error);
          return [];
        }

        console.log(`GroupService: Found ${response.data.length} join requests for group:`, groupId);
        return response.data as GroupJoinRequest[];
      }),
      catchError(error => {
        console.error('GroupService: Error getting join requests:', error);
        return of([]);
      })
    );
  }

  getGroupJoinRequestsForUser(username: string): Observable<GroupJoinRequest[]> {
    console.log('GroupService: Getting join requests for user by username:', username);

    // Log the exact username for debugging
    console.log('GroupService: Username exact value:', JSON.stringify(username));
    console.log('GroupService: Username length:', username.length);
    console.log('GroupService: Username character codes:', Array.from(username as string).map((c: string) => c.charCodeAt(0)));



    // Check if we have permission to access the group_join_requests table
    // This might fail due to RLS policies if the user doesn't have permission
    return from(
      this.supabaseService.getClient()
        .from('group_join_requests')
        .select('*, groups(name, emoji)')
        .order('created', { ascending: false })
    ).pipe(
      switchMap(response => {
        // Check for permission errors
        if (response.error) {
          console.error('GroupService: Error getting join requests for user:', response.error);

          // If it's a permission error, we might need to use a function or service role
          if (response.error.code === '42501' || response.error.message?.includes('permission denied')) {
            console.log('GroupService: Permission denied. This might be due to RLS policies.');
            console.log('GroupService: Please run the migration script to update the RLS policies.');
          }

          return of([]);
        }

        console.log(`GroupService: Found ${response.data.length} join requests for username:`, username);
        console.log('GroupService: Join requests data:', response.data);

        // Log all requests for debugging
        console.log('GroupService: All join requests:', JSON.stringify(response.data));

        // Filter to ensure exact matching
        const filteredRequests = response.data.filter((req: any) => {
          console.log('GroupService: Checking request:', req.id, 'username_invited:', req.username_invited);

          if (!req.username_invited) {
            console.log('GroupService: username_invited field missing in request:', req);
            return false;
          }

          // Log the comparison values
          const reqUsername = req.username_invited.trim().toLowerCase();
          const targetUsername = username.trim().toLowerCase();
          console.log('GroupService: Comparing:', reqUsername, 'vs', targetUsername, 'match:', reqUsername === targetUsername);

          // Use case-insensitive, trimmed comparison for best matching
          return reqUsername === targetUsername;
        });

        console.log(`GroupService: Filtered ${filteredRequests.length} join requests for username:`, username);

        // Get inviter details for each request
        if (filteredRequests.length === 0) {
          return of([]);
        }

        // Create an array of observables for each request to get inviter details
        const requestsWithInviterDetails$ = filteredRequests.map((request: any) => {
          console.log('GroupService: Getting inviter details for request:', request.id, 'invited_by:', request.invited_by);

          // Log the exact invited_by value for debugging
          console.log('GroupService: invited_by exact value:', JSON.stringify(request.invited_by));

          // Make sure we have a valid invited_by value
          if (!request.invited_by) {
            console.error('GroupService: No invited_by value found in request:', request);
            return of({
              ...request,
              inviter_username: 'Unknown'
            } as GroupJoinRequest);
          }

          // Since invited_by is now the username directly, we can just use it
          console.log('GroupService: Using invited_by directly as username:', request.invited_by);

          // Return the request with inviter_username set to invited_by
          return of({
            ...request,
            inviter_username: request.invited_by
          } as GroupJoinRequest);
        });

        // Combine all the observables
        return combineLatest(requestsWithInviterDetails$);
      }),
      catchError(error => {
        console.error('GroupService: Error getting join requests for user:', error);
        return of([]);
      })
    );
  }

  getJoinRequestsForUserId(userId: string): Observable<GroupJoinRequest[]> {
    console.log('GroupService: Getting join requests for user by ID:', userId);

    // First get the username for this user ID
    return from(
      this.supabaseService.getClient()
        .from('profiles')
        .select('username')
        .eq('id', userId)
        .single()
    ).pipe(
      switchMap(userResponse => {
        if (userResponse.error) {
          console.error('GroupService: Error getting username for user ID:', userResponse.error);
          return of([]);
        }

        const username = userResponse.data.username;
        console.log('GroupService: Found username for user ID:', username);

        // Get join requests by username_invited
        return this.getGroupJoinRequestsForUser(username);
      }),
      catchError(error => {
        console.error('GroupService: Error getting join requests for user ID:', error);
        return of([]);
      })
    );
  }

  async createGroupJoinRequest(request: Omit<GroupJoinRequest, 'id' | 'created'>): Promise<string> {
    console.log('GroupService: Creating join request:', request);

    // Create the request with username_invited
    const newRequest: Omit<GroupJoinRequest, 'id'> = {
      ...request,
      created: new Date(),
      requested_at: new Date(),
      status: 'pending'
    };

    const { data, error } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .insert(newRequest)
      .select('id')
      .single();

    if (error) {
      console.error('GroupService: Error creating join request:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Created join request successfully with ID:', data.id);
    return data.id;
  }

  async acceptGroupJoinRequest(requestId: string, adminUserId: string): Promise<void> {
    console.log('GroupService: Accepting join request:', requestId, 'by admin:', adminUserId);

    // Get the request
    const { data: request, error: getError } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .select('*')
      .eq('id', requestId)
      .single();

    if (getError || !request) {
      console.error('GroupService: Join request not found:', getError);
      throw new Error('Join request not found');
    }

    console.log('GroupService: Found join request:', request);

    // Get the username from the request
    const username = request.username_invited;

    if (!username) {
      console.error('GroupService: No username_invited found in request:', request);
      throw new Error('Username not found in request');
    }

    // Get the user ID from the username
    const { data: userData, error: userError } = await this.supabaseService.getClient()
      .from('profiles')
      .select('id')
      .eq('username', username)
      .single();

    if (userError || !userData) {
      console.error('GroupService: Error finding user by username:', userError);
      throw new Error('User not found');
    }

    const userId = userData.id;

    // Always use username as nickname
    const nickname: string = username;

    // Add user to group members with nickname
    const member = {
      group_id: request.group_id,
      user_id: userId,
      nickname: nickname, // Use nickname or username
      is_admin: false,
      joined_date: new Date()
    } as GroupMember;

    // Insert the member directly
    const { error: insertError } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (insertError) {
      console.error('GroupService: Error adding member to group:', insertError);
      throw new Error(insertError.message);
    }

    console.log('GroupService: Added member to group successfully');

    // Delete the request after accepting
    const { error: deleteError } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .delete()
      .eq('id', requestId);

    if (deleteError) {
      console.error('GroupService: Error deleting join request:', deleteError);
      // We don't throw here since the user was already added to the group
    }

    console.log('GroupService: Accepted join request successfully');
  }

  async rejectGroupJoinRequest(requestId: string): Promise<void> {
    console.log('GroupService: Rejecting join request:', requestId);

    // Delete the request instead of updating status
    const { error } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .delete()
      .eq('id', requestId);

    if (error) {
      console.error('GroupService: Error rejecting join request:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Rejected join request successfully');
  }

  // Group Invitation Code operations
  async generateGroupInvitationCode(groupId: string): Promise<string> {
    console.log('GroupService: Generating invitation code for group:', groupId);

    // Generate a random code
    const code = Math.random().toString(36).substring(2, 10).toUpperCase();

    // Set expiry to 24 hours from now
    const expiry = new Date();
    expiry.setDate(expiry.getDate() + 1);

    // Update the group with the new code
    const { error } = await this.supabaseService.getClient()
      .from('groups')
      .update({
        invitation_code: code,
        code_expiry: expiry
      })
      .eq('id', groupId);

    if (error) {
      console.error('GroupService: Error generating invitation code:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Generated invitation code successfully:', code);
    return code;
  }

  async joinGroupByCode(userId: string, code: string, nickname?: string): Promise<boolean> {
    console.log('GroupService: Joining group by code:', code, userId, nickname);

    // Find group with this code
    const { data: groups, error: findError } = await this.supabaseService.getClient()
      .from('groups')
      .select('*')
      .eq('invitation_code', code);

    if (findError || !groups || groups.length === 0) {
      console.log('GroupService: Invalid code or error finding group:', findError);
      return false; // Invalid code
    }

    const group = groups[0] as Group;
    const groupId = group.id;

    console.log('GroupService: Found group:', group);

    // Check if code is expired
    const expiryDate = group.code_expiry ? new Date(group.code_expiry) : null;
    if (!expiryDate || expiryDate < new Date()) {
      console.log('GroupService: Code expired');
      return false; // Code expired
    }

    // Check if already a member
    const { data: members, error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', userId);

    if (memberError) {
      console.error('GroupService: Error checking membership:', memberError);
      return false;
    }

    if (members && members.length > 0) {
      console.log('GroupService: User is already a member of this group');
      return false; // Already a member
    }

    // Get the user's username from profiles to use as nickname
    let memberNickname: string = nickname || '';

    if (!memberNickname) {
      try {
        const { data, error } = await this.supabaseService.getClient()
          .from('profiles')
          .select('username')
          .eq('id', userId)
          .single();

        if (error) {
          console.error('GroupService: Error getting username:', error);
          memberNickname = 'Member'; // Fallback
        } else if (data && data.username) {
          memberNickname = data.username;
          console.log('GroupService: Using username as nickname:', memberNickname);
        } else {
          memberNickname = 'Member'; // Fallback
        }
      } catch (err) {
        console.error('GroupService: Error getting username:', err);
        memberNickname = 'Member'; // Fallback
      }
    }

    // Create the member object
    const member = {
      group_id: groupId,
      user_id: userId,
      nickname: memberNickname,
      is_admin: false,
      joined_date: new Date()
    } as GroupMember;

    // Insert the member directly
    const { error: insertError } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (insertError) {
      console.error('GroupService: Error adding member to group:', insertError);
      return false;
    }

    console.log('GroupService: Added member to group successfully');

    // Clear the invitation code
    const { error: updateError } = await this.supabaseService.getClient()
      .from('groups')
      .update({
        invitation_code: null,
        code_expiry: null
      })
      .eq('id', groupId);

    if (updateError) {
      console.error('GroupService: Error clearing invitation code:', updateError);
      // We still return true since the user was added to the group
    }

    console.log('GroupService: Successfully joined group');
    return true;
  }

  // Group Quest operations
  getGroupQuests(groupId: string): Observable<GroupQuest[]> {
    console.log('GroupService: Getting quests for group:', groupId);

    return from(
      this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('group_id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting group quests:', response.error);
          return [];
        }

        console.log(`GroupService: Found ${response.data.length} quests for group:`, groupId);
        return response.data as GroupQuest[];
      }),
      catchError(error => {
        console.error('GroupService: Error getting group quests:', error);
        return of([]);
      })
    );
  }

  getGroupQuest(questId: string): Observable<GroupQuest> {
    console.log('GroupService: Getting quest details:', questId);

    return from(
      this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('id', questId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting quest details:', response.error);
          throw new Error(response.error.message);
        }

        console.log('GroupService: Found quest details:', response.data);
        return response.data as GroupQuest;
      }),
      catchError(error => {
        console.error('GroupService: Error getting quest details:', error);
        throw error;
      })
    );
  }

  async createGroupQuest(quest: Omit<GroupQuest, 'id' | 'created' | 'streak'>): Promise<string> {
    console.log('GroupService: Creating new group quest:', JSON.stringify(quest, null, 2));

    // Ensure priority is set
    if (!quest.priority) {
      console.warn('GroupService: Priority not set, defaulting to "basic"');
      quest.priority = 'basic';
    }

    // Ensure all required fields are present
    if (!quest.name || !quest.category || !quest.quest_type || !quest.goal_unit || !quest.goal_period) {
      console.error('GroupService: Missing required fields for quest creation');
      throw new Error('Missing required fields for quest creation');
    }

    // Ensure emoji is set
    if (!quest.emoji) {
      console.warn('GroupService: Emoji not set, defaulting to "🎯"');
      quest.emoji = '🎯';
    }

    const newQuest: Omit<GroupQuest, 'id'> = {
      ...quest,
      created: new Date(),
      streak: 0
    };

    console.log('GroupService: Prepared quest for insertion:', JSON.stringify(newQuest, null, 2));

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('group_quests')
        .insert(newQuest)
        .select('id')
        .single();

      if (error) {
        console.error('GroupService: Error creating group quest:', error);
        throw new Error(error.message);
      }

      if (!data || !data.id) {
        console.error('GroupService: No data returned from insert operation');
        throw new Error('Failed to create quest: No data returned');
      }

      console.log('GroupService: Created group quest successfully with ID:', data.id);
      return data.id;
    } catch (error) {
      console.error('GroupService: Exception during quest creation:', error);
      throw error;
    }
  }

  async updateGroupQuest(questId: string, data: Partial<GroupQuest>): Promise<void> {
    console.log('GroupService: Updating group quest:', questId, data);

    const { error } = await this.supabaseService.getClient()
      .from('group_quests')
      .update(data)
      .eq('id', questId);

    if (error) {
      console.error('GroupService: Error updating group quest:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Updated group quest successfully');
  }

  async deleteGroupQuest(questId: string): Promise<void> {
    console.log('GroupService: Deleting group quest:', questId);

    try {
      // Get the quest details first to handle XP subtraction
      const { data: quest, error: questError } = await this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('id', questId)
        .single();

      if (questError) {
        console.error('GroupService: Error getting quest details for deletion:', questError);
        throw new Error('Failed to get quest details for deletion');
      }

      if (quest && quest.streak > 0) {
        // If the quest has a streak, we need to subtract XP
        const groupId = quest.group_id;
        const category = quest.category;
        const priority = quest.priority;

        // Get all eligible members of the group
        const { data: members, error: membersError } = await this.supabaseService.getClient()
          .from('group_members')
          .select('user_id, joined_date')
          .eq('group_id', groupId);

        if (!membersError && members && members.length > 0) {
          // Filter eligible members
          const today = new Date();
          const eligibleMembers = members.filter(member => {
            const joinDate = new Date(member.joined_date);
            joinDate.setHours(0, 0, 0, 0);
            const todayDate = new Date(today);
            todayDate.setHours(0, 0, 0, 0);
            return joinDate < todayDate;
          });

          if (eligibleMembers.length > 0) {
            // Calculate XP to subtract
            const xpValue = priority === 'high' ? 2 : 1;
            const totalXP = xpValue * eligibleMembers.length;
            const xpField = `${category}_xp`;

            // Get the group's current XP
            const { data: group, error: groupError } = await this.supabaseService.getClient()
              .from('groups')
              .select('*')
              .eq('id', groupId)
              .single();

            if (!groupError && group) {
              let currentXP = group[xpField as keyof typeof group] as number || 0;

              // Subtract XP for the streak
              currentXP = Math.max(0, currentXP - (totalXP * quest.streak));
              console.log(`GroupService: Subtracting ${totalXP * quest.streak} XP from ${xpField} due to quest deletion, new total: ${currentXP}`);

              // Update the group's XP
              const { error: xpUpdateError } = await this.supabaseService.getClient()
                .from('groups')
                .update({ [xpField]: currentXP })
                .eq('id', groupId);

              if (xpUpdateError) {
                console.error('GroupService: Error updating group XP during quest deletion:', xpUpdateError);
              } else {
                console.log('GroupService: Updated group XP successfully during quest deletion');
              }
            }
          }
        }
      }

      // Now delete the quest
      const { error } = await this.supabaseService.getClient()
        .from('group_quests')
        .delete()
        .eq('id', questId);

      if (error) {
        console.error('GroupService: Error deleting group quest:', error);
        throw new Error(error.message);
      }

      console.log('GroupService: Deleted group quest successfully');
    } catch (error) {
      console.error('GroupService: Error in deleteGroupQuest:', error);
      throw error;
    }
  }

  // Group Quest Progress operations
  getGroupQuestProgress(questId: string, userId: string, date: Date): Observable<GroupQuestProgress | undefined> {
    const dateString = date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    console.log('GroupService: Getting quest progress:', questId, userId, dateString);

    return from(
      this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('*')
        .eq('quest_id', questId)
        .eq('user_id', userId)
        .eq('date', dateString)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting quest progress:', response.error);
          return undefined;
        }

        if (response.data && response.data.length > 0) {
          console.log('GroupService: Found quest progress:', response.data[0]);
          return response.data[0] as GroupQuestProgress;
        } else {
          console.log('GroupService: No quest progress found');
          return undefined;
        }
      }),
      catchError(error => {
        console.error('GroupService: Error getting quest progress:', error);
        return of(undefined);
      })
    );
  }

  getGroupQuestProgressForDate(questId: string, date: Date): Observable<GroupQuestProgress[]> {
    const dateString = date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    console.log('GroupService: Getting all quest progress for date:', questId, dateString);

    return from(
      this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('*')
        .eq('quest_id', questId)
        .eq('date', dateString)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting quest progress for date:', response.error);
          return [];
        }

        console.log(`GroupService: Found ${response.data.length} progress entries for quest:`, questId);
        return response.data as GroupQuestProgress[];
      }),
      catchError(error => {
        console.error('GroupService: Error getting quest progress for date:', error);
        return of([]);
      })
    );
  }

  /**
   * Check if a user can participate in group quests for a specific date
   * Users can only participate in quests starting the day after they joined the group
   */
  async canUserParticipateInGroupQuests(userId: string, groupId: string, date: Date): Promise<boolean> {
    console.log('GroupService: Checking if user can participate in group quests:', userId, groupId, date.toISOString().split('T')[0]);

    try {
      // Get the user's join date for this group
      const { data, error } = await this.supabaseService.getClient()
        .from('group_members')
        .select('joined_date')
        .eq('user_id', userId)
        .eq('group_id', groupId)
        .single();

      if (error) {
        console.error('GroupService: Error getting user join date:', error);
        return false;
      }

      if (!data || !data.joined_date) {
        console.error('GroupService: User is not a member of this group or join date is missing');
        return false;
      }

      // Parse the join date
      const joinDate = new Date(data.joined_date);
      joinDate.setHours(0, 0, 0, 0);

      // Set the target date to midnight for proper comparison
      const targetDate = new Date(date);
      targetDate.setHours(0, 0, 0, 0);

      // User can participate if the target date is AFTER the join date
      const canParticipate = targetDate > joinDate;
      console.log(`GroupService: User joined on ${joinDate.toISOString().split('T')[0]}, target date is ${targetDate.toISOString().split('T')[0]}, can participate: ${canParticipate}`);

      return canParticipate;
    } catch (error) {
      console.error('GroupService: Error checking if user can participate:', error);
      return false;
    }
  }

  async toggleGroupQuestCompletion(
    questId: string,
    userId: string,
    date: Date,
    valueAchieved: number
  ): Promise<{ success: boolean; message?: string }> {
    const dateString = date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    console.log('GroupService: Toggling quest completion:', questId, userId, dateString, valueAchieved);

    try {
      // Get the quest to determine group ID and completion logic
      const { data: quest, error: questError } = await this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('id', questId)
        .single();

      if (questError || !quest) {
        console.error('GroupService: Error getting quest details:', questError);
        return { success: false, message: 'Quest not found' };
      }

      // Check if user can participate in quests for this date
      const canParticipate = await this.canUserParticipateInGroupQuests(userId, quest.group_id, date);
      if (!canParticipate) {
        console.log('GroupService: User cannot participate in quests for this date (joined too recently)');
        return {
          success: false,
          message: 'You can participate in group quests starting tomorrow after joining the group.'
        };
      }

      // Check if progress document exists
      const { data: progressData, error: findError } = await this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('*')
        .eq('quest_id', questId)
        .eq('user_id', userId)
        .eq('date', dateString);

      if (findError) {
        console.error('GroupService: Error finding quest progress:', findError);
        return { success: false, message: findError.message };
      }

      let progressId: string;
      let wasCompleted = false;

      if (!progressData || progressData.length === 0) {
        console.log('GroupService: Creating new progress document');
        // Create new progress document
        const newProgress = {
          quest_id: questId,
          user_id: userId,
          date: dateString,
          completed: false,
          value_achieved: 0
        };

        const { data: insertData, error: insertError } = await this.supabaseService.getClient()
          .from('group_quest_progress')
          .insert(newProgress)
          .select('id')
          .single();

        if (insertError || !insertData) {
          console.error('GroupService: Error creating progress document:', insertError);
          return { success: false, message: 'Failed to create progress document' };
        }

        progressId = insertData.id;
      } else {
        console.log('GroupService: Found existing progress document:', progressData[0]);
        progressId = progressData[0].id;
        wasCompleted = progressData[0].completed;
      }

      console.log('GroupService: Found quest details:', quest);

      // Determine completion status based on quest type and value
      let isCompleted: boolean;
      if (quest.quest_type === 'build') {
        isCompleted = valueAchieved >= quest.goal_value;
      } else { // 'quit' type
        isCompleted = valueAchieved < quest.goal_value;
      }

      // Update the progress
      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_quest_progress')
        .update({
          value_achieved: valueAchieved,
          completed: isCompleted
        })
        .eq('id', progressId);

      if (updateError) {
        console.error('GroupService: Error updating progress:', updateError);
        return { success: false, message: updateError.message };
      }

      console.log('GroupService: Updated progress successfully');

      // Check if this is a previous day's quest
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(date);
      selectedDate.setHours(0, 0, 0, 0);
      const isPreviousDay = selectedDate < today;

      if (isPreviousDay) {
        console.log('GroupService: Quest toggled for a previous day, handling XP update');

        // Get all eligible members for the selected date
        const { data: members, error: membersError } = await this.supabaseService.getClient()
          .from('group_members')
          .select('user_id, joined_date')
          .eq('group_id', quest.group_id);

        if (membersError || !members || members.length === 0) {
          console.error('GroupService: Error getting group members for XP update:', membersError);
        } else {
          // Filter members who were eligible on the selected date
          const eligibleMembers = members.filter(member => {
            const joinDate = new Date(member.joined_date);
            joinDate.setHours(0, 0, 0, 0);
            return selectedDate > joinDate;
          });

          if (eligibleMembers.length > 0) {
            const eligibleMemberIds = eligibleMembers.map(member => member.user_id);
            console.log(`GroupService: Found ${eligibleMembers.length} eligible members for the selected date`);

            // Get all progress entries for this quest and date
            const { data: allProgress, error: progressError } = await this.supabaseService.getClient()
              .from('group_quest_progress')
              .select('user_id, completed')
              .eq('quest_id', questId)
              .eq('date', dateString);

            if (progressError) {
              console.error('GroupService: Error getting quest progress for XP update:', progressError);
            } else if (allProgress) {
              // Create a copy of the progress data to represent the state before this update
              const previousProgress = [...allProgress];

              // Find the current user's entry and update it with the previous completion status
              const currentUserIndex = previousProgress.findIndex(p => p.user_id === userId);
              if (currentUserIndex >= 0) {
                previousProgress[currentUserIndex].completed = wasCompleted;
              }

              // Check if all eligible members had completed the quest before this update
              const wasAllEligibleMembersCompleted = eligibleMemberIds.every(id => {
                if (id === userId) {
                  return wasCompleted;
                } else {
                  const userProgress = previousProgress.find(p => p.user_id === id);
                  return userProgress && userProgress.completed;
                }
              });

              // Check if all eligible members have completed the quest now
              const allEligibleMembersCompleted = eligibleMemberIds.every(id => {
                if (id === userId) {
                  return isCompleted;
                } else {
                  const userProgress = allProgress.find(p => p.user_id === id);
                  return userProgress && userProgress.completed;
                }
              });

              console.log(`GroupService: All eligible members completed now? ${allEligibleMembersCompleted}`);
              console.log(`GroupService: All eligible members completed before? ${wasAllEligibleMembersCompleted}`);

              // Calculate XP value based on priority and eligible member count
              const xpValue = quest.priority === 'high' ? 2 : 1;
              const totalXP = xpValue * eligibleMembers.length;

              // Get the field name for the category XP
              const xpField = `${quest.category}_xp`;

              // Get the group's current XP
              const { data: group, error: groupError } = await this.supabaseService.getClient()
                .from('groups')
                .select('*')
                .eq('id', quest.group_id)
                .single();

              if (groupError || !group) {
                console.error('GroupService: Error getting group for XP update:', groupError);
              } else {
                let currentXP = group[xpField as keyof typeof group] as number || 0;
                let xpChanged = false;

                // XP should be added if all members completed now but not before
                if (allEligibleMembersCompleted && !wasAllEligibleMembersCompleted) {
                  // Add XP to the group
                  currentXP += totalXP;
                  xpChanged = true;
                  console.log(`GroupService: Adding ${totalXP} XP to ${xpField} for previous day completion, new total: ${currentXP}`);
                }
                // XP should be subtracted if all members were completed before but not now
                else if (!allEligibleMembersCompleted && wasAllEligibleMembersCompleted) {
                  // Remove XP from the group
                  currentXP = Math.max(0, currentXP - totalXP);
                  xpChanged = true;
                  console.log(`GroupService: Removing ${totalXP} XP from ${xpField} for previous day incompletion, new total: ${currentXP}`);
                } else {
                  console.log('GroupService: No XP change needed for previous day');
                }

                // Update the group's XP if it changed
                if (xpChanged) {
                  const { error: xpUpdateError } = await this.supabaseService.getClient()
                    .from('groups')
                    .update({ [xpField]: currentXP })
                    .eq('id', quest.group_id);

                  if (xpUpdateError) {
                    console.error('GroupService: Error updating group XP for previous day:', xpUpdateError);
                  } else {
                    console.log('GroupService: Updated group XP successfully for previous day');

                    // Check if group can level up
                    await this.checkAndLevelUpGroup(quest.group_id);
                  }
                }
              }
            }
          }
        }
      }

      // Update streak - XP will be updated based on streak changes for today
      await this.updateGroupQuestStreak(questId);

      return { success: true };
    } catch (error) {
      console.error('GroupService: Error in toggleGroupQuestCompletion:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }

  private async updateGroupQuestStreak(questId: string): Promise<void> {
    console.log('GroupService: Updating quest streak for:', questId);

    try {
      // Get the quest details
      const { data: quest, error: questError } = await this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('id', questId)
        .single();

      if (questError || !quest) {
        console.error('GroupService: Error getting quest details for streak update:', questError);
        return;
      }

      console.log('GroupService: Found quest details for streak update:', quest);
      const groupId = quest.group_id;
      const oldStreak = quest.streak;

      // Get all eligible members of the group (members who joined before today)
      const today = new Date();
      const todayString = today.toISOString().split('T')[0];

      const { data: members, error: membersError } = await this.supabaseService.getClient()
        .from('group_members')
        .select('user_id, joined_date')
        .eq('group_id', groupId);

      if (membersError || !members || members.length === 0) {
        console.error('GroupService: Error getting group members for streak update:', membersError);
        return;
      }

      // Filter members who joined before today (they are eligible to participate)
      const eligibleMembers = members.filter(member => {
        const joinDate = new Date(member.joined_date);
        joinDate.setHours(0, 0, 0, 0);

        const todayDate = new Date(today);
        todayDate.setHours(0, 0, 0, 0);

        return joinDate < todayDate;
      });

      console.log(`GroupService: Found ${members.length} total members, ${eligibleMembers.length} eligible members for group:`, groupId);

      if (eligibleMembers.length === 0) {
        console.log('GroupService: No eligible members found for the group');
        return;
      }

      const eligibleMemberIds = eligibleMembers.map(member => member.user_id);

      // Get yesterday's date
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayString = yesterday.toISOString().split('T')[0];

      // Get yesterday's eligible members (members who joined before yesterday)
      const yesterdayEligibleMembers = members.filter(member => {
        const joinDate = new Date(member.joined_date);
        joinDate.setHours(0, 0, 0, 0);

        const yesterdayDate = new Date(yesterday);
        yesterdayDate.setHours(0, 0, 0, 0);

        return joinDate < yesterdayDate;
      });

      const yesterdayEligibleMemberIds = yesterdayEligibleMembers.map(member => member.user_id);
      console.log(`GroupService: Found ${yesterdayEligibleMembers.length} members eligible for yesterday:`, yesterdayEligibleMemberIds);

      // Check if all eligible members completed yesterday
      const { data: yesterdayProgress, error: yesterdayError } = await this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('user_id, completed')
        .eq('quest_id', questId)
        .eq('date', yesterdayString);

      if (yesterdayError) {
        console.error('GroupService: Error getting yesterday progress for streak update:', yesterdayError);
        return;
      }

      let allCompletedYesterday = false;

      if (yesterdayEligibleMemberIds.length > 0) {
        if (!yesterdayProgress || yesterdayProgress.length === 0) {
          console.log('GroupService: No progress found for yesterday');
          allCompletedYesterday = false;
        } else {
          const yesterdayCompletions = yesterdayProgress
            .filter(progress => progress.completed)
            .map(progress => progress.user_id);

          console.log('GroupService: Yesterday completions:', yesterdayCompletions);
          allCompletedYesterday = yesterdayEligibleMemberIds.every(id => yesterdayCompletions.includes(id));
          console.log(`GroupService: All ${yesterdayEligibleMemberIds.length} eligible members completed yesterday? ${allCompletedYesterday}`);
        }
      } else {
        console.log('GroupService: No eligible members for yesterday, skipping yesterday check');
      }

      // Check if all eligible members completed today
      const { data: todayProgress, error: todayError } = await this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('user_id, completed')
        .eq('quest_id', questId)
        .eq('date', todayString);

      if (todayError) {
        console.error('GroupService: Error getting today progress for streak update:', todayError);
        return;
      }

      let allCompletedToday = false;

      if (eligibleMemberIds.length > 0) {
        if (!todayProgress || todayProgress.length === 0) {
          console.log('GroupService: No progress found for today');
          allCompletedToday = false;
        } else {
          const todayCompletions = todayProgress
            .filter(progress => progress.completed)
            .map(progress => progress.user_id);

          console.log('GroupService: Today completions:', todayCompletions);
          allCompletedToday = eligibleMemberIds.every(id => todayCompletions.includes(id));
          console.log(`GroupService: All ${eligibleMemberIds.length} eligible members completed today? ${allCompletedToday}`);
        }
      } else {
        console.log('GroupService: No eligible members for today, skipping today check');
      }

      // Calculate new streak
      let newStreak = 0;

      // Get the date when the quest was created
      const createdDate = new Date(quest.created);
      createdDate.setHours(0, 0, 0, 0);

      // First, get the streak from previous days (not including today)
      let currentDate = new Date(yesterday);
      let streakCount = 0;

      // Count consecutive completed days from yesterday backwards
      while (true) {
        // Stop if we reach a date before the quest was created
        if (currentDate < createdDate) {
          console.log('GroupService: Reached date before quest was created, stopping streak calculation');
          break;
        }

        // Get eligible members for this date (joined at least one day before)
        const eligibleDate = new Date(currentDate);
        eligibleDate.setDate(eligibleDate.getDate() - 1);

        const historicalEligibleMembers = members.filter(member => {
          const joinDate = new Date(member.joined_date);
          joinDate.setHours(0, 0, 0, 0);
          return joinDate <= eligibleDate;
        });

        if (historicalEligibleMembers.length === 0) {
          console.log(`GroupService: No eligible members for date ${currentDate.toISOString().split('T')[0]}, stopping streak calculation`);
          break;
        }

        const historicalEligibleIds = historicalEligibleMembers.map(member => member.user_id);
        const currentDateString = currentDate.toISOString().split('T')[0];

        // Get progress for this date
        const { data: dateProgress, error: dateError } = await this.supabaseService.getClient()
          .from('group_quest_progress')
          .select('user_id, completed')
          .eq('quest_id', questId)
          .eq('date', currentDateString);

        if (dateError) {
          console.error(`GroupService: Error getting progress for date ${currentDateString}:`, dateError);
          break;
        }

        // Check if all eligible members completed the quest for this date
        if (!dateProgress || dateProgress.length === 0) {
          console.log(`GroupService: No progress found for date ${currentDateString}, breaking streak`);
          break;
        }

        const completedMemberIds = dateProgress
          .filter(progress => progress.completed)
          .map(progress => progress.user_id);

        const allEligibleCompleted = historicalEligibleIds.every(id =>
          completedMemberIds.includes(id)
        );

        if (!allEligibleCompleted) {
          console.log(`GroupService: Not all eligible members completed quest on ${currentDateString}, breaking streak`);
          break;
        }

        // Add to streak count
        streakCount++;
        console.log(`GroupService: All eligible members completed quest on ${currentDateString}, streak count: ${streakCount}`);

        // Move to the previous day
        currentDate.setDate(currentDate.getDate() - 1);
      }

      // Now calculate final streak based on today's completion
      if (allCompletedToday) {
        // If all completed today, add 1 to the streak count
        newStreak = streakCount + 1;
        console.log(`GroupService: All eligible members completed today, final streak: ${newStreak}`);
      } else {
        // If not all completed today, just use the streak count from previous days
        newStreak = streakCount;
        console.log(`GroupService: Not all eligible members completed today, final streak: ${newStreak}`);
      }

      console.log('GroupService: New streak value:', newStreak);

      // Update the streak
      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_quests')
        .update({ streak: newStreak })
        .eq('id', questId);

      if (updateError) {
        console.error('GroupService: Error updating streak:', updateError);
        return;
      }

      console.log('GroupService: Updated streak successfully');

      // Update XP based on streak changes
      if (newStreak !== oldStreak) {
        // Calculate XP value based on priority and eligible member count
        const xpValue = quest.priority === 'high' ? 2 : 1;
        const totalXP = xpValue * eligibleMembers.length;

        // Get the field name for the category XP
        const xpField = `${quest.category}_xp`;

        // Get the group's current XP
        const { data: group, error: groupError } = await this.supabaseService.getClient()
          .from('groups')
          .select('*')
          .eq('id', groupId)
          .single();

        if (groupError || !group) {
          console.error('GroupService: Error getting group for XP update:', groupError);
          return;
        }

        let currentXP = group[xpField as keyof typeof group] as number || 0;

        if (newStreak > oldStreak) {
          // Streak increased - add XP
          currentXP += totalXP;
          console.log(`GroupService: Streak increased (${oldStreak} -> ${newStreak}). Adding ${totalXP} XP to ${xpField}, new total: ${currentXP}`);
        } else if (newStreak < oldStreak) {
          // Streak decreased - subtract XP
          currentXP = Math.max(0, currentXP - totalXP);
          console.log(`GroupService: Streak decreased (${oldStreak} -> ${newStreak}). Removing ${totalXP} XP from ${xpField}, new total: ${currentXP}`);
        }

        // Update the group's XP
        const { error: xpUpdateError } = await this.supabaseService.getClient()
          .from('groups')
          .update({ [xpField]: currentXP })
          .eq('id', groupId);

        if (xpUpdateError) {
          console.error('GroupService: Error updating group XP:', xpUpdateError);
          return;
        }

        console.log('GroupService: Updated group XP successfully based on streak change');

        // Check if group can level up
        await this.checkAndLevelUpGroup(groupId);
      } else {
        console.log('GroupService: No XP update needed - streak unchanged');
      }
    } catch (error) {
      console.error('GroupService: Error in updateGroupQuestStreak:', error);
    }
  }

  /**
   * Update group XP directly (for special cases like quest reset)
   * Note: For normal quest completion, XP is handled by updateGroupQuestStreak
   */
  private async updateGroupXP(
    questId: string,
    groupId: string,
    category: string,
    isCompleted: boolean,
    wasCompleted: boolean,
    priority: string
  ): Promise<void> {
    console.log('GroupService: Directly updating group XP (special case):', questId, groupId, category, isCompleted, wasCompleted, priority);

    try {
      // Only update XP if completion status changed
      if (isCompleted === wasCompleted) {
        console.log('GroupService: No XP update needed - completion status unchanged');
        return;
      }

      // Get all eligible members of the group (members who joined before today)
      const today = new Date();
      const todayString = today.toISOString().split('T')[0];

      const { data: members, error: membersError } = await this.supabaseService.getClient()
        .from('group_members')
        .select('user_id, joined_date')
        .eq('group_id', groupId);

      if (membersError || !members || members.length === 0) {
        console.error('GroupService: Error getting group members for XP update:', membersError);
        return;
      }

      // Filter members who joined before today (they are eligible to participate)
      const eligibleMembers = members.filter(member => {
        const joinDate = new Date(member.joined_date);
        joinDate.setHours(0, 0, 0, 0);

        const todayDate = new Date(today);
        todayDate.setHours(0, 0, 0, 0);

        return joinDate < todayDate;
      });

      if (eligibleMembers.length === 0) {
        console.log('GroupService: No eligible members found for the group');
        return;
      }

      const eligibleMemberIds = eligibleMembers.map(member => member.user_id);
      console.log(`GroupService: Found ${members.length} total members, ${eligibleMembers.length} eligible members for group:`, groupId);

      // Get the field name for the category XP
      const xpField = `${category}_xp`;

      // Calculate XP value based on priority and eligible member count
      const xpValue = priority === 'high' ? 2 : 1;
      const totalXP = xpValue * eligibleMembers.length;
      console.log('GroupService: Total XP to award/subtract:', totalXP);

      // Get the group
      const { data: group, error: groupError } = await this.supabaseService.getClient()
        .from('groups')
        .select('*')
        .eq('id', groupId)
        .single();

      if (groupError || !group) {
        console.error('GroupService: Error getting group for XP update:', groupError);
        return;
      }

      console.log('GroupService: Found group for XP update:', group);
      let currentXP = group[xpField as keyof typeof group] as number || 0;

      // For direct XP updates, we simply add or subtract based on the completion status
      if (isCompleted) {
        // Add XP to the group
        currentXP += totalXP;
        console.log(`GroupService: Adding ${totalXP} XP to ${xpField}, new total: ${currentXP}`);
      } else {
        // Remove XP from the group
        currentXP = Math.max(0, currentXP - totalXP);
        console.log(`GroupService: Removing ${totalXP} XP from ${xpField}, new total: ${currentXP}`);
      }

      // Update the group's XP
      const { error: updateError } = await this.supabaseService.getClient()
        .from('groups')
        .update({ [xpField]: currentXP })
        .eq('id', groupId);

      if (updateError) {
        console.error('GroupService: Error updating group XP:', updateError);
        return;
      }

      console.log('GroupService: Updated group XP successfully');

      // Check if group can level up
      await this.checkAndLevelUpGroup(groupId);
    } catch (error) {
      console.error('GroupService: Error in updateGroupXP:', error);
    }
  }

  async checkAndLevelUpGroup(groupId: string): Promise<boolean> {
    console.log('GroupService: Checking if group can level up:', groupId);

    // Get the group
    const { data: group, error: groupError } = await this.supabaseService.getClient()
      .from('groups')
      .select('*')
      .eq('id', groupId)
      .single();

    if (groupError || !group) {
      console.error('GroupService: Error getting group for level up check:', groupError);
      return false;
    }

    console.log('GroupService: Found group for level up check:', group);

    // Max level is 100
    if (group.level >= 100) {
      console.log('GroupService: Group already at max level (100)');
      return false;
    }

    // Get required XP for next level from the XP service
    const requiredXP = await firstValueFrom(this.xpService.getRequiredXpForNextLevel(group.level, EntityType.GROUP));
    console.log(`GroupService: XP required for level ${group.level + 1}: ${requiredXP}`);

    // Check if group has enough XP in all categories
    const categories = ['strength', 'money', 'health', 'knowledge'];
    const hasEnoughXP = categories.every(category => {
      const fieldName = `${category}_xp`;
      const xpValue = group[fieldName as keyof typeof group];
      const hasEnough = typeof xpValue === 'number' && xpValue >= requiredXP;
      console.log(`GroupService: ${category}_xp: ${xpValue}/${requiredXP} - ${hasEnough ? 'Enough' : 'Not enough'}`);
      return hasEnough;
    });

    if (hasEnoughXP) {
      console.log(`GroupService: Group has enough XP to level up to level ${group.level + 1}`);

      // Level up and deduct required XP from each category
      const updates: any = {
        level: group.level + 1
      };

      categories.forEach(category => {
        const fieldName = `${category}_xp`;
        const currentXP = group[fieldName as keyof typeof group] as number;
        updates[fieldName] = currentXP - requiredXP;
        console.log(`GroupService: Deducting ${requiredXP} from ${fieldName}, new value: ${currentXP - requiredXP}`);
      });

      // Update the group
      const { error: updateError } = await this.supabaseService.getClient()
        .from('groups')
        .update(updates)
        .eq('id', groupId);

      if (updateError) {
        console.error('GroupService: Error updating group after level up:', updateError);
        return false;
      }

      console.log('GroupService: Successfully leveled up group and deducted XP');
      return true;
    }

    console.log('GroupService: Group does not have enough XP to level up');
    return false;
  }

  // Group Side Quest operations


  getGroupSideQuestPool(questId: string): Observable<GroupSideQuestPool | undefined> {
    console.log('GroupService: Getting side quest pool item:', questId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .eq('id', questId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting side quest pool item:', response.error);
          return undefined;
        }

        console.log('GroupService: Found side quest pool item:', response.data);
        return response.data as GroupSideQuestPool;
      }),
      catchError(error => {
        console.error('GroupService: Error getting side quest pool item:', error);
        return of(undefined);
      })
    );
  }



  async createGroupSideQuest(groupId: string): Promise<string> {
    console.log('GroupService: Creating side quest for group:', groupId);

    // Get a random active quest from the pool
    const { data: poolQuests, error: poolError } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .select('*')
      .eq('active', true);

    if (poolError) {
      console.error('GroupService: Error getting side quest pool:', poolError);
      throw new Error('Error getting side quest pool: ' + poolError.message);
    }

    if (!poolQuests || poolQuests.length === 0) {
      console.error('GroupService: No active side quests found in the pool');
      throw new Error('No active side quests available');
    }

    console.log(`GroupService: Found ${poolQuests.length} active side quests in the pool`);

    // Select a random quest from the pool
    const randomIndex = Math.floor(Math.random() * poolQuests.length);
    const randomQuest = poolQuests[randomIndex];
    console.log('GroupService: Selected random quest:', randomQuest);

    // Create new group side quest
    const newSideQuest = {
      group_id: groupId,
      current_quest_id: randomQuest.id,
      streak: 0,
      date_assigned: new Date(),
      completed: false,
      value_achieved: 0,
      category: randomQuest.category
    };

    console.log('GroupService: Creating new side quest:', newSideQuest);

    const { data: insertData, error: insertError } = await this.supabaseService.getClient()
      .from('group_side_quests')
      .insert(newSideQuest)
      .select('id')
      .single();

    if (insertError) {
      console.error('GroupService: Error creating side quest:', insertError);
      throw new Error('Error creating side quest: ' + insertError.message);
    }

    console.log('GroupService: Created side quest with ID:', insertData.id);
    return insertData.id;
  }

  // Group Side Quest Member Status operations


  async toggleGroupSideQuestCompletion(statusId: string): Promise<void> {
    console.log('GroupService: Toggling side quest completion for status:', statusId);

    // Get the current status
    const { data: status, error: statusError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .select('*')
      .eq('id', statusId)
      .single();

    if (statusError || !status) {
      console.error('GroupService: Error getting side quest member status:', statusError);
      throw new Error('Member status not found');
    }

    console.log('GroupService: Found side quest member status:', status);
    const wasCompleted = status.completed;

    // Toggle completion
    const isCompleted = !wasCompleted;
    const today = new Date();

    // Get the group side quest
    const { data: groupQuest, error: questError } = await this.supabaseService.getClient()
      .from('group_side_quests')
      .select('*')
      .eq('id', status.group_quest_id)
      .single();

    if (questError || !groupQuest) {
      console.error('GroupService: Error getting group side quest:', questError);
      throw new Error('Group side quest not found');
    }

    console.log('GroupService: Found group side quest:', groupQuest);

    // Get the quest details from the pool
    const { data: questPool, error: poolError } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .select('*')
      .eq('id', groupQuest.current_quest_id)
      .single();

    if (poolError || !questPool) {
      console.error('GroupService: Error getting quest pool item:', poolError);
      throw new Error('Side quest pool item not found');
    }

    console.log('GroupService: Found quest pool item:', questPool);

    // Update the status
    const { error: updateError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .update({
        completed: isCompleted,
        value_achieved: isCompleted ? questPool.goal_value : 0,
        last_updated: today
      })
      .eq('id', statusId);

    if (updateError) {
      console.error('GroupService: Error updating side quest member status:', updateError);
      throw new Error('Error updating status: ' + updateError.message);
    }

    console.log('GroupService: Updated side quest member status successfully');

    // Update the group side quest completion status
    if (groupQuest.id) {
      await this.updateGroupSideQuestCompletionStatus(groupQuest.id);
    }
  }

  private async updateGroupSideQuestCompletionStatus(groupQuestId: string): Promise<void> {
    console.log('GroupService: Updating side quest completion status for quest:', groupQuestId);

    // Get the group side quest
    const { data: groupQuest, error: questError } = await this.supabaseService.getClient()
      .from('group_side_quests')
      .select('*')
      .eq('id', groupQuestId)
      .single();

    if (questError || !groupQuest) {
      console.error('GroupService: Error getting group side quest:', questError);
      return;
    }

    console.log('GroupService: Found group side quest:', groupQuest);
    const groupId = groupQuest.group_id;

    // Get all members of the group
    const { data: members, error: membersError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('user_id, joined_date')
      .eq('group_id', groupId);

    if (membersError || !members || members.length === 0) {
      console.error('GroupService: Error getting group members:', membersError);
      return;
    }

    console.log(`GroupService: Found ${members.length} members for group:`, groupId);

    // Filter eligible members (joined at least one day before)
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const eligibleMembers = members.filter(member => {
      const joinDate = new Date(member.joined_date);
      joinDate.setHours(0, 0, 0, 0);
      return joinDate <= yesterday;
    });

    if (eligibleMembers.length === 0) {
      console.log('GroupService: No eligible members found for the group');
      return;
    }

    const memberIds = eligibleMembers.map(member => member.user_id);

    // Get all member statuses
    const { data: statuses, error: statusesError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .select('*')
      .eq('group_quest_id', groupQuestId);

    if (statusesError || !statuses) {
      console.error('GroupService: Error getting member statuses:', statusesError);
      return;
    }

    console.log(`GroupService: Found ${statuses.length} member statuses`);

    const completedMemberIds = statuses
      .filter(status => status.completed)
      .map(status => status.member_id);

    console.log('GroupService: Completed member IDs:', completedMemberIds);

    // Check if all members have completed the quest
    const allCompleted = memberIds.every(id => completedMemberIds.includes(id));
    console.log('GroupService: All members completed:', allCompleted);

    // Update the group side quest
    const updates: any = {
      completed: allCompleted
    };

    if (allCompleted) {
      // Get the quest details from the pool
      const { data: questPool, error: poolError } = await this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .eq('id', groupQuest.current_quest_id)
        .single();

      if (poolError || !questPool) {
        console.error('GroupService: Error getting quest pool item:', poolError);
      } else {
        console.log('GroupService: Found quest pool item:', questPool);
        updates.value_achieved = questPool.goal_value;
      }

      updates.last_completed_date = new Date();

      // Update group XP
      await this.updateGroupXPForSideQuest(groupId, groupQuest.category, true);
    } else {
      // If not all members have completed, update XP if it was previously completed
      if (groupQuest.completed) {
        await this.updateGroupXPForSideQuest(groupId, groupQuest.category, false);
      }
    }

    const { error: updateError } = await this.supabaseService.getClient()
      .from('group_sidequests')
      .update(updates)
      .eq('id', groupQuestId);

    if (updateError) {
      console.error('GroupService: Error updating group side quest:', updateError);
      return;
    }

    console.log('GroupService: Updated group side quest successfully');
  }



  // Group Activities
  getGroupActivities(groupId: string): Observable<any[]> {
    console.log('GroupService: Getting activities for group:', groupId);

    return from(
      this.supabaseService.getClient()
        .from('group_activities')
        .select('*')
        .eq('group_id', groupId)
        .order('created_at', { ascending: false })
        .limit(20)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting group activities:', response.error);
          return [];
        }

        console.log(`GroupService: Found ${response.data.length} activities for group:`, groupId);
        return response.data;
      }),
      catchError(error => {
        console.error('GroupService: Error getting group activities:', error);
        return of([]);
      })
    );
  }

  async addGroupActivity(groupId: string, activityData: { user_id: string, message: string, icon: string, color: string }): Promise<void> {
    console.log('GroupService: Adding activity to group:', groupId, activityData);

    // Get user's nickname in the group
    const { data: memberData, error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('nickname')
      .eq('group_id', groupId)
      .eq('user_id', activityData.user_id)
      .single();

    if (memberError) {
      console.error('GroupService: Error getting member nickname:', memberError);
      throw new Error(memberError.message);
    }

    const activity = {
      group_id: groupId,
      user_id: activityData.user_id,
      user_nickname: memberData.nickname,
      message: activityData.message,
      icon: activityData.icon,
      color: activityData.color,
      created_at: new Date().toISOString()
    };

    const { error } = await this.supabaseService.getClient()
      .from('group_activities')
      .insert(activity);

    if (error) {
      console.error('GroupService: Error adding group activity:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Added activity to group successfully');
  }

  // Group Side Quests
  getGroupSideQuests(groupId: string): Observable<any[]> {
    console.log('GroupService: Getting side quests for group:', groupId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*')
        .eq('group_id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting group side quests:', response.error);
          return [];
        }

        console.log(`GroupService: Found ${response.data.length} side quests for group:`, groupId);
        return response.data;
      }),
      catchError(error => {
        console.error('GroupService: Error getting group side quests:', error);
        return of([]);
      })
    );
  }

  getGroupSideQuest(groupId: string): Observable<GroupSideQuest | null> {
    console.log('GroupService: Getting side quest for group:', groupId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*')
        .eq('group_id', groupId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          if (response.error.code === 'PGRST116') {
            // No results found
            console.log('GroupService: No side quest found for group:', groupId);
            return null;
          }
          console.error('GroupService: Error getting group side quest:', response.error);
          return null;
        }

        console.log('GroupService: Found side quest for group:', response.data);
        return response.data as GroupSideQuest;
      }),
      catchError(error => {
        console.error('GroupService: Error getting group side quest:', error);
        return of(null);
      })
    );
  }

  getGroupSideQuestPoolItem(questId: string): Observable<GroupSideQuestPool | null> {
    console.log('GroupService: Getting side quest pool item:', questId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .eq('id', questId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting side quest pool item:', response.error);
          return null;
        }

        console.log('GroupService: Found side quest pool item:', response.data);
        return response.data as GroupSideQuestPool;
      }),
      catchError(error => {
        console.error('GroupService: Error getting side quest pool item:', error);
        return of(null);
      })
    );
  }

  getGroupSideQuestMemberStatus(questId: string, userId: string): Observable<GroupSideQuestMemberStatus | null> {
    console.log('GroupService: Getting side quest member status:', questId, userId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*')
        .eq('group_quest_id', questId)
        .eq('member_id', userId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          if (response.error.code === 'PGRST116') {
            // No results found
            console.log('GroupService: No member status found for quest:', questId, 'and user:', userId);
            return null;
          }
          console.error('GroupService: Error getting side quest member status:', response.error);
          return null;
        }

        console.log('GroupService: Found side quest member status:', response.data);
        return response.data as GroupSideQuestMemberStatus;
      }),
      catchError(error => {
        console.error('GroupService: Error getting side quest member status:', error);
        return of(null);
      })
    );
  }

  getGroupSideQuestMemberStatuses(questId: string): Observable<GroupSideQuestMemberStatus[]> {
    console.log('GroupService: Getting all side quest member statuses for quest:', questId);

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*')
        .eq('group_quest_id', questId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('GroupService: Error getting side quest member statuses:', response.error);
          return [];
        }

        console.log(`GroupService: Found ${response.data.length} member statuses for quest:`, questId);
        return response.data as GroupSideQuestMemberStatus[];
      }),
      catchError(error => {
        console.error('GroupService: Error getting side quest member statuses:', error);
        return of([]);
      })
    );
  }

  async updateGroupSideQuestMemberStatus(
    statusId: string,
    completed: boolean,
    valueAchieved: number
  ): Promise<void> {
    console.log('GroupService: Updating side quest member status:', statusId, completed, valueAchieved);

    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .update({
        completed: completed,
        value_achieved: valueAchieved,
        last_updated: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
      })
      .eq('id', statusId);

    if (error) {
      console.error('GroupService: Error updating side quest member status:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Updated side quest member status successfully');

    // Update the group side quest completion status
    await this.updateGroupSideQuestCompletion(statusId);
  }

  async createGroupSideQuestMemberStatus(
    questId: string,
    userId: string,
    completed: boolean,
    valueAchieved: number
  ): Promise<string> {
    console.log('GroupService: Creating side quest member status:', questId, userId, completed, valueAchieved);

    const newStatus = {
      group_quest_id: questId,
      member_id: userId,
      completed: completed,
      value_achieved: valueAchieved,
      last_updated: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
    };

    const { data, error } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .insert(newStatus)
      .select('id')
      .single();

    if (error) {
      console.error('GroupService: Error creating side quest member status:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Created side quest member status successfully with ID:', data.id);

    // Update the group side quest completion status
    await this.updateGroupSideQuestCompletion(data.id);

    return data.id;
  }

  private async updateGroupSideQuestCompletion(statusId: string): Promise<void> {
    console.log('GroupService: Updating group side quest completion for status:', statusId);

    // Get the status to find the quest ID
    const { data: status, error: statusError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .select('*')
      .eq('id', statusId)
      .single();

    if (statusError || !status) {
      console.error('GroupService: Error getting status for completion update:', statusError);
      return;
    }

    console.log('GroupService: Found status for completion update:', status);
    const questId = status.group_quest_id;

    // Get the group side quest
    const { data: quest, error: questError } = await this.supabaseService.getClient()
      .from('group_sidequests')
      .select('*')
      .eq('id', questId)
      .single();

    if (questError || !quest) {
      console.error('GroupService: Error getting quest for completion update:', questError);
      return;
    }

    console.log('GroupService: Found quest for completion update:', quest);
    const groupId = quest.group_id;

    // Get all members of the group
    const { data: members, error: membersError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('user_id, joined_date')
      .eq('group_id', groupId);

    if (membersError || !members || members.length === 0) {
      console.error('GroupService: Error getting group members for completion update:', membersError);
      return;
    }

    console.log(`GroupService: Found ${members.length} members for group:`, groupId);

    // Get today's date
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];

    // Get yesterday's date
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayString = yesterday.toISOString().split('T')[0];

    // Filter eligible members (joined at least one day before)
    const eligibleDate = yesterday.toISOString().split('T')[0];
    const eligibleMembers = members.filter(member => {
      const joinedDate = new Date(member.joined_date);
      return joinedDate <= new Date(eligibleDate);
    });

    console.log(`GroupService: Found ${eligibleMembers.length} eligible members for group:`, groupId);

    if (eligibleMembers.length === 0) {
      console.log('GroupService: No eligible members found');
      return;
    }

    const eligibleMemberIds = eligibleMembers.map(member => member.user_id);

    // Get all member statuses for this quest
    const { data: allStatuses, error: statusesError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .select('*')
      .eq('group_quest_id', questId);

    if (statusesError) {
      console.error('GroupService: Error getting all statuses for completion update:', statusesError);
      return;
    }

    console.log(`GroupService: Found ${allStatuses ? allStatuses.length : 0} statuses for quest:`, questId);

    if (!allStatuses || allStatuses.length === 0) {
      console.log('GroupService: No statuses found');
      return;
    }

    // Check if all eligible members have completed the quest
    const completedMemberIds = allStatuses
      .filter(s => s.completed)
      .map(s => s.member_id);

    console.log('GroupService: Completed member IDs:', completedMemberIds);
    console.log('GroupService: Eligible member IDs:', eligibleMemberIds);

    const allEligibleCompleted = eligibleMemberIds.every(id => completedMemberIds.includes(id));
    console.log('GroupService: All eligible members completed:', allEligibleCompleted);

    // Update the quest completion status
    if (allEligibleCompleted) {
      // Get the quest details to get the goal value
      const { data: questPool, error: poolError } = await this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .eq('id', quest.current_quest_id)
        .single();

      if (poolError || !questPool) {
        console.error('GroupService: Error getting quest pool item for completion update:', poolError);
        return;
      }

      console.log('GroupService: Found quest pool item for completion update:', questPool);

      // Update the quest as completed
      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_sidequests')
        .update({
          completed: true,
          value_achieved: questPool.goal_value,
          last_completed_date: todayString
        })
        .eq('id', questId);

      if (updateError) {
        console.error('GroupService: Error updating quest completion:', updateError);
        return;
      }

      console.log('GroupService: Updated quest completion successfully');

      // Check if yesterday was completed to update streak
      if (quest.last_completed_date === yesterdayString) {
        // Increment streak
        const { error: streakError } = await this.supabaseService.getClient()
          .from('group_sidequests')
          .update({
            streak: quest.streak + 1
          })
          .eq('id', questId);

        if (streakError) {
          console.error('GroupService: Error updating quest streak:', streakError);
          return;
        }

        console.log('GroupService: Incremented quest streak successfully');
      } else if (quest.last_completed_date !== todayString) {
        // Start new streak
        const { error: streakError } = await this.supabaseService.getClient()
          .from('group_sidequests')
          .update({
            streak: 1
          })
          .eq('id', questId);

        if (streakError) {
          console.error('GroupService: Error updating quest streak:', streakError);
          return;
        }

        console.log('GroupService: Started new quest streak successfully');
      }

      // Update group XP
      await this.updateGroupXPForSideQuest(groupId, quest.category, true);
    } else {
      // Update the quest as not completed
      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_sidequests')
        .update({
          completed: false
        })
        .eq('id', questId);

      if (updateError) {
        console.error('GroupService: Error updating quest completion:', updateError);
        return;
      }

      console.log('GroupService: Updated quest as not completed successfully');

      // Update group XP if it was previously completed
      if (quest.completed) {
        await this.updateGroupXPForSideQuest(groupId, quest.category, false);
      }
    }
  }

  private async updateGroupXPForSideQuest(
    groupId: string,
    category: string,
    isCompleted: boolean
  ): Promise<void> {
    console.log('GroupService: Updating group XP for side quest:', groupId, category, isCompleted);

    // Get all members of the group
    const { data: members, error: membersError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('user_id, joined_date')
      .eq('group_id', groupId);

    if (membersError || !members || members.length === 0) {
      console.error('GroupService: Error getting group members for XP update:', membersError);
      return;
    }

    // Filter members who were eligible (joined at least one day before)
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const eligibleMembers = members.filter(member => {
      const joinDate = new Date(member.joined_date);
      joinDate.setHours(0, 0, 0, 0);
      return joinDate <= yesterday;
    });

    if (eligibleMembers.length === 0) {
      console.log('GroupService: No eligible members found for the group');
      return;
    }

    const memberCount = eligibleMembers.length;
    console.log(`GroupService: Found ${memberCount} members for group:`, groupId);

    // Get the field name for the category XP
    const xpField = `${category}_xp`;

    // Calculate XP value based on member count
    // Side quests give 2 XP per eligible member
    const totalXP = 2 * memberCount;
    console.log('GroupService: Total XP to award:', totalXP);

    // Get the group
    const { data: group, error: groupError } = await this.supabaseService.getClient()
      .from('groups')
      .select('*')
      .eq('id', groupId)
      .single();

    if (groupError || !group) {
      console.error('GroupService: Error getting group for XP update:', groupError);
      return;
    }

    console.log('GroupService: Found group for XP update:', group);
    let currentXP = group[xpField as keyof typeof group] as number || 0;

    if (isCompleted) {
      // Add XP to the group
      currentXP += totalXP;
      console.log(`GroupService: Adding ${totalXP} XP to ${xpField}, new total: ${currentXP}`);
    } else {
      // Remove XP from the group
      currentXP = Math.max(0, currentXP - totalXP);
      console.log(`GroupService: Removing ${totalXP} XP from ${xpField}, new total: ${currentXP}`);
    }

    // Update the group's XP
    const { error: updateError } = await this.supabaseService.getClient()
      .from('groups')
      .update({ [xpField]: currentXP })
      .eq('id', groupId);

    if (updateError) {
      console.error('GroupService: Error updating group XP:', updateError);
      return;
    }

    console.log('GroupService: Updated group XP successfully');

    // Check if group can level up
    await this.checkAndLevelUpGroup(groupId);
  }

  async updateSideQuestStatus(groupId: string, questId: string, completed: boolean): Promise<void> {
    console.log('GroupService: Updating side quest status:', groupId, questId, completed);

    const userId = this.supabaseService.getCurrentUserId();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Check if status entry exists
    const { data, error: checkError } = await this.supabaseService.getClient()
      .from('group_side_quest_statuses')
      .select('id')
      .eq('quest_id', questId)
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('GroupService: Error checking side quest status:', checkError);
      throw new Error(checkError.message);
    }

    if (data) {
      // Update existing status
      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_side_quest_statuses')
        .update({ completed })
        .eq('id', data.id);

      if (updateError) {
        console.error('GroupService: Error updating side quest status:', updateError);
        throw new Error(updateError.message);
      }
    } else {
      // Create new status
      const { error: insertError } = await this.supabaseService.getClient()
        .from('group_side_quest_statuses')
        .insert({
          quest_id: questId,
          user_id: userId,
          completed,
          completed_at: completed ? new Date().toISOString() : null
        });

      if (insertError) {
        console.error('GroupService: Error creating side quest status:', insertError);
        throw new Error(insertError.message);
      }
    }

    console.log('GroupService: Updated side quest status successfully');
  }

  async inviteUserToGroup(groupId: string, username: string): Promise<void> {
    console.log('GroupService: Inviting user to group by username:', groupId, username);

    // Get the current user's username (the inviter)
    const currentUser = await this.supabaseService.getCurrentUser();
    if (!currentUser || !currentUser.username) {
      throw new Error('User not authenticated or username not available');
    }

    const inviterUsername = currentUser.username;
    console.log('GroupService: Inviter username:', inviterUsername);

    // Check if the username exists
    const { data: userData, error: userError } = await this.supabaseService.getClient()
      .from('profiles')
      .select('id')
      .eq('username', username)
      .single();

    if (userError) {
      console.error('GroupService: Error finding user by username:', userError);
      throw new Error('User not found');
    }

    // Check if user is already a member
    const { data: memberData, error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', userData.id);

    if (memberError) {
      console.error('GroupService: Error checking if user is already a member:', memberError);
      throw new Error(memberError.message);
    }

    if (memberData && memberData.length > 0) {
      throw new Error('User is already a member of this group');
    }

    // Check if invite already exists
    const { data: existingInvites, error: checkError } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .select('id')
      .eq('group_id', groupId)
      .eq('username_invited', username);

    if (checkError) {
      console.error('GroupService: Error checking existing invites:', checkError);
      throw new Error(checkError.message);
    }

    if (existingInvites && existingInvites.length > 0) {
      console.log('GroupService: Invite already exists for this user');
      throw new Error('An invitation has already been sent to this user');
    }

    // Create a join request with the inviter's username instead of UUID
    const { error: requestError } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .insert({
        group_id: groupId,
        username_invited: username,
        invited_by: inviterUsername, // Use username instead of UUID
        created: new Date().toISOString(),
        status: 'pending'
      });

    if (requestError) {
      console.error('GroupService: Error creating join request:', requestError);
      throw new Error(requestError.message);
    }

    console.log('GroupService: Invited user to group successfully');
  }

  async generateInvitationCode(groupId: string): Promise<string> {
    console.log('GroupService: Generating invitation code for group:', groupId);

    // Generate a random 6-character code
    const code = Math.random().toString(36).substring(2, 8).toUpperCase();
    console.log('GroupService: Generated code:', code);

    // Set expiry to 24 hours from now
    const expiry = new Date();
    expiry.setHours(expiry.getHours() + 24);
    console.log('GroupService: Code expiry:', expiry);

    // Update the group with the new code and expiry
    const { error } = await this.supabaseService.getClient()
      .from('groups')
      .update({
        invitation_code: code,
        code_expiry: expiry.toISOString()
      })
      .eq('id', groupId);

    if (error) {
      console.error('GroupService: Error generating invitation code:', error);
      throw new Error(error.message);
    }

    console.log('GroupService: Invitation code generated successfully');
    return code;
  }
}
