{"ast": null, "code": "import GoTrueAdminApi from './GoTrueAdminApi';\nimport GoTrueClient from './GoTrueClient';\nimport AuthAdminApi from './AuthAdminApi';\nimport AuthClient from './AuthClient';\nexport { GoTrueAdminApi, GoTrueClient, AuthAdminApi, AuthClient };\nexport * from './lib/types';\nexport * from './lib/errors';\nexport { navigatorLock, NavigatorLockAcquireTimeoutError, internals as lockInternals } from './lib/locks';\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}