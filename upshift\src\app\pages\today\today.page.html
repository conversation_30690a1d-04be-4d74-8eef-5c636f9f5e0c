<ion-content class="ion-padding" [fullscreen]="true">
  <div class="background-container">
    <div class="gradient-bg"></div>
    <div class="celestial-body"></div>
  </div>
  <ion-header class="ion-no-border">
    <ion-toolbar>
      <app-header [headerText]="headerText"></app-header>
      <ion-row class="week-row ion-padding-top">
        <div class="day-container" *ngFor="let date of weekDates; let i = index">
          <ion-text class="day-name" [class.active]="date.is_today"
            [class.selected]="date.is_selected && !date.is_today" [class.unselected]="!date.is_selected">
            {{ ['M', 'T', 'W', 'T', 'F', 'S', 'S'][i] }}
          </ion-text>
          <div class="date" [class.selected]="date.is_selected" [class.disabled]="date.is_future"
            (click)="!date.is_future && selectDate(date)">
            <svg class="date-progress" viewBox="0 0 36 36">
              <circle cx="18" cy="18" r="13" stroke-dasharray="81.68, 81.68" class="background-circle"></circle>

              <circle cx="18" cy="18" r="13" *ngIf="!date.is_future"
                [attr.stroke-dasharray]="(date.completion_percentage * 81.68 / 100) + ', 81.68'"
                [attr.data-date]="date.date" class="progress-circle" [class.low]="date.completion_percentage < 50">
              </circle>
            </svg>
          </div>
        </div>
      </ion-row>
    </ion-toolbar>
  </ion-header>
  <ion-grid>
    <ion-row class="ion-justify-content-center">
      <div class="heartbeat-circle gradient-text"></div>
    </ion-row>
    <ion-row class="add-quest">
      <ion-col>
        <h2>Quests</h2>
      </ion-col>
      <ion-col>
        <ion-button fill="clear" id="add-quest-btn" class="add-quest-btn" (click)="openAddQuestModal($event)">
          + Add Quest
        </ion-button>
      </ion-col>
    </ion-row>
    <ion-row class="quests">
      <ion-col>
        <ion-card *ngif="quests.length === 0" class="ion-text-center no-quest-card">
          <ion-card-header>
            <h2>No quests found..</h2>
          </ion-card-header>
          <ion-card-content>
            <ion-row>
              <ion-col size="8">
                <ion-text>No quests found. Try adding a quest ;)</ion-text>
              </ion-col>
              <ion-col size="4">
                <ion-icon name="warning"></ion-icon>
              </ion-col>
            </ion-row>
          </ion-card-content>
        </ion-card>
        <ion-card *ngFor="let quest of quests" class="quest-item ion-margin-bottom" [class.completed]="quest.completed"
          [attr.data-quest-id]="quest.id" [attr.data-regular-quest]="true" (click)="toggleQuest(quest)">
          <ion-row>
            <ion-col size="2">
              <div class="quest-icon">{{ quest.emoji }}</div>
            </ion-col>
            <ion-col size="8" class="quest-info">
              <h3>{{ quest.name }}</h3>
              <ion-text>{{ quest.description }}</ion-text>
              <div class="progress-container">
                <div class="progress-time"
                  *ngIf="quest.goal_unit === 'time' || quest.goal_unit === 'min' || quest.goal_unit === 'hr' || quest.goal_unit === 'sec'">
                  <ion-range min="0" [max]="quest.goal_value" [(ngModel)]="quest.value_achieved" class="progress-slider"
                    [attr.data-quest-id]="quest.id" [attr.data-quest-type]="quest.quest_type" [step]="1" snaps="true"
                    ticks="false" snaps-per-step="true" (ionChange)="updateQuestProgress(quest, $event)"
                    (ionInput)="$event.target && updateSliderBackground($event.target)"
                    style="--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%">
                  </ion-range>
                  <ion-text class="progress-text">
                    {{ quest.value_achieved }}{{ quest.goal_unit === 'min' ? 'm'
                    : quest.goal_unit === 'hr' ? 'h' : 's' }}/{{
                    quest.goal_value }}{{ quest.goal_unit === 'min' ? 'm' :
                    quest.goal_unit === 'hr' ? 'h' : 's' }}
                  </ion-text>
                </div>
                <div class="progress"
                  *ngIf="quest.goal_unit !== 'time' && quest.goal_unit !== 'min' && quest.goal_unit !== 'hr' && quest.goal_unit !== 'sec'">
                  <ion-range min="0" [max]="quest.goal_value" [(ngModel)]="quest.value_achieved" class="progress-slider"
                    [attr.data-quest-id]="quest.id" [attr.data-quest-type]="quest.quest_type" [step]="1" snaps="true"
                    ticks="false" snaps-per-step="true" (ionChange)="updateQuestProgress(quest, $event)"
                    (ionInput)="$event.target && updateSliderBackground($event.target)"
                    style="--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%">
                  </ion-range>
                  <ion-text class="progress-text">
                    {{ quest.value_achieved }}/{{ quest.goal_value }}
                    <ng-container *ngIf="quest.goal_unit !== 'count'">
                      {{ quest.goal_unit }}
                    </ng-container>
                  </ion-text>
                </div>
              </div>
            </ion-col>
            <ion-col size="2">
              <ion-text class="quest-streak" *ngIf="isSameDay(selectedDate, getToday())">
                🔥{{ quest.streak }}d
              </ion-text>
            </ion-col>
          </ion-row>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row class="ion-padding-top">
      <ion-col>
        <h2>Daily Side Quest</h2>
      </ion-col>
    </ion-row>
    <ion-row class="quests">
      <ion-col>
        <ion-card *ngIf="dailyQuest && dailyQuest.current_quest" class="quest-item"
          [class.completed]="dailyQuest.completed" [attr.data-quest-id]="dailyQuest.id" [attr.data-side-quest]="true"
          (click)="toggleSideQuest(dailyQuest)">
          <ion-row>
            <ion-col size="2">
              <div class="quest-icon">{{ dailyQuest.emoji }}</div>
            </ion-col>
            <ion-col class="quest-info" size="8">
              <h3>{{ dailyQuest.current_quest.name }}</h3>
              <ion-text class="quest-description">{{ dailyQuest.current_quest.description }}</ion-text>
              <!-- <div class="progress-text">
              {{ dailyQuest.value_achieved }}/{{ dailyQuest.current_quest.goal_value }}
              <ng-container *ngIf="dailyQuest.current_quest.goal_unit !== 'count'">
                {{ dailyQuest.current_quest.goal_unit }}
              </ng-container>
            </div> -->
            </ion-col>
            <ion-col size="2">
              <div class="quest-streak" *ngIf="isSameDay(selectedDate, getToday())">
                🔥{{ dailyQuest.streak }}d
              </div>
            </ion-col>
          </ion-row>
        </ion-card>
        <ion-card class="quest-item" *ngIf="!dailyQuest">
          <ion-card-header>
            <ion-card-title> Daily Side Quest </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-text>
              No daily side quests are currently available. Please check back
              later or contact an administrator.
            </ion-text>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>

  <!-- Add Quest Modal -->
  <!-- <ion-modal [isOpen]="showAddQuestModal" class="add-quest-modal" (ionModalDidDismiss)="closeAddQuestModal()" -->
  <ion-modal [isOpen]="true" class="add-quest-modal" (ionModalDidDismiss)="closeAddQuestModal()"
    [backdropDismiss]="true">
    <ng-template>
      <ion-content class="ion-padding">
        <app-aurora></app-aurora>
        <ion-grid>
          <ion-row class="modal-header ion-padding-top ion-text-center">
            <ion-col>
              <h2>Add New Quest</h2>
              <ion-progress-bar [value]="progress" color="success"></ion-progress-bar>
            </ion-col>
            <ion-icon (click)="closeAddQuestModal()" name="close"></ion-icon>
          </ion-row>
          <form (ngSubmit)="createQuest()" #questForm="ngForm">

            <div *ngIf="currentStep === 1" class="choose-quest">
              <ion-row>
                <ion-col size="6">
                  <ion-card class="ion-padding ion-no-margin ion-text-center">
                    <ion-icon class="ion-margin-bottom" name="earth-outline"></ion-icon>
                    <h2>Explore</h2>
                    <ion-text>Our upshift qusts!</ion-text>
                  </ion-card>
                </ion-col>
                <ion-col size="6">
                  <ion-card class="ion-padding ion-no-margin ion-text-center">
                    <ion-icon class="ion-margin-bottom" name="create-outline"></ion-icon>
                    <h2>Create</h2>
                    <ion-text>Create your own!</ion-text>
                  </ion-card>
                </ion-col>
              </ion-row>
            </div>


            <div *ngIf="currentStep === 2" class="first-step">
              <ion-row class="ion-margin-top">
                <ion-col>
                  <h2>Let's choose the <span class="gradient-text">emoji!!</span></h2>
                </ion-col>
              </ion-row>
              <ion-row>
                <ion-col>
                  <ion-text class="dark-text">Choose an EMOJI that you think represents this quest!</ion-text>
                </ion-col>
              </ion-row>
              <ion-row class="preview-emoji">
                <ion-col>
                  <ion-input type="text" id="emoji" name="emoji" [(ngModel)]="newQuest.emoji" value="🎯" appEmojiInput
                    required></ion-input>
                </ion-col>
              </ion-row>
              <ion-row class="emoji-row ion-margin-top ion-margin-bottom">
                <ion-col *ngFor="let emoji of emojis" size="2"
                  class="ion-justify-content-center ion-align-items-center">
                  <ion-text>{{ emoji }}</ion-text>
                </ion-col>
              </ion-row>
              <ion-button class="social-button">Custom</ion-button>
            </div>

            <div *ngIf="currentStep === 3" class="step-three-container">
              <ion-row class="step-header ion-margin-top">
                <ion-col class="ion-text-center">
                  <div class="floating-emoji ion-margin-bottom">{{ newQuest.emoji || '🎯' }}</div>
                  <h2>Tell us about your <span class="gradient-text">quest!</span></h2>
                  <ion-text class="dark-text">Give your quest a name and describe what you want to
                    achieve</ion-text>
                </ion-col>
              </ion-row>

              <ion-row class="input-section">
                <ion-col>
                  <div class="floating-input-container">
                    <div class="input-icon">✏️</div>
                    <ion-input class="dark-input" type="text" id="name" name="name" [(ngModel)]="newQuest.name"
                      placeholder=" " required>
                    </ion-input>
                    <label class="floating-label" for="name">Quest Name</label>
                    <div class="input-border"></div>
                  </div>
                </ion-col>
              </ion-row>

              <ion-row class="input-section">
                <ion-col>
                  <div class="floating-textarea-container">
                    <div class="input-icon">📝</div>
                    <ion-textarea class="dark-input" id="description" name="description"
                      [(ngModel)]="newQuest.description" placeholder=" " rows="3" maxlength="150">
                    </ion-textarea>
                    <label class="floating-label" for="description">Quest Description</label>
                    <div class="input-border"></div>
                    <div class="character-counter">
                      <span [class.warning]="(newQuest.description?.length || 0) > 120">
                        {{ newQuest.description?.length || 0 }}/150
                      </span>
                    </div>
                  </div>
                </ion-col>
              </ion-row>
            </div>

            <div *ngIf="currentStep === 4" class="step-four-container">
              <ion-row class="step-header">
                <ion-col class="ion-text-center">
                  <div class="step-icon-container">
                    <div class="floating-emoji">⚙️</div>
                  </div>
                  <h2 class="step-title">Configure your <span class="gradient-text">quest!</span></h2>
                  <p class="step-subtitle">Choose the type, category and set your goal</p>
                </ion-col>
              </ion-row>

              <!-- Quest Type Toggle -->
              <ion-row class="quest-type-section" *ngIf="!newQuest.quest_type"
                [class.slide-out-left]="questTypeAnimating && selectedQuestType">
                <ion-col>
                  <div class="section-label">
                    <ion-icon name="trending-up-outline"></ion-icon>
                    <span>Quest Type</span>
                  </div>
                  <div class="toggle-container">
                    <div class="toggle-option" [class.active]="newQuest.quest_type === 'build'"
                      (click)="selectQuestType('build')">
                      <div class="toggle-icon">🏗️</div>
                      <div class="toggle-text">
                        <h4>Build Habit</h4>
                        <p>Create a new positive habit</p>
                      </div>
                    </div>
                    <div class="toggle-option" [class.active]="newQuest.quest_type === 'quit'"
                      (click)="selectQuestType('quit')">
                      <div class="toggle-icon">🚫</div>
                      <div class="toggle-text">
                        <h4>Quit Habit</h4>
                        <p>Break a negative habit</p>
                      </div>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

              <!-- Category Cards -->
              <ion-row class="category-section" *ngIf="newQuest.quest_type && !categorySelected"
                [class.slide-out-left]="questTypeAnimating && selectedCategory">
                <ion-col>
                  <div class="section-label">
                    <ion-icon name="grid-outline"></ion-icon>
                    <span>Category</span>
                  </div>
                  <div class="category-grid">
                    <div class="category-card" [class.selected]="newQuest.category === 'strength'"
                      (click)="selectCategory('strength')">
                      <div class="category-icon">💪</div>
                      <span>Strength</span>
                    </div>
                    <div class="category-card" [class.selected]="newQuest.category === 'money'"
                      (click)="selectCategory('money')">
                      <div class="category-icon">💰</div>
                      <span>Money</span>
                    </div>
                    <div class="category-card" [class.selected]="newQuest.category === 'health'"
                      (click)="selectCategory('health')">
                      <div class="category-icon">🏥</div>
                      <span>Health</span>
                    </div>
                    <div class="category-card" [class.selected]="newQuest.category === 'knowledge'"
                      (click)="selectCategory('knowledge')">
                      <div class="category-icon">🧠</div>
                      <span>Knowledge</span>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

              <!-- Priority Selection -->
              <ion-row class="priority-section" *ngIf="newQuest.category" [class.slide-out-left]="priorityAnimated">
                <ion-col>
                  <div class="section-label">
                    <ion-icon name="flag-outline"></ion-icon>
                    <span>Priority</span>
                  </div>
                  <div class="priority-container">
                    <div class="priority-option" [class.active]="newQuest.priority === 'basic'" [class.disabled]="false"
                      (click)="newQuest.priority = 'basic'">
                      <div class="priority-indicator basic"></div>
                      <span>Basic Priority</span>
                    </div>
                    <div class="priority-option" [class.active]="newQuest.priority === 'high'"
                      [class.disabled]="hasHighPriorityQuest"
                      (click)="!hasHighPriorityQuest && (newQuest.priority = 'high')">
                      <div class="priority-indicator high"></div>
                      <span>High Priority</span>
                    </div>
                  </div>
                  <div class="priority-warning" *ngIf="hasHighPriorityQuest">
                    <ion-icon name="warning-outline"></ion-icon>
                    <span>You already have a high priority quest in this category</span>
                  </div>
                </ion-col>
              </ion-row>
            </div>

            <div *ngIf="currentStep === 5">
              <!-- Goal Section -->
              <ion-row class="goal-section">
                <ion-col>
                  <div class="section-label">
                    <ion-icon name="target-outline"></ion-icon>
                    <span>Goal</span>
                  </div>
                  <div class="goal-container" [class.slide-in]="goalAnimated">
                    <div class="goal-input-wrapper">
                      <div class="goal-number">
                        <ion-input class="goal-value-input" type="number" id="goal_value" name="goal_value"
                          [(ngModel)]="newQuest.goal_value" value="1" min="1" required>
                        </ion-input>
                      </div>
                      <div class="goal-unit">
                        <ion-select class="goal-unit-select" id="goal_unit" name="goal_unit"
                          [(ngModel)]="newQuest.goal_unit" interface="popover" required>
                          <ion-select-option value="count">times</ion-select-option>
                          <ion-select-option value="steps">steps</ion-select-option>
                          <ion-select-option value="m">meters</ion-select-option>
                          <ion-select-option value="km">kilometers</ion-select-option>
                          <ion-select-option value="sec">seconds</ion-select-option>
                          <ion-select-option value="min">minutes</ion-select-option>
                          <ion-select-option value="hr">hours</ion-select-option>
                          <ion-select-option value="Cal">calories</ion-select-option>
                          <ion-select-option value="g">grams</ion-select-option>
                          <ion-select-option value="mg">milligrams</ion-select-option>
                          <ion-select-option value="l">liters</ion-select-option>
                          <ion-select-option value="drink">drinks</ion-select-option>
                          <ion-select-option value="pages">pages</ion-select-option>
                          <ion-select-option value="books">books</ion-select-option>
                          <ion-select-option value="%">percent</ion-select-option>
                          <ion-select-option value="€">euros</ion-select-option>
                          <ion-select-option value="$">dollars</ion-select-option>
                          <ion-select-option value="£">pounds</ion-select-option>
                        </ion-select>
                      </div>
                    </div>
                    <div class="goal-preview">
                      <div class="preview-text">
                        Goal: <span class="highlight">{{ newQuest.goal_value || 1 }} {{ newQuest.goal_unit || 'times'
                          }}</span>
                      </div>
                    </div>
                  </div>
                </ion-col>
              </ion-row>
              <ion-row class="ion-margin-top">
                <ion-col>
                  <label>Frequency</label>
                  <ion-select class="dark-input" id="goal_period" name="goal_period" [(ngModel)]="newQuest.goal_period"
                    (ionChange)="updatePeriodDisplay()" interface="popover" required>
                    <ion-select-option value="day">Every Day</ion-select-option>
                    <ion-select-option value="week">Specific days of the week</ion-select-option>
                    <ion-select-option value="month">Specific days of the month</ion-select-option>
                  </ion-select>
                </ion-col>
              </ion-row>
              <ion-row *ngIf="newQuest.goal_period === 'week'" class="select-days ion-padding-bottom">
                <ion-col *ngFor="let day of weekDays" size="3" class="ion-text-center">
                  <ion-button type="button" class="day-checked" (click)="updateDaysOfWeek(day.value)"
                    [class.selected]="selectedDaysOfWeek.includes(day.value)">
                    {{ day.label }}
                  </ion-button>
                </ion-col>
              </ion-row>
              <ion-row *ngIf="newQuest.goal_period === 'month'" class="ion-margin-bottom">
                <ion-col>
                  <div class="day-checkbox" *ngFor="let day of monthDays">
                    <ion-checkbox [id]="'month-day-' + day" name="days_of_month" [value]="day"
                      (ionChange)="updateDaysOfMonth($event, day)">
                    </ion-checkbox>
                    <label [for]="'month-day-' + day">{{ day }}</label>
                  </div>
                </ion-col>
              </ion-row>
            </div>

            <ion-row *ngIf="currentStep === 5">
              <ion-col>
                <ion-card class="quest-item ion-margin-bottom" [attr.data-quest-id]="newQuest.id"
                  [attr.data-regular-quest]="true">
                  <ion-row>
                    <ion-col size="2">
                      <div class="quest-icon">{{ newQuest.emoji }}</div>
                    </ion-col>
                    <ion-col size="8" class="quest-info">
                      <h3>{{ newQuest.name }}</h3>
                      <ion-text>{{ newQuest.description }}</ion-text>
                      <div class="progress-container">
                        <div class="progress-time"
                          *ngIf="newQuest.goal_unit === 'time' || newQuest.goal_unit === 'min' || newQuest.goal_unit === 'hr' || newQuest.goal_unit === 'sec'">
                          <ion-range min="0" [max]="newQuest.goal_value" class="progress-slider"
                            [attr.data-quest-id]="newQuest.id" [attr.data-quest-type]="newQuest.quest_type" [step]="1"
                            snaps="true" ticks="false" snaps-per-step="true"
                            (ionInput)="$event.target && updateSliderBackground($event.target)">
                          </ion-range>
                          <ion-text class="progress-text">
                            0{{ newQuest.goal_unit === 'min' ? 'm'
                            : newQuest.goal_unit === 'hr' ? 'h' : 's' }}/{{
                            newQuest.goal_value }}{{ newQuest.goal_unit === 'min' ? 'm' :
                            newQuest.goal_unit === 'hr' ? 'h' : 's' }}
                          </ion-text>
                        </div>
                        <div class="progress"
                          *ngIf="newQuest.goal_unit !== 'time' && newQuest.goal_unit !== 'min' && newQuest.goal_unit !== 'hr' && newQuest.goal_unit !== 'sec'">
                          <ion-range min="0" [max]="newQuest.goal_value" class="progress-slider"
                            [attr.data-quest-id]="newQuest.id" [attr.data-quest-type]="newQuest.quest_type" [step]="1"
                            snaps="true" ticks="false" snaps-per-step="true"
                            (ionInput)="$event.target && updateSliderBackground($event.target)">
                          </ion-range>
                          <ion-text class="progress-text">
                            0/{{ newQuest.goal_value }}
                            <ng-container *ngIf="newQuest.goal_unit !== 'count'">
                              {{ newQuest.goal_unit }}
                            </ng-container>
                          </ion-text>
                        </div>
                      </div>
                    </ion-col>
                    <ion-col size="2">
                      <ion-text class="quest-streak" *ngIf="isSameDay(selectedDate, getToday())">
                        🔥{{ newQuest.streak }}d
                      </ion-text>
                    </ion-col>
                  </ion-row>
                </ion-card>
              </ion-col>
            </ion-row>

            <ion-row class="ion-padding create-quest-row">
              <ion-col *ngIf="currentStep !== 5">
                <ion-button (click)="prevStep()" class="social-button">Back</ion-button>
              </ion-col>
              <ion-col *ngIf="currentStep !== 5">
                <ion-button (click)="nextStep()" class="blue-button">Next</ion-button>
              </ion-col>
              <ion-col *ngIf="currentStep === 5">
                <ion-button type="submit" class="blue-button create-quest">Create Quest</ion-button>
              </ion-col>
            </ion-row>
          </form>
        </ion-grid>
      </ion-content>
    </ng-template>
  </ion-modal>

  <app-celebration *ngIf="showCelebration" [user]="currentUser" [date]="formatDate(selectedDate)"
    (close)="closeCelebration()">
  </app-celebration>
</ion-content>
<app-navigation></app-navigation>