{"ast": null, "code": "import { version } from './version';\n/** Current session will be checked for refresh at this interval. */\nexport const AUTO_REFRESH_TICK_DURATION_MS = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nexport const AUTO_REFRESH_TICK_THRESHOLD = 3;\n/*\n * Earliest time before an access token expires that the session should be refreshed.\n */\nexport const EXPIRY_MARGIN_MS = AUTO_REFRESH_TICK_THRESHOLD * AUTO_REFRESH_TICK_DURATION_MS;\nexport const GOTRUE_URL = 'http://localhost:9999';\nexport const STORAGE_KEY = 'supabase.auth.token';\nexport const AUDIENCE = '';\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `gotrue-js/${version}`\n};\nexport const NETWORK_FAILURE = {\n  MAX_RETRIES: 10,\n  RETRY_INTERVAL: 2 // in deciseconds\n};\nexport const API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version';\nexport const API_VERSIONS = {\n  '2024-01-01': {\n    timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n    name: '2024-01-01'\n  }\n};\nexport const BASE64URL_REGEX = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;\nexport const JWKS_TTL = 600000; // 10 minutes\n//# sourceMappingURL=constants.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}