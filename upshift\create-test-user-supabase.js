// This is a script to create a test user with a plan that expires in a month
// You would need to run this with Node.js and Supabase JS client

const { createClient } = require('@supabase/supabase-js');

// Replace with your Supabase config
const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
const supabaseServiceRoleKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; // Replace with your service role key

// Initialize Supabase with service role key (admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Test user details
const email = '<EMAIL>';
const password = 'Test123!';
const username = 'testuser';
const name = 'Test User';

// Create the user in Supabase Authentication and database
async function createTestUser() {
  try {
    // Create the user in Supabase Authentication
    console.log('Creating user in Supabase Authentication...');
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        name
      }
    });

    if (authError) {
      console.error('Error creating user in Supabase Authentication:', authError.message);
      return;
    }

    console.log('User created in Supabase Authentication with UID:', authData.user.id);

    // Set expiry date to one month from now
    const expiryDate = new Date();
    expiryDate.setMonth(expiryDate.getMonth() + 1);

    // Create user profile in the database
    console.log('Creating user profile in Supabase database...');
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        email,
        username,
        name,
        registration_date: new Date(),
        last_login: new Date(),
        plan: 'premium',
        plan_expiry: expiryDate,
        level: 1,
        strength_xp: 0,
        money_xp: 0,
        health_xp: 0,
        knowledge_xp: 0
      })
      .select()
      .single();

    if (userError) {
      console.error('Error creating user profile in Supabase database:', userError.message);
      // Clean up by deleting the auth user if profile creation fails
      await supabase.auth.admin.deleteUser(authData.user.id);
      return;
    }

    console.log('User profile created successfully:', userData);
    console.log('Test user created with plan expiring on:', expiryDate.toISOString());
  } catch (error) {
    console.error('Error creating test user:', error.message);
  }
}

// Run the function
createTestUser();
