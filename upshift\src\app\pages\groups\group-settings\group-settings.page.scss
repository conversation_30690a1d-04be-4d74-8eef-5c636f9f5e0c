:host {
  --bg: #0c0c0f;
  --card: #121217;
  --pill: #1c1c1e;
  --text: #fff;
  --muted: #8e8e93;
  --accent: #4d7bff;
  --danger: #ff4d4d;
  --success: #34c759;
  --radius: 14px;
}

:host {
  background-color: var(--bg);
  color: var(--text);
  min-height: 100vh;
  display: block;
}

/* Settings Container */
.settings-container {
  width: 480px;
  margin: 0 auto;
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none;
  padding-bottom: 60px;
}
.settings-container::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

/* Header */
.settings-header {
  margin-bottom: 24px;
}

.back-link {
  display: inline-block;
  color: var(--muted);
  text-decoration: none;
  margin-bottom: 16px;
  font-size: 14px;
  transition: color 0.2s;
}

.back-link:hover {
  color: var(--accent);
}

.back-arrow {
  margin-right: 4px;
}

.settings-header h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.group-level-badge {
  display: inline-block;
  background: var(--accent);
  color: white;
  font-size: 14px;
  padding: 4px 10px;
  border-radius: 12px;
  margin-top: 5px;
}

/* Messages */
.messages {
  margin-bottom: 20px;
}

.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 10px;
  font-size: 14px;
}

.message.error {
  background-color: rgba(255, 77, 77, 0.2);
  border-left: 3px solid var(--danger);
  color: var(--danger);
}

.message.success {
  background-color: rgba(52, 199, 89, 0.2);
  border-left: 3px solid var(--success);
  color: var(--success);
}

/* Sections */
.settings-section {
  background: var(--card);
  border-radius: var(--radius);
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.section-icon {
  font-size: 20px;
  margin-right: 10px;
}

.section-header h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

/* XP Section */
.xp-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.category-card {
  background: var(--pill);
  border-radius: 10px;
  padding: 12px;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.category-icon {
  font-size: 20px;
  margin-right: 8px;
}

.category-name {
  font-weight: 500;
}

.xp-progress {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.xp-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--accent);
}

.progress-bar {
  height: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--accent);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Group Info Form */
#edit-group-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  color: #aaa;
}

.form-group input {
  padding: 10px 12px;
  background: var(--pill);
  border: 1px solid #2c2c2e;
  border-radius: 8px;
  color: white;
  font-size: 16px;
}

.form-group .error-message {
  color: var(--danger);
  font-size: 12px;
  margin-top: 4px;
}

.form-group .checking-message {
  color: var(--muted);
  font-size: 12px;
  margin-top: 4px;
  font-style: italic;
}

.form-group .success-message {
  color: var(--success);
  font-size: 12px;
  margin-top: 4px;
}

#emoji {
  width: 60px;
  text-align: center;
}

.save-btn {
  background: var(--accent);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  height: 43px;
  transition: background 0.2s;
}

.save-btn:hover {
  background: #3a68e0;
}

/* Members List */
.members-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--pill);
  border-radius: 10px;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: #2c2c2e;
  display: flex;
  align-items: center;
  justify-content: center;
}

.default-avatar {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-details {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-size: 16px;
  font-weight: 500;
  color: white;
  text-decoration: none;
}

.member-username {
  font-size: 14px;
  color: var(--secondary-text);
  margin-left: 5px;
}

.member-name-link {
  text-decoration: none;
}

.admin-badge {
  display: inline-block;
  background: var(--accent);
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 4px;
}

.member-joined {
  font-size: 12px;
  color: var(--muted);
  margin-top: 2px;
}

.member-actions {
  display: flex;
  gap: 8px;
}

.edit-nickname-btn {
  background: transparent;
  color: var(--accent);
  border: 1px solid var(--accent);
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-nickname-btn:hover {
  background: rgba(77, 123, 255, 0.1);
}

.remove-btn, .leave-btn {
  background: transparent;
  color: var(--danger);
  border: 1px solid var(--danger);
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.remove-btn:hover, .leave-btn:hover {
  background: rgba(255, 77, 77, 0.1);
}

/* Invite Section */
.invite-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #2c2c2e;
}

.invite-tab {
  padding: 8px 16px;
  background: transparent;
  border: none;
  color: var(--muted);
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.invite-tab.active {
  color: var(--accent);
}

.invite-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--accent);
}

.invite-tab-content {
  padding: 16px 0;
}

.invite-tab-content p {
  margin-bottom: 16px;
  color: var(--muted);
}

.code-header {
  margin-bottom: 15px;
}

.code-header p {
  margin-top: 10px;
  font-size: 14px;
  color: var(--muted);
}

.invite-input-container {
  position: relative;
  margin-bottom: 16px;
}

.invite-input-container input {
  width: 100%;
  padding: 10px 12px;
  background: var(--pill);
  border: 1px solid #2c2c2e;
  border-radius: 8px;
  color: white;
  font-size: 16px;
}

.username-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: var(--pill);
  border: 1px solid #2c2c2e;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
}

.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.suggestion-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.invite-btn {
  background: var(--accent);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.2s;
}

.invite-btn:hover {
  background: #3a68e0;
}

.invitation-sent-message {
  margin: 12px 0;
}

.validation-error {
  color: var(--danger);
  font-size: 12px;
  margin-top: 5px;
}

.invite-note {
  font-size: 14px;
  color: var(--muted);
  margin-top: 12px;
}

.code-display {
  background: var(--pill);
  padding: 16px;
  border-radius: 8px;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  letter-spacing: 2px;
}

.code-info {
  font-size: 14px;
  color: var(--muted);
  text-align: center;
}

/* Side Quest Settings */
.sidequest-settings-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #2c2c2e;
  transition: .4s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.setting-label {
  font-size: 15px;
}

.save-settings-btn {
  background: var(--accent);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 10px;
  align-self: flex-start;
}

.save-settings-btn:hover {
  background: #3a68e0;
}

/* Danger Zone */
.danger-section {
  border: 1px solid var(--danger);
}

.danger-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.delete-btn {
  background: var(--danger);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.2s;
  align-self: flex-start;
}

.delete-btn:hover {
  background: #ff3333;
}

.danger-note {
  font-size: 14px;
  color: var(--muted);
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
  background-color: var(--bg);
  margin: 15% auto;
  padding: 20px;
  width: 90%;
  max-width: 400px;
  position: relative;
  color: white;
  border-radius: 12px;
}

.close-modal {
  color: #666;
  position: absolute;
  top: 5px;
  right: 10px;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
}

.close-modal:hover {
  color: white;
}

.modal h3 {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

#edit-nickname-input {
  width: 100%;
  padding: 10px;
  background-color: var(--pill);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  margin-bottom: 15px;
}

.save-nickname-btn {
  background-color: var(--accent);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.save-nickname-btn:hover {
  background-color: #3a5bcf;
}

/* Adjust for mobile */
@media (max-width: 480px) {
  .settings-container {
    padding: 16px;
  }

  .member-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .member-actions {
    margin-top: 12px;
    align-self: flex-end;
  }
}
