/* Exact CSS from Django template */
:host {
    --background-color: #0C0C0F;
    --text-color: #FFFFFF;
    --secondary-text: #8E8E93;
    --accent-color: #4169E1;
    --quest-bg: #1C1C1E;
    --quest-border: #2C2C2E;
    --active-date: #4169E1;
    --inactive-date: #2C2C2E;
    --card-bg: #1C1C1E;
    --border-color: #2C2C2E;
    --bg-color: #0C0C0F;
    --danger-color: #FF3B30;
}

:host {
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    display: block;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
    max-width: 480px;
    margin: 0 auto;
    padding: 20px;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    height: 24px;
}

.logo span {
    font-size: 20px;
    font-weight: 600;
}

h1 {
    font-size: 20px;
    font-weight: 600;
}

.join-requests {
    margin-bottom: 20px;
}

.card {
    background-color: var(--card-bg);
    border-radius: 12px;
    overflow: hidden;
}

.card-header {
    display: flex;
    align-items: center;
    padding: 15px;
}

.card-header.link {
    text-decoration: none;
    color: var(--text-color);
}

.icon {
    font-size: 24px;
    margin-right: 10px;
}

h2 {
    font-size: 18px;
    font-weight: 600;
    flex-grow: 1;
}

.badge {
    background-color: var(--accent-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}

.group-actions {
    margin-bottom: 30px;
}

.btn {
    display: inline-block;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    border: none;
}

.primary {
    background-color: var(--accent-color);
    color: white;
}

.secondary {
    background-color: var(--card-bg);
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
}

.full-width {
    width: 100%;
}

.join-group-section {
    margin-top: 20px;
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 15px;
}

h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
}

p {
    font-size: 14px;
    color: var(--secondary-text);
    margin-bottom: 15px;
}

.join-group-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

input[type="text"] {
    padding: 12px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
    color: var(--text-color);
    font-size: 16px;
}

.your-groups {
    margin-bottom: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.leaderboard-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.group-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.group-card {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
}

.group-icon {
    font-size: 24px;
    margin-right: 15px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.group-info {
    flex-grow: 1;
}

.group-info h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.group-link {
    font-size: 14px;
    color: var(--accent-color);
    text-decoration: none;
}

/* Navigation Styles */
.main-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #121212;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    z-index: 1000;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 600px;
    margin: 0 auto;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #888;
    padding: 5px 0;
    transition: color 0.2s ease;
    width: 20%;
}

.nav-item:hover {
    color: #fff;
}

.nav-item.active {
    color: #4D7BFF;
    position: relative;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: #4D7BFF;
    border-radius: 50%;
}

.nav-icon {
    font-size: 18px;
    margin-bottom: 4px;
}

.nav-text {
    font-size: 12px;
    font-weight: 500;
}

/* Adjust container padding to account for navigation bar */
.container {
    padding-bottom: 120px !important;
}
