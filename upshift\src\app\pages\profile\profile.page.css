/* Exact CSS from Django template */
:root {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --quest-bg: #1C1C1E;
  --quest-border: #2C2C2E;
  --active-date: #4169E1;
  --inactive-date: #2C2C2E;
  --card-bg: #1C1C1E;
  --border-color: #2C2C2E;
  --bg-color: #0C0C0F;
}

:host {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
  display: block;
}

ion-content {
  --background: var(--background-color);
  --color: var(--text-color);
  --padding-bottom: 100px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
  overflow-y: auto;
  padding-bottom: 100px; /* Space for navigation */
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.profile-container {
  padding: 20px;
}

.profile-header {
  display: flex;
  align-items: center;
}

.profile-picture {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin-right: 20px;
  overflow: hidden;
}

.profile-picture img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.profile-username {
  font-size: 16px;
  color: var(--secondary-text);
  margin-bottom: 5px;
}

.profile-level {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.level-badge {
  background-color: var(--accent-color);
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 14px;
  font-weight: 600;
  margin-right: 10px;
}

.profile-title {
  font-size: 16px;
  color: var(--accent-color);
}

.button-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  margin-bottom: 30px; /* Added margin at the bottom */
  position: relative;
}

.badges-button {
  display: inline-block;
  background-color: #1c1c1e;
  color: white;
  border: 2px solid #4d7bff;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  margin-right: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.badges-button:hover {
  background-color: #2c2c2e;
  transform: translateY(-2px);
}

.settings-button {
  display: inline-block;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.settings-button:hover {
  background-color: #3158c7;
  transform: translateY(-2px);
}

.xp-section {
  margin-top: 30px;
}

.xp-section h2 {
  font-size: 20px;
  margin-bottom: 20px;
}

.category-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.category-icon {
  font-size: 24px;
  margin-right: 10px;
}

.category-name {
  font-size: 18px;
  font-weight: 600;
}

.progress-container {
  height: 10px;
  background-color: var(--bg-color);
  border-radius: 5px;
  margin-bottom: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 5px;
}

.xp-text {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: var(--secondary-text);
}

.next-level-info {
  text-align: center;
  margin-top: 30px;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 12px;
}

.next-level-text {
  font-size: 16px;
  margin-bottom: 10px;
}

.next-level-requirements {
  font-size: 14px;
  color: var(--secondary-text);
}

.logout-button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 15px;
  display: inline-block;
  text-decoration: none;
  text-align: center;
}

.logout-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.profile-bio {
  font-size: 14px;
  color: var(--secondary-text);
  margin-bottom: 5px;
  font-style: italic;
  max-width: 300px;
}

.profile-bio-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.edit-bio-btn {
  background: none;
  border: none;
  color: var(--accent-color);
  font-size: 12px;
  cursor: pointer;
  padding: 2px 5px;
  margin-left: 10px;
}

.bio-form {
  margin-bottom: 10px;
}

.bio-form input[type=text] {
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--secondary-text);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 14px;
  margin-bottom: 5px;
}

.bio-form button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
}

.settings-section {
  margin-top: 30px;
}

.settings-options {
  margin-top: 15px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
}

.toggle-form {
  margin: 0;
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--accent-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--accent-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Navigation Styles */
.main-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #121212;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  z-index: 1000;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #888;
  padding: 5px 0;
  transition: color 0.2s ease;
  width: 20%;
}

.nav-item:hover {
  color: #fff;
}

.nav-item.active {
  color: #4D7BFF;
  position: relative;
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #4D7BFF;
  border-radius: 50%;
}

.nav-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
}

/* Container padding is now set directly in the main container definition *//*# sourceMappingURL=profile.page.css.map */