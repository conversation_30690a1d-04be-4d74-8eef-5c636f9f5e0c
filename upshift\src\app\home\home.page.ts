import { Component, inject } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';

import { PreferencesService } from '../services/preferences.service';
import { UserService } from '../services/user.service';
import { SupabaseService } from '../services/supabase.service';

@Component({
  selector: 'app-home',
  standalone: true,
  templateUrl: 'home.page.html',
  imports: [IonicModule, CommonModule,  RouterModule],
})
export class HomePage {
  private router = inject(Router);
  private userService = inject(UserService);
  private preferencesService = inject(PreferencesService);
  private supabaseService = inject(SupabaseService);
  private isRedirecting = false; // Flag to prevent multiple redirects
  private authSubscription: any;

  async logout() {
    try {
      await this.supabaseService.signOut();
      console.log('User logged out');
      this.router.navigateByUrl('/signup/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  ionViewWillEnter() {
    this.authSubscription = this.supabaseService.currentUser$.subscribe(async (user) => {
      if (!user) {
        console.log('Home: No authenticated user found');
        this.router.navigateByUrl('/signup');
        return;
      }

      try {
        const { value: onboarding } = await this.preferencesService.get('onboarding_complete');
        if (!onboarding) {
          console.log('Home: Onboarding not complete');
          this.router.navigateByUrl('/onboarding');
          return;
        }
      } catch (error) {
        console.error('Home: Error getting preferences:', error);
        this.router.navigateByUrl('/onboarding');
        return;
      }

      // Check if user has an ID
      if (!user.id) {
        console.error('Home: User ID is missing, cannot fetch user document');
        this.router.navigateByUrl('/signup');
        return;
      }

      console.log('Home: Looking up user document for ID:', user.id);

      // Use the UserService to get or create the user document
      this.userService.ensureUserExists(user).subscribe(userData => {
        if (!userData) {
          console.error('Home: Failed to get or create user document');
          this.router.navigateByUrl('/signup');
          return;
        }

        console.log('Home: User document:', userData);

        // Get end date
        let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
        const username = userData.username;

        // Add detailed logging to debug the date comparison
        console.log('Home: End date:', endDate);
        console.log('Home: End date type:', typeof endDate);
        console.log('Home: Username:', username);

        const currentDate = new Date();
        console.log('Home: Current date:', currentDate);

        // Compare dates properly
        let isValidPlan = false;
        if (endDate instanceof Date) {
          isValidPlan = endDate > currentDate;
          console.log('Home: Plan valid until:', endDate);
          console.log('Home: Is plan still valid?', isValidPlan);
        } else {
          console.log('Home: No valid end date found or not a Date object');
        }

        if (!isValidPlan) {
          console.log('Home: User has no valid plan or plan has expired');
          // Prevent multiple redirects
          if (this.isRedirecting) {
            console.log('Home: Already redirecting, skipping additional redirect');
            return;
          }

          this.isRedirecting = true;
          console.log('Home: Redirecting to pricing page');

          // Add a small delay to prevent immediate redirection loops
          setTimeout(() => {
            this.router.navigateByUrl('/pricing');
            // Reset the flag after a longer delay
            setTimeout(() => {
              this.isRedirecting = false;
            }, 2000);
          }, 500);
          return;
        }

        if (!username) {
          console.log('Home: User has valid plan but no username');
          this.router.navigateByUrl('/signup-step3');
          return;
        }

        console.log('Home: User has valid plan and username');
        // ✅ User is all set, stays on tabs
      });
    });
  }

  ionViewWillLeave() {
    // Clean up the auth subscription when leaving the page
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }
}
