{"ast": null, "code": "import { supportsLocalStorage } from './helpers';\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nexport const localStorageAdapter = {\n  getItem: key => {\n    if (!supportsLocalStorage()) {\n      return null;\n    }\n    return globalThis.localStorage.getItem(key);\n  },\n  setItem: (key, value) => {\n    if (!supportsLocalStorage()) {\n      return;\n    }\n    globalThis.localStorage.setItem(key, value);\n  },\n  removeItem: key => {\n    if (!supportsLocalStorage()) {\n      return;\n    }\n    globalThis.localStorage.removeItem(key);\n  }\n};\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store = {}) {\n  return {\n    getItem: key => {\n      return store[key] || null;\n    },\n    setItem: (key, value) => {\n      store[key] = value;\n    },\n    removeItem: key => {\n      delete store[key];\n    }\n  };\n}\n//# sourceMappingURL=local-storage.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}