import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ToastController } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { GroupService } from '../../../services/group.service';
import { Group, GroupJoinRequest } from '../../../models/group.model';
import { take } from 'rxjs';
import { NavigationComponent } from '../../../components/navigation/navigation.component';
import { SupabaseService } from '../../../services/supabase.service';

@Component({
  selector: 'app-group-list',
  templateUrl: './group-list.page.html',
  styleUrls: ['./group-list.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]
})
export class GroupListPage implements OnInit {
  // User data
  userId: string | null = null;

  // Groups
  groups: Group[] = [];
  joinRequests: GroupJoinRequest[] = [];

  // Join group
  invitationCode = '';

  private supabaseService = inject(SupabaseService);
  private groupService = inject(GroupService);
  private router = inject(Router);
  private toastController = inject(ToastController);

  constructor() {}

  ngOnInit() {
    console.log('GroupListPage: ngOnInit called');

    this.supabaseService.currentUser$.pipe(
      take(1)
    ).subscribe(authUser => {
      console.log('GroupListPage: Current user:', authUser);

      if (authUser) {
        this.userId = authUser.id;
        console.log('GroupListPage: User ID set:', this.userId);

        // Log that we're loading data
        console.log('GroupListPage: Loading data for user ID:', this.userId);

        this.loadGroups();
        this.loadJoinRequests();
      } else {
        console.log('GroupListPage: No authenticated user found');
      }
    });
  }

  async loadGroups() {
    if (!this.userId) return;

    return new Promise<void>((resolve) => {
      // Use non-null assertion operator since we've already checked that userId is not null
      this.groupService.getUserGroups(this.userId!).subscribe(groups => {
        this.groups = groups;
        console.log('GroupListPage: Groups loaded:', groups);
        resolve();
      });
    });
  }

  loadJoinRequests() {
    if (!this.userId) {
      console.log('GroupListPage: Cannot load join requests - userId is null');
      return;
    }

    console.log('GroupListPage: Loading join requests for user ID:', this.userId);

    // First get the username for this user ID
    this.supabaseService.getClient()
      .from('profiles')
      .select('username')
      .eq('id', this.userId)
      .single()
      .then(response => {
        if (response.error) {
          console.error('GroupListPage: Error getting username:', response.error);
          return;
        }

        const username = response.data.username;
        console.log('GroupListPage: Found username for user ID:', username);

        // Now try to get join requests directly from the database
        console.log('GroupListPage: Querying join requests directly...');
        this.supabaseService.getClient()
          .from('group_join_requests')
          .select('*')
          .ilike('username_invited', username)
          .then(directResponse => {
            console.log('GroupListPage: Direct query response:', directResponse);

            if (directResponse.error) {
              console.error('GroupListPage: Error querying join requests directly:', directResponse.error);
            } else {
              console.log('GroupListPage: Direct query found', directResponse.data.length, 'join requests');
              console.log('GroupListPage: Join requests data:', directResponse.data);

              // Update the UI with the results
              this.joinRequests = directResponse.data;

              // Check if we have any requests and log the section visibility
              console.log('GroupListPage: Join requests section should be visible:', this.joinRequests && this.joinRequests.length > 0);
              console.log('GroupListPage: Number of join requests:', this.joinRequests ? this.joinRequests.length : 0);
            }
          });
      });

    // Also try using the service method for comparison
    this.groupService.getJoinRequestsForUserId(this.userId).subscribe({
      next: (requests) => {
        console.log('GroupListPage: Service method returned join requests:', requests);
        console.log('GroupListPage: Number of join requests from service:', requests ? requests.length : 0);
      },
      error: (error) => {
        console.error('GroupListPage: Error loading join requests from service:', error);
      }
    });
  }

  async joinGroupByCode() {
    if (!this.userId || !this.invitationCode.trim()) return;

    try {
      // Use non-null assertion operator since we've already checked that userId is not null
      const success = await this.groupService.joinGroupByCode(this.userId!, this.invitationCode.trim());

      if (success) {
        // Show success message with toast
        const toast = await this.toastController.create({
          message: 'Successfully joined the group!',
          duration: 2000,
          position: 'bottom',
          color: 'success'
        });
        await toast.present();

        this.invitationCode = '';

        // Reload the groups list
        await this.loadGroups();

        // Force a refresh of the UI by reloading the page
        // This ensures the newly joined group is displayed immediately
        window.location.reload();
      } else {
        // Show error message with toast
        const toast = await this.toastController.create({
          message: 'Invalid or expired invitation code.',
          duration: 2000,
          position: 'bottom',
          color: 'danger'
        });
        await toast.present();
      }
    } catch (error) {
      console.error('Error joining group:', error);

      // Show error message with toast
      const toast = await this.toastController.create({
        message: 'Failed to join group. Please try again.',
        duration: 2000,
        position: 'bottom',
        color: 'danger'
      });
      await toast.present();
    }
  }

  goToLeaderboard() {
    console.log('Navigating to leaderboard/groups using window.location...');
    // Use window.location.href for direct navigation
    window.location.href = '/leaderboard/groups';
  }

  refreshData() {
    console.log('GroupListPage: Refreshing data...');

    if (this.userId) {
      // Get the user's username for debugging
      this.supabaseService.getClient()
        .from('profiles')
        .select('username')
        .eq('id', this.userId!)  // Use non-null assertion operator
        .single()
        .then(response => {
          if (response.error) {
            console.error('GroupListPage: Error getting username:', response.error);
          } else {
            const username = response.data.username;
            console.log('GroupListPage: User username for refresh:', username);

            // Get join requests by username_invited
            this.supabaseService.getClient()
              .from('group_join_requests')
              .select('*')
              .ilike('username_invited', username)
              .then(joinResponse => {
                if (joinResponse.error) {
                  console.error('GroupListPage: Error checking join requests by username:', joinResponse.error);
                } else {
                  console.log('GroupListPage: Join requests by username on refresh:', joinResponse.data);
                  console.log('GroupListPage: Join requests count by username on refresh:', joinResponse.data.length);

                  // Update the joinRequests array directly
                  this.joinRequests = joinResponse.data;
                }
              });
          }
        });

      // Also reload groups
      this.loadGroups();
    }
  }
}
