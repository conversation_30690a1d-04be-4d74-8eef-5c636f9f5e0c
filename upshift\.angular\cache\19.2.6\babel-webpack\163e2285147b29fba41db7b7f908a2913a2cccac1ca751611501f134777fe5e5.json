{"ast": null, "code": "var _AuroraComponent;\nimport * as i0 from \"@angular/core\";\nexport class AuroraComponent {\n  constructor() {}\n  ngOnInit() {}\n}\n_AuroraComponent = AuroraComponent;\n_AuroraComponent.ɵfac = function AuroraComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AuroraComponent)();\n};\n_AuroraComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _AuroraComponent,\n  selectors: [[\"app-aurora\"]],\n  decls: 1,\n  vars: 0,\n  consts: [[1, \"aurora-bg\"]],\n  template: function AuroraComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n    }\n  },\n  styles: [\".aurora-bg[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100vh;\\n  max-height: 100vh;\\n  z-index: -1;\\n  filter: blur(12px);\\n  background: radial-gradient(ellipse 40% 25% at 0% 80%, rgba(65, 105, 225, 0.25) 0%, transparent 70%), radial-gradient(ellipse 35% 20% at 15% 65%, rgba(135, 206, 250, 0.2) 0%, transparent 60%), radial-gradient(ellipse 45% 30% at 35% 45%, rgba(64, 224, 208, 0.18) 0%, transparent 65%), radial-gradient(ellipse 50% 25% at 60% 35%, rgba(82, 119, 232, 0.15) 0%, transparent 70%), radial-gradient(ellipse 40% 20% at 85% 30%, rgba(135, 206, 250, 0.12) 0%, transparent 60%);\\n  animation: aurora-curve 15s ease-in-out infinite alternate;\\n}\\n\\n.aurora-bg[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  filter: blur(20px);\\n  background: radial-gradient(ellipse 30% 15% at 5% 75%, rgba(64, 224, 208, 0.15) 0%, transparent 80%), radial-gradient(ellipse 35% 18% at 25% 55%, rgba(65, 105, 225, 0.12) 0%, transparent 75%), radial-gradient(ellipse 40% 22% at 50% 40%, rgba(135, 206, 250, 0.1) 0%, transparent 70%), radial-gradient(ellipse 35% 16% at 75% 32%, rgba(64, 224, 208, 0.08) 0%, transparent 65%);\\n  animation: aurora-flow 20s ease-in-out infinite alternate-reverse;\\n}\\n\\n.aurora-bg[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  filter: blur(30px);\\n  background: radial-gradient(ellipse 60% 35% at 10% 70%, rgba(135, 206, 250, 0.08) 0%, transparent 80%), radial-gradient(ellipse 55% 30% at 40% 45%, rgba(82, 119, 232, 0.06) 0%, transparent 75%), radial-gradient(ellipse 50% 25% at 70% 35%, rgba(64, 224, 208, 0.05) 0%, transparent 70%);\\n  animation: aurora-glow 25s ease-in-out infinite alternate;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["AuroraComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "AuroraComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\components\\aurora\\aurora.component.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\components\\aurora\\aurora.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-aurora',\r\n  standalone: true,\r\n  templateUrl: './aurora.component.html',\r\n  styleUrls: ['./aurora.component.scss'],\r\n})\r\nexport class AuroraComponent implements OnInit {\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit() { }\r\n\r\n}\r\n", "<div class=\"aurora-bg\"></div>"], "mappings": ";;AAQA,OAAM,MAAOA,eAAe;EAE1BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GAAK;;mBAJFF,eAAe;;mCAAfA,gBAAe;AAAA;;QAAfA,gBAAe;EAAAG,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCR5BE,EAAA,CAAAC,SAAA,aAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}