import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Router, RouterModule } from '@angular/router';
import { FriendService } from '../../services/friend.service';
import { UserService } from '../../services/user.service';
import { User } from '../../models/user.model';
import { Observable, Subscription, combineLatest, map, of, switchMap, take } from 'rxjs';
import { NavigationComponent } from '../../components/navigation/navigation.component';
import { SupabaseService } from '../../services/supabase.service';

interface LeaderboardEntry {
  user: User;
  rank: number;
  isCurrentUser: boolean;
}

@Component({
  selector: 'app-friends',
  templateUrl: './friends.page.html',
  styleUrls: ['./friends.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]
})
export class FriendsPage implements OnInit {
  // User data
  currentUser: User | null = null;
  userId: string | null = null;
  userAffiliateCount: number = 0;

  // Friend code
  friendCode: string | null = null;
  codeIsValid = false;
  enteredCode = '';

  // Friends leaderboard
  leaderboard: LeaderboardEntry[] = [];

  // Messages
  successMessage = '';
  errorMessage = '';

  private supabaseService = inject(SupabaseService);
  private friendService = inject(FriendService);
  private userService = inject(UserService);
  private router = inject(Router);

  constructor() {}

  ngOnInit() {
    this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (authUser) {
          this.userId = authUser.id;
          return this.userService.getUser(authUser.id);
        }
        return of(null);
      })
    ).subscribe(user => {
      if (user) {
        this.currentUser = user;
        // Get number_of_affiliates from profiles table
        this.loadUserAffiliateCount();
        this.checkFriendCode();
        this.loadFriends();
      }
    });
  }

  checkFriendCode() {
    if (!this.currentUser || !this.userId) return;

    // Check if user has a valid friend code
    this.friendCode = this.currentUser.friend_code || null;

    if (this.friendCode && this.currentUser.friend_code_expiry) {
      const expiry = new Date(this.currentUser.friend_code_expiry);
      this.codeIsValid = expiry > new Date();
    } else {
      this.codeIsValid = false;
    }
  }

  loadFriends() {
    if (!this.userId) return;

    this.friendService.getFriendsWithProfiles(this.userId).pipe(
      take(1)
    ).subscribe(friends => {
      // Create leaderboard with current user and friends
      // Filter out null profiles
      const validFriends = friends.filter(f => f.profile !== null);
      console.log('Valid friends with profiles:', validFriends.length);

      // Jednoduchšie - len zoberieme profily priateľov
      const friendProfiles = validFriends.map(f => f.profile);
      console.log('Friend profiles:', friendProfiles.length);

      // Pridáme aktuálneho používateľa
      const allUsers = [...friendProfiles];
      if (this.currentUser) {
        allUsers.push(this.currentUser);
      }

      // Sort by level (descending)
      allUsers.sort((a, b) => (b?.level || 0) - (a?.level || 0));

      // Create leaderboard entries
      this.leaderboard = allUsers.map((user, index) => ({
        user,
        rank: index + 1,
        isCurrentUser: user.id === this.userId
      }));
    });
  }

  async generateFriendCode() {
    if (!this.userId) return;

    try {
      this.friendCode = await this.friendService.generateFriendCode(this.userId);
      this.codeIsValid = true;
      this.successMessage = 'Friend code generated successfully!';

      // Clear success message after 3 seconds
      setTimeout(() => {
        this.successMessage = '';
      }, 3000);
    } catch (error) {
      console.error('Error generating friend code:', error);
      this.errorMessage = 'Failed to generate friend code. Please try again.';

      // Clear error message after 3 seconds
      setTimeout(() => {
        this.errorMessage = '';
      }, 3000);
    }
  }

  async addFriendByCode() {
    if (!this.userId || !this.enteredCode.trim()) return;

    const trimmedCode = this.enteredCode.trim();

    // Validate the code format first
    if (!this.friendService.validateUsernameFormat(trimmedCode)) {
      this.errorMessage = 'Invalid friend code format. Code should only contain letters, numbers, and @/./+/-/_ characters.';
      setTimeout(() => {
        this.errorMessage = '';
      }, 3000);
      return;
    }

    try {
      const success = await this.friendService.addFriendByCode(this.userId, trimmedCode);

      if (success) {
        this.successMessage = 'Friend added successfully!';
        this.enteredCode = '';
        this.loadFriends();
      } else {
        this.errorMessage = 'Invalid or expired friend code.';
      }

      // Clear messages after 3 seconds
      setTimeout(() => {
        this.successMessage = '';
        this.errorMessage = '';
      }, 3000);
    } catch (error) {
      console.error('Error adding friend by code:', error);
      this.errorMessage = 'Failed to add friend. Please try again.';

      // Clear error message after 3 seconds
      setTimeout(() => {
        this.errorMessage = '';
      }, 3000);
    }
  }

  async removeFriend(friendId: string, event: Event) {
    event.preventDefault();

    if (!this.userId) return;

    if (confirm('Are you sure you want to remove this friend?')) {
      try {
        await this.friendService.removeFriend(this.userId, friendId);
        this.successMessage = 'Friend removed successfully!';
        this.loadFriends();

        // Clear success message after 3 seconds
        setTimeout(() => {
          this.successMessage = '';
        }, 3000);
      } catch (error) {
        console.error('Error removing friend:', error);
        this.errorMessage = 'Failed to remove friend. Please try again.';

        // Clear error message after 3 seconds
        setTimeout(() => {
          this.errorMessage = '';
        }, 3000);
      }
    }
  }

  goToAffiliateRewards() {
    this.router.navigate(['/affiliates']);
  }

  async loadUserAffiliateCount() {
    if (!this.userId) return;

    try {
      // Get user data from profiles table directly
      const { data, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('number_of_affiliates')
        .eq('id', this.userId)
        .single();

      if (data && !error) {
        // Get number_of_affiliates from profiles table
        this.userAffiliateCount = data.number_of_affiliates || 0;
      }
    } catch (error) {
      console.error('Error loading user affiliate count:', error);
    }
  }
}
