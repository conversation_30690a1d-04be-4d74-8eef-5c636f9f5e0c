import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { GroupService } from '../../../services/group.service';
import { UserService } from '../../../services/user.service';
import { SupabaseService } from '../../../services/supabase.service';
import { Group, GroupJoinRequest } from '../../../models/group.model';
import { User } from '../../../models/user.model';
import { Observable, Subscription, catchError, map, of, switchMap, take } from 'rxjs';
import { NavigationComponent } from '../../../components/navigation/navigation.component';

// Custom interface that includes both GroupJoinRequest fields and additional details
interface GroupJoinRequestWithDetails {
  id?: string;
  group_id: string;
  user_id: string;
  username_invited?: string;
  invited_by: string | User | any; // Can be either a string ID, User object, or object with inviter_username
  created?: Date;
  requested_at?: Date;
  status?: string;
  nickname?: string;
  group: Group | null;
  created_at: Date; // For template compatibility
  inviter_username?: string; // Added field to store the username of the inviter
}

@Component({
  selector: 'app-group-requests',
  templateUrl: './group-requests.page.html',
  styleUrls: ['./group-requests.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]
})
export class GroupRequestsPage implements OnInit {
  // User data
  userId: string | null = null;
  username: string | null = null;

  // Requests data
  requestsRecent: GroupJoinRequestWithDetails[] = [];
  requestsMonth: GroupJoinRequestWithDetails[] = [];
  requestsOlder: GroupJoinRequestWithDetails[] = [];

  // Messages
  successMessage = '';
  errorMessage = '';

  private supabaseService = inject(SupabaseService);
  private groupService = inject(GroupService);
  private userService = inject(UserService);
  private router = inject(Router);

  constructor() {}



  ngOnInit() {
    console.log('GroupRequestsPage: ngOnInit called');

    this.supabaseService.currentUser$.pipe(
      take(1),
      switchMap(authUser => {
        console.log('GroupRequestsPage: Current user:', authUser);

        if (authUser) {
          this.userId = authUser.id;
          console.log('GroupRequestsPage: User ID set:', this.userId);

          // Get the username from the profiles table
          return this.userService.getUser(this.userId).pipe(
            take(1),
            map(user => {
              this.username = user?.username || null;
              console.log('GroupRequestsPage: Username set:', this.username);
              return user;
            })
          );
        }
        return of(null);
      })
    ).subscribe(user => {
      console.log('GroupRequestsPage: User data loaded:', user);

      if (user && this.userId) {
        this.loadJoinRequests();

        // Fetch all usernames from profiles table
        setTimeout(() => {
          this.fetchAllInviterUsernames();
        }, 1000); // Wait a bit for the requests to load
      } else {
        console.error('GroupRequestsPage: No user data available');
      }
    });
  }

  // Update all inviter usernames
  async fetchAllInviterUsernames() {
    console.log('GroupRequestsPage: Updating all inviter usernames');

    // Collect all inviter usernames
    const inviterUsernames = new Set<string>();

    // Add usernames from recent requests
    this.requestsRecent.forEach(request => {
      if (typeof request.invited_by === 'string') {
        inviterUsernames.add(request.invited_by);
      }
    });

    // Add usernames from month requests
    this.requestsMonth.forEach(request => {
      if (typeof request.invited_by === 'string') {
        inviterUsernames.add(request.invited_by);
      }
    });

    // Add usernames from older requests
    this.requestsOlder.forEach(request => {
      if (typeof request.invited_by === 'string') {
        inviterUsernames.add(request.invited_by);
      }
    });

    console.log('GroupRequestsPage: Found', inviterUsernames.size, 'unique inviter usernames');

    // Update all requests with the usernames directly
    if (inviterUsernames.size > 0) {
      inviterUsernames.forEach(username => {
        // Since invited_by is now the username directly, we can just use it
        this.updateInviterUsernameDirectly(username, username);
      });
    }
  }

  // This method is no longer needed since invited_by is now the username directly
  // Keeping it as a fallback just in case
  private async fetchUsernameDirectly(invitedBy: string): Promise<void> {
    console.log('GroupRequestsPage: Using invited_by directly as username:', invitedBy);

    // Since invited_by is now the username directly, we can just use it
    this.updateInviterUsernameDirectly(invitedBy, invitedBy);
  }

  loadJoinRequests() {
    if (!this.userId) {
      console.error('GroupRequestsPage: Cannot load join requests - userId is null');
      return;
    }

    console.log('GroupRequestsPage: Loading join requests for user ID:', this.userId);
    console.log('GroupRequestsPage: Username:', this.username);

    // Clear existing requests
    this.requestsRecent = [];
    this.requestsMonth = [];
    this.requestsOlder = [];

    // Track processed request IDs to avoid duplicates
    const processedRequestIds = new Set<string>();

    // Use the service method which handles all the logic
    this.groupService.getJoinRequestsForUserId(this.userId).pipe(
      take(1)
    ).subscribe({
      next: (requests) => {
        console.log('GroupRequestsPage: Service method returned', requests.length, 'join requests');

        if (requests.length > 0) {
          // Collect all inviter IDs to fetch usernames in batch
          const inviterIds = new Set<string>();

          requests.forEach(request => {
            if (request.invited_by && typeof request.invited_by === 'string') {
              inviterIds.add(request.invited_by);
            }
          });

          console.log('GroupRequestsPage: Found', inviterIds.size, 'unique inviter IDs');

          // Fetch usernames for all inviters at once
          if (inviterIds.size > 0) {
            this.fetchAllUsernames(Array.from(inviterIds));
          }

          // Process each request
          requests.forEach(request => {
            if (request.id && !processedRequestIds.has(request.id)) {
              processedRequestIds.add(request.id);
              this.processRequest(request);
            }
          });
        } else if (this.username) {
          // If service method returns no results, try direct query as fallback
          console.log('GroupRequestsPage: No requests from service, trying direct query...');
          this.supabaseService.getClient()
            .from('group_join_requests')
            .select('*')
            .then(directResponse => {
              console.log('GroupRequestsPage: Direct query response:', directResponse);

              if (directResponse.error) {
                console.error('GroupRequestsPage: Error querying join requests directly:', directResponse.error);
              } else {
                console.log('GroupRequestsPage: Direct query found', directResponse.data.length, 'join requests');

                // Collect all inviter IDs to fetch usernames in batch
                const inviterIds = new Set<string>();

                directResponse.data.forEach(request => {
                  if (request.invited_by && typeof request.invited_by === 'string') {
                    inviterIds.add(request.invited_by);
                  }
                });

                console.log('GroupRequestsPage: Found', inviterIds.size, 'unique inviter IDs');

                // Fetch usernames for all inviters at once
                if (inviterIds.size > 0) {
                  this.fetchAllUsernames(Array.from(inviterIds));
                }

                // Process each request, avoiding duplicates
                directResponse.data.forEach(request => {
                  if (request.id && !processedRequestIds.has(request.id)) {
                    processedRequestIds.add(request.id);
                    this.processRequest(request as GroupJoinRequest);
                  }
                });
              }
            });
        }
      },
      error: (error) => {
        console.error('GroupRequestsPage: Error loading join requests from service:', error);
      }
    });
  }

  // Fetch usernames for multiple user IDs at once
  async fetchAllUsernames(userIds: string[]): Promise<void> {
    if (!userIds.length) return;

    console.log('GroupRequestsPage: Fetching usernames for multiple IDs:', userIds);

    try {
      // Since invited_by is now the username directly, we can just use it
      const usernameMap = new Map<string, string>();

      // Set each ID to itself as the username
      for (const userId of userIds) {
        usernameMap.set(userId, userId);
      }

      // Update all requests with the usernames
      this.updateAllInviters(usernameMap);
    } catch (error) {
      console.error('GroupRequestsPage: Error in fetchAllUsernames:', error);
    }
  }

  // Update all requests with usernames from the map
  updateAllInviters(usernameMap: Map<string, string>): void {
    console.log('GroupRequestsPage: Updating all inviters with username map');

    // Update recent requests
    this.requestsRecent.forEach(request => {
      if (typeof request.invited_by === 'string' && usernameMap.has(request.invited_by)) {
        const username = usernameMap.get(request.invited_by);
        if (username) {
          request.inviter_username = username;
          console.log('GroupRequestsPage: Updated inviter username for request:', request.id, username);
        }
      }
    });

    // Update month requests
    this.requestsMonth.forEach(request => {
      if (typeof request.invited_by === 'string' && usernameMap.has(request.invited_by)) {
        const username = usernameMap.get(request.invited_by);
        if (username) {
          request.inviter_username = username;
          console.log('GroupRequestsPage: Updated inviter username for request:', request.id, username);
        }
      }
    });

    // Update older requests
    this.requestsOlder.forEach(request => {
      if (typeof request.invited_by === 'string' && usernameMap.has(request.invited_by)) {
        const username = usernameMap.get(request.invited_by);
        if (username) {
          request.inviter_username = username;
          console.log('GroupRequestsPage: Updated inviter username for request:', request.id, username);
        }
      }
    });
  }

  processRequest(request: GroupJoinRequest) {
    console.log('GroupRequestsPage: Processing request:', request);

    // Check if we already have the inviter_username from the service
    if (request.inviter_username) {
      console.log('GroupRequestsPage: Using inviter_username from request:', request.inviter_username);
    }

    // Get group details
    this.groupService.getGroup(request.group_id).pipe(
      take(1),
      switchMap(group => {
        console.log('GroupRequestsPage: Got group details:', group);

        // If we already have the inviter_username, no need to fetch user details
        if (request.inviter_username) {
          // Get the created date from either created or requested_at
          const createdDate = request.created || request.requested_at || new Date();

          const requestWithDetails: GroupJoinRequestWithDetails = {
            id: request.id,
            group_id: request.group_id,
            user_id: request.invited_by, // Use invited_by as user_id if no user object
            username_invited: request.username_invited,
            invited_by: request.invited_by,
            created: createdDate,
            requested_at: request.requested_at,
            status: request.status,
            nickname: request.nickname,
            group: group,
            created_at: createdDate,
            inviter_username: request.inviter_username
          };

          console.log('GroupRequestsPage: Created request with details (using inviter_username):', requestWithDetails);
          return of<GroupJoinRequestWithDetails>(requestWithDetails);
        }

        // Since invited_by is now the username directly, we can just use it
        console.log('GroupRequestsPage: Using invited_by directly as username:', request.invited_by);

        // Create a simple observable with a user object
        return of({
          id: '',
          username: request.invited_by
        }).pipe(
          take(1),
          map(user => {
            console.log('GroupRequestsPage: Got inviter details:', user);

            // Get the created date from either created or requested_at
            const createdDate = request.created || request.requested_at || new Date();

            // Get user ID from the user object if available
            let userId = '';
            if (user && user.id) {
              userId = user.id;
            } else {
              userId = request.invited_by; // Use invited_by as fallback
            }

            const requestWithDetails: GroupJoinRequestWithDetails = {
              id: request.id,
              group_id: request.group_id,
              user_id: userId,
              username_invited: request.username_invited,
              invited_by: user || request.invited_by, // Use user object if available, otherwise use the ID
              created: createdDate,
              requested_at: request.requested_at,
              status: request.status,
              nickname: request.nickname,
              group: group,
              created_at: createdDate,
              inviter_username: user?.username || request.inviter_username // Use username from user object or from request
            };

            console.log('GroupRequestsPage: Created request with details:', requestWithDetails);
            console.log('GroupRequestsPage: Inviter info:',
              typeof requestWithDetails.invited_by === 'string'
                ? requestWithDetails.invited_by
                : requestWithDetails.invited_by?.username);

            return requestWithDetails;
          }),
          catchError((error: any) => {
            console.error('GroupRequestsPage: Error in processRequest:', error);

            // Create request with details using invited_by as the username
            const createdDate = request.created || request.requested_at || new Date();

            const requestWithDetails: GroupJoinRequestWithDetails = {
              id: request.id,
              group_id: request.group_id,
              user_id: request.invited_by,
              username_invited: request.username_invited,
              invited_by: request.invited_by,
              created: createdDate,
              requested_at: request.requested_at,
              status: request.status,
              nickname: request.nickname,
              group: group,
              created_at: createdDate,
              inviter_username: request.invited_by // Use invited_by directly as the username
            };

            return of<GroupJoinRequestWithDetails>(requestWithDetails);
          })
        );
      })
    ).subscribe((requestWithDetails: GroupJoinRequestWithDetails) => {
      // Categorize by date
      const now = new Date();
      const requestDate = new Date(requestWithDetails.created_at);
      const daysDiff = Math.floor((now.getTime() - requestDate.getTime()) / (1000 * 60 * 60 * 24));

      console.log('GroupRequestsPage: Categorizing request:', requestWithDetails.id, 'Days diff:', daysDiff);

      if (daysDiff < 7) {
        // Check if this request is already in the list
        if (!this.requestsRecent.some(r => r.id === requestWithDetails.id)) {
          this.requestsRecent.push(requestWithDetails);
          console.log('GroupRequestsPage: Added to recent requests');
        }
      } else if (daysDiff < 30) {
        if (!this.requestsMonth.some(r => r.id === requestWithDetails.id)) {
          this.requestsMonth.push(requestWithDetails);
          console.log('GroupRequestsPage: Added to month requests');
        }
      } else {
        if (!this.requestsOlder.some(r => r.id === requestWithDetails.id)) {
          this.requestsOlder.push(requestWithDetails);
          console.log('GroupRequestsPage: Added to older requests');
        }
      }
    });
  }

  acceptRequest(requestId: string) {
    if (!this.userId) return;

    this.groupService.acceptGroupJoinRequest(requestId, this.userId).then(() => {
      this.successMessage = 'You have joined the group!';

      // Remove the request from the lists
      this.removeRequestFromLists(requestId);

      // Navigate to the groups page after a short delay
      setTimeout(() => {
        this.router.navigate(['/groups']);
      }, 1500);

      // Clear success message after 3 seconds
      setTimeout(() => {
        this.successMessage = '';
      }, 3000);
    }).catch(error => {
      console.error('Error accepting join request:', error);
      this.errorMessage = 'Failed to join the group. Please try again.';

      // Clear error message after 3 seconds
      setTimeout(() => {
        this.errorMessage = '';
      }, 3000);
    });
  }

  rejectRequest(requestId: string) {
    this.groupService.rejectGroupJoinRequest(requestId).then(() => {
      this.successMessage = 'Request rejected.';

      // Remove the request from the lists
      this.removeRequestFromLists(requestId);

      // Check if there are any requests left
      if (this.requestsRecent.length === 0 && this.requestsMonth.length === 0 && this.requestsOlder.length === 0) {
        // Navigate to the groups page after a short delay if no requests left
        setTimeout(() => {
          this.router.navigate(['/groups']);
        }, 1500);
      }

      // Clear success message after 3 seconds
      setTimeout(() => {
        this.successMessage = '';
      }, 3000);
    }).catch(error => {
      console.error('Error rejecting join request:', error);
      this.errorMessage = 'Failed to reject the request. Please try again.';

      // Clear error message after 3 seconds
      setTimeout(() => {
        this.errorMessage = '';
      }, 3000);
    });
  }

  removeRequestFromLists(requestId: string) {
    this.requestsRecent = this.requestsRecent.filter(r => r.id !== requestId);
    this.requestsMonth = this.requestsMonth.filter(r => r.id !== requestId);
    this.requestsOlder = this.requestsOlder.filter(r => r.id !== requestId);
  }

  // Helper method to safely get the username from invited_by
  getInviterUsername(inviter: string | User | any): string {
    console.log('GroupRequestsPage: Getting inviter username from:', inviter);

    // First check if the request has the inviter_username field (added by our service)
    if (typeof inviter === 'object' && inviter && 'inviter_username' in inviter && inviter.inviter_username) {
      console.log('GroupRequestsPage: Using inviter_username from request:', inviter.inviter_username);
      return String(inviter.inviter_username);
    }

    // If it's a User object with username
    if (typeof inviter === 'object' && inviter && 'username' in inviter && inviter.username) {
      console.log('GroupRequestsPage: Using username from user object:', inviter.username);
      return String(inviter.username);
    }

    // If it's a string, it's already the username
    if (typeof inviter === 'string') {
      console.log('GroupRequestsPage: Using invited_by directly as username:', inviter);
      return inviter;
    }

    return 'Unknown';
  }

  // Helper method to update inviter in request lists
  private updateInviterInLists(inviterId: string, user: User): void {
    console.log('GroupRequestsPage: Updating inviter in lists:', inviterId, user);

    // Update in recent requests
    this.requestsRecent.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === inviterId) {
        request.invited_by = user;
        request.inviter_username = user.username;
      }
    });

    // Update in month requests
    this.requestsMonth.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === inviterId) {
        request.invited_by = user;
        request.inviter_username = user.username;
      }
    });

    // Update in older requests
    this.requestsOlder.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === inviterId) {
        request.invited_by = user;
        request.inviter_username = user.username;
      }
    });
  }

  // Direct method to use the username from invited_by
  fetchUsernameFromProfiles(invitedBy: string): void {
    console.log('GroupRequestsPage: Using invited_by directly as username:', invitedBy);

    // Since invited_by is now the username directly, we can just use it
    this.updateInviterUsernameDirectly(invitedBy, invitedBy);

    // Create a user object with the username
    const user: User = {
      id: '', // We don't have the ID, but it's not needed
      username: invitedBy
    } as User;

    // Update the inviter in the request lists
    this.updateInviterInLists(invitedBy, user);
  }

  // Update inviter_username directly in the request objects
  updateInviterUsernameDirectly(userId: string, username: string): void {
    console.log('GroupRequestsPage: Directly updating inviter_username for ID:', userId, 'to:', username);

    // Update in recent requests
    this.requestsRecent.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === userId) {
        request.inviter_username = username;
      }
    });

    // Update in month requests
    this.requestsMonth.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === userId) {
        request.inviter_username = username;
      }
    });

    // Update in older requests
    this.requestsOlder.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === userId) {
        request.inviter_username = username;
      }
    });
  }
}
