{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport GoTrueAdminApi from './GoTrueAdminApi';\nimport { DEFAULT_HEADERS, EXPIRY_MARGIN_MS, AUTO_REFRESH_TICK_DURATION_MS, AUTO_REFRESH_TICK_THRESHOLD, GOTRUE_URL, STORAGE_KEY, JWKS_TTL } from './lib/constants';\nimport { AuthImplicitGrantRedirectError, AuthPKCEGrantCodeExchangeError, AuthInvalidCredentialsError, AuthSessionMissingError, AuthInvalidTokenResponseError, AuthUnknownError, isAuthApiError, isAuthError, isAuthRetryableFetchError, isAuthSessionMissingError, isAuthImplicitGrantRedirectError, AuthInvalidJwtError } from './lib/errors';\nimport { _request, _sessionResponse, _sessionResponsePassword, _userResponse, _ssoResponse } from './lib/fetch';\nimport { Deferred, getItemAsync, isBrowser, removeItemAsync, resolveFetch, setItemAsync, uuid, retryable, sleep, supportsLocalStorage, parseParametersFromURL, getCodeChallengeAndMethod, getAlgorithm, validateExp, decodeJWT } from './lib/helpers';\nimport { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage';\nimport { polyfillGlobalThis } from './lib/polyfills';\nimport { version } from './lib/version';\nimport { LockAcquireTimeoutError, navigatorLock } from './lib/locks';\nimport { stringToUint8Array } from './lib/base64url';\npolyfillGlobalThis(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n  url: GOTRUE_URL,\n  storageKey: STORAGE_KEY,\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  headers: DEFAULT_HEADERS,\n  flowType: 'implicit',\n  debug: false,\n  hasCustomAuthorizationHeader: false\n};\nfunction lockNoOp(_x, _x2, _x3) {\n  return _lockNoOp.apply(this, arguments);\n}\nfunction _lockNoOp() {\n  _lockNoOp = _asyncToGenerator(function* (name, acquireTimeout, fn) {\n    return yield fn();\n  });\n  return _lockNoOp.apply(this, arguments);\n}\nlet GoTrueClient = /*#__PURE__*/(() => {\n  class GoTrueClient {\n    /**\n     * Create a new client for use in the browser.\n     */\n    constructor(options) {\n      var _this = this;\n      var _a, _b;\n      this.memoryStorage = null;\n      this.stateChangeEmitters = new Map();\n      this.autoRefreshTicker = null;\n      this.visibilityChangedCallback = null;\n      this.refreshingDeferred = null;\n      /**\n       * Keeps track of the async client initialization.\n       * When null or not yet resolved the auth state is `unknown`\n       * Once resolved the the auth state is known and it's save to call any further client methods.\n       * Keep extra care to never reject or throw uncaught errors\n       */\n      this.initializePromise = null;\n      this.detectSessionInUrl = true;\n      this.hasCustomAuthorizationHeader = false;\n      this.suppressGetSessionWarning = false;\n      this.lockAcquired = false;\n      this.pendingInLock = [];\n      /**\n       * Used to broadcast state change events to other tabs listening.\n       */\n      this.broadcastChannel = null;\n      this.logger = console.log;\n      this.instanceID = GoTrueClient.nextInstanceID;\n      GoTrueClient.nextInstanceID += 1;\n      if (this.instanceID > 0 && isBrowser()) {\n        console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n      }\n      const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n      this.logDebugMessages = !!settings.debug;\n      if (typeof settings.debug === 'function') {\n        this.logger = settings.debug;\n      }\n      this.persistSession = settings.persistSession;\n      this.storageKey = settings.storageKey;\n      this.autoRefreshToken = settings.autoRefreshToken;\n      this.admin = new GoTrueAdminApi({\n        url: settings.url,\n        headers: settings.headers,\n        fetch: settings.fetch\n      });\n      this.url = settings.url;\n      this.headers = settings.headers;\n      this.fetch = resolveFetch(settings.fetch);\n      this.lock = settings.lock || lockNoOp;\n      this.detectSessionInUrl = settings.detectSessionInUrl;\n      this.flowType = settings.flowType;\n      this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader;\n      if (settings.lock) {\n        this.lock = settings.lock;\n      } else if (isBrowser() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {\n        this.lock = navigatorLock;\n      } else {\n        this.lock = lockNoOp;\n      }\n      this.jwks = {\n        keys: []\n      };\n      this.jwks_cached_at = Number.MIN_SAFE_INTEGER;\n      this.mfa = {\n        verify: this._verify.bind(this),\n        enroll: this._enroll.bind(this),\n        unenroll: this._unenroll.bind(this),\n        challenge: this._challenge.bind(this),\n        listFactors: this._listFactors.bind(this),\n        challengeAndVerify: this._challengeAndVerify.bind(this),\n        getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this)\n      };\n      if (this.persistSession) {\n        if (settings.storage) {\n          this.storage = settings.storage;\n        } else {\n          if (supportsLocalStorage()) {\n            this.storage = localStorageAdapter;\n          } else {\n            this.memoryStorage = {};\n            this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n          }\n        }\n      } else {\n        this.memoryStorage = {};\n        this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n      }\n      if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n        try {\n          this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n        } catch (e) {\n          console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n        }\n        (_b = this.broadcastChannel) === null || _b === void 0 ? void 0 : _b.addEventListener('message', /*#__PURE__*/function () {\n          var _ref = _asyncToGenerator(function* (event) {\n            _this._debug('received broadcast notification from other tab or client', event);\n            yield _this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n          });\n          return function (_x4) {\n            return _ref.apply(this, arguments);\n          };\n        }());\n      }\n      this.initialize();\n    }\n    _debug(...args) {\n      if (this.logDebugMessages) {\n        this.logger(`GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`, ...args);\n      }\n      return this;\n    }\n    /**\n     * Initializes the client session either from the url or from storage.\n     * This method is automatically called when instantiating the client, but should also be called\n     * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n     */\n    initialize() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (_this2.initializePromise) {\n          return yield _this2.initializePromise;\n        }\n        _this2.initializePromise = _asyncToGenerator(function* () {\n          return yield _this2._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n            return yield _this2._initialize();\n          }));\n        })();\n        return yield _this2.initializePromise;\n      })();\n    }\n    /**\n     * IMPORTANT:\n     * 1. Never throw in this method, as it is called from the constructor\n     * 2. Never return a session from this method as it would be cached over\n     *    the whole lifetime of the client\n     */\n    _initialize() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        var _a;\n        try {\n          const params = parseParametersFromURL(window.location.href);\n          let callbackUrlType = 'none';\n          if (_this3._isImplicitGrantCallback(params)) {\n            callbackUrlType = 'implicit';\n          } else if (yield _this3._isPKCECallback(params)) {\n            callbackUrlType = 'pkce';\n          }\n          /**\n           * Attempt to get the session from the URL only if these conditions are fulfilled\n           *\n           * Note: If the URL isn't one of the callback url types (implicit or pkce),\n           * then there could be an existing session so we don't want to prematurely remove it\n           */\n          if (isBrowser() && _this3.detectSessionInUrl && callbackUrlType !== 'none') {\n            const {\n              data,\n              error\n            } = yield _this3._getSessionFromURL(params, callbackUrlType);\n            if (error) {\n              _this3._debug('#_initialize()', 'error detecting session from URL', error);\n              if (isAuthImplicitGrantRedirectError(error)) {\n                const errorCode = (_a = error.details) === null || _a === void 0 ? void 0 : _a.code;\n                if (errorCode === 'identity_already_exists' || errorCode === 'identity_not_found' || errorCode === 'single_identity_not_deletable') {\n                  return {\n                    error\n                  };\n                }\n              }\n              // failed login attempt via url,\n              // remove old session as in verifyOtp, signUp and signInWith*\n              yield _this3._removeSession();\n              return {\n                error\n              };\n            }\n            const {\n              session,\n              redirectType\n            } = data;\n            _this3._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n            yield _this3._saveSession(session);\n            setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n              if (redirectType === 'recovery') {\n                yield _this3._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n              } else {\n                yield _this3._notifyAllSubscribers('SIGNED_IN', session);\n              }\n            }), 0);\n            return {\n              error: null\n            };\n          }\n          // no login attempt via callback url try to recover session from storage\n          yield _this3._recoverAndRefresh();\n          return {\n            error: null\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              error\n            };\n          }\n          return {\n            error: new AuthUnknownError('Unexpected error during initialization', error)\n          };\n        } finally {\n          yield _this3._handleVisibilityChange();\n          _this3._debug('#_initialize()', 'end');\n        }\n      })();\n    }\n    /**\n     * Creates a new anonymous user.\n     *\n     * @returns A session where the is_anonymous claim in the access token JWT set to true\n     */\n    signInAnonymously(credentials) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        var _a, _b, _c;\n        try {\n          const res = yield _request(_this4.fetch, 'POST', `${_this4.url}/signup`, {\n            headers: _this4.headers,\n            body: {\n              data: (_b = (_a = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _a === void 0 ? void 0 : _a.data) !== null && _b !== void 0 ? _b : {},\n              gotrue_meta_security: {\n                captcha_token: (_c = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _c === void 0 ? void 0 : _c.captchaToken\n              }\n            },\n            xform: _sessionResponse\n          });\n          const {\n            data,\n            error\n          } = res;\n          if (error || !data) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error: error\n            };\n          }\n          const session = data.session;\n          const user = data.user;\n          if (data.session) {\n            yield _this4._saveSession(data.session);\n            yield _this4._notifyAllSubscribers('SIGNED_IN', session);\n          }\n          return {\n            data: {\n              user,\n              session\n            },\n            error: null\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Creates a new user.\n     *\n     * Be aware that if a user account exists in the system you may get back an\n     * error message that attempts to hide this information from the user.\n     * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n     *\n     * @returns A logged-in session if the server has \"autoconfirm\" ON\n     * @returns A user if the server has \"autoconfirm\" OFF\n     */\n    signUp(credentials) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        var _a, _b, _c;\n        try {\n          let res;\n          if ('email' in credentials) {\n            const {\n              email,\n              password,\n              options\n            } = credentials;\n            let codeChallenge = null;\n            let codeChallengeMethod = null;\n            if (_this5.flowType === 'pkce') {\n              ;\n              [codeChallenge, codeChallengeMethod] = yield getCodeChallengeAndMethod(_this5.storage, _this5.storageKey);\n            }\n            res = yield _request(_this5.fetch, 'POST', `${_this5.url}/signup`, {\n              headers: _this5.headers,\n              redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n              body: {\n                email,\n                password,\n                data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                gotrue_meta_security: {\n                  captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n                },\n                code_challenge: codeChallenge,\n                code_challenge_method: codeChallengeMethod\n              },\n              xform: _sessionResponse\n            });\n          } else if ('phone' in credentials) {\n            const {\n              phone,\n              password,\n              options\n            } = credentials;\n            res = yield _request(_this5.fetch, 'POST', `${_this5.url}/signup`, {\n              headers: _this5.headers,\n              body: {\n                phone,\n                password,\n                data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n                channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n                gotrue_meta_security: {\n                  captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n                }\n              },\n              xform: _sessionResponse\n            });\n          } else {\n            throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n          }\n          const {\n            data,\n            error\n          } = res;\n          if (error || !data) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error: error\n            };\n          }\n          const session = data.session;\n          const user = data.user;\n          if (data.session) {\n            yield _this5._saveSession(data.session);\n            yield _this5._notifyAllSubscribers('SIGNED_IN', session);\n          }\n          return {\n            data: {\n              user,\n              session\n            },\n            error: null\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Log in an existing user with an email and password or phone and password.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or that the\n     * email/phone and password combination is wrong or that the account can only\n     * be accessed via social login.\n     */\n    signInWithPassword(credentials) {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          let res;\n          if ('email' in credentials) {\n            const {\n              email,\n              password,\n              options\n            } = credentials;\n            res = yield _request(_this6.fetch, 'POST', `${_this6.url}/token?grant_type=password`, {\n              headers: _this6.headers,\n              body: {\n                email,\n                password,\n                gotrue_meta_security: {\n                  captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n                }\n              },\n              xform: _sessionResponsePassword\n            });\n          } else if ('phone' in credentials) {\n            const {\n              phone,\n              password,\n              options\n            } = credentials;\n            res = yield _request(_this6.fetch, 'POST', `${_this6.url}/token?grant_type=password`, {\n              headers: _this6.headers,\n              body: {\n                phone,\n                password,\n                gotrue_meta_security: {\n                  captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n                }\n              },\n              xform: _sessionResponsePassword\n            });\n          } else {\n            throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n          }\n          const {\n            data,\n            error\n          } = res;\n          if (error) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          } else if (!data || !data.session || !data.user) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error: new AuthInvalidTokenResponseError()\n            };\n          }\n          if (data.session) {\n            yield _this6._saveSession(data.session);\n            yield _this6._notifyAllSubscribers('SIGNED_IN', data.session);\n          }\n          return {\n            data: Object.assign({\n              user: data.user,\n              session: data.session\n            }, data.weak_password ? {\n              weakPassword: data.weak_password\n            } : null),\n            error\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Log in an existing user via a third-party provider.\n     * This method supports the PKCE flow.\n     */\n    signInWithOAuth(credentials) {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        var _a, _b, _c, _d;\n        return yield _this7._handleProviderSignIn(credentials.provider, {\n          redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n          scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n          queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n          skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect\n        });\n      })();\n    }\n    /**\n     * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n     */\n    exchangeCodeForSession(authCode) {\n      var _this8 = this;\n      return _asyncToGenerator(function* () {\n        yield _this8.initializePromise;\n        return _this8._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return _this8._exchangeCodeForSession(authCode);\n        }));\n      })();\n    }\n    _exchangeCodeForSession(authCode) {\n      var _this9 = this;\n      return _asyncToGenerator(function* () {\n        const storageItem = yield getItemAsync(_this9.storage, `${_this9.storageKey}-code-verifier`);\n        const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n        try {\n          const {\n            data,\n            error\n          } = yield _request(_this9.fetch, 'POST', `${_this9.url}/token?grant_type=pkce`, {\n            headers: _this9.headers,\n            body: {\n              auth_code: authCode,\n              code_verifier: codeVerifier\n            },\n            xform: _sessionResponse\n          });\n          yield removeItemAsync(_this9.storage, `${_this9.storageKey}-code-verifier`);\n          if (error) {\n            throw error;\n          }\n          if (!data || !data.session || !data.user) {\n            return {\n              data: {\n                user: null,\n                session: null,\n                redirectType: null\n              },\n              error: new AuthInvalidTokenResponseError()\n            };\n          }\n          if (data.session) {\n            yield _this9._saveSession(data.session);\n            yield _this9._notifyAllSubscribers('SIGNED_IN', data.session);\n          }\n          return {\n            data: Object.assign(Object.assign({}, data), {\n              redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null\n            }),\n            error\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null,\n                redirectType: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Allows signing in with an OIDC ID token. The authentication provider used\n     * should be enabled and configured.\n     */\n    signInWithIdToken(credentials) {\n      var _this10 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const {\n            options,\n            provider,\n            token,\n            access_token,\n            nonce\n          } = credentials;\n          const res = yield _request(_this10.fetch, 'POST', `${_this10.url}/token?grant_type=id_token`, {\n            headers: _this10.headers,\n            body: {\n              provider,\n              id_token: token,\n              access_token,\n              nonce,\n              gotrue_meta_security: {\n                captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n              }\n            },\n            xform: _sessionResponse\n          });\n          const {\n            data,\n            error\n          } = res;\n          if (error) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          } else if (!data || !data.session || !data.user) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error: new AuthInvalidTokenResponseError()\n            };\n          }\n          if (data.session) {\n            yield _this10._saveSession(data.session);\n            yield _this10._notifyAllSubscribers('SIGNED_IN', data.session);\n          }\n          return {\n            data,\n            error\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Log in a user using magiclink or a one-time password (OTP).\n     *\n     * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n     * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n     * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or, that the account\n     * can only be accessed via social login.\n     *\n     * Do note that you will need to configure a Whatsapp sender on Twilio\n     * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n     * channel is not supported on other providers\n     * at this time.\n     * This method supports PKCE when an email is passed.\n     */\n    signInWithOtp(credentials) {\n      var _this11 = this;\n      return _asyncToGenerator(function* () {\n        var _a, _b, _c, _d, _e;\n        try {\n          if ('email' in credentials) {\n            const {\n              email,\n              options\n            } = credentials;\n            let codeChallenge = null;\n            let codeChallengeMethod = null;\n            if (_this11.flowType === 'pkce') {\n              ;\n              [codeChallenge, codeChallengeMethod] = yield getCodeChallengeAndMethod(_this11.storage, _this11.storageKey);\n            }\n            const {\n              error\n            } = yield _request(_this11.fetch, 'POST', `${_this11.url}/otp`, {\n              headers: _this11.headers,\n              body: {\n                email,\n                data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n                gotrue_meta_security: {\n                  captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n                },\n                code_challenge: codeChallenge,\n                code_challenge_method: codeChallengeMethod\n              },\n              redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n            });\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          if ('phone' in credentials) {\n            const {\n              phone,\n              options\n            } = credentials;\n            const {\n              data,\n              error\n            } = yield _request(_this11.fetch, 'POST', `${_this11.url}/otp`, {\n              headers: _this11.headers,\n              body: {\n                phone,\n                data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n                create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n                gotrue_meta_security: {\n                  captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n                },\n                channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms'\n              }\n            });\n            return {\n              data: {\n                user: null,\n                session: null,\n                messageId: data === null || data === void 0 ? void 0 : data.message_id\n              },\n              error\n            };\n          }\n          throw new AuthInvalidCredentialsError('You must provide either an email or phone number.');\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n     */\n    verifyOtp(params) {\n      var _this12 = this;\n      return _asyncToGenerator(function* () {\n        var _a, _b;\n        try {\n          let redirectTo = undefined;\n          let captchaToken = undefined;\n          if ('options' in params) {\n            redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n            captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n          }\n          const {\n            data,\n            error\n          } = yield _request(_this12.fetch, 'POST', `${_this12.url}/verify`, {\n            headers: _this12.headers,\n            body: Object.assign(Object.assign({}, params), {\n              gotrue_meta_security: {\n                captcha_token: captchaToken\n              }\n            }),\n            redirectTo,\n            xform: _sessionResponse\n          });\n          if (error) {\n            throw error;\n          }\n          if (!data) {\n            throw new Error('An error occurred on token verification.');\n          }\n          const session = data.session;\n          const user = data.user;\n          if (session === null || session === void 0 ? void 0 : session.access_token) {\n            yield _this12._saveSession(session);\n            yield _this12._notifyAllSubscribers(params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN', session);\n          }\n          return {\n            data: {\n              user,\n              session\n            },\n            error: null\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Attempts a single-sign on using an enterprise Identity Provider. A\n     * successful SSO attempt will redirect the current page to the identity\n     * provider authorization page. The redirect URL is implementation and SSO\n     * protocol specific.\n     *\n     * You can use it by providing a SSO domain. Typically you can extract this\n     * domain by asking users for their email address. If this domain is\n     * registered on the Auth instance the redirect will use that organization's\n     * currently active SSO Identity Provider for the login.\n     *\n     * If you have built an organization-specific login page, you can use the\n     * organization's SSO Identity Provider UUID directly instead.\n     */\n    signInWithSSO(params) {\n      var _this13 = this;\n      return _asyncToGenerator(function* () {\n        var _a, _b, _c;\n        try {\n          let codeChallenge = null;\n          let codeChallengeMethod = null;\n          if (_this13.flowType === 'pkce') {\n            ;\n            [codeChallenge, codeChallengeMethod] = yield getCodeChallengeAndMethod(_this13.storage, _this13.storageKey);\n          }\n          return yield _request(_this13.fetch, 'POST', `${_this13.url}/sso`, {\n            body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, 'providerId' in params ? {\n              provider_id: params.providerId\n            } : null), 'domain' in params ? {\n              domain: params.domain\n            } : null), {\n              redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined\n            }), ((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken) ? {\n              gotrue_meta_security: {\n                captcha_token: params.options.captchaToken\n              }\n            } : null), {\n              skip_http_redirect: true,\n              code_challenge: codeChallenge,\n              code_challenge_method: codeChallengeMethod\n            }),\n            headers: _this13.headers,\n            xform: _ssoResponse\n          });\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: null,\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Sends a reauthentication OTP to the user's email or phone number.\n     * Requires the user to be signed-in.\n     */\n    reauthenticate() {\n      var _this14 = this;\n      return _asyncToGenerator(function* () {\n        yield _this14.initializePromise;\n        return yield _this14._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return yield _this14._reauthenticate();\n        }));\n      })();\n    }\n    _reauthenticate() {\n      var _this15 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          return yield _this15._useSession(/*#__PURE__*/function () {\n            var _ref7 = _asyncToGenerator(function* (result) {\n              const {\n                data: {\n                  session\n                },\n                error: sessionError\n              } = result;\n              if (sessionError) throw sessionError;\n              if (!session) throw new AuthSessionMissingError();\n              const {\n                error\n              } = yield _request(_this15.fetch, 'GET', `${_this15.url}/reauthenticate`, {\n                headers: _this15.headers,\n                jwt: session.access_token\n              });\n              return {\n                data: {\n                  user: null,\n                  session: null\n                },\n                error\n              };\n            });\n            return function (_x5) {\n              return _ref7.apply(this, arguments);\n            };\n          }());\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n     */\n    resend(credentials) {\n      var _this16 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const endpoint = `${_this16.url}/resend`;\n          if ('email' in credentials) {\n            const {\n              email,\n              type,\n              options\n            } = credentials;\n            const {\n              error\n            } = yield _request(_this16.fetch, 'POST', endpoint, {\n              headers: _this16.headers,\n              body: {\n                email,\n                type,\n                gotrue_meta_security: {\n                  captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n                }\n              },\n              redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n            });\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          } else if ('phone' in credentials) {\n            const {\n              phone,\n              type,\n              options\n            } = credentials;\n            const {\n              data,\n              error\n            } = yield _request(_this16.fetch, 'POST', endpoint, {\n              headers: _this16.headers,\n              body: {\n                phone,\n                type,\n                gotrue_meta_security: {\n                  captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n                }\n              }\n            });\n            return {\n              data: {\n                user: null,\n                session: null,\n                messageId: data === null || data === void 0 ? void 0 : data.message_id\n              },\n              error\n            };\n          }\n          throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Returns the session, refreshing it if necessary.\n     *\n     * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n     *\n     * **IMPORTANT:** This method loads values directly from the storage attached\n     * to the client. If that storage is based on request cookies for example,\n     * the values in it may not be authentic and therefore it's strongly advised\n     * against using this method and its results in such circumstances. A warning\n     * will be emitted if this is detected. Use {@link #getUser()} instead.\n     */\n    getSession() {\n      var _this17 = this;\n      return _asyncToGenerator(function* () {\n        yield _this17.initializePromise;\n        const result = yield _this17._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return _this17._useSession(/*#__PURE__*/function () {\n            var _ref9 = _asyncToGenerator(function* (result) {\n              return result;\n            });\n            return function (_x6) {\n              return _ref9.apply(this, arguments);\n            };\n          }());\n        }));\n        return result;\n      })();\n    }\n    /**\n     * Acquires a global lock based on the storage key.\n     */\n    _acquireLock(acquireTimeout, fn) {\n      var _this18 = this;\n      return _asyncToGenerator(function* () {\n        _this18._debug('#_acquireLock', 'begin', acquireTimeout);\n        try {\n          if (_this18.lockAcquired) {\n            const last = _this18.pendingInLock.length ? _this18.pendingInLock[_this18.pendingInLock.length - 1] : Promise.resolve();\n            const result = _asyncToGenerator(function* () {\n              yield last;\n              return yield fn();\n            })();\n            _this18.pendingInLock.push(_asyncToGenerator(function* () {\n              try {\n                yield result;\n              } catch (e) {\n                // we just care if it finished\n              }\n            })());\n            return result;\n          }\n          return yield _this18.lock(`lock:${_this18.storageKey}`, acquireTimeout, /*#__PURE__*/_asyncToGenerator(function* () {\n            _this18._debug('#_acquireLock', 'lock acquired for storage key', _this18.storageKey);\n            try {\n              _this18.lockAcquired = true;\n              const result = fn();\n              _this18.pendingInLock.push(_asyncToGenerator(function* () {\n                try {\n                  yield result;\n                } catch (e) {\n                  // we just care if it finished\n                }\n              })());\n              yield result;\n              // keep draining the queue until there's nothing to wait on\n              while (_this18.pendingInLock.length) {\n                const waitOn = [..._this18.pendingInLock];\n                yield Promise.all(waitOn);\n                _this18.pendingInLock.splice(0, waitOn.length);\n              }\n              return yield result;\n            } finally {\n              _this18._debug('#_acquireLock', 'lock released for storage key', _this18.storageKey);\n              _this18.lockAcquired = false;\n            }\n          }));\n        } finally {\n          _this18._debug('#_acquireLock', 'end');\n        }\n      })();\n    }\n    /**\n     * Use instead of {@link #getSession} inside the library. It is\n     * semantically usually what you want, as getting a session involves some\n     * processing afterwards that requires only one client operating on the\n     * session at once across multiple tabs or processes.\n     */\n    _useSession(fn) {\n      var _this19 = this;\n      return _asyncToGenerator(function* () {\n        _this19._debug('#_useSession', 'begin');\n        try {\n          // the use of __loadSession here is the only correct use of the function!\n          const result = yield _this19.__loadSession();\n          return yield fn(result);\n        } finally {\n          _this19._debug('#_useSession', 'end');\n        }\n      })();\n    }\n    /**\n     * NEVER USE DIRECTLY!\n     *\n     * Always use {@link #_useSession}.\n     */\n    __loadSession() {\n      var _this20 = this;\n      return _asyncToGenerator(function* () {\n        _this20._debug('#__loadSession()', 'begin');\n        if (!_this20.lockAcquired) {\n          _this20._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n        }\n        try {\n          let currentSession = null;\n          const maybeSession = yield getItemAsync(_this20.storage, _this20.storageKey);\n          _this20._debug('#getSession()', 'session from storage', maybeSession);\n          if (maybeSession !== null) {\n            if (_this20._isValidSession(maybeSession)) {\n              currentSession = maybeSession;\n            } else {\n              _this20._debug('#getSession()', 'session from storage is not valid');\n              yield _this20._removeSession();\n            }\n          }\n          if (!currentSession) {\n            return {\n              data: {\n                session: null\n              },\n              error: null\n            };\n          }\n          // A session is considered expired before the access token _actually_\n          // expires. When the autoRefreshToken option is off (or when the tab is\n          // in the background), very eager users of getSession() -- like\n          // realtime-js -- might send a valid JWT which will expire by the time it\n          // reaches the server.\n          const hasExpired = currentSession.expires_at ? currentSession.expires_at * 1000 - Date.now() < EXPIRY_MARGIN_MS : false;\n          _this20._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n          if (!hasExpired) {\n            if (_this20.storage.isServer) {\n              let suppressWarning = _this20.suppressGetSessionWarning;\n              const proxySession = new Proxy(currentSession, {\n                get: (target, prop, receiver) => {\n                  if (!suppressWarning && prop === 'user') {\n                    // only show warning when the user object is being accessed from the server\n                    console.warn('Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.');\n                    suppressWarning = true; // keeps this proxy instance from logging additional warnings\n                    _this20.suppressGetSessionWarning = true; // keeps this client's future proxy instances from warning\n                  }\n                  return Reflect.get(target, prop, receiver);\n                }\n              });\n              currentSession = proxySession;\n            }\n            return {\n              data: {\n                session: currentSession\n              },\n              error: null\n            };\n          }\n          const {\n            session,\n            error\n          } = yield _this20._callRefreshToken(currentSession.refresh_token);\n          if (error) {\n            return {\n              data: {\n                session: null\n              },\n              error\n            };\n          }\n          return {\n            data: {\n              session\n            },\n            error: null\n          };\n        } finally {\n          _this20._debug('#__loadSession()', 'end');\n        }\n      })();\n    }\n    /**\n     * Gets the current user details if there is an existing session. This method\n     * performs a network request to the Supabase Auth server, so the returned\n     * value is authentic and can be used to base authorization rules on.\n     *\n     * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n     */\n    getUser(jwt) {\n      var _this21 = this;\n      return _asyncToGenerator(function* () {\n        if (jwt) {\n          return yield _this21._getUser(jwt);\n        }\n        yield _this21.initializePromise;\n        const result = yield _this21._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return yield _this21._getUser();\n        }));\n        return result;\n      })();\n    }\n    _getUser(jwt) {\n      var _this22 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (jwt) {\n            return yield _request(_this22.fetch, 'GET', `${_this22.url}/user`, {\n              headers: _this22.headers,\n              jwt: jwt,\n              xform: _userResponse\n            });\n          }\n          return yield _this22._useSession(/*#__PURE__*/function () {\n            var _ref15 = _asyncToGenerator(function* (result) {\n              var _a, _b, _c;\n              const {\n                data,\n                error\n              } = result;\n              if (error) {\n                throw error;\n              }\n              // returns an error if there is no access_token or custom authorization header\n              if (!((_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) && !_this22.hasCustomAuthorizationHeader) {\n                return {\n                  data: {\n                    user: null\n                  },\n                  error: new AuthSessionMissingError()\n                };\n              }\n              return yield _request(_this22.fetch, 'GET', `${_this22.url}/user`, {\n                headers: _this22.headers,\n                jwt: (_c = (_b = data.session) === null || _b === void 0 ? void 0 : _b.access_token) !== null && _c !== void 0 ? _c : undefined,\n                xform: _userResponse\n              });\n            });\n            return function (_x7) {\n              return _ref15.apply(this, arguments);\n            };\n          }());\n        } catch (error) {\n          if (isAuthError(error)) {\n            if (isAuthSessionMissingError(error)) {\n              // JWT contains a `session_id` which does not correspond to an active\n              // session in the database, indicating the user is signed out.\n              yield _this22._removeSession();\n              yield removeItemAsync(_this22.storage, `${_this22.storageKey}-code-verifier`);\n            }\n            return {\n              data: {\n                user: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Updates user data for a logged in user.\n     */\n    updateUser(attributes, options = {}) {\n      var _this23 = this;\n      return _asyncToGenerator(function* () {\n        yield _this23.initializePromise;\n        return yield _this23._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return yield _this23._updateUser(attributes, options);\n        }));\n      })();\n    }\n    _updateUser(attributes, options = {}) {\n      var _this24 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          return yield _this24._useSession(/*#__PURE__*/function () {\n            var _ref17 = _asyncToGenerator(function* (result) {\n              const {\n                data: sessionData,\n                error: sessionError\n              } = result;\n              if (sessionError) {\n                throw sessionError;\n              }\n              if (!sessionData.session) {\n                throw new AuthSessionMissingError();\n              }\n              const session = sessionData.session;\n              let codeChallenge = null;\n              let codeChallengeMethod = null;\n              if (_this24.flowType === 'pkce' && attributes.email != null) {\n                ;\n                [codeChallenge, codeChallengeMethod] = yield getCodeChallengeAndMethod(_this24.storage, _this24.storageKey);\n              }\n              const {\n                data,\n                error: userError\n              } = yield _request(_this24.fetch, 'PUT', `${_this24.url}/user`, {\n                headers: _this24.headers,\n                redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                body: Object.assign(Object.assign({}, attributes), {\n                  code_challenge: codeChallenge,\n                  code_challenge_method: codeChallengeMethod\n                }),\n                jwt: session.access_token,\n                xform: _userResponse\n              });\n              if (userError) throw userError;\n              session.user = data.user;\n              yield _this24._saveSession(session);\n              yield _this24._notifyAllSubscribers('USER_UPDATED', session);\n              return {\n                data: {\n                  user: session.user\n                },\n                error: null\n              };\n            });\n            return function (_x8) {\n              return _ref17.apply(this, arguments);\n            };\n          }());\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n     * If the refresh token or access token in the current session is invalid, an error will be thrown.\n     * @param currentSession The current session that minimally contains an access token and refresh token.\n     */\n    setSession(currentSession) {\n      var _this25 = this;\n      return _asyncToGenerator(function* () {\n        yield _this25.initializePromise;\n        return yield _this25._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return yield _this25._setSession(currentSession);\n        }));\n      })();\n    }\n    _setSession(currentSession) {\n      var _this26 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (!currentSession.access_token || !currentSession.refresh_token) {\n            throw new AuthSessionMissingError();\n          }\n          const timeNow = Date.now() / 1000;\n          let expiresAt = timeNow;\n          let hasExpired = true;\n          let session = null;\n          const {\n            payload\n          } = decodeJWT(currentSession.access_token);\n          if (payload.exp) {\n            expiresAt = payload.exp;\n            hasExpired = expiresAt <= timeNow;\n          }\n          if (hasExpired) {\n            const {\n              session: refreshedSession,\n              error\n            } = yield _this26._callRefreshToken(currentSession.refresh_token);\n            if (error) {\n              return {\n                data: {\n                  user: null,\n                  session: null\n                },\n                error: error\n              };\n            }\n            if (!refreshedSession) {\n              return {\n                data: {\n                  user: null,\n                  session: null\n                },\n                error: null\n              };\n            }\n            session = refreshedSession;\n          } else {\n            const {\n              data,\n              error\n            } = yield _this26._getUser(currentSession.access_token);\n            if (error) {\n              throw error;\n            }\n            session = {\n              access_token: currentSession.access_token,\n              refresh_token: currentSession.refresh_token,\n              user: data.user,\n              token_type: 'bearer',\n              expires_in: expiresAt - timeNow,\n              expires_at: expiresAt\n            };\n            yield _this26._saveSession(session);\n            yield _this26._notifyAllSubscribers('SIGNED_IN', session);\n          }\n          return {\n            data: {\n              user: session.user,\n              session\n            },\n            error: null\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                session: null,\n                user: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Returns a new session, regardless of expiry status.\n     * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n     * If the current session's refresh token is invalid, an error will be thrown.\n     * @param currentSession The current session. If passed in, it must contain a refresh token.\n     */\n    refreshSession(currentSession) {\n      var _this27 = this;\n      return _asyncToGenerator(function* () {\n        yield _this27.initializePromise;\n        return yield _this27._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return yield _this27._refreshSession(currentSession);\n        }));\n      })();\n    }\n    _refreshSession(currentSession) {\n      var _this28 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          return yield _this28._useSession(/*#__PURE__*/function () {\n            var _ref20 = _asyncToGenerator(function* (result) {\n              var _a;\n              if (!currentSession) {\n                const {\n                  data,\n                  error\n                } = result;\n                if (error) {\n                  throw error;\n                }\n                currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n              }\n              if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n                throw new AuthSessionMissingError();\n              }\n              const {\n                session,\n                error\n              } = yield _this28._callRefreshToken(currentSession.refresh_token);\n              if (error) {\n                return {\n                  data: {\n                    user: null,\n                    session: null\n                  },\n                  error: error\n                };\n              }\n              if (!session) {\n                return {\n                  data: {\n                    user: null,\n                    session: null\n                  },\n                  error: null\n                };\n              }\n              return {\n                data: {\n                  user: session.user,\n                  session\n                },\n                error: null\n              };\n            });\n            return function (_x9) {\n              return _ref20.apply(this, arguments);\n            };\n          }());\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Gets the session data from a URL string\n     */\n    _getSessionFromURL(params, callbackUrlType) {\n      var _this29 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (!isBrowser()) throw new AuthImplicitGrantRedirectError('No browser detected.');\n          // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n          if (params.error || params.error_description || params.error_code) {\n            // The error class returned implies that the redirect is from an implicit grant flow\n            // but it could also be from a redirect error from a PKCE flow.\n            throw new AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n              error: params.error || 'unspecified_error',\n              code: params.error_code || 'unspecified_code'\n            });\n          }\n          // Checks for mismatches between the flowType initialised in the client and the URL parameters\n          switch (callbackUrlType) {\n            case 'implicit':\n              if (_this29.flowType === 'pkce') {\n                throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n              }\n              break;\n            case 'pkce':\n              if (_this29.flowType === 'implicit') {\n                throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n              }\n              break;\n            default:\n            // there's no mismatch so we continue\n          }\n          // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n          if (callbackUrlType === 'pkce') {\n            _this29._debug('#_initialize()', 'begin', 'is PKCE flow', true);\n            if (!params.code) throw new AuthPKCEGrantCodeExchangeError('No code detected.');\n            const {\n              data,\n              error\n            } = yield _this29._exchangeCodeForSession(params.code);\n            if (error) throw error;\n            const url = new URL(window.location.href);\n            url.searchParams.delete('code');\n            window.history.replaceState(window.history.state, '', url.toString());\n            return {\n              data: {\n                session: data.session,\n                redirectType: null\n              },\n              error: null\n            };\n          }\n          const {\n            provider_token,\n            provider_refresh_token,\n            access_token,\n            refresh_token,\n            expires_in,\n            expires_at,\n            token_type\n          } = params;\n          if (!access_token || !expires_in || !refresh_token || !token_type) {\n            throw new AuthImplicitGrantRedirectError('No session defined in URL');\n          }\n          const timeNow = Math.round(Date.now() / 1000);\n          const expiresIn = parseInt(expires_in);\n          let expiresAt = timeNow + expiresIn;\n          if (expires_at) {\n            expiresAt = parseInt(expires_at);\n          }\n          const actuallyExpiresIn = expiresAt - timeNow;\n          if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION_MS) {\n            console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n          }\n          const issuedAt = expiresAt - expiresIn;\n          if (timeNow - issuedAt >= 120) {\n            console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n          } else if (timeNow - issuedAt < 0) {\n            console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew', issuedAt, expiresAt, timeNow);\n          }\n          const {\n            data,\n            error\n          } = yield _this29._getUser(access_token);\n          if (error) throw error;\n          const session = {\n            provider_token,\n            provider_refresh_token,\n            access_token,\n            expires_in: expiresIn,\n            expires_at: expiresAt,\n            refresh_token,\n            token_type,\n            user: data.user\n          };\n          // Remove tokens from URL\n          window.location.hash = '';\n          _this29._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n          return {\n            data: {\n              session,\n              redirectType: params.type\n            },\n            error: null\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                session: null,\n                redirectType: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n     */\n    _isImplicitGrantCallback(params) {\n      return Boolean(params.access_token || params.error_description);\n    }\n    /**\n     * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n     */\n    _isPKCECallback(params) {\n      var _this30 = this;\n      return _asyncToGenerator(function* () {\n        const currentStorageContent = yield getItemAsync(_this30.storage, `${_this30.storageKey}-code-verifier`);\n        return !!(params.code && currentStorageContent);\n      })();\n    }\n    /**\n     * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n     *\n     * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n     * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n     *\n     * If using `others` scope, no `SIGNED_OUT` event is fired!\n     */\n    signOut(options = {\n      scope: 'global'\n    }) {\n      var _this31 = this;\n      return _asyncToGenerator(function* () {\n        yield _this31.initializePromise;\n        return yield _this31._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return yield _this31._signOut(options);\n        }));\n      })();\n    }\n    _signOut({\n      scope\n    } = {\n      scope: 'global'\n    }) {\n      var _this32 = this;\n      return _asyncToGenerator(function* () {\n        return yield _this32._useSession(/*#__PURE__*/function () {\n          var _ref22 = _asyncToGenerator(function* (result) {\n            var _a;\n            const {\n              data,\n              error: sessionError\n            } = result;\n            if (sessionError) {\n              return {\n                error: sessionError\n              };\n            }\n            const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n            if (accessToken) {\n              const {\n                error\n              } = yield _this32.admin.signOut(accessToken, scope);\n              if (error) {\n                // ignore 404s since user might not exist anymore\n                // ignore 401s since an invalid or expired JWT should sign out the current session\n                if (!(isAuthApiError(error) && (error.status === 404 || error.status === 401 || error.status === 403))) {\n                  return {\n                    error\n                  };\n                }\n              }\n            }\n            if (scope !== 'others') {\n              yield _this32._removeSession();\n              yield removeItemAsync(_this32.storage, `${_this32.storageKey}-code-verifier`);\n            }\n            return {\n              error: null\n            };\n          });\n          return function (_x10) {\n            return _ref22.apply(this, arguments);\n          };\n        }());\n      })();\n    }\n    /**\n     * Receive a notification every time an auth event happens.\n     * @param callback A callback function to be invoked when an auth event happens.\n     */\n    onAuthStateChange(callback) {\n      var _this33 = this;\n      const id = uuid();\n      const subscription = {\n        id,\n        callback,\n        unsubscribe: () => {\n          this._debug('#unsubscribe()', 'state change callback with id removed', id);\n          this.stateChangeEmitters.delete(id);\n        }\n      };\n      this._debug('#onAuthStateChange()', 'registered callback with id', id);\n      this.stateChangeEmitters.set(id, subscription);\n      _asyncToGenerator(function* () {\n        yield _this33.initializePromise;\n        yield _this33._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          _this33._emitInitialSession(id);\n        }));\n      })();\n      return {\n        data: {\n          subscription\n        }\n      };\n    }\n    _emitInitialSession(id) {\n      var _this34 = this;\n      return _asyncToGenerator(function* () {\n        return yield _this34._useSession(/*#__PURE__*/function () {\n          var _ref25 = _asyncToGenerator(function* (result) {\n            var _a, _b;\n            try {\n              const {\n                data: {\n                  session\n                },\n                error\n              } = result;\n              if (error) throw error;\n              yield (_a = _this34.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session);\n              _this34._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n            } catch (err) {\n              yield (_b = _this34.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null);\n              _this34._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n              console.error(err);\n            }\n          });\n          return function (_x11) {\n            return _ref25.apply(this, arguments);\n          };\n        }());\n      })();\n    }\n    /**\n     * Sends a password reset request to an email address. This method supports the PKCE flow.\n     *\n     * @param email The email address of the user.\n     * @param options.redirectTo The URL to send the user to after they click the password reset link.\n     * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n     */\n    resetPasswordForEmail(email, options = {}) {\n      var _this35 = this;\n      return _asyncToGenerator(function* () {\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (_this35.flowType === 'pkce') {\n          ;\n          [codeChallenge, codeChallengeMethod] = yield getCodeChallengeAndMethod(_this35.storage, _this35.storageKey, true // isPasswordRecovery\n          );\n        }\n        try {\n          return yield _request(_this35.fetch, 'POST', `${_this35.url}/recover`, {\n            body: {\n              email,\n              code_challenge: codeChallenge,\n              code_challenge_method: codeChallengeMethod,\n              gotrue_meta_security: {\n                captcha_token: options.captchaToken\n              }\n            },\n            headers: _this35.headers,\n            redirectTo: options.redirectTo\n          });\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: null,\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Gets all the identities linked to a user.\n     */\n    getUserIdentities() {\n      var _this36 = this;\n      return _asyncToGenerator(function* () {\n        var _a;\n        try {\n          const {\n            data,\n            error\n          } = yield _this36.getUser();\n          if (error) throw error;\n          return {\n            data: {\n              identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : []\n            },\n            error: null\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: null,\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Links an oauth identity to an existing user.\n     * This method supports the PKCE flow.\n     */\n    linkIdentity(credentials) {\n      var _this37 = this;\n      return _asyncToGenerator(function* () {\n        var _a;\n        try {\n          const {\n            data,\n            error\n          } = yield _this37._useSession(/*#__PURE__*/function () {\n            var _ref26 = _asyncToGenerator(function* (result) {\n              var _a, _b, _c, _d, _e;\n              const {\n                data,\n                error\n              } = result;\n              if (error) throw error;\n              const url = yield _this37._getUrlForProvider(`${_this37.url}/user/identities/authorize`, credentials.provider, {\n                redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n                scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n                queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n                skipBrowserRedirect: true\n              });\n              return yield _request(_this37.fetch, 'GET', url, {\n                headers: _this37.headers,\n                jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined\n              });\n            });\n            return function (_x12) {\n              return _ref26.apply(this, arguments);\n            };\n          }());\n          if (error) throw error;\n          if (isBrowser() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n            window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n          }\n          return {\n            data: {\n              provider: credentials.provider,\n              url: data === null || data === void 0 ? void 0 : data.url\n            },\n            error: null\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: {\n                provider: credentials.provider,\n                url: null\n              },\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n     */\n    unlinkIdentity(identity) {\n      var _this38 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          return yield _this38._useSession(/*#__PURE__*/function () {\n            var _ref27 = _asyncToGenerator(function* (result) {\n              var _a, _b;\n              const {\n                data,\n                error\n              } = result;\n              if (error) {\n                throw error;\n              }\n              return yield _request(_this38.fetch, 'DELETE', `${_this38.url}/user/identities/${identity.identity_id}`, {\n                headers: _this38.headers,\n                jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined\n              });\n            });\n            return function (_x13) {\n              return _ref27.apply(this, arguments);\n            };\n          }());\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: null,\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * Generates a new JWT.\n     * @param refreshToken A valid refresh token that was returned on login.\n     */\n    _refreshAccessToken(refreshToken) {\n      var _this39 = this;\n      return _asyncToGenerator(function* () {\n        const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n        _this39._debug(debugName, 'begin');\n        try {\n          const startedAt = Date.now();\n          // will attempt to refresh the token with exponential backoff\n          return yield retryable(/*#__PURE__*/function () {\n            var _ref28 = _asyncToGenerator(function* (attempt) {\n              if (attempt > 0) {\n                yield sleep(200 * Math.pow(2, attempt - 1)); // 200, 400, 800, ...\n              }\n              _this39._debug(debugName, 'refreshing attempt', attempt);\n              return yield _request(_this39.fetch, 'POST', `${_this39.url}/token?grant_type=refresh_token`, {\n                body: {\n                  refresh_token: refreshToken\n                },\n                headers: _this39.headers,\n                xform: _sessionResponse\n              });\n            });\n            return function (_x14) {\n              return _ref28.apply(this, arguments);\n            };\n          }(), (attempt, error) => {\n            const nextBackOffInterval = 200 * Math.pow(2, attempt);\n            return error && isAuthRetryableFetchError(error) &&\n            // retryable only if the request can be sent before the backoff overflows the tick duration\n            Date.now() + nextBackOffInterval - startedAt < AUTO_REFRESH_TICK_DURATION_MS;\n          });\n        } catch (error) {\n          _this39._debug(debugName, 'error', error);\n          if (isAuthError(error)) {\n            return {\n              data: {\n                session: null,\n                user: null\n              },\n              error\n            };\n          }\n          throw error;\n        } finally {\n          _this39._debug(debugName, 'end');\n        }\n      })();\n    }\n    _isValidSession(maybeSession) {\n      const isValidSession = typeof maybeSession === 'object' && maybeSession !== null && 'access_token' in maybeSession && 'refresh_token' in maybeSession && 'expires_at' in maybeSession;\n      return isValidSession;\n    }\n    _handleProviderSignIn(provider, options) {\n      var _this40 = this;\n      return _asyncToGenerator(function* () {\n        const url = yield _this40._getUrlForProvider(`${_this40.url}/authorize`, provider, {\n          redirectTo: options.redirectTo,\n          scopes: options.scopes,\n          queryParams: options.queryParams\n        });\n        _this40._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n        // try to open on the browser\n        if (isBrowser() && !options.skipBrowserRedirect) {\n          window.location.assign(url);\n        }\n        return {\n          data: {\n            provider,\n            url\n          },\n          error: null\n        };\n      })();\n    }\n    /**\n     * Recovers the session from LocalStorage and refreshes the token\n     * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n     */\n    _recoverAndRefresh() {\n      var _this41 = this;\n      return _asyncToGenerator(function* () {\n        var _a;\n        const debugName = '#_recoverAndRefresh()';\n        _this41._debug(debugName, 'begin');\n        try {\n          const currentSession = yield getItemAsync(_this41.storage, _this41.storageKey);\n          _this41._debug(debugName, 'session from storage', currentSession);\n          if (!_this41._isValidSession(currentSession)) {\n            _this41._debug(debugName, 'session is not valid');\n            if (currentSession !== null) {\n              yield _this41._removeSession();\n            }\n            return;\n          }\n          const expiresWithMargin = ((_a = currentSession.expires_at) !== null && _a !== void 0 ? _a : Infinity) * 1000 - Date.now() < EXPIRY_MARGIN_MS;\n          _this41._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN_MS}s`);\n          if (expiresWithMargin) {\n            if (_this41.autoRefreshToken && currentSession.refresh_token) {\n              const {\n                error\n              } = yield _this41._callRefreshToken(currentSession.refresh_token);\n              if (error) {\n                console.error(error);\n                if (!isAuthRetryableFetchError(error)) {\n                  _this41._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n                  yield _this41._removeSession();\n                }\n              }\n            }\n          } else {\n            // no need to persist currentSession again, as we just loaded it from\n            // local storage; persisting it again may overwrite a value saved by\n            // another client with access to the same local storage\n            yield _this41._notifyAllSubscribers('SIGNED_IN', currentSession);\n          }\n        } catch (err) {\n          _this41._debug(debugName, 'error', err);\n          console.error(err);\n          return;\n        } finally {\n          _this41._debug(debugName, 'end');\n        }\n      })();\n    }\n    _callRefreshToken(refreshToken) {\n      var _this42 = this;\n      return _asyncToGenerator(function* () {\n        var _a, _b;\n        if (!refreshToken) {\n          throw new AuthSessionMissingError();\n        }\n        // refreshing is already in progress\n        if (_this42.refreshingDeferred) {\n          return _this42.refreshingDeferred.promise;\n        }\n        const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n        _this42._debug(debugName, 'begin');\n        try {\n          _this42.refreshingDeferred = new Deferred();\n          const {\n            data,\n            error\n          } = yield _this42._refreshAccessToken(refreshToken);\n          if (error) throw error;\n          if (!data.session) throw new AuthSessionMissingError();\n          yield _this42._saveSession(data.session);\n          yield _this42._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n          const result = {\n            session: data.session,\n            error: null\n          };\n          _this42.refreshingDeferred.resolve(result);\n          return result;\n        } catch (error) {\n          _this42._debug(debugName, 'error', error);\n          if (isAuthError(error)) {\n            const result = {\n              session: null,\n              error\n            };\n            if (!isAuthRetryableFetchError(error)) {\n              yield _this42._removeSession();\n            }\n            (_a = _this42.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n            return result;\n          }\n          (_b = _this42.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n          throw error;\n        } finally {\n          _this42.refreshingDeferred = null;\n          _this42._debug(debugName, 'end');\n        }\n      })();\n    }\n    _notifyAllSubscribers(event, session, broadcast = true) {\n      var _this43 = this;\n      return _asyncToGenerator(function* () {\n        const debugName = `#_notifyAllSubscribers(${event})`;\n        _this43._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n        try {\n          if (_this43.broadcastChannel && broadcast) {\n            _this43.broadcastChannel.postMessage({\n              event,\n              session\n            });\n          }\n          const errors = [];\n          const promises = Array.from(_this43.stateChangeEmitters.values()).map(/*#__PURE__*/function () {\n            var _ref29 = _asyncToGenerator(function* (x) {\n              try {\n                yield x.callback(event, session);\n              } catch (e) {\n                errors.push(e);\n              }\n            });\n            return function (_x15) {\n              return _ref29.apply(this, arguments);\n            };\n          }());\n          yield Promise.all(promises);\n          if (errors.length > 0) {\n            for (let i = 0; i < errors.length; i += 1) {\n              console.error(errors[i]);\n            }\n            throw errors[0];\n          }\n        } finally {\n          _this43._debug(debugName, 'end');\n        }\n      })();\n    }\n    /**\n     * set currentSession and currentUser\n     * process to _startAutoRefreshToken if possible\n     */\n    _saveSession(session) {\n      var _this44 = this;\n      return _asyncToGenerator(function* () {\n        _this44._debug('#_saveSession()', session);\n        // _saveSession is always called whenever a new session has been acquired\n        // so we can safely suppress the warning returned by future getSession calls\n        _this44.suppressGetSessionWarning = true;\n        yield setItemAsync(_this44.storage, _this44.storageKey, session);\n      })();\n    }\n    _removeSession() {\n      var _this45 = this;\n      return _asyncToGenerator(function* () {\n        _this45._debug('#_removeSession()');\n        yield removeItemAsync(_this45.storage, _this45.storageKey);\n        yield _this45._notifyAllSubscribers('SIGNED_OUT', null);\n      })();\n    }\n    /**\n     * Removes any registered visibilitychange callback.\n     *\n     * {@see #startAutoRefresh}\n     * {@see #stopAutoRefresh}\n     */\n    _removeVisibilityChangedCallback() {\n      this._debug('#_removeVisibilityChangedCallback()');\n      const callback = this.visibilityChangedCallback;\n      this.visibilityChangedCallback = null;\n      try {\n        if (callback && isBrowser() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n          window.removeEventListener('visibilitychange', callback);\n        }\n      } catch (e) {\n        console.error('removing visibilitychange callback failed', e);\n      }\n    }\n    /**\n     * This is the private implementation of {@link #startAutoRefresh}. Use this\n     * within the library.\n     */\n    _startAutoRefresh() {\n      var _this46 = this;\n      return _asyncToGenerator(function* () {\n        yield _this46._stopAutoRefresh();\n        _this46._debug('#_startAutoRefresh()');\n        const ticker = setInterval(() => _this46._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION_MS);\n        _this46.autoRefreshTicker = ticker;\n        if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n          // ticker is a NodeJS Timeout object that has an `unref` method\n          // https://nodejs.org/api/timers.html#timeoutunref\n          // When auto refresh is used in NodeJS (like for testing) the\n          // `setInterval` is preventing the process from being marked as\n          // finished and tests run endlessly. This can be prevented by calling\n          // `unref()` on the returned object.\n          ticker.unref();\n          // @ts-expect-error TS has no context of Deno\n        } else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n          // similar like for NodeJS, but with the Deno API\n          // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n          // @ts-expect-error TS has no context of Deno\n          Deno.unrefTimer(ticker);\n        }\n        // run the tick immediately, but in the next pass of the event loop so that\n        // #_initialize can be allowed to complete without recursively waiting on\n        // itself\n        setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n          yield _this46.initializePromise;\n          yield _this46._autoRefreshTokenTick();\n        }), 0);\n      })();\n    }\n    /**\n     * This is the private implementation of {@link #stopAutoRefresh}. Use this\n     * within the library.\n     */\n    _stopAutoRefresh() {\n      var _this47 = this;\n      return _asyncToGenerator(function* () {\n        _this47._debug('#_stopAutoRefresh()');\n        const ticker = _this47.autoRefreshTicker;\n        _this47.autoRefreshTicker = null;\n        if (ticker) {\n          clearInterval(ticker);\n        }\n      })();\n    }\n    /**\n     * Starts an auto-refresh process in the background. The session is checked\n     * every few seconds. Close to the time of expiration a process is started to\n     * refresh the session. If refreshing fails it will be retried for as long as\n     * necessary.\n     *\n     * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n     * to call this function, it will be called for you.\n     *\n     * On browsers the refresh process works only when the tab/window is in the\n     * foreground to conserve resources as well as prevent race conditions and\n     * flooding auth with requests. If you call this method any managed\n     * visibility change callback will be removed and you must manage visibility\n     * changes on your own.\n     *\n     * On non-browser platforms the refresh process works *continuously* in the\n     * background, which may not be desirable. You should hook into your\n     * platform's foreground indication mechanism and call these methods\n     * appropriately to conserve resources.\n     *\n     * {@see #stopAutoRefresh}\n     */\n    startAutoRefresh() {\n      var _this48 = this;\n      return _asyncToGenerator(function* () {\n        _this48._removeVisibilityChangedCallback();\n        yield _this48._startAutoRefresh();\n      })();\n    }\n    /**\n     * Stops an active auto refresh process running in the background (if any).\n     *\n     * If you call this method any managed visibility change callback will be\n     * removed and you must manage visibility changes on your own.\n     *\n     * See {@link #startAutoRefresh} for more details.\n     */\n    stopAutoRefresh() {\n      var _this49 = this;\n      return _asyncToGenerator(function* () {\n        _this49._removeVisibilityChangedCallback();\n        yield _this49._stopAutoRefresh();\n      })();\n    }\n    /**\n     * Runs the auto refresh token tick.\n     */\n    _autoRefreshTokenTick() {\n      var _this50 = this;\n      return _asyncToGenerator(function* () {\n        _this50._debug('#_autoRefreshTokenTick()', 'begin');\n        try {\n          yield _this50._acquireLock(0, /*#__PURE__*/_asyncToGenerator(function* () {\n            try {\n              const now = Date.now();\n              try {\n                return yield _this50._useSession(/*#__PURE__*/function () {\n                  var _ref32 = _asyncToGenerator(function* (result) {\n                    const {\n                      data: {\n                        session\n                      }\n                    } = result;\n                    if (!session || !session.refresh_token || !session.expires_at) {\n                      _this50._debug('#_autoRefreshTokenTick()', 'no session');\n                      return;\n                    }\n                    // session will expire in this many ticks (or has already expired if <= 0)\n                    const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION_MS);\n                    _this50._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n                    if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                      yield _this50._callRefreshToken(session.refresh_token);\n                    }\n                  });\n                  return function (_x16) {\n                    return _ref32.apply(this, arguments);\n                  };\n                }());\n              } catch (e) {\n                console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n              }\n            } finally {\n              _this50._debug('#_autoRefreshTokenTick()', 'end');\n            }\n          }));\n        } catch (e) {\n          if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n            _this50._debug('auto refresh token tick lock not available');\n          } else {\n            throw e;\n          }\n        }\n      })();\n    }\n    /**\n     * Registers callbacks on the browser / platform, which in-turn run\n     * algorithms when the browser window/tab are in foreground. On non-browser\n     * platforms it assumes always foreground.\n     */\n    _handleVisibilityChange() {\n      var _this51 = this;\n      return _asyncToGenerator(function* () {\n        _this51._debug('#_handleVisibilityChange()');\n        if (!isBrowser() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n          if (_this51.autoRefreshToken) {\n            // in non-browser environments the refresh token ticker runs always\n            _this51.startAutoRefresh();\n          }\n          return false;\n        }\n        try {\n          _this51.visibilityChangedCallback = /*#__PURE__*/_asyncToGenerator(function* () {\n            return yield _this51._onVisibilityChanged(false);\n          });\n          window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', _this51.visibilityChangedCallback);\n          // now immediately call the visbility changed callback to setup with the\n          // current visbility state\n          yield _this51._onVisibilityChanged(true); // initial call\n        } catch (error) {\n          console.error('_handleVisibilityChange', error);\n        }\n      })();\n    }\n    /**\n     * Callback registered with `window.addEventListener('visibilitychange')`.\n     */\n    _onVisibilityChanged(calledFromInitialize) {\n      var _this52 = this;\n      return _asyncToGenerator(function* () {\n        const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n        _this52._debug(methodName, 'visibilityState', document.visibilityState);\n        if (document.visibilityState === 'visible') {\n          if (_this52.autoRefreshToken) {\n            // in browser environments the refresh token ticker runs only on focused tabs\n            // which prevents race conditions\n            _this52._startAutoRefresh();\n          }\n          if (!calledFromInitialize) {\n            // called when the visibility has changed, i.e. the browser\n            // transitioned from hidden -> visible so we need to see if the session\n            // should be recovered immediately... but to do that we need to acquire\n            // the lock first asynchronously\n            yield _this52.initializePromise;\n            yield _this52._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n              if (document.visibilityState !== 'visible') {\n                _this52._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n                // visibility has changed while waiting for the lock, abort\n                return;\n              }\n              // recover the session\n              yield _this52._recoverAndRefresh();\n            }));\n          }\n        } else if (document.visibilityState === 'hidden') {\n          if (_this52.autoRefreshToken) {\n            _this52._stopAutoRefresh();\n          }\n        }\n      })();\n    }\n    /**\n     * Generates the relevant login URL for a third-party provider.\n     * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n     * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n     * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n     */\n    _getUrlForProvider(url, provider, options) {\n      var _this53 = this;\n      return _asyncToGenerator(function* () {\n        const urlParams = [`provider=${encodeURIComponent(provider)}`];\n        if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n          urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n        }\n        if (options === null || options === void 0 ? void 0 : options.scopes) {\n          urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n        }\n        if (_this53.flowType === 'pkce') {\n          const [codeChallenge, codeChallengeMethod] = yield getCodeChallengeAndMethod(_this53.storage, _this53.storageKey);\n          const flowParams = new URLSearchParams({\n            code_challenge: `${encodeURIComponent(codeChallenge)}`,\n            code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`\n          });\n          urlParams.push(flowParams.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.queryParams) {\n          const query = new URLSearchParams(options.queryParams);\n          urlParams.push(query.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n          urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n        }\n        return `${url}?${urlParams.join('&')}`;\n      })();\n    }\n    _unenroll(params) {\n      var _this54 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          return yield _this54._useSession(/*#__PURE__*/function () {\n            var _ref35 = _asyncToGenerator(function* (result) {\n              var _a;\n              const {\n                data: sessionData,\n                error: sessionError\n              } = result;\n              if (sessionError) {\n                return {\n                  data: null,\n                  error: sessionError\n                };\n              }\n              return yield _request(_this54.fetch, 'DELETE', `${_this54.url}/factors/${params.factorId}`, {\n                headers: _this54.headers,\n                jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n              });\n            });\n            return function (_x17) {\n              return _ref35.apply(this, arguments);\n            };\n          }());\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: null,\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    _enroll(params) {\n      var _this55 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          return yield _this55._useSession(/*#__PURE__*/function () {\n            var _ref36 = _asyncToGenerator(function* (result) {\n              var _a, _b;\n              const {\n                data: sessionData,\n                error: sessionError\n              } = result;\n              if (sessionError) {\n                return {\n                  data: null,\n                  error: sessionError\n                };\n              }\n              const body = Object.assign({\n                friendly_name: params.friendlyName,\n                factor_type: params.factorType\n              }, params.factorType === 'phone' ? {\n                phone: params.phone\n              } : {\n                issuer: params.issuer\n              });\n              const {\n                data,\n                error\n              } = yield _request(_this55.fetch, 'POST', `${_this55.url}/factors`, {\n                body,\n                headers: _this55.headers,\n                jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n              });\n              if (error) {\n                return {\n                  data: null,\n                  error\n                };\n              }\n              if (params.factorType === 'totp' && ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code)) {\n                data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n              }\n              return {\n                data,\n                error: null\n              };\n            });\n            return function (_x18) {\n              return _ref36.apply(this, arguments);\n            };\n          }());\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: null,\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n    /**\n     * {@see GoTrueMFAApi#verify}\n     */\n    _verify(params) {\n      var _this56 = this;\n      return _asyncToGenerator(function* () {\n        return _this56._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          try {\n            return yield _this56._useSession(/*#__PURE__*/function () {\n              var _ref38 = _asyncToGenerator(function* (result) {\n                var _a;\n                const {\n                  data: sessionData,\n                  error: sessionError\n                } = result;\n                if (sessionError) {\n                  return {\n                    data: null,\n                    error: sessionError\n                  };\n                }\n                const {\n                  data,\n                  error\n                } = yield _request(_this56.fetch, 'POST', `${_this56.url}/factors/${params.factorId}/verify`, {\n                  body: {\n                    code: params.code,\n                    challenge_id: params.challengeId\n                  },\n                  headers: _this56.headers,\n                  jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n                });\n                if (error) {\n                  return {\n                    data: null,\n                    error\n                  };\n                }\n                yield _this56._saveSession(Object.assign({\n                  expires_at: Math.round(Date.now() / 1000) + data.expires_in\n                }, data));\n                yield _this56._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n                return {\n                  data,\n                  error\n                };\n              });\n              return function (_x19) {\n                return _ref38.apply(this, arguments);\n              };\n            }());\n          } catch (error) {\n            if (isAuthError(error)) {\n              return {\n                data: null,\n                error\n              };\n            }\n            throw error;\n          }\n        }));\n      })();\n    }\n    /**\n     * {@see GoTrueMFAApi#challenge}\n     */\n    _challenge(params) {\n      var _this57 = this;\n      return _asyncToGenerator(function* () {\n        return _this57._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          try {\n            return yield _this57._useSession(/*#__PURE__*/function () {\n              var _ref40 = _asyncToGenerator(function* (result) {\n                var _a;\n                const {\n                  data: sessionData,\n                  error: sessionError\n                } = result;\n                if (sessionError) {\n                  return {\n                    data: null,\n                    error: sessionError\n                  };\n                }\n                return yield _request(_this57.fetch, 'POST', `${_this57.url}/factors/${params.factorId}/challenge`, {\n                  body: {\n                    channel: params.channel\n                  },\n                  headers: _this57.headers,\n                  jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n                });\n              });\n              return function (_x20) {\n                return _ref40.apply(this, arguments);\n              };\n            }());\n          } catch (error) {\n            if (isAuthError(error)) {\n              return {\n                data: null,\n                error\n              };\n            }\n            throw error;\n          }\n        }));\n      })();\n    }\n    /**\n     * {@see GoTrueMFAApi#challengeAndVerify}\n     */\n    _challengeAndVerify(params) {\n      var _this58 = this;\n      return _asyncToGenerator(function* () {\n        // both _challenge and _verify independently acquire the lock, so no need\n        // to acquire it here\n        const {\n          data: challengeData,\n          error: challengeError\n        } = yield _this58._challenge({\n          factorId: params.factorId\n        });\n        if (challengeError) {\n          return {\n            data: null,\n            error: challengeError\n          };\n        }\n        return yield _this58._verify({\n          factorId: params.factorId,\n          challengeId: challengeData.id,\n          code: params.code\n        });\n      })();\n    }\n    /**\n     * {@see GoTrueMFAApi#listFactors}\n     */\n    _listFactors() {\n      var _this59 = this;\n      return _asyncToGenerator(function* () {\n        // use #getUser instead of #_getUser as the former acquires a lock\n        const {\n          data: {\n            user\n          },\n          error: userError\n        } = yield _this59.getUser();\n        if (userError) {\n          return {\n            data: null,\n            error: userError\n          };\n        }\n        const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n        const totp = factors.filter(factor => factor.factor_type === 'totp' && factor.status === 'verified');\n        const phone = factors.filter(factor => factor.factor_type === 'phone' && factor.status === 'verified');\n        return {\n          data: {\n            all: factors,\n            totp,\n            phone\n          },\n          error: null\n        };\n      })();\n    }\n    /**\n     * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n     */\n    _getAuthenticatorAssuranceLevel() {\n      var _this60 = this;\n      return _asyncToGenerator(function* () {\n        return _this60._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return yield _this60._useSession(/*#__PURE__*/function () {\n            var _ref42 = _asyncToGenerator(function* (result) {\n              var _a, _b;\n              const {\n                data: {\n                  session\n                },\n                error: sessionError\n              } = result;\n              if (sessionError) {\n                return {\n                  data: null,\n                  error: sessionError\n                };\n              }\n              if (!session) {\n                return {\n                  data: {\n                    currentLevel: null,\n                    nextLevel: null,\n                    currentAuthenticationMethods: []\n                  },\n                  error: null\n                };\n              }\n              const {\n                payload\n              } = decodeJWT(session.access_token);\n              let currentLevel = null;\n              if (payload.aal) {\n                currentLevel = payload.aal;\n              }\n              let nextLevel = currentLevel;\n              const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter(factor => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n              if (verifiedFactors.length > 0) {\n                nextLevel = 'aal2';\n              }\n              const currentAuthenticationMethods = payload.amr || [];\n              return {\n                data: {\n                  currentLevel,\n                  nextLevel,\n                  currentAuthenticationMethods\n                },\n                error: null\n              };\n            });\n            return function (_x21) {\n              return _ref42.apply(this, arguments);\n            };\n          }());\n        }));\n      })();\n    }\n    fetchJwk(kid, jwks = {\n      keys: []\n    }) {\n      var _this61 = this;\n      return _asyncToGenerator(function* () {\n        // try fetching from the supplied jwks\n        let jwk = jwks.keys.find(key => key.kid === kid);\n        if (jwk) {\n          return jwk;\n        }\n        // try fetching from cache\n        jwk = _this61.jwks.keys.find(key => key.kid === kid);\n        // jwk exists and jwks isn't stale\n        if (jwk && _this61.jwks_cached_at + JWKS_TTL > Date.now()) {\n          return jwk;\n        }\n        // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n        const {\n          data,\n          error\n        } = yield _request(_this61.fetch, 'GET', `${_this61.url}/.well-known/jwks.json`, {\n          headers: _this61.headers\n        });\n        if (error) {\n          throw error;\n        }\n        if (!data.keys || data.keys.length === 0) {\n          throw new AuthInvalidJwtError('JWKS is empty');\n        }\n        _this61.jwks = data;\n        _this61.jwks_cached_at = Date.now();\n        // Find the signing key\n        jwk = data.keys.find(key => key.kid === kid);\n        if (!jwk) {\n          throw new AuthInvalidJwtError('No matching signing key found in JWKS');\n        }\n        return jwk;\n      })();\n    }\n    /**\n     * @experimental This method may change in future versions.\n     * @description Gets the claims from a JWT. If the JWT is symmetric JWTs, it will call getUser() to verify against the server. If the JWT is asymmetric, it will be verified against the JWKS using the WebCrypto API.\n     */\n    getClaims(jwt, jwks = {\n      keys: []\n    }) {\n      var _this62 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          let token = jwt;\n          if (!token) {\n            const {\n              data,\n              error\n            } = yield _this62.getSession();\n            if (error || !data.session) {\n              return {\n                data: null,\n                error\n              };\n            }\n            token = data.session.access_token;\n          }\n          const {\n            header,\n            payload,\n            signature,\n            raw: {\n              header: rawHeader,\n              payload: rawPayload\n            }\n          } = decodeJWT(token);\n          // Reject expired JWTs\n          validateExp(payload.exp);\n          // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n          if (!header.kid || header.alg === 'HS256' || !('crypto' in globalThis && 'subtle' in globalThis.crypto)) {\n            const {\n              error\n            } = yield _this62.getUser(token);\n            if (error) {\n              throw error;\n            }\n            // getUser succeeds so the claims in the JWT can be trusted\n            return {\n              data: {\n                claims: payload,\n                header,\n                signature\n              },\n              error: null\n            };\n          }\n          const algorithm = getAlgorithm(header.alg);\n          const signingKey = yield _this62.fetchJwk(header.kid, jwks);\n          // Convert JWK to CryptoKey\n          const publicKey = yield crypto.subtle.importKey('jwk', signingKey, algorithm, true, ['verify']);\n          // Verify the signature\n          const isValid = yield crypto.subtle.verify(algorithm, publicKey, signature, stringToUint8Array(`${rawHeader}.${rawPayload}`));\n          if (!isValid) {\n            throw new AuthInvalidJwtError('Invalid JWT signature');\n          }\n          // If verification succeeds, decode and return claims\n          return {\n            data: {\n              claims: payload,\n              header,\n              signature\n            },\n            error: null\n          };\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: null,\n              error\n            };\n          }\n          throw error;\n        }\n      })();\n    }\n  }\n  GoTrueClient.nextInstanceID = 0;\n  //# sourceMappingURL=GoTrueClient.js.map\n  return GoTrueClient;\n})();\nexport { GoTrueClient as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}