.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  padding-bottom: 80px;
}

header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  text-align: center;
}

.logo-wrap {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.logo {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
}

.logo img {
  height: 24px;
  margin-right: 8px;
}

h1 {
  font-size: 24px;
  margin: 0;
  padding: 10px 0;
}

.back-link {
  display: inline-block;
  margin-bottom: 20px;
  color: var(--ion-color-primary);
  text-decoration: none;
}

.settings-content {
  background-color: var(--ion-card-background);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-message {
  text-align: center;
  padding: 40px 0;
}

.test-message h2 {
  color: var(--ion-color-success);
  margin-bottom: 20px;
}

.test-message p {
  margin-bottom: 10px;
}
