export interface Friend {
  id?: string; // Firestore ID
  user_id: string;
  friend_id: string;
  created: Date;
}

export interface UserBadges {
  id?: string; // Firestore ID
  user_id: string;
  
  // Title badges
  badge_newbie: boolean;
  badge_warrior: boolean;
  badge_hardcore: boolean;
  badge_peak_performer: boolean;
  badge_indestructible: boolean;
  badge_professional: boolean;
  
  // Quest streak badges
  badge_streak_7_days: boolean;
  badge_streak_30_days: boolean;
  badge_streak_100_days: boolean;
  badge_streak_365_days: boolean;
  
  // Side quest streak badges
  badge_sidequest_streak_7_days: boolean;
  badge_sidequest_streak_30_days: boolean;
  badge_sidequest_streak_100_days: boolean;
  badge_sidequest_streak_365_days: boolean;
  
  // Friend badges
  badge_friends_5: boolean;
  badge_friends_10: boolean;
  
  // Category badges
  badge_strength_master: boolean;
  badge_money_master: boolean;
  badge_health_master: boolean;
  badge_knowledge_master: boolean;
  
  created_at: Date;
  updated_at: Date;
}
