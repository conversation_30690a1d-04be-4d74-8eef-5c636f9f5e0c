{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-49c88215.js';\nimport { r as raf } from './helpers-78efeec3.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-41a5d51b.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport { c as config, b as getIonMode } from './ionic-global-ca86cf32.js';\nimport { c as createAnimation } from './animation-eab5a4ca.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-864101a3.js';\nimport './framework-delegate-2eea1763.js';\nimport './gesture-controller-314a54f6.js';\nimport './index-738d7504.js';\n\n/**\n * iOS Loading Enter Animation\n */\nconst iosEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.01,\n    transform: 'scale(1.1)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: 'scale(1)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Loading Leave Animation\n */\nconst iosLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.99,\n    transform: 'scale(1)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.9)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Enter Animation\n */\nconst mdEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.01,\n    transform: 'scale(1.1)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: 'scale(1)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Leave Animation\n */\nconst mdLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.99,\n    transform: 'scale(1)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.9)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst loadingIosCss = \".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}\";\nconst IonLoadingIosStyle0 = loadingIosCss;\nconst loadingMdCss = \".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #0054e9);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}\";\nconst IonLoadingMdStyle0 = loadingMdCss;\nconst Loading = /*#__PURE__*/(() => {\n  let Loading = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.didPresent = createEvent(this, \"ionLoadingDidPresent\", 7);\n      this.willPresent = createEvent(this, \"ionLoadingWillPresent\", 7);\n      this.willDismiss = createEvent(this, \"ionLoadingWillDismiss\", 7);\n      this.didDismiss = createEvent(this, \"ionLoadingDidDismiss\", 7);\n      this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n      this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n      this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n      this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n      this.delegateController = createDelegateController(this);\n      this.lockController = createLockController();\n      this.triggerController = createTriggerController();\n      this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n      this.presented = false;\n      this.onBackdropTap = () => {\n        this.dismiss(undefined, BACKDROP);\n      };\n      this.overlayIndex = undefined;\n      this.delegate = undefined;\n      this.hasController = false;\n      this.keyboardClose = true;\n      this.enterAnimation = undefined;\n      this.leaveAnimation = undefined;\n      this.message = undefined;\n      this.cssClass = undefined;\n      this.duration = 0;\n      this.backdropDismiss = false;\n      this.showBackdrop = true;\n      this.spinner = undefined;\n      this.translucent = false;\n      this.animated = true;\n      this.htmlAttributes = undefined;\n      this.isOpen = false;\n      this.trigger = undefined;\n    }\n    onIsOpenChange(newValue, oldValue) {\n      if (newValue === true && oldValue === false) {\n        this.present();\n      } else if (newValue === false && oldValue === true) {\n        this.dismiss();\n      }\n    }\n    triggerChanged() {\n      const {\n        trigger,\n        el,\n        triggerController\n      } = this;\n      if (trigger) {\n        triggerController.addClickListener(el, trigger);\n      }\n    }\n    connectedCallback() {\n      prepareOverlay(this.el);\n      this.triggerChanged();\n    }\n    componentWillLoad() {\n      var _a;\n      if (this.spinner === undefined) {\n        const mode = getIonMode(this);\n        this.spinner = config.get('loadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n      }\n      if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n        setOverlayId(this.el);\n      }\n    }\n    componentDidLoad() {\n      /**\n       * If loading indicator was rendered with isOpen=\"true\"\n       * then we should open loading indicator immediately.\n       */\n      if (this.isOpen === true) {\n        raf(() => this.present());\n      }\n      /**\n       * When binding values in frameworks such as Angular\n       * it is possible for the value to be set after the Web Component\n       * initializes but before the value watcher is set up in Stencil.\n       * As a result, the watcher callback may not be fired.\n       * We work around this by manually calling the watcher\n       * callback when the component has loaded and the watcher\n       * is configured.\n       */\n      this.triggerChanged();\n    }\n    disconnectedCallback() {\n      this.triggerController.removeClickListener();\n    }\n    /**\n     * Present the loading overlay after it has been created.\n     */\n    present() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const unlock = yield _this.lockController.lock();\n        yield _this.delegateController.attachViewToDom();\n        yield present(_this, 'loadingEnter', iosEnterAnimation, mdEnterAnimation);\n        if (_this.duration > 0) {\n          _this.durationTimeout = setTimeout(() => _this.dismiss(), _this.duration + 10);\n        }\n        unlock();\n      })();\n    }\n    /**\n     * Dismiss the loading overlay after it has been presented.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the loading.\n     * This can be useful in a button handler for determining which button was\n     * clicked to dismiss the loading.\n     * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n     *\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     */\n    dismiss(data, role) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const unlock = yield _this2.lockController.lock();\n        if (_this2.durationTimeout) {\n          clearTimeout(_this2.durationTimeout);\n        }\n        const dismissed = yield dismiss(_this2, data, role, 'loadingLeave', iosLeaveAnimation, mdLeaveAnimation);\n        if (dismissed) {\n          _this2.delegateController.removeViewFromDom();\n        }\n        unlock();\n        return dismissed;\n      })();\n    }\n    /**\n     * Returns a promise that resolves when the loading did dismiss.\n     */\n    onDidDismiss() {\n      return eventMethod(this.el, 'ionLoadingDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the loading will dismiss.\n     */\n    onWillDismiss() {\n      return eventMethod(this.el, 'ionLoadingWillDismiss');\n    }\n    renderLoadingMessage(msgId) {\n      const {\n        customHTMLEnabled,\n        message\n      } = this;\n      if (customHTMLEnabled) {\n        return h(\"div\", {\n          class: \"loading-content\",\n          id: msgId,\n          innerHTML: sanitizeDOMString(message)\n        });\n      }\n      return h(\"div\", {\n        class: \"loading-content\",\n        id: msgId\n      }, message);\n    }\n    render() {\n      const {\n        message,\n        spinner,\n        htmlAttributes,\n        overlayIndex\n      } = this;\n      const mode = getIonMode(this);\n      const msgId = `loading-${overlayIndex}-msg`;\n      /**\n       * If the message is defined, use that as the label.\n       * Otherwise, don't set aria-labelledby.\n       */\n      const ariaLabelledBy = message !== undefined ? msgId : null;\n      return h(Host, Object.assign({\n        key: 'd6066c8b56b1fe4b597a243a7dab191ef0d21286',\n        role: \"dialog\",\n        \"aria-modal\": \"true\",\n        \"aria-labelledby\": ariaLabelledBy,\n        tabindex: \"-1\"\n      }, htmlAttributes, {\n        style: {\n          zIndex: `${40000 + this.overlayIndex}`\n        },\n        onIonBackdropTap: this.onBackdropTap,\n        class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), {\n          [mode]: true,\n          'overlay-hidden': true,\n          'loading-translucent': this.translucent\n        })\n      }), h(\"ion-backdrop\", {\n        key: '2431eda00a2a3f510f5dfc39b7c7d47c056dfa3d',\n        visible: this.showBackdrop,\n        tappable: this.backdropDismiss\n      }), h(\"div\", {\n        key: 'cf210aaf5e754e4eccdb49cf7ead4647b3f9b2d1',\n        tabindex: \"0\",\n        \"aria-hidden\": \"true\"\n      }), h(\"div\", {\n        key: 'fa9375143d391656d70e181d25b952c77c2fc6ec',\n        class: \"loading-wrapper ion-overlay-wrapper\"\n      }, spinner && h(\"div\", {\n        key: '8e4a4ed994f7f62df86b03696ac95162df41f52d',\n        class: \"loading-spinner\"\n      }, h(\"ion-spinner\", {\n        key: 'e5b323c272d365853ba92bd211e390b4fd4751d2',\n        name: spinner,\n        \"aria-hidden\": \"true\"\n      })), message !== undefined && this.renderLoadingMessage(msgId)), h(\"div\", {\n        key: 'cae35ec8c34800477bff3ebcec8010e632158233',\n        tabindex: \"0\",\n        \"aria-hidden\": \"true\"\n      }));\n    }\n    get el() {\n      return getElement(this);\n    }\n    static get watchers() {\n      return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n      };\n    }\n  };\n  Loading.style = {\n    ios: IonLoadingIosStyle0,\n    md: IonLoadingMdStyle0\n  };\n  return Loading;\n})();\nexport { Loading as ion_loading };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}