import { Component, inject, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router, RouterModule } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { Subscription } from 'rxjs';
import { SupabaseService } from '../../services/supabase.service';
import { AuthGuard } from '../../guards/auth.guard';

@Component({
  selector: 'app-signup',
  standalone: true,
  imports: [IonicModule, CommonModule, ReactiveFormsModule, RouterModule],
  templateUrl: './signup.component.html',
  styleUrls: ['signup.component.scss'],
})
export class SignupComponent implements OnDestroy {
  form: FormGroup;
  skipRoute: string = '/onboarding'; // Default route
  private fb = inject(FormBuilder);
  private supabaseService = inject(SupabaseService);
  private router = inject(Router);
  private authSubscription: Subscription | undefined;

  constructor() {
    this.form = this.fb.group({
      email: [''],
      password: [''],
      username: [''],
      name: [''],
      profile_picture: [null],
      level: [null],
      xp: [null],
      active: [false],
      affiliate_code_used: [null],
      plan: ['none'],
      start_of_current_plan: [null],
      end_of_current_plan: [null],
      auto_renew: [true],
      start_of_sick_days: [null],
      end_of_sick_days: [null],
      subscription_status: ['email marketing'],
    });

    // Determine the correct route for the "Skip for now" button
    this.updateSkipRoute();

    // Subscribe to auth state changes
    this.authSubscription = this.supabaseService.currentUser$.subscribe(user => {
      if (user) {
        this.checkUserData(user.id);
      }
    });
  }

  async updateSkipRoute() {
    try {
      const { value: onboarding } = await Preferences.get({ key: 'onboarding_complete' });
      console.log('Signup: Onboarding complete:', onboarding);

      // If onboarding is complete, route to pricing, otherwise to onboarding
      this.skipRoute = onboarding === 'true' ? '/pricing' : '/onboarding';
      console.log('Signup: Skip route set to:', this.skipRoute);
    } catch (error) {
      console.error('Error checking onboarding status:', error);
      // Default to onboarding if there's an error
      this.skipRoute = '/onboarding';
    }
  }

  async ionViewWillEnter() {
    // Update the skip route when the page is entered
    await this.updateSkipRoute();

    const { value: onboarding } = await Preferences.get({ key: 'onboarding_complete' });

    // Get current user from Supabase
    const user = this.supabaseService._currentUser.value;
    console.log('Signup: Current user:', user);

    if (user) {
      if (!onboarding) {
        this.router.navigateByUrl('/onboarding');
        return;
      }

      try {
        // Check user data in Supabase
        const { data: userData, error } = await this.supabaseService.getClient()
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Signup: Error fetching user data:', error);
          return;
        }

        console.log('Signup: User data:', userData);

        if (!userData) {
          // Create new user record if it doesn't exist
          console.log('Signup: No user data found, creating new user record');
          this.router.navigateByUrl('/signup');
          return;
        }

        // Check if user has a valid plan
        const endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
        const username = userData.username;
        console.log('Signup: End date:', endDate);

        if (endDate && endDate >= new Date()) {
          if (username) {
            console.log('Signup: User has valid plan and username, redirecting to home');
            this.router.navigateByUrl('/today');
          } else {
            console.log('Signup: User has valid plan but no username, redirecting to signup-step3');
            this.router.navigateByUrl('/signup-step3');
          }
        } else {
          console.log('Signup: User has no valid plan or plan has expired, redirecting to pricing');
          this.router.navigateByUrl('/pricing');
        }
      } catch (error) {
        console.error('Error checking user data:', error);
      }
    }
  }

  ngOnDestroy() {
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  async signInWithGoogle() {
    console.log('Signup: Attempting to sign in with Google');
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: '/'
        }
      });

      if (error) {
        console.error('Signup: Google sign in error:', error);
        alert('Error signing in with Google: ' + error.message);
        return;
      }

      console.log('Signup: Google sign in initiated:', data);
    } catch (error) {
      console.error('Signup: Google sign in exception:', error);
      alert('Error signing in with Google');
    }
  }

  async signInWithApple() {
    console.log('Signup: Attempting to sign in with Apple');
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: '/'
        }
      });

      if (error) {
        console.error('Signup: Apple sign in error:', error);
        alert('Error signing in with Apple: ' + error.message);
        return;
      }

      console.log('Signup: Apple sign in initiated:', data);
    } catch (error) {
      console.error('Signup: Apple sign in exception:', error);
      alert('Error signing in with Apple');
    }
  }

  private async checkUserData(userId: string) {
    console.log('Signup: Checking user data for:', userId);
    try {
      // First ensure profile exists
      await this.supabaseService.ensureProfileExists(this.supabaseService._currentUser.value!);

      // Then get the profile data
      const { data: userData, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Signup: Error fetching user data:', error);
        return;
      }

      console.log('Signup: User data:', userData);

      const { value: onboarding } = await Preferences.get({ key: 'onboarding_complete' });

      // Profile should always exist at this point
      if (!onboarding) {
        this.router.navigateByUrl('/onboarding');
      } else {
        // Update last login
        const { error: updateError } = await this.supabaseService.getClient()
          .from('profiles')
          .update({ last_login: new Date() })
          .eq('id', userId);

        if (updateError) {
          console.error('Signup: Error updating last login:', updateError);
        } else {
          console.log('Signup: Updated last login');
        }

        // Check if user has a valid plan
        const endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
        const username = userData.username;

        if (endDate && endDate >= new Date()) {
          if (username) {
            console.log('Signup: User has valid plan and username, checking for stored URL');
            // Check if we have a stored URL to redirect back to
            if (AuthGuard.lastAttemptedUrl) {
              console.log('Signup: Redirecting to stored URL:', AuthGuard.lastAttemptedUrl);
              const url = AuthGuard.lastAttemptedUrl;
              // Use the static method to clear the URL
              AuthGuard.clearLastAttemptedUrl();
              // Use a longer delay to ensure everything is loaded
              setTimeout(() => {
                console.log('Delayed redirect to:', url);
                // Check if the URL is still valid (not a public path)
                if (!AuthGuard.isPublicPath(url)) {
                  this.router.navigateByUrl(url);
                } else {
                  console.log('URL is a public path, redirecting to /today instead');
                  this.router.navigateByUrl('/today');
                }
              }, 1500);
            } else {
              console.log('Signup: No stored URL, redirecting to home');
              this.router.navigateByUrl('/today');
            }
          } else {
            console.log('Signup: User has valid plan but no username, redirecting to signup-step3');
            this.router.navigateByUrl('/signup-step3');
          }
        } else {
          console.log('Signup: User has no valid plan or plan has expired, redirecting to pricing');
          this.router.navigateByUrl('/pricing');
        }
      }
    } catch (error: any) {
      console.error('Signup: Error checking user data:', error);
    }
  }
}
