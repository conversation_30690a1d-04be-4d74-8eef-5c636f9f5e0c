ion-content {
  --background: var(--bg, #1a1b41);
}

.calculating-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}
.calculating-container h1 {
  font-size: 32px;
  font-weight: 600;
  margin: 24px 0 8px;
}
.calculating-container .subtitle {
  font-size: 16px;
  opacity: 0.7;
  margin: 0;
}

.progress-circle {
  width: 200px;
  height: 200px;
  margin-bottom: 20px;
}

.circular-chart {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  max-height: 100%;
  transform: rotate(-90deg);
}

.circle-bg {
  fill: none;
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 2.8;
}

@keyframes gradient {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -100;
  }
}
.circle {
  fill: none;
  stroke: url(#gradient);
  stroke-width: 2.8;
  stroke-linecap: round;
  transition: stroke-dasharray 0.1s ease;
}

.percentage {
  fill: white;
  font-family: sans-serif;
  font-size: 8px;
  text-anchor: middle;
  transform: rotate(90deg);
  transform-origin: center;
  font-weight: bold;
}/*# sourceMappingURL=calculating.page.css.map */