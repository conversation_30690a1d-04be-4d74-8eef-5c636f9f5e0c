/* Exact CSS from Django template */
:root {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --quest-bg: #1C1C1E;
  --quest-border: #2C2C2E;
  --active-date: #4169E1;
  --inactive-date: #2C2C2E;
  --card-bg: #1C1C1E;
  --border-color: #2C2C2E;
  --bg-color: #0C0C0F;
}

:host {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
  display: block;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.week-calendar {
  margin-bottom: 24px;
}

.calendar-nav {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.nav-arrow {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 20px;
  cursor: pointer;
  padding: 5px 10px;
}

.days {
  display: flex;
  justify-content: space-between;
  flex: 1;
}

.day-name {
  width: 14.28%;
  text-align: center;
  font-size: 12px;
  color: var(--secondary-text);
}

.dates {
  display: flex;
  justify-content: space-between;
}

.date {
  width: 14.28%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  position: relative;
}

.date.active {
  background-color: var(--active-date);
  color: white;
}

.date.selected {
  border: 2px solid var(--accent-color);
}

.date.disabled {
  color: var(--secondary-text);
  cursor: not-allowed;
}

.time-tracking {
  margin-bottom: 80px;
}

h2 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
}

.activity-input {
  margin-bottom: 20px;
}

select {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 16px;
  margin-bottom: 10px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
}

.time-input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 5px;
}

.time-inputs input {
  width: 60px;
  padding: 10px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 16px;
  text-align: center;
}

.time-inputs span {
  color: var(--secondary-text);
}

button {
  padding: 12px;
  border-radius: 8px;
  border: none;
  background-color: var(--accent-color);
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.custom-inline-form {
  display: none;
  flex-direction: column;
  gap: 10px;
}

.custom-row {
  display: flex;
  gap: 10px;
}

.custom-row input[type=text] {
  padding: 10px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 16px;
}

.custom-row input[name=emoji] {
  width: 60px;
  text-align: center;
  font-size: 20px;
}

.custom-row input#customActivityName {
  flex: 1;
}

.time-visualization {
  margin-bottom: 20px;
}

canvas {
  width: 100%;
  height: 250px;
  margin-bottom: 10px;
}

.time-summary {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: var(--secondary-text);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.activity-item {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
}

.activity-icon {
  font-size: 24px;
  margin-right: 15px;
}

.activity-info {
  flex-grow: 1;
}

.activity-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.delete-activity {
  background: none;
  border: none;
  color: #FF3B30;
  font-size: 24px;
  cursor: pointer;
  padding: 0 5px;
}

/* Navigation Styles */
.main-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #121212;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  z-index: 1000;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #888;
  padding: 5px 0;
  transition: color 0.2s ease;
  width: 20%;
}

.nav-item:hover {
  color: #fff;
}

.nav-item.active {
  color: #4D7BFF;
  position: relative;
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: #4D7BFF;
  border-radius: 50%;
}

.nav-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
}

/* Adjust container padding to account for navigation bar */
.container {
  padding-bottom: 120px !important;
}/*# sourceMappingURL=time-tracker.page.css.map */