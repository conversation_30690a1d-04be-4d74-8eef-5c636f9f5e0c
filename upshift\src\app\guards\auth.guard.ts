
import { Injectable, EnvironmentInjector, runInInjectionContext, inject } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router, NavigationEnd } from '@angular/router';
import { Observable, map, of, switchMap, from, catchError, firstValueFrom, take, timeout, filter } from 'rxjs';
import { User } from '../models/user.model';
import { SupabaseService } from '../services/supabase.service';
import { UserService } from '../services/user.service';
import { PreferencesService } from '../services/preferences.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);
  private injector = inject(EnvironmentInjector);
  private preferencesService = inject(PreferencesService);

  constructor() {
    // Initialize lastAttemptedUrl from localStorage if available
    try {
      const storedUrl = localStorage.getItem('lastAttemptedUrl');
      if (storedUrl && !AuthGuard.lastAttemptedUrl) {
        console.log('AuthGuard: Restored lastAttemptedUrl from localStorage:', storedUrl);
        AuthGuard.lastAttemptedUrl = storedUrl;
      }
    } catch (error) {
      console.error('AuthGuard: Failed to restore URL from localStorage:', error);
    }

    // Subscribe to router events to clear lastAttemptedUrl when navigating to the stored URL
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      if (AuthGuard.lastAttemptedUrl && event.url === AuthGuard.lastAttemptedUrl) {
        console.log('AuthGuard: Navigated to stored URL, clearing lastAttemptedUrl');
        AuthGuard.clearLastAttemptedUrl();
      }

      // We now handle this in the AppComponent
    });
  }

  // Store the last attempted URL to redirect back after authentication
  public static lastAttemptedUrl: string | null = null;

  // Static initializer not supported in some browsers, so we'll initialize in the constructor

  // Static method to check if user is already authenticated (for synchronous checks)
  public static isAuthenticated(supabaseService: SupabaseService): boolean {
    const isAuth = !!supabaseService._currentUser.value;
    console.log('AuthGuard.isAuthenticated:', isAuth);
    return isAuth;
  }

  // Static method to clear the last attempted URL
  public static clearLastAttemptedUrl(): void {
    if (AuthGuard.lastAttemptedUrl) {
      console.log('AuthGuard: Clearing lastAttemptedUrl:', AuthGuard.lastAttemptedUrl);
      AuthGuard.lastAttemptedUrl = null;

      // Also clear from localStorage
      try {
        localStorage.removeItem('lastAttemptedUrl');
        console.log('AuthGuard: Also cleared URL from localStorage');
      } catch (error) {
        console.error('AuthGuard: Failed to clear URL from localStorage:', error);
      }

      // Add a small delay to ensure the URL is cleared before any new navigation
      setTimeout(() => {
        if (AuthGuard.lastAttemptedUrl === null) {
          console.log('AuthGuard: lastAttemptedUrl successfully cleared');
        }
      }, 100);
    }
  }

  // Static method to check if a URL is a public path
  public static isPublicPath(url: string): boolean {
    // If URL is null or empty, consider it public
    if (!url) return true;

    const publicPaths = [
      '/signup',
      '/login',
      '/register',
      '/onboarding',
      '/pricing',
      '/',
      '/redirect',
      '/assets',
      '/callback',
      '/auth-redirect',
      '/auth',
      '/transition'
    ];
    return publicPaths.some(path => url.startsWith(path));
  }

  // Static method to store the current URL
  public static storeCurrentUrl(url: string): void {
    // If URL is null or empty, don't store it
    if (!url) return;

    // Don't store the URL if it's a public path or if we already have a stored URL
    if (!AuthGuard.isPublicPath(url) && !AuthGuard.lastAttemptedUrl) {
      // Store the URL with any query parameters
      AuthGuard.lastAttemptedUrl = url;
      console.log('AuthGuard: Storing URL:', AuthGuard.lastAttemptedUrl);

      // Store the URL in localStorage as a backup
      try {
        localStorage.setItem('lastAttemptedUrl', url);
        console.log('AuthGuard: Also stored URL in localStorage');
      } catch (error) {
        console.error('AuthGuard: Failed to store URL in localStorage:', error);
      }
    }
  }

  async canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean> {
    console.log('AuthGuard.canActivate called for route:', route.routeConfig?.path);
    console.log('AuthGuard: Full URL:', state.url);

    // Store the current URL if it's not a public path and not already stored
    AuthGuard.storeCurrentUrl(state.url);

    // Log the current URL and stored URL for debugging
    console.log('AuthGuard: Current URL:', state.url);
    console.log('AuthGuard: Stored URL:', AuthGuard.lastAttemptedUrl);

    // Check if we already have a user in memory (synchronous check first)
    const cachedUser = this.supabaseService._currentUser.value;
    if (cachedUser) {
      console.log('AuthGuard: User already in memory, proceeding with validation');
      return this.validateUser(cachedUser, route);
    }

    // If we're on a page that doesn't require authentication, allow access
    if (AuthGuard.isPublicPath(state.url)) {
      console.log('AuthGuard: Public path, allowing access without authentication');
      return true;
    }

    // We already checked for public paths above, so we don't need these checks anymore

    try {
      // Wait for authentication state to be loaded (max 10 seconds)
      const authUser = await firstValueFrom(this.supabaseService.currentUser$.pipe(
        take(1),
        timeout(10000), // Add a longer timeout to prevent infinite waiting
        catchError(() => {
          console.log('AuthGuard: Timeout waiting for auth state, assuming not logged in');
          return of(null);
        }) // Return null if timeout occurs
      ));

      console.log('AuthGuard: User auth state:', authUser ? 'logged in' : 'not logged in');

      // If no user is authenticated, store the URL and redirect to signup
      if (!authUser) {
        console.log('AuthGuard: User not logged in, redirecting to /signup');
        // Store the current URL if it's not already stored and not a public path
        AuthGuard.storeCurrentUrl(state.url);
        console.log('AuthGuard: After storing URL in redirect case:', AuthGuard.lastAttemptedUrl);
        // Use a small delay to ensure the URL is stored before redirecting
        setTimeout(() => {
          console.log('AuthGuard: Delayed redirect to /signup');
          this.router.navigateByUrl('/signup');
        }, 100);
        return false;
      }

      return this.validateUser(authUser, route);
    } catch (error) {
      console.error('AuthGuard error:', error);
      // Store the current URL if it's not already stored and not a public path
      AuthGuard.storeCurrentUrl(state.url);
      console.log('AuthGuard: After storing URL in error case:', AuthGuard.lastAttemptedUrl);
      // Use a small delay to ensure the URL is stored before redirecting
      setTimeout(() => {
        console.log('AuthGuard: Delayed redirect to /signup after error');
        this.router.navigateByUrl('/signup');
      }, 100);
      return false;
    }
  }

  // Separate method to validate the user once we have the auth user
  private async validateUser(authUser: any, route: ActivatedRouteSnapshot): Promise<boolean> {
    try {
      // Check if onboarding is complete
      try {
        const { value: onboarding } = await this.preferencesService.get('onboarding_complete');
        if (!onboarding) {
          console.log('AuthGuard: Onboarding not complete, redirecting to /onboarding');
          // Don't store the URL for onboarding redirects - we want to go to today after onboarding
          // Use a small delay to ensure any previous navigation has completed
          setTimeout(() => {
            console.log('AuthGuard: Delayed redirect to /onboarding');
            this.router.navigateByUrl('/onboarding');
          }, 100);
          return false;
        }
      } catch (error) {
        console.log('AuthGuard: Error checking onboarding status, redirecting to /onboarding');
        // Don't store the URL for onboarding redirects - we want to go to today after onboarding
        // Use a small delay to ensure any previous navigation has completed
        setTimeout(() => {
          console.log('AuthGuard: Delayed redirect to /onboarding after error');
          this.router.navigateByUrl('/onboarding');
        }, 100);
        return false;
      }

      // Check if user has a valid profile
      const userData = await firstValueFrom(this.userService.getUserById(authUser.id).pipe(
        take(1),
        catchError(err => {
          console.error('AuthGuard: Error getting user data:', err);
          return of(null);
        })
      ));

      if (!userData) {
        console.log('AuthGuard: No user data found, redirecting to /signup');
        // URL is already stored in canActivate
        // Use a small delay to ensure any previous navigation has completed
        setTimeout(() => {
          console.log('AuthGuard: Delayed redirect to /signup due to missing user data');
          this.router.navigateByUrl('/signup');
        }, 100);
        return false;
      }

      // Check if user has a username
      if (!userData.username) {
        console.log('AuthGuard: User has no username, redirecting to /register');
        // Store the URL for register redirects - we want to go back to the original URL after registration
        const state = this.router.getCurrentNavigation()?.extractedUrl.toString() || '';
        if (state) {
          AuthGuard.storeCurrentUrl(state);
          console.log('AuthGuard: After storing URL in username case:', AuthGuard.lastAttemptedUrl);
        }
        // Use a small delay to ensure any previous navigation has completed
        setTimeout(() => {
          console.log('AuthGuard: Delayed redirect to /register');
          this.router.navigateByUrl('/register');
        }, 100);
        return false;
      }

      // Get end date
      let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
      const currentDate = new Date();
      console.log('AuthGuard: End date:', endDate);
      console.log('AuthGuard: Current date:', currentDate);

      // Compare dates properly
      let isValidPlan = false;
      if (endDate instanceof Date) {
        isValidPlan = endDate > currentDate;
      }

      // Get the current route path
      const currentPath = route.routeConfig?.path || '';
      console.log('AuthGuard: Current route path:', currentPath);

      // Allow access to today page even if plan has expired
      if (!isValidPlan && currentPath !== 'today') {
        console.log('AuthGuard: User plan has expired, redirecting to /pricing');
        // Store the URL for pricing redirects - we want to go back to the original URL after purchasing
        const state = this.router.getCurrentNavigation()?.extractedUrl.toString() || '';
        if (state) {
          AuthGuard.storeCurrentUrl(state);
          console.log('AuthGuard: After storing URL in pricing case:', AuthGuard.lastAttemptedUrl);
        }
        // Use a small delay to ensure any previous navigation has completed
        setTimeout(() => {
          console.log('AuthGuard: Delayed redirect to /pricing');
          this.router.navigateByUrl('/pricing');
        }, 100);
        return false;
      }

      // All checks passed, allow access
      console.log('AuthGuard: User is valid, allowing access');
      return true;
    } catch (error) {
      console.error('AuthGuard validation error:', error);
      // We don't need to store the URL here since it's already stored in the canActivate method
      // Use a small delay to ensure any previous navigation has completed
      setTimeout(() => {
        console.log('AuthGuard: Delayed redirect to /signup after validation error');
        this.router.navigateByUrl('/signup');
      }, 100);
      return false;
    }
  }
}
