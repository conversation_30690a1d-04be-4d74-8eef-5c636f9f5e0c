// Group sidequest methods to add to import-sidequests.page.ts

// Fetch existing group side quests from Supabase
async fetchExistingGroupSideQuests() {
  this.importStatus = 'Fetching existing group side quests from Supabase...';

  try {
    const { data, error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .select('*')
      .order('id', { ascending: true });

    if (error) {
      console.error('Error fetching existing group side quests:', error);
      this.importStatus = `Error fetching existing group side quests: ${error.message}`;
      return;
    }

    if (data && data.length > 0) {
      this.existingGroupSideQuests = data;
      console.log(`Found ${data.length} existing group side quests in Supabase`);
      this.importStatus = `Found ${data.length} existing group side quests in Supabase.`;
    } else {
      console.log('No existing group side quests found in Supabase');
      this.importStatus = 'No existing group side quests found in Supabase.';
    }
  } catch (error: any) {
    console.error('Error fetching existing group side quests:', error);
    this.importStatus = `Error fetching existing group side quests: ${error.message}`;
  }
}

// Toggle group JSON import section
toggleGroupJsonImport() {
  this.showGroupJsonImport = !this.showGroupJsonImport;
  if (this.showGroupJsonImport) {
    this.fetchJsonGroupSideQuests();
  }
}

// Toggle group add form
toggleGroupAddForm() {
  this.showGroupAddForm = !this.showGroupAddForm;
}

// Fetch group side quests from JSON file
fetchJsonGroupSideQuests() {
  this.importStatus = 'Fetching group side quests from JSON...';

  this.http.get<GroupSideQuestPool[]>('assets/data/group-sidequest-pool.json').subscribe({
    next: (data) => {
      if (!data || data.length === 0) {
        this.importStatus = 'No group side quests found in JSON file';
        return;
      }

      this.jsonGroupSideQuests = data;
      this.importStatus = `Found ${data.length} group side quests in JSON file. Ready to import.`;
    },
    error: (error) => {
      this.importStatus = `Error fetching group side quests JSON: ${error.message}`;
    }
  });
}

// Import group side quests from JSON to Supabase
async importJsonGroupSideQuests() {
  if (this.jsonGroupSideQuests.length === 0) {
    this.importStatus = 'No group side quests to import from JSON';
    return;
  }

  this.isImporting = true;
  this.importStatus = 'Importing group side quests from JSON...';

  try {
    // Prepare group side quests with active flag and remove any custom IDs
    const groupSideQuestsToImport = this.jsonGroupSideQuests.map(quest => {
      // Create a new object without the id field
      const { id, ...questWithoutId } = quest;
      return {
        ...questWithoutId,
        active: true
      };
    });

    console.log('Group side quests prepared for import:', groupSideQuestsToImport);

    // Insert into Supabase
    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .insert(groupSideQuestsToImport);

    if (error) {
      console.error('Error importing group side quests:', error);
      this.importStatus = `Error importing group side quests: ${error.message}`;
      this.isImporting = false;
      return;
    }

    console.log('Group side quests imported successfully');
    this.importStatus = 'Group side quests imported successfully';
    this.isImporting = false;

    // Refresh the list of existing group side quests
    this.fetchExistingGroupSideQuests();
  } catch (error: any) {
    console.error('Error importing group side quests:', error);
    this.importStatus = `Error importing group side quests: ${error.message}`;
    this.isImporting = false;
  }
}

// Add a new group side quest
async addGroupSideQuest() {
  if (!this.groupSideQuestForm.valid) {
    this.importStatus = 'Please fill out all required fields correctly';
    return;
  }

  this.isImporting = true;
  this.importStatus = 'Adding new group side quest...';

  try {
    const newGroupSideQuest = {
      ...this.groupSideQuestForm.value,
      active: true
    };

    console.log('New group side quest to add:', newGroupSideQuest);

    // Insert into Supabase
    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .insert([newGroupSideQuest]);

    if (error) {
      console.error('Error adding group side quest:', error);
      this.importStatus = `Error adding group side quest: ${error.message}`;
      this.isImporting = false;
      return;
    }

    console.log('Group side quest added successfully');
    this.importStatus = 'Group side quest added successfully';

    // Reset the form
    this.resetGroupForm();

    // Refresh the list of existing group side quests
    this.fetchExistingGroupSideQuests();
  } catch (error: any) {
    console.error('Error adding group side quest:', error);
    this.importStatus = `Error adding group side quest: ${error.message}`;
  } finally {
    this.isImporting = false;
  }
}

// Toggle active status of a group side quest
async toggleGroupSideQuestActive(quest: GroupSideQuestPool) {
  try {
    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .update({ active: !quest.active })
      .eq('id', quest.id);

    if (error) {
      console.error('Error updating group side quest:', error);
      this.importStatus = `Error updating group side quest: ${error.message}`;
      return;
    }

    console.log('Group side quest updated successfully');
    this.importStatus = 'Group side quest updated successfully';

    // Update the local state
    quest.active = !quest.active;
  } catch (error: any) {
    console.error('Error updating group side quest:', error);
    this.importStatus = `Error updating group side quest: ${error.message}`;
  }
}

// Delete a group side quest
async deleteGroupSideQuest(questId: string) {
  if (!questId) {
    this.importStatus = 'Cannot delete quest: Invalid quest ID';
    return;
  }

  if (!confirm('Are you sure you want to delete this group side quest?')) {
    return;
  }

  try {
    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .delete()
      .eq('id', questId);

    if (error) {
      console.error('Error deleting group side quest:', error);
      this.importStatus = `Error deleting group side quest: ${error.message}`;
      return;
    }

    console.log('Group side quest deleted successfully');
    this.importStatus = 'Group side quest deleted successfully';

    // Refresh the list of existing group side quests
    this.fetchExistingGroupSideQuests();
  } catch (error: any) {
    console.error('Error deleting group side quest:', error);
    this.importStatus = `Error deleting group side quest: ${error.message}`;
  }
}
