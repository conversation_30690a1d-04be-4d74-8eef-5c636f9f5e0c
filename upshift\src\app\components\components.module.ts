import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { GroupSideQuestComponent } from './group-sidequest/group-sidequest.component';

@NgModule({
  declarations: [
    // No declarations needed for standalone components
  ],
  imports: [
    IonicModule,
    CommonModule,
    IonicModule,
    FormsModule,
    RouterModule,
    // Import standalone components
    GroupSideQuestComponent
  ],
  exports: [
    // Export standalone components
    GroupSideQuestComponent
  ]
})
export class ComponentsModule { }
