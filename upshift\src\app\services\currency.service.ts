import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CurrencyService {
  private userCurrency: string = 'USD'; // Default currency
  private currencySymbols: { [key: string]: string } = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CAD': 'C$',
    'AUD': 'A$',
    'CHF': 'CHF',
    'CNY': '¥',
    'INR': '₹',
    'BRL': 'R$'
  };

  constructor() {
    this.detectUserCurrency();
  }

  /**
   * Detect user's currency based on their locale
   */
  private detectUserCurrency(): void {
    try {
      // Try to get currency from browser's locale
      const locale = navigator.language;
      
      // Simple mapping of common locales to currencies
      const localeToCurrency: { [key: string]: string } = {
        'en-US': 'USD',
        'en-GB': 'GBP',
        'en-CA': 'CAD',
        'en-AU': 'AUD',
        'fr-FR': 'EUR',
        'de-DE': 'EUR',
        'it-IT': 'EUR',
        'es-ES': 'EUR',
        'ja-JP': 'JPY',
        'zh-CN': 'CNY',
        'ru-RU': 'RUB',
        'pt-BR': 'BRL',
        'hi-IN': 'INR'
      };
      
      // Set currency based on locale, or default to USD
      if (locale in localeToCurrency) {
        this.userCurrency = localeToCurrency[locale];
      } else if (locale.split('-')[0] === 'en') {
        this.userCurrency = 'USD';
      } else if (locale.split('-')[0] === 'fr' || 
                locale.split('-')[0] === 'de' || 
                locale.split('-')[0] === 'it' || 
                locale.split('-')[0] === 'es') {
        this.userCurrency = 'EUR';
      }
    } catch (error) {
      console.error('Error detecting user currency:', error);
      // Default to USD if there's an error
      this.userCurrency = 'USD';
    }
  }

  /**
   * Format a price value with the user's currency
   * @param value The price value to format
   * @param showCode Whether to show the currency code (e.g., USD) instead of symbol
   * @returns Formatted price string
   */
  formatPrice(value: number, showCode: boolean = false): string {
    if (!value && value !== 0) return '';
    
    try {
      if (showCode) {
        return `${value.toFixed(2)} ${this.userCurrency}`;
      } else {
        const symbol = this.currencySymbols[this.userCurrency] || '$';
        return `${symbol}${value.toFixed(2)}`;
      }
    } catch (error) {
      console.error('Error formatting price:', error);
      return `$${value}`;
    }
  }

  /**
   * Get the user's detected currency
   */
  getUserCurrency(): string {
    return this.userCurrency;
  }

  /**
   * Set the user's currency manually
   * @param currency Currency code (e.g., 'USD', 'EUR')
   */
  setUserCurrency(currency: string): void {
    if (currency in this.currencySymbols) {
      this.userCurrency = currency;
    } else {
      console.warn(`Unsupported currency: ${currency}, defaulting to USD`);
      this.userCurrency = 'USD';
    }
  }
}
