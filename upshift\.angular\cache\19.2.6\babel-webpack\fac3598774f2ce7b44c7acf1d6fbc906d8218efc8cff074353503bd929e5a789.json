{"ast": null, "code": "\"use strict\";\n\nvar _asyncToGenerator = require(\"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/asyncToGenerator.js\").default;\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n// @ts-ignore\nconst node_fetch_1 = __importDefault(require(\"@supabase/node-fetch\"));\nconst PostgrestError_1 = __importDefault(require(\"./PostgrestError\"));\nclass PostgrestBuilder {\n  constructor(builder) {\n    this.shouldThrowOnError = false;\n    this.method = builder.method;\n    this.url = builder.url;\n    this.headers = builder.headers;\n    this.schema = builder.schema;\n    this.body = builder.body;\n    this.shouldThrowOnError = builder.shouldThrowOnError;\n    this.signal = builder.signal;\n    this.isMaybeSingle = builder.isMaybeSingle;\n    if (builder.fetch) {\n      this.fetch = builder.fetch;\n    } else if (typeof fetch === 'undefined') {\n      this.fetch = node_fetch_1.default;\n    } else {\n      this.fetch = fetch;\n    }\n  }\n  /**\n   * If there's an error with the query, throwOnError will reject the promise by\n   * throwing the error instead of returning it as part of a successful response.\n   *\n   * {@link https://github.com/supabase/supabase-js/issues/92}\n   */\n  throwOnError() {\n    this.shouldThrowOnError = true;\n    return this;\n  }\n  /**\n   * Set an HTTP header for the request.\n   */\n  setHeader(name, value) {\n    this.headers = Object.assign({}, this.headers);\n    this.headers[name] = value;\n    return this;\n  }\n  then(onfulfilled, onrejected) {\n    var _this = this;\n    // https://postgrest.org/en/stable/api.html#switching-schemas\n    if (this.schema === undefined) {\n      // skip\n    } else if (['GET', 'HEAD'].includes(this.method)) {\n      this.headers['Accept-Profile'] = this.schema;\n    } else {\n      this.headers['Content-Profile'] = this.schema;\n    }\n    if (this.method !== 'GET' && this.method !== 'HEAD') {\n      this.headers['Content-Type'] = 'application/json';\n    }\n    // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n    // https://github.com/supabase/postgrest-js/pull/247\n    const _fetch = this.fetch;\n    let res = _fetch(this.url.toString(), {\n      method: this.method,\n      headers: this.headers,\n      body: JSON.stringify(this.body),\n      signal: this.signal\n    }).then(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (res) {\n        var _a, _b, _c;\n        let error = null;\n        let data = null;\n        let count = null;\n        let status = res.status;\n        let statusText = res.statusText;\n        if (res.ok) {\n          if (_this.method !== 'HEAD') {\n            const body = yield res.text();\n            if (body === '') {\n              // Prefer: return=minimal\n            } else if (_this.headers['Accept'] === 'text/csv') {\n              data = body;\n            } else if (_this.headers['Accept'] && _this.headers['Accept'].includes('application/vnd.pgrst.plan+text')) {\n              data = body;\n            } else {\n              data = JSON.parse(body);\n            }\n          }\n          const countHeader = (_a = _this.headers['Prefer']) === null || _a === void 0 ? void 0 : _a.match(/count=(exact|planned|estimated)/);\n          const contentRange = (_b = res.headers.get('content-range')) === null || _b === void 0 ? void 0 : _b.split('/');\n          if (countHeader && contentRange && contentRange.length > 1) {\n            count = parseInt(contentRange[1]);\n          }\n          // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n          // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n          if (_this.isMaybeSingle && _this.method === 'GET' && Array.isArray(data)) {\n            if (data.length > 1) {\n              error = {\n                // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n                code: 'PGRST116',\n                details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n                hint: null,\n                message: 'JSON object requested, multiple (or no) rows returned'\n              };\n              data = null;\n              count = null;\n              status = 406;\n              statusText = 'Not Acceptable';\n            } else if (data.length === 1) {\n              data = data[0];\n            } else {\n              data = null;\n            }\n          }\n        } else {\n          const body = yield res.text();\n          try {\n            error = JSON.parse(body);\n            // Workaround for https://github.com/supabase/postgrest-js/issues/295\n            if (Array.isArray(error) && res.status === 404) {\n              data = [];\n              error = null;\n              status = 200;\n              statusText = 'OK';\n            }\n          } catch (_d) {\n            // Workaround for https://github.com/supabase/postgrest-js/issues/295\n            if (res.status === 404 && body === '') {\n              status = 204;\n              statusText = 'No Content';\n            } else {\n              error = {\n                message: body\n              };\n            }\n          }\n          if (error && _this.isMaybeSingle && ((_c = error === null || error === void 0 ? void 0 : error.details) === null || _c === void 0 ? void 0 : _c.includes('0 rows'))) {\n            error = null;\n            status = 200;\n            statusText = 'OK';\n          }\n          if (error && _this.shouldThrowOnError) {\n            throw new PostgrestError_1.default(error);\n          }\n        }\n        const postgrestResponse = {\n          error,\n          data,\n          count,\n          status,\n          statusText\n        };\n        return postgrestResponse;\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    if (!this.shouldThrowOnError) {\n      res = res.catch(fetchError => {\n        var _a, _b, _c;\n        return {\n          error: {\n            message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : 'FetchError'}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n            details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : ''}`,\n            hint: '',\n            code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : ''}`\n          },\n          data: null,\n          count: null,\n          status: 0,\n          statusText: ''\n        };\n      });\n    }\n    return res.then(onfulfilled, onrejected);\n  }\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n   */\n  returns() {\n    /* istanbul ignore next */\n    return this;\n  }\n  /**\n   * Override the type of the returned `data` field in the response.\n   *\n   * @typeParam NewResult - The new type to cast the response data to\n   * @typeParam Options - Optional type configuration (defaults to { merge: true })\n   * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n   * @example\n   * ```typescript\n   * // Merge with existing types (default behavior)\n   * const query = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ custom_field: string }>()\n   *\n   * // Replace existing types completely\n   * const replaceQuery = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n   * ```\n   * @returns A PostgrestBuilder instance with the new type\n   */\n  overrideTypes() {\n    return this;\n  }\n}\nexports.default = PostgrestBuilder;\n//# sourceMappingURL=PostgrestBuilder.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}