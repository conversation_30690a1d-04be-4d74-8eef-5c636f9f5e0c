:host {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --card-bg: #1C1C1E;
  --card-border: #2C2C2E;
  /* Badge colors */
  --title-badge-color: #FF9500;
  --streak-badge-color: #FF3B30;
  --sidequest-badge-color: #5856D6;
  --friend-badge-color: #34C759;
  --strength-badge-color: #FF9500;
  --money-badge-color: #30D158;
  --health-badge-color: #FF2D55;
  --knowledge-badge-color: #5E5CE6;
  display: block;
  min-height: 100vh;
  background-color: var(--background-color);
}

ion-content {
  --background: var(--background-color);
  --color: var(--text-color);
  --padding-bottom: 100px;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
  color: var(--text-color);
  padding-bottom: 100px; /* Space for navigation */
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.badges-container {
  margin-bottom: 100px; /* Space for navigation */
  overflow-y: auto;
}

.back-button {
  display: inline-block;
  margin-bottom: 20px;
  padding: 8px 16px;
  background-color: transparent;
  border: 1px solid var(--card-border);
  border-radius: 6px;
  color: var(--text-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.badge-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--card-border);
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.badge-item {
  background-color: var(--card-bg);
  border-radius: 12px;
  padding: 16px;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--card-border);
  transition: transform 0.2s, box-shadow 0.2s;
}

.badge-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.badge-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.badge-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.badge-description {
  font-size: 14px;
  color: var(--secondary-text);
}

.locked {
  opacity: 0.6;
}

.locked-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 20px;
}

/* Badge type specific styles */
.title-badge .badge-icon {
  color: var(--title-badge-color);
}

.streak-badge .badge-icon {
  color: var(--streak-badge-color);
}

.sidequest-badge .badge-icon {
  color: var(--sidequest-badge-color);
}

.friend-badge .badge-icon {
  color: var(--friend-badge-color);
}

/* Loading state */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--card-border);
  border-top: 4px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Responsive adjustments */
@media (max-width: 480px) {
  .badges-grid {
    grid-template-columns: 1fr;
  }
}/*# sourceMappingURL=user-badges.page.css.map */