import { Injectable, inject } from '@angular/core';
import { Activity, ActivityType, DayTracking } from '../models/activity.model';
import { Observable, from, of, map, catchError } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class ActivityService {
  private supabaseService = inject(SupabaseService);

  constructor() {}

  // Activity Type operations
  getActivityTypes(): Observable<ActivityType[]> {
    console.log('ActivityService: Getting activity types');

    return from(
      this.supabaseService.getClient()
        .from('activity_types')
        .select('*')
        .order('order')
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('ActivityService: Error getting activity types:', response.error);
          return [];
        }

        console.log('ActivityService: Found activity types:', response.data);
        return response.data as ActivityType[];
      }),
      catchError(error => {
        console.error('ActivityService: Error getting activity types:', error);
        return of([]);
      })
    );
  }

  async createActivityType(activityType: Omit<ActivityType, 'id'>): Promise<string> {
    console.log('ActivityService: Creating activity type:', activityType);

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('activity_types')
        .insert(activityType)
        .select('id')
        .single();

      if (error) {
        console.error('ActivityService: Error creating activity type:', error);
        throw error;
      }

      console.log('ActivityService: Created activity type with ID:', data.id);
      return data.id;
    } catch (error) {
      console.error('ActivityService: Error creating activity type:', error);
      throw error;
    }
  }

  async updateActivityType(typeId: string, data: Partial<ActivityType>): Promise<void> {
    console.log('ActivityService: Updating activity type:', typeId, data);

    try {
      const { error } = await this.supabaseService.getClient()
        .from('activity_types')
        .update(data)
        .eq('id', typeId);

      if (error) {
        console.error('ActivityService: Error updating activity type:', error);
        throw error;
      }

      console.log('ActivityService: Updated activity type successfully');
    } catch (error) {
      console.error('ActivityService: Error updating activity type:', error);
      throw error;
    }
  }

  async deleteActivityType(typeId: string): Promise<void> {
    console.log('ActivityService: Deleting activity type:', typeId);

    try {
      const { error } = await this.supabaseService.getClient()
        .from('activity_types')
        .delete()
        .eq('id', typeId);

      if (error) {
        console.error('ActivityService: Error deleting activity type:', error);
        throw error;
      }

      console.log('ActivityService: Deleted activity type successfully');
    } catch (error) {
      console.error('ActivityService: Error deleting activity type:', error);
      throw error;
    }
  }

  // Day Tracking operations
  getDayTracking(userId: string, date: Date): Observable<DayTracking | undefined> {
    const dateString = date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    console.log('ActivityService: Getting day tracking for user:', userId, 'date:', dateString);

    return from(
      this.supabaseService.getClient()
        .from('day_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', dateString)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('ActivityService: Error getting day tracking:', response.error);
          return undefined;
        }

        if (response.data && response.data.length > 0) {
          console.log('ActivityService: Found day tracking:', response.data[0]);
          return response.data[0] as DayTracking;
        } else {
          console.log('ActivityService: No day tracking found');
          return undefined;
        }
      }),
      catchError(error => {
        console.error('ActivityService: Error getting day tracking:', error);
        return of(undefined);
      })
    );
  }

  async createDayTracking(userId: string, date: Date): Promise<string> {
    const dateString = date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    console.log('ActivityService: Creating day tracking for user:', userId, 'date:', dateString);

    try {
      // Check if tracking already exists
      const { data: existingData, error: existingError } = await this.supabaseService.getClient()
        .from('day_tracking')
        .select('id')
        .eq('user_id', userId)
        .eq('date', dateString);

      if (existingError) {
        console.error('ActivityService: Error checking existing day tracking:', existingError);
        throw existingError;
      }

      if (existingData && existingData.length > 0) {
        console.log('ActivityService: Day tracking already exists with ID:', existingData[0].id);
        return existingData[0].id;
      }

      // Create new tracking
      const newTracking: Omit<DayTracking, 'id'> = {
        user_id: userId,
        date: dateString
      };

      console.log('ActivityService: Creating new day tracking:', newTracking);

      const { data, error } = await this.supabaseService.getClient()
        .from('day_tracking')
        .insert(newTracking)
        .select('id')
        .single();

      if (error) {
        console.error('ActivityService: Error creating day tracking:', error);
        throw error;
      }

      console.log('ActivityService: Created day tracking with ID:', data.id);
      return data.id;
    } catch (error) {
      console.error('ActivityService: Error creating day tracking:', error);
      throw error;
    }
  }

  // Activity operations
  getActivities(dayTrackingId: string): Observable<Activity[]> {
    console.log('ActivityService: Getting activities for day tracking:', dayTrackingId);

    return from(
      this.supabaseService.getClient()
        .from('activities')
        .select('*')
        .eq('day_tracking_id', dayTrackingId)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('ActivityService: Error getting activities:', response.error);
          return [];
        }

        console.log('ActivityService: Found activities:', response.data);
        return response.data as Activity[];
      }),
      catchError(error => {
        console.error('ActivityService: Error getting activities:', error);
        return of([]);
      })
    );
  }

  async createActivity(activity: Omit<Activity, 'id'>): Promise<string> {
    console.log('ActivityService: Creating activity:', activity);

    // Validate that hours and minutes are within reasonable ranges
    if (activity.hours < 0 || activity.hours >= 24) {
      throw new Error('Hours must be between 0 and 23');
    }

    if (activity.minutes < 0 || activity.minutes >= 60) {
      throw new Error('Minutes must be between 0 and 59');
    }

    try {
      // Calculate total minutes for this activity
      const totalMinutes = activity.hours * 60 + activity.minutes;

      // Get all existing activities for this day
      const { data: existingActivities, error: existingError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', activity.day_tracking_id);

      if (existingError) {
        console.error('ActivityService: Error getting existing activities:', existingError);
        throw existingError;
      }

      if (existingActivities && existingActivities.length > 0) {
        // Calculate total minutes of existing activities
        let existingTotalMinutes = 0;
        existingActivities.forEach(act => {
          existingTotalMinutes += act.hours * 60 + act.minutes;
        });

        console.log('ActivityService: Existing total minutes:', existingTotalMinutes, 'New activity minutes:', totalMinutes);

        // Ensure total time doesn't exceed 24 hours
        if (existingTotalMinutes + totalMinutes > 24 * 60) {
          throw new Error('Total activities cannot exceed 24 hours per day');
        }
      }

      // Create the activity
      const { data, error } = await this.supabaseService.getClient()
        .from('activities')
        .insert(activity)
        .select('id')
        .single();

      if (error) {
        console.error('ActivityService: Error creating activity:', error);
        throw error;
      }

      console.log('ActivityService: Created activity with ID:', data.id);
      return data.id;
    } catch (error) {
      console.error('ActivityService: Error creating activity:', error);
      throw error;
    }
  }

  async updateActivity(activityId: string, data: Partial<Activity>): Promise<void> {
    console.log('ActivityService: Updating activity:', activityId, data);

    try {
      // Get the current activity
      const { data: activityData, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .select('*')
        .eq('id', activityId)
        .single();

      if (activityError) {
        console.error('ActivityService: Error getting activity:', activityError);
        throw activityError;
      }

      if (!activityData) {
        throw new Error('Activity not found');
      }

      const activity = activityData as Activity;
      console.log('ActivityService: Found activity:', activity);

      // Calculate new hours and minutes
      const newHours = data.hours !== undefined ? data.hours : activity.hours;
      const newMinutes = data.minutes !== undefined ? data.minutes : activity.minutes;

      // Validate that hours and minutes are within reasonable ranges
      if (newHours < 0 || newHours >= 24) {
        throw new Error('Hours must be between 0 and 23');
      }

      if (newMinutes < 0 || newMinutes >= 60) {
        throw new Error('Minutes must be between 0 and 59');
      }

      // Calculate total minutes for this activity
      const oldTotalMinutes = activity.hours * 60 + activity.minutes;
      const newTotalMinutes = newHours * 60 + newMinutes;
      const minutesDiff = newTotalMinutes - oldTotalMinutes;

      console.log('ActivityService: Old total minutes:', oldTotalMinutes, 'New total minutes:', newTotalMinutes, 'Difference:', minutesDiff);

      if (minutesDiff > 0) {
        // Get all existing activities for this day
        const { data: existingActivities, error: existingError } = await this.supabaseService.getClient()
          .from('activities')
          .select('hours, minutes')
          .eq('day_tracking_id', activity.day_tracking_id)
          .neq('id', activityId);

        if (existingError) {
          console.error('ActivityService: Error getting existing activities:', existingError);
          throw existingError;
        }

        if (existingActivities && existingActivities.length > 0) {
          // Calculate total minutes of existing activities
          let existingTotalMinutes = 0;
          existingActivities.forEach(act => {
            existingTotalMinutes += act.hours * 60 + act.minutes;
          });

          console.log('ActivityService: Existing total minutes:', existingTotalMinutes);

          // Ensure total time doesn't exceed 24 hours
          if (existingTotalMinutes + oldTotalMinutes + minutesDiff > 24 * 60) {
            throw new Error('Total activities cannot exceed 24 hours per day');
          }
        }
      }

      // Update the activity
      const { error: updateError } = await this.supabaseService.getClient()
        .from('activities')
        .update(data)
        .eq('id', activityId);

      if (updateError) {
        console.error('ActivityService: Error updating activity:', updateError);
        throw updateError;
      }

      console.log('ActivityService: Updated activity successfully');
    } catch (error) {
      console.error('ActivityService: Error updating activity:', error);
      throw error;
    }
  }

  async deleteActivity(activityId: string): Promise<void> {
    console.log('ActivityService: Deleting activity:', activityId);

    try {
      const { error } = await this.supabaseService.getClient()
        .from('activities')
        .delete()
        .eq('id', activityId);

      if (error) {
        console.error('ActivityService: Error deleting activity:', error);
        throw error;
      }

      console.log('ActivityService: Deleted activity successfully');
    } catch (error) {
      console.error('ActivityService: Error deleting activity:', error);
      throw error;
    }
  }

  // Initialize default activity types
  async initializeDefaultActivityTypes(): Promise<void> {
    console.log('ActivityService: Initializing default activity types');

    const defaults = [
      { name: 'Sleep', emoji: '😴', is_active: true, order: 1, is_default: true },
      { name: 'Work', emoji: '💼', is_active: true, order: 2, is_default: true },
      { name: 'Exercise', emoji: '🏃', is_active: true, order: 3, is_default: true },
      { name: 'Screen Time', emoji: '📱', is_active: true, order: 4, is_default: true },
      { name: 'Study', emoji: '📚', is_active: true, order: 5, is_default: true },
      { name: 'Meditation', emoji: '🧘', is_active: true, order: 6, is_default: true },
      { name: 'Reading', emoji: '📖', is_active: true, order: 7, is_default: true },
      { name: 'Social', emoji: '👥', is_active: true, order: 8, is_default: true },
      { name: 'Hobby', emoji: '🎨', is_active: true, order: 9, is_default: true },
      { name: 'Free Time', emoji: '🎮', is_active: true, order: 10, is_default: true }
    ];

    try {
      for (const activityType of defaults) {
        console.log('ActivityService: Checking if activity type exists:', activityType.name);

        // Check if this activity type already exists
        const { data: existingData, error: existingError } = await this.supabaseService.getClient()
          .from('activity_types')
          .select('id')
          .eq('name', activityType.name);

        if (existingError) {
          console.error('ActivityService: Error checking existing activity type:', existingError);
          continue;
        }

        if (!existingData || existingData.length === 0) {
          console.log('ActivityService: Creating activity type:', activityType.name);
          // Create the activity type
          await this.createActivityType(activityType);
        } else {
          console.log('ActivityService: Activity type already exists:', activityType.name);
        }
      }

      console.log('ActivityService: Finished initializing default activity types');
    } catch (error) {
      console.error('ActivityService: Error initializing default activity types:', error);
    }
  }
}
