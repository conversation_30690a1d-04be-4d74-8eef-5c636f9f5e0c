# Supabase Row Level Security (RLS) Policies

This directory contains SQL scripts for setting up Row Level Security (RLS) policies in Supabase for the Upshift application.

## What is Row Level Security?

Row Level Security (RLS) is a feature in Supabase that allows you to control which rows in a table a user can access. This is important for ensuring that users can only access their own data and not the data of other users.

## How to Apply RLS Policies

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of the `rls_policies.sql` file
4. Paste the SQL into the editor
5. Click "Run" to execute the SQL

## Troubleshooting

If you encounter errors like:

```
Error: new row violates row-level security policy for table "user_badges"
```

It means that the RLS policies are not set up correctly. Make sure you have applied the RLS policies from the `rls_policies.sql` file.

## Policies Included

The `rls_policies.sql` file includes policies for the following tables:

- `user_badges`: Allows users to view, create, and update their own badges
- `quests`: Allows users to view, create, update, and delete their own quests
- `quest_progress`: Allows users to view, create, update, and delete their own quest progress
- `side_quests`: Allows users to view, create, update, and delete their own side quests
- `groups`: Allows users to view all groups, create their own groups, and update/delete groups where they are the admin (admin_id field)
- `group_members`: Allows users to view all group members, join groups, and leave groups
- `group_quests`: Allows users to view group quests for groups they are members of, and allows group admins to create, update, and delete group quests
- `group_quest_progress`: Allows users to view group quest progress for groups they are members of, create and update their own progress, and allows group admins to update any progress
- `group_join_requests`: Allows users to view and create group join requests, and allows group admins to update and delete them
- `friends`: Allows users to view their own friends, add friends, and remove friends
- `profiles`: Allows users to view all profiles and update their own profile
- `notifications`: Allows users to view, update, and delete their own notifications, and create notifications for others
- `activities`: Allows users to view, create, update, and delete activities linked to their day_tracking entries
- `activity_types`: Allows all users to view activity types, and allows admins to manage them
- `daily_sidequest_pool`: Allows all users to view the daily sidequest pool, and allows admins to manage it
- `day_tracking`: Allows users to view, create, update, and delete their own day tracking
- `goals`: Allows users to view, create, update, and delete their own goals
- `goal_journal_entries`: Allows users to view, create, update, and delete their own goal journal entries
- `microgoals`: Allows users to view, create, update, and delete their own microgoals
- `subscription_history`: Allows users to view and create their own subscription history
- `user_daily_sidequests`: Allows users to view, create, update, and delete their own daily sidequests

## Admin Access

All policies include a clause that allows users with the username 'admin' to access all data in the table. This is useful for administrative purposes.

## Custom Policies

If you need to add custom policies for specific use cases, you can add them to the `rls_policies.sql` file and re-run it in the SQL Editor.
