<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
  <header>
    <div class="logo">
      <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
      <span>Upshift</span>
    </div>
    <h1>Profile Settings</h1>
  </header>

  <div class="settings-container" *ngIf="user">
    <div *ngIf="successMessage" class="success-message" id="success-message">
      <ion-icon name="checkmark-circle-outline"></ion-icon> {{ successMessage }}
    </div>

    <form (ngSubmit)="saveChanges()" [formGroup]="settingsForm" #form="ngForm">
      <div class="form-group">
        <label for="firstName">First Name</label>
        <input type="text" id="firstName" formControlName="firstName">
        <div class="error-message" *ngIf="settingsForm.get('firstName')?.invalid && settingsForm.get('firstName')?.touched">
          First name is required
        </div>
      </div>

      <div class="form-group">
        <label for="lastName">Last Name</label>
        <input type="text" id="lastName" formControlName="lastName">
        <div class="error-message" *ngIf="settingsForm.get('lastName')?.invalid && settingsForm.get('lastName')?.touched">
          Last name is required
        </div>
      </div>

      <div class="form-group">
        <label for="username">Username</label>
        <input type="text" id="username" formControlName="username">
        <div class="error-message" *ngIf="settingsForm.get('username')?.invalid && settingsForm.get('username')?.touched">
          Username is required
        </div>
      </div>

      <div class="form-group">
        <label for="bio">Bio (max 100 characters)</label>
        <textarea id="bio" formControlName="bio" maxlength="100"></textarea>
        <div class="error-message" *ngIf="settingsForm.get('bio')?.invalid && settingsForm.get('bio')?.touched">
          Bio is too long
        </div>
      </div>

      <div class="form-group">
        <label>Profile Picture</label>
        <div class="profile-picture-preview">
          <img *ngIf="user.profile_picture" [src]="user.profile_picture" [alt]="user.username" id="profile-picture-preview">
          <span *ngIf="!user.profile_picture" id="profile-picture-placeholder">👤</span>
        </div>
        <div class="file-input-container">
          <label for="profilePicture" class="file-input-label">
            <span class="file-button">Choose Image</span>
          </label>
          <input type="file" id="profilePicture" (change)="onFileSelected($event)" accept="image/*">
          <div class="file-status" id="file-status">{{ fileStatus }}</div>
          <div class="help-text">Upload a profile picture (JPG, PNG, WebP, HEIC/HEIF)</div>
          <div class="error-message" *ngIf="errors['profilePicture']">{{ errors['profilePicture'] }}</div>
        </div>
      </div>

      <h3>Preferences</h3>

      <div class="checkbox-group">
        <input type="checkbox" id="sidequests_switch" formControlName="sidequests_switch">
        <label for="sidequests_switch">Show Side Quests</label>
      </div>

      <div class="checkbox-group">
        <input type="checkbox" id="show_celebration" formControlName="show_celebration" (change)="toggleCelebrationSettings()">
        <label for="show_celebration">Show Celebration Animation</label>
      </div>

      <div id="celebration-settings" [style.display]="settingsForm.get('show_celebration')?.value ? 'block' : 'none'">
        <h4 style="margin-top: 0; margin-bottom: 15px; font-size: 16px;
    font-weight: 500;">Celebration Settings</h4>
        <div class="form-group">
          <label for="celebration_name">Celebration Title</label>
          <input type="text" id="celebration_name" formControlName="celebration_name">
          <div class="help-text">Custom title to show when you complete all quests (default: 'Another Day, Another W')</div>
        </div>

        <div class="form-group">
          <label for="celebration_description">Celebration Message</label>
          <textarea id="celebration_description" formControlName="celebration_description"></textarea>
          <div class="help-text">Custom description text shown below the title (default: 'You've completed all your quests for today...')</div>
        </div>

        <div class="form-group">
          <label for="celebration_emoji">Celebration Emoji</label>
          <div style="display: flex; gap: 10px; align-items: center;">
            <input type="text" id="celebration_emoji" formControlName="celebration_emoji" appEmojiInput>
            <div id="emoji-preview" style="font-size: 24px; min-width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background-color: rgba(255,255,255,0.1); border-radius: 8px;">
              <ng-container *ngIf="settingsForm.get('celebration_emoji')?.value">{{ settingsForm.get('celebration_emoji')?.value }}</ng-container>
              <span *ngIf="!settingsForm.get('celebration_emoji')?.value" style="color: var(--secondary-text); font-size: 12px;">None</span>
            </div>
          </div>
          <div class="help-text">Click to select an emoji. Only one emoji can be used.</div>
        </div>
      </div>

      <button type="button" class="submit-button" [disabled]="(settingsForm.invalid || !settingsForm.dirty) && !profilePicture" (click)="saveChanges()">Save Changes</button>
    </form>

    <div>
      <a (click)="navigateToProfile()" class="back-button">Back to Profile</a>
      <a (click)="logout()" class="logout-button">Logout</a>
    </div>
  </div>
  <div style="margin-top: 20px;">
    <a [routerLink]="['/badges']" style="display: block; background-color: #1e1e1e; color: white; text-decoration: none; padding: 12px; border-radius: 8px; text-align: center;">
      <span style="margin-right: 5px;">🏆</span> View Your Badges
    </a>
  </div>
</div>

<!-- Navigation -->
<app-navigation></app-navigation>
