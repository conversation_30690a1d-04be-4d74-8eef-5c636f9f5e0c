.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  color: white;
  min-height: 100vh;
  padding-bottom: 80px;
  overflow-y: scroll;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: bold;
}

.logo img {
  height: 30px;
}

.admin-content {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
}

.collections-list h2 {
  margin-bottom: 20px;
  color: #FF9500;
}

.loading, .no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.collection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.collection-card {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.collection-card:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.collection-card h3 {
  margin: 0 0 10px 0;
  color: #FF9500;
}

.collection-card p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.add-collection {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.add-collection h3 {
  margin: 0 0 15px 0;
  color: #FF9500;
}

.add-collection-form {
  display: flex;
  gap: 10px;
}

.add-collection-form input {
  flex: 1;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
}

.add-collection-form button {
  padding: 10px 20px;
  border-radius: 4px;
  border: none;
  background-color: #FF9500;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-collection-form button:hover {
  background-color: #e68600;
}
