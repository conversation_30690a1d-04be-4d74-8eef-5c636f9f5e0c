import { Injectable, inject } from '@angular/core';
import { Activity, ActivityType, DayTracking } from '../models/activity.model';
import { Observable, from, of, catchError, map, switchMap } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class TimeTrackerUnifiedService {
  private supabaseService = inject(SupabaseService);

  /**
   * Get all activity types
   */
  getActivityTypes(): Observable<ActivityType[]> {
    console.log('TimeTrackerService: Getting activity types');

    return from(
      this.supabaseService.getClient()
        .from('activity_types')
        .select('*')
        .eq('is_active', true)
        .order('order', { ascending: true })
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('TimeTrackerService: Error getting activity types:', response.error);
          return [];
        }

        console.log('TimeTrackerService: Found activity types:', response.data);
        return response.data as ActivityType[];
      }),
      catchError(error => {
        console.error('TimeTrackerService: Error getting activity types:', error);
        return of([]);
      })
    );
  }

  /**
   * Get or create a day tracking entry for a user and date
   */
  getDayTracking(userId: string, date: string): Observable<DayTracking> {
    console.log('TimeTrackerService: Getting day tracking for user:', userId, 'date:', date);

    if (!userId || !date) {
      console.error('TimeTrackerService: Missing userId or date');
      return of({ id: '', user_id: userId, date: date });
    }

    return from(
      this.supabaseService.getClient()
        .from('day_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', date)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error && response.error.code === 'PGRST116') { // Not found error
          console.log('TimeTrackerService: Day tracking not found, creating new one');
          return from(
            this.supabaseService.getClient()
              .from('day_tracking')
              .insert({ user_id: userId, date: date })
              .select()
              .single()
          ).pipe(
            map(insertResponse => {
              if (insertResponse.error) {
                console.error('TimeTrackerService: Error creating day tracking:', insertResponse.error);
                return { id: '', user_id: userId, date: date };
              }

              console.log('TimeTrackerService: Created day tracking:', insertResponse.data);
              return insertResponse.data as DayTracking;
            }),
            catchError(error => {
              console.error('TimeTrackerService: Error creating day tracking:', error);
              return of({ id: '', user_id: userId, date: date });
            })
          );
        }

        if (response.error) {
          console.error('TimeTrackerService: Error getting day tracking:', response.error);
          return of({ id: '', user_id: userId, date: date });
        }

        console.log('TimeTrackerService: Found day tracking:', response.data);
        return of(response.data as DayTracking);
      }),
      catchError(error => {
        console.error('TimeTrackerService: Error in getDayTracking:', error);
        return of({ id: '', user_id: userId, date: date });
      })
    );
  }

  /**
   * Get activities for a day tracking entry
   */
  getActivitiesForDayTracking(dayTrackingId: string): Observable<Activity[]> {
    console.log('TimeTrackerService: Getting activities for day tracking:', dayTrackingId);

    if (!dayTrackingId) {
      console.error('TimeTrackerService: Missing dayTrackingId');
      return of([]);
    }

    return from(
      this.supabaseService.getClient()
        .from('activities')
        .select('*')
        .eq('day_tracking_id', dayTrackingId)
        .order('name')
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('TimeTrackerService: Error getting activities:', response.error);
          return [];
        }

        console.log('TimeTrackerService: Found activities:', response.data);
        return response.data as Activity[];
      }),
      catchError(error => {
        console.error('TimeTrackerService: Error getting activities:', error);
        return of([]);
      })
    );
  }

  /**
   * Get activities for a user and date
   */
  getActivities(userId: string, date: string): Observable<Activity[]> {
    console.log('TimeTrackerService: Getting activities for user:', userId, 'date:', date);

    if (!userId || !date) {
      console.error('TimeTrackerService: Missing userId or date');
      return of([]);
    }

    return this.getDayTracking(userId, date).pipe(
      switchMap(dayTracking => {
        if (!dayTracking.id) {
          console.log('TimeTrackerService: No day tracking found, returning empty activities array');
          return of([]);
        }

        return this.getActivitiesForDayTracking(dayTracking.id);
      })
    );
  }

  /**
   * Create a new activity
   */
  async createActivity(
    userId: string,
    date: string,
    name: string,
    emoji: string,
    hours: number,
    minutes: number,
    isCustom: boolean
  ): Promise<{ id: string, total_hours: string, remaining_hours: string }> {
    console.log('TimeTrackerService: Creating activity:', { name, emoji, hours, minutes, isCustom });

    try {
      // First, get or create day tracking
      const { data: dayTracking, error: dayTrackingError } = await this.supabaseService.getClient()
        .from('day_tracking')
        .select('id')
        .eq('user_id', userId)
        .eq('date', date)
        .single();

      let dayTrackingId: string;

      if (dayTrackingError && dayTrackingError.code === 'PGRST116') { // Not found error
        // Create new day tracking
        console.log('TimeTrackerService: Creating new day tracking for user:', userId, 'date:', date);
        const { data: newDayTracking, error: newDayTrackingError } = await this.supabaseService.getClient()
          .from('day_tracking')
          .insert({ user_id: userId, date: date })
          .select('id')
          .single();

        if (newDayTrackingError) {
          console.error('TimeTrackerService: Error creating day tracking:', newDayTrackingError);
          return Promise.reject(newDayTrackingError);
        }

        dayTrackingId = newDayTracking.id;
        console.log('TimeTrackerService: Created new day tracking with ID:', dayTrackingId);
      } else if (dayTrackingError) {
        console.error('TimeTrackerService: Error getting day tracking:', dayTrackingError);
        return Promise.reject(dayTrackingError);
      } else {
        dayTrackingId = dayTracking.id;
        console.log('TimeTrackerService: Found existing day tracking with ID:', dayTrackingId);
      }

      // Now create the activity
      const { data: newActivity, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .insert({
          day_tracking_id: dayTrackingId,
          name: name,
          emoji: emoji,
          hours: hours,
          minutes: minutes,
          is_custom: isCustom
        })
        .select('id')
        .single();

      if (activityError) {
        console.error('TimeTrackerService: Error creating activity:', activityError);
        return Promise.reject(activityError);
      }

      console.log('TimeTrackerService: Created activity with ID:', newActivity.id);

      // Calculate total time
      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);

      // We don't need to update day_tracking with totals anymore
      // Totals are calculated on the fly from activities

      return {
        id: newActivity.id,
        total_hours: totalHours.toFixed(1),
        remaining_hours: remainingHours.toFixed(1)
      };
    } catch (error) {
      console.error('TimeTrackerService: Error creating activity:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Update an activity
   */
  async updateActivity(activityId: string, hours: number, minutes: number): Promise<{ total_hours: string, remaining_hours: string }> {
    console.log('TimeTrackerService: Updating activity:', activityId, { hours, minutes });

    try {
      // First get the activity to get the day_tracking_id
      const { data: activity, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .select('day_tracking_id')
        .eq('id', activityId)
        .single();

      if (activityError) {
        console.error('TimeTrackerService: Error getting activity:', activityError);
        return Promise.reject(activityError);
      }

      const dayTrackingId = activity.day_tracking_id;

      // Update the activity
      const { error: updateError } = await this.supabaseService.getClient()
        .from('activities')
        .update({ hours, minutes })
        .eq('id', activityId);

      if (updateError) {
        console.error('TimeTrackerService: Error updating activity:', updateError);
        return Promise.reject(updateError);
      }

      console.log('TimeTrackerService: Updated activity successfully');

      // Calculate total time
      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);

      // We don't need to update day_tracking with totals anymore
      // Totals are calculated on the fly from activities

      return {
        total_hours: totalHours.toFixed(1),
        remaining_hours: remainingHours.toFixed(1)
      };
    } catch (error) {
      console.error('TimeTrackerService: Error updating activity:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Delete an activity
   */
  async deleteActivity(activityId: string): Promise<{ total_hours: string, remaining_hours: string }> {
    console.log('TimeTrackerService: Deleting activity:', activityId);

    try {
      // First get the activity to get the day_tracking_id
      const { data: activity, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .select('day_tracking_id')
        .eq('id', activityId)
        .single();

      if (activityError) {
        console.error('TimeTrackerService: Error getting activity:', activityError);
        return Promise.reject(activityError);
      }

      const dayTrackingId = activity.day_tracking_id;

      // Delete the activity
      const { error: deleteError } = await this.supabaseService.getClient()
        .from('activities')
        .delete()
        .eq('id', activityId);

      if (deleteError) {
        console.error('TimeTrackerService: Error deleting activity:', deleteError);
        return Promise.reject(deleteError);
      }

      console.log('TimeTrackerService: Deleted activity successfully');

      // Calculate total time
      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);

      // We don't need to update day_tracking with totals anymore
      // Totals are calculated on the fly from activities

      return {
        total_hours: totalHours.toFixed(1),
        remaining_hours: remainingHours.toFixed(1)
      };
    } catch (error) {
      console.error('TimeTrackerService: Error deleting activity:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Get total time for a day tracking entry
   */
  async getTotalTime(dayTrackingId: string): Promise<{ total_hours: number, remaining_hours: number }> {
    console.log('TimeTrackerService: Getting total time for day tracking:', dayTrackingId);

    try {
      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);

      return {
        total_hours: totalHours,
        remaining_hours: remainingHours
      };
    } catch (error) {
      console.error('TimeTrackerService: Error getting total time:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Create a new activity type
   */
  async createActivityType(activityType: Omit<ActivityType, 'id'>): Promise<string> {
    console.log('TimeTrackerService: Creating activity type:', activityType);

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('activity_types')
        .insert(activityType)
        .select('id')
        .single();

      if (error) {
        console.error('TimeTrackerService: Error creating activity type:', error);
        return Promise.reject(error);
      }

      console.log('TimeTrackerService: Created activity type with ID:', data.id);
      return data.id;
    } catch (error) {
      console.error('TimeTrackerService: Error creating activity type:', error);
      return Promise.reject(error);
    }
  }
}
