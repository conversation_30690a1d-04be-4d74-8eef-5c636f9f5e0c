{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as writeTask, r as registerInstance, c as createEvent, d as readTask, h, f as getElement, e as Host } from './index-527b9e34.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { I as ION_CONTENT_CLASS_SELECTOR, b as ION_CONTENT_ELEMENT_SELECTOR, p as printIonContentErrorMsg, g as getScrollElement } from './index-e919e353.js';\nimport { t as transitionEndAsync, c as componentOnReady, j as clamp, g as getElementRoot, r as raf } from './helpers-78efeec3.js';\nimport { d as hapticImpact, I as ImpactStyle } from './haptic-ac164e4c.js';\nimport { b as getIonMode, c as config } from './ionic-global-ca86cf32.js';\nimport { c as createAnimation } from './animation-eab5a4ca.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-49c88215.js';\nimport { h as caretBackSharp, i as arrowDown } from './index-e2cf2ceb.js';\nimport { S as SPINNERS } from './spinner-configs-964f7cf3.js';\nimport './index-738d7504.js';\nimport './capacitor-59395cbd.js';\nimport './index-a5d50daf.js';\nconst getRefresherAnimationType = contentEl => {\n  const previousSibling = contentEl.previousElementSibling;\n  const hasHeader = previousSibling !== null && previousSibling.tagName === 'ION-HEADER';\n  return hasHeader ? 'translate' : 'scale';\n};\nconst createPullingAnimation = (type, pullingSpinner, refresherEl) => {\n  return type === 'scale' ? createScaleAnimation(pullingSpinner, refresherEl) : createTranslateAnimation(pullingSpinner, refresherEl);\n};\nconst createBaseAnimation = pullingRefresherIcon => {\n  const spinner = pullingRefresherIcon.querySelector('ion-spinner');\n  const circle = spinner.shadowRoot.querySelector('circle');\n  const spinnerArrowContainer = pullingRefresherIcon.querySelector('.spinner-arrow-container');\n  const arrowContainer = pullingRefresherIcon.querySelector('.arrow-container');\n  const arrow = arrowContainer ? arrowContainer.querySelector('ion-icon') : null;\n  const baseAnimation = createAnimation().duration(1000).easing('ease-out');\n  const spinnerArrowContainerAnimation = createAnimation().addElement(spinnerArrowContainer).keyframes([{\n    offset: 0,\n    opacity: '0.3'\n  }, {\n    offset: 0.45,\n    opacity: '0.3'\n  }, {\n    offset: 0.55,\n    opacity: '1'\n  }, {\n    offset: 1,\n    opacity: '1'\n  }]);\n  const circleInnerAnimation = createAnimation().addElement(circle).keyframes([{\n    offset: 0,\n    strokeDasharray: '1px, 200px'\n  }, {\n    offset: 0.2,\n    strokeDasharray: '1px, 200px'\n  }, {\n    offset: 0.55,\n    strokeDasharray: '100px, 200px'\n  }, {\n    offset: 1,\n    strokeDasharray: '100px, 200px'\n  }]);\n  const circleOuterAnimation = createAnimation().addElement(spinner).keyframes([{\n    offset: 0,\n    transform: 'rotate(-90deg)'\n  }, {\n    offset: 1,\n    transform: 'rotate(210deg)'\n  }]);\n  /**\n   * Only add arrow animation if present\n   * this allows users to customize the spinners\n   * without errors being thrown\n   */\n  if (arrowContainer && arrow) {\n    const arrowContainerAnimation = createAnimation().addElement(arrowContainer).keyframes([{\n      offset: 0,\n      transform: 'rotate(0deg)'\n    }, {\n      offset: 0.3,\n      transform: 'rotate(0deg)'\n    }, {\n      offset: 0.55,\n      transform: 'rotate(280deg)'\n    }, {\n      offset: 1,\n      transform: 'rotate(400deg)'\n    }]);\n    const arrowAnimation = createAnimation().addElement(arrow).keyframes([{\n      offset: 0,\n      transform: 'translateX(2px) scale(0)'\n    }, {\n      offset: 0.3,\n      transform: 'translateX(2px) scale(0)'\n    }, {\n      offset: 0.55,\n      transform: 'translateX(-1.5px) scale(1)'\n    }, {\n      offset: 1,\n      transform: 'translateX(-1.5px) scale(1)'\n    }]);\n    baseAnimation.addAnimation([arrowContainerAnimation, arrowAnimation]);\n  }\n  return baseAnimation.addAnimation([spinnerArrowContainerAnimation, circleInnerAnimation, circleOuterAnimation]);\n};\nconst createScaleAnimation = (pullingRefresherIcon, refresherEl) => {\n  /**\n   * Do not take the height of the refresher icon\n   * because at this point the DOM has not updated,\n   * so the refresher icon is still hidden with\n   * display: none.\n   * The `ion-refresher` container height\n   * is roughly the amount we need to offset\n   * the icon by when pulling down.\n   */\n  const height = refresherEl.clientHeight;\n  const spinnerAnimation = createAnimation().addElement(pullingRefresherIcon).keyframes([{\n    offset: 0,\n    transform: `scale(0) translateY(-${height}px)`\n  }, {\n    offset: 1,\n    transform: 'scale(1) translateY(100px)'\n  }]);\n  return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createTranslateAnimation = (pullingRefresherIcon, refresherEl) => {\n  /**\n   * Do not take the height of the refresher icon\n   * because at this point the DOM has not updated,\n   * so the refresher icon is still hidden with\n   * display: none.\n   * The `ion-refresher` container height\n   * is roughly the amount we need to offset\n   * the icon by when pulling down.\n   */\n  const height = refresherEl.clientHeight;\n  const spinnerAnimation = createAnimation().addElement(pullingRefresherIcon).keyframes([{\n    offset: 0,\n    transform: `translateY(-${height}px)`\n  }, {\n    offset: 1,\n    transform: 'translateY(100px)'\n  }]);\n  return createBaseAnimation(pullingRefresherIcon).addAnimation([spinnerAnimation]);\n};\nconst createSnapBackAnimation = pullingRefresherIcon => {\n  return createAnimation().duration(125).addElement(pullingRefresherIcon).fromTo('transform', 'translateY(var(--ion-pulling-refresher-translate, 100px))', 'translateY(0px)');\n};\n// iOS Native Refresher\n// -----------------------------\nconst setSpinnerOpacity = (spinner, opacity) => {\n  spinner.style.setProperty('opacity', opacity.toString());\n};\nconst handleScrollWhilePulling = (ticks, numTicks, pullAmount) => {\n  const max = 1;\n  writeTask(() => {\n    ticks.forEach((el, i) => {\n      /**\n       * Compute the opacity of each tick\n       * mark as a percentage of the pullAmount\n       * offset by max / numTicks so\n       * the tick marks are shown staggered.\n       */\n      const min = i * (max / numTicks);\n      const range = max - min;\n      const start = pullAmount - min;\n      const progression = clamp(0, start / range, 1);\n      el.style.setProperty('opacity', progression.toString());\n    });\n  });\n};\nconst handleScrollWhileRefreshing = (spinner, lastVelocityY) => {\n  writeTask(() => {\n    // If user pulls down quickly, the spinner should spin faster\n    spinner.style.setProperty('--refreshing-rotation-duration', lastVelocityY >= 1.0 ? '0.5s' : '2s');\n    spinner.style.setProperty('opacity', '1');\n  });\n};\nconst translateElement = (el, value, duration = 200) => {\n  if (!el) {\n    return Promise.resolve();\n  }\n  const trans = transitionEndAsync(el, duration);\n  writeTask(() => {\n    el.style.setProperty('transition', `${duration}ms all ease-out`);\n    if (value === undefined) {\n      el.style.removeProperty('transform');\n    } else {\n      el.style.setProperty('transform', `translate3d(0px, ${value}, 0px)`);\n    }\n  });\n  return trans;\n};\n// Utils\n// -----------------------------\n/**\n * In order to use the native iOS refresher the device must support rubber band scrolling.\n * As part of this, we need to exclude Desktop Safari because it has a slightly different rubber band effect that is not compatible with the native refresher in Ionic.\n *\n * We also need to be careful not to include devices that spoof their user agent.\n * For example, when using iOS emulation in Chrome the user agent will be spoofed such that\n * navigator.maxTouchPointer > 0. To work around this,\n * we check to see if the apple-pay-logo is supported as a named image which is only\n * true on Apple devices.\n *\n * We previously checked referencEl.style.webkitOverflowScrolling to explicitly check\n * for rubber band support. However, this property was removed on iPadOS and it's possible\n * that this will be removed on iOS in the future too.\n *\n */\nconst supportsRubberBandScrolling = () => {\n  return navigator.maxTouchPoints > 0 && CSS.supports('background: -webkit-named-image(apple-pay-logo-black)');\n};\nconst shouldUseNativeRefresher = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (referenceEl, mode) {\n    const refresherContent = referenceEl.querySelector('ion-refresher-content');\n    if (!refresherContent) {\n      return Promise.resolve(false);\n    }\n    yield new Promise(resolve => componentOnReady(refresherContent, resolve));\n    const pullingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n    const refreshingSpinner = referenceEl.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n    return pullingSpinner !== null && refreshingSpinner !== null && (mode === 'ios' && supportsRubberBandScrolling() || mode === 'md');\n  });\n  return function shouldUseNativeRefresher(_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst refresherIosCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}ion-refresher{inset-inline-start:0}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-ios .refresher-pulling-icon,.refresher-ios .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-ios .refresher-pulling-text,.refresher-ios .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-lines-ios line,.refresher-ios .refresher-refreshing .spinner-lines-small-ios line,.refresher-ios .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-ios .refresher-refreshing .spinner-bubbles circle,.refresher-ios .refresher-refreshing .spinner-circles circle,.refresher-ios .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}.refresher-native .refresher-refreshing ion-spinner{--refreshing-rotation-duration:2s;display:none;-webkit-animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards;animation:var(--refreshing-rotation-duration) ease-out refresher-rotate forwards}.refresher-native .refresher-refreshing{display:none;-webkit-animation:250ms linear refresher-pop forwards;animation:250ms linear refresher-pop forwards}.refresher-native ion-spinner{width:32px;height:32px;color:var(--ion-color-step-450, var(--ion-background-color-step-450, #747577))}.refresher-native.refresher-refreshing .refresher-pulling ion-spinner,.refresher-native.refresher-completing .refresher-pulling ion-spinner{display:none}.refresher-native.refresher-refreshing .refresher-refreshing ion-spinner,.refresher-native.refresher-completing .refresher-refreshing ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-pulling ion-spinner{display:block}.refresher-native.refresher-pulling .refresher-refreshing ion-spinner{display:none}.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0) rotate(180deg);transform:scale(0) rotate(180deg);-webkit-transition:300ms;transition:300ms}@-webkit-keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes refresher-pop{0%{-webkit-transform:scale(1);transform:scale(1);-webkit-animation-timing-function:ease-in;animation-timing-function:ease-in}50%{-webkit-transform:scale(1.2);transform:scale(1.2);-webkit-animation-timing-function:ease-out;animation-timing-function:ease-out}100%{-webkit-transform:scale(1);transform:scale(1)}}@-webkit-keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes refresher-rotate{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}\";\nconst IonRefresherIosStyle0 = refresherIosCss;\nconst refresherMdCss = \"ion-refresher{top:0;display:none;position:absolute;width:100%;height:60px;pointer-events:none;z-index:-1}ion-refresher{inset-inline-start:0}ion-refresher.refresher-active{display:block}ion-refresher-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;height:100%}.refresher-pulling,.refresher-refreshing{display:none;width:100%}.refresher-pulling-icon,.refresher-refreshing-icon{-webkit-transform-origin:center;transform-origin:center;-webkit-transition:200ms;transition:200ms;font-size:30px;text-align:center}:host-context([dir=rtl]) .refresher-pulling-icon,:host-context([dir=rtl]) .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] .refresher-pulling-icon,[dir=rtl] .refresher-refreshing-icon{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){.refresher-pulling-icon:dir(rtl),.refresher-refreshing-icon:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}.refresher-pulling-text,.refresher-refreshing-text{font-size:16px;text-align:center}ion-refresher-content .arrow-container{display:none}.refresher-pulling ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling{display:block}.refresher-ready ion-refresher-content .refresher-pulling-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.refresher-refreshing ion-refresher-content .refresher-refreshing{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling{display:block}.refresher-cancelling ion-refresher-content .refresher-pulling-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-completing ion-refresher-content .refresher-refreshing{display:block}.refresher-completing ion-refresher-content .refresher-refreshing-icon{-webkit-transform:scale(0);transform:scale(0)}.refresher-native .refresher-pulling-text,.refresher-native .refresher-refreshing-text{display:none}.refresher-md .refresher-pulling-icon,.refresher-md .refresher-refreshing-icon{color:var(--ion-text-color, #000)}.refresher-md .refresher-pulling-text,.refresher-md .refresher-refreshing-text{color:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-lines-md line,.refresher-md .refresher-refreshing .spinner-lines-small-md line,.refresher-md .refresher-refreshing .spinner-crescent circle{stroke:var(--ion-text-color, #000)}.refresher-md .refresher-refreshing .spinner-bubbles circle,.refresher-md .refresher-refreshing .spinner-circles circle,.refresher-md .refresher-refreshing .spinner-dots circle{fill:var(--ion-text-color, #000)}ion-refresher.refresher-native{display:block;z-index:1}ion-refresher.refresher-native ion-spinner{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:24px;height:24px;color:var(--ion-color-primary, #0054e9)}ion-refresher.refresher-native .spinner-arrow-container{display:inherit}ion-refresher.refresher-native .arrow-container{display:block;position:absolute;width:24px;height:24px}ion-refresher.refresher-native .arrow-container ion-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;left:0;right:0;bottom:-4px;position:absolute;color:var(--ion-color-primary, #0054e9);font-size:12px}ion-refresher.refresher-native.refresher-pulling ion-refresher-content .refresher-pulling,ion-refresher.refresher-native.refresher-ready ion-refresher-content .refresher-pulling{display:-ms-flexbox;display:flex}ion-refresher.refresher-native.refresher-refreshing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-completing ion-refresher-content .refresher-refreshing,ion-refresher.refresher-native.refresher-cancelling ion-refresher-content .refresher-refreshing{display:-ms-flexbox;display:flex}ion-refresher.refresher-native .refresher-pulling-icon{-webkit-transform:translateY(calc(-100% - 10px));transform:translateY(calc(-100% - 10px))}ion-refresher.refresher-native .refresher-pulling-icon,ion-refresher.refresher-native .refresher-refreshing-icon{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;border-radius:100%;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;display:-ms-flexbox;display:flex;border:1px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #ececec));background:var(--ion-color-step-250, var(--ion-background-color-step-250, #ffffff));-webkit-box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1);box-shadow:0px 1px 6px rgba(0, 0, 0, 0.1)}\";\nconst IonRefresherMdStyle0 = refresherMdCss;\nconst Refresher = /*#__PURE__*/(() => {\n  let Refresher = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.ionRefresh = createEvent(this, \"ionRefresh\", 7);\n      this.ionPull = createEvent(this, \"ionPull\", 7);\n      this.ionStart = createEvent(this, \"ionStart\", 7);\n      this.appliedStyles = false;\n      this.didStart = false;\n      this.progress = 0;\n      this.pointerDown = false;\n      this.needsCompletion = false;\n      this.didRefresh = false;\n      this.contentFullscreen = false;\n      this.lastVelocityY = 0;\n      this.animations = [];\n      this.nativeRefresher = false;\n      this.state = 1 /* RefresherState.Inactive */;\n      this.pullMin = 60;\n      this.pullMax = this.pullMin + 60;\n      this.closeDuration = '280ms';\n      this.snapbackDuration = '280ms';\n      this.pullFactor = 1;\n      this.disabled = false;\n    }\n    disabledChanged() {\n      if (this.gesture) {\n        this.gesture.enable(!this.disabled);\n      }\n    }\n    checkNativeRefresher() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const useNativeRefresher = yield shouldUseNativeRefresher(_this.el, getIonMode(_this));\n        if (useNativeRefresher && !_this.nativeRefresher) {\n          const contentEl = _this.el.closest('ion-content');\n          _this.setupNativeRefresher(contentEl);\n        } else if (!useNativeRefresher) {\n          _this.destroyNativeRefresher();\n        }\n      })();\n    }\n    destroyNativeRefresher() {\n      if (this.scrollEl && this.scrollListenerCallback) {\n        this.scrollEl.removeEventListener('scroll', this.scrollListenerCallback);\n        this.scrollListenerCallback = undefined;\n      }\n      this.nativeRefresher = false;\n    }\n    resetNativeRefresher(el, state) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        _this2.state = state;\n        if (getIonMode(_this2) === 'ios') {\n          yield translateElement(el, undefined, 300);\n        } else {\n          yield transitionEndAsync(_this2.el.querySelector('.refresher-refreshing-icon'), 200);\n        }\n        _this2.didRefresh = false;\n        _this2.needsCompletion = false;\n        _this2.pointerDown = false;\n        _this2.animations.forEach(ani => ani.destroy());\n        _this2.animations = [];\n        _this2.progress = 0;\n        _this2.state = 1 /* RefresherState.Inactive */;\n      })();\n    }\n    setupiOSNativeRefresher(pullingSpinner, refreshingSpinner) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        _this3.elementToTransform = _this3.scrollEl;\n        const ticks = pullingSpinner.shadowRoot.querySelectorAll('svg');\n        let MAX_PULL = _this3.scrollEl.clientHeight * 0.16;\n        const NUM_TICKS = ticks.length;\n        writeTask(() => ticks.forEach(el => el.style.setProperty('animation', 'none')));\n        _this3.scrollListenerCallback = () => {\n          // If pointer is not on screen or refresher is not active, ignore scroll\n          if (!_this3.pointerDown && _this3.state === 1 /* RefresherState.Inactive */) {\n            return;\n          }\n          readTask(() => {\n            // PTR should only be active when overflow scrolling at the top\n            const scrollTop = _this3.scrollEl.scrollTop;\n            const refresherHeight = _this3.el.clientHeight;\n            if (scrollTop > 0) {\n              /**\n               * If refresher is refreshing and user tries to scroll\n               * progressively fade refresher out/in\n               */\n              if (_this3.state === 8 /* RefresherState.Refreshing */) {\n                const ratio = clamp(0, scrollTop / (refresherHeight * 0.5), 1);\n                writeTask(() => setSpinnerOpacity(refreshingSpinner, 1 - ratio));\n                return;\n              }\n              return;\n            }\n            if (_this3.pointerDown) {\n              if (!_this3.didStart) {\n                _this3.didStart = true;\n                _this3.ionStart.emit();\n              }\n              // emit \"pulling\" on every move\n              if (_this3.pointerDown) {\n                _this3.ionPull.emit();\n              }\n            }\n            /**\n             * We want to delay the start of this gesture by ~30px\n             * when initially pulling down so the refresher does not\n             * overlap with the content. But when letting go of the\n             * gesture before the refresher completes, we want the\n             * refresher tick marks to quickly fade out.\n             */\n            const offset = _this3.didStart ? 30 : 0;\n            const pullAmount = _this3.progress = clamp(0, (Math.abs(scrollTop) - offset) / MAX_PULL, 1);\n            const shouldShowRefreshingSpinner = _this3.state === 8 /* RefresherState.Refreshing */ || pullAmount === 1;\n            if (shouldShowRefreshingSpinner) {\n              if (_this3.pointerDown) {\n                handleScrollWhileRefreshing(refreshingSpinner, _this3.lastVelocityY);\n              }\n              if (!_this3.didRefresh) {\n                _this3.beginRefresh();\n                _this3.didRefresh = true;\n                hapticImpact({\n                  style: ImpactStyle.Light\n                });\n                /**\n                 * Translate the content element otherwise when pointer is removed\n                 * from screen the scroll content will bounce back over the refresher\n                 */\n                if (!_this3.pointerDown) {\n                  translateElement(_this3.elementToTransform, `${refresherHeight}px`);\n                }\n              }\n            } else {\n              _this3.state = 2 /* RefresherState.Pulling */;\n              handleScrollWhilePulling(ticks, NUM_TICKS, pullAmount);\n            }\n          });\n        };\n        _this3.scrollEl.addEventListener('scroll', _this3.scrollListenerCallback);\n        _this3.gesture = (yield import('./index-39782642.js')).createGesture({\n          el: _this3.scrollEl,\n          gestureName: 'refresher',\n          gesturePriority: 31,\n          direction: 'y',\n          threshold: 5,\n          onStart: () => {\n            _this3.pointerDown = true;\n            if (!_this3.didRefresh) {\n              translateElement(_this3.elementToTransform, '0px');\n            }\n            /**\n             * If the content had `display: none` when\n             * the refresher was initialized, its clientHeight\n             * will be 0. When the gesture starts, the content\n             * will be visible, so try to get the correct\n             * client height again. This is most common when\n             * using the refresher in an ion-menu.\n             */\n            if (MAX_PULL === 0) {\n              MAX_PULL = _this3.scrollEl.clientHeight * 0.16;\n            }\n          },\n          onMove: ev => {\n            _this3.lastVelocityY = ev.velocityY;\n          },\n          onEnd: () => {\n            _this3.pointerDown = false;\n            _this3.didStart = false;\n            if (_this3.needsCompletion) {\n              _this3.resetNativeRefresher(_this3.elementToTransform, 32 /* RefresherState.Completing */);\n              _this3.needsCompletion = false;\n            } else if (_this3.didRefresh) {\n              readTask(() => translateElement(_this3.elementToTransform, `${_this3.el.clientHeight}px`));\n            }\n          }\n        });\n        _this3.disabledChanged();\n      })();\n    }\n    setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        const circle = getElementRoot(pullingSpinner).querySelector('circle');\n        const pullingRefresherIcon = _this4.el.querySelector('ion-refresher-content .refresher-pulling-icon');\n        const refreshingCircle = getElementRoot(refreshingSpinner).querySelector('circle');\n        if (circle !== null && refreshingCircle !== null) {\n          writeTask(() => {\n            circle.style.setProperty('animation', 'none');\n            // This lines up the animation on the refreshing spinner with the pulling spinner\n            refreshingSpinner.style.setProperty('animation-delay', '-655ms');\n            refreshingCircle.style.setProperty('animation-delay', '-655ms');\n          });\n        }\n        _this4.gesture = (yield import('./index-39782642.js')).createGesture({\n          el: _this4.scrollEl,\n          gestureName: 'refresher',\n          gesturePriority: 31,\n          direction: 'y',\n          threshold: 5,\n          canStart: () => _this4.state !== 8 /* RefresherState.Refreshing */ && _this4.state !== 32 /* RefresherState.Completing */ && _this4.scrollEl.scrollTop === 0,\n          onStart: ev => {\n            _this4.progress = 0;\n            ev.data = {\n              animation: undefined,\n              didStart: false,\n              cancelled: false\n            };\n          },\n          onMove: ev => {\n            if (ev.velocityY < 0 && _this4.progress === 0 && !ev.data.didStart || ev.data.cancelled) {\n              ev.data.cancelled = true;\n              return;\n            }\n            if (!ev.data.didStart) {\n              ev.data.didStart = true;\n              _this4.state = 2 /* RefresherState.Pulling */;\n              // When ion-refresher is being used with a custom scroll target, the overflow styles need to be applied directly instead of via a css variable\n              const {\n                scrollEl\n              } = _this4;\n              const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n              writeTask(() => scrollEl.style.setProperty(overflowProperty, 'hidden'));\n              const animationType = getRefresherAnimationType(contentEl);\n              const animation = createPullingAnimation(animationType, pullingRefresherIcon, _this4.el);\n              ev.data.animation = animation;\n              animation.progressStart(false, 0);\n              _this4.ionStart.emit();\n              _this4.animations.push(animation);\n              return;\n            }\n            // Since we are using an easing curve, slow the gesture tracking down a bit\n            _this4.progress = clamp(0, ev.deltaY / 180 * 0.5, 1);\n            ev.data.animation.progressStep(_this4.progress);\n            _this4.ionPull.emit();\n          },\n          onEnd: ev => {\n            if (!ev.data.didStart) {\n              return;\n            }\n            _this4.gesture.enable(false);\n            const {\n              scrollEl\n            } = _this4;\n            const overflowProperty = scrollEl.matches(ION_CONTENT_CLASS_SELECTOR) ? 'overflow' : '--overflow';\n            writeTask(() => scrollEl.style.removeProperty(overflowProperty));\n            if (_this4.progress <= 0.4) {\n              ev.data.animation.progressEnd(0, _this4.progress, 500).onFinish(() => {\n                _this4.animations.forEach(ani => ani.destroy());\n                _this4.animations = [];\n                _this4.gesture.enable(true);\n                _this4.state = 1 /* RefresherState.Inactive */;\n              });\n              return;\n            }\n            const progress = getTimeGivenProgression([0, 0], [0, 0], [1, 1], [1, 1], _this4.progress)[0];\n            const snapBackAnimation = createSnapBackAnimation(pullingRefresherIcon);\n            _this4.animations.push(snapBackAnimation);\n            writeTask(/*#__PURE__*/_asyncToGenerator(function* () {\n              pullingRefresherIcon.style.setProperty('--ion-pulling-refresher-translate', `${progress * 100}px`);\n              ev.data.animation.progressEnd();\n              yield snapBackAnimation.play();\n              _this4.beginRefresh();\n              ev.data.animation.destroy();\n              _this4.gesture.enable(true);\n            }));\n          }\n        });\n        _this4.disabledChanged();\n      })();\n    }\n    setupNativeRefresher(contentEl) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        if (_this5.scrollListenerCallback || !contentEl || _this5.nativeRefresher || !_this5.scrollEl) {\n          return;\n        }\n        /**\n         * If using non-native refresher before make sure\n         * we clean up any old CSS. This can happen when\n         * a user manually calls the refresh method in a\n         * component create callback before the native\n         * refresher is setup.\n         */\n        _this5.setCss(0, '', false, '');\n        _this5.nativeRefresher = true;\n        const pullingSpinner = _this5.el.querySelector('ion-refresher-content .refresher-pulling ion-spinner');\n        const refreshingSpinner = _this5.el.querySelector('ion-refresher-content .refresher-refreshing ion-spinner');\n        if (getIonMode(_this5) === 'ios') {\n          _this5.setupiOSNativeRefresher(pullingSpinner, refreshingSpinner);\n        } else {\n          _this5.setupMDNativeRefresher(contentEl, pullingSpinner, refreshingSpinner);\n        }\n      })();\n    }\n    componentDidUpdate() {\n      this.checkNativeRefresher();\n    }\n    connectedCallback() {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        if (_this6.el.getAttribute('slot') !== 'fixed') {\n          console.error('Make sure you use: <ion-refresher slot=\"fixed\">');\n          return;\n        }\n        const contentEl = _this6.el.closest(ION_CONTENT_ELEMENT_SELECTOR);\n        if (!contentEl) {\n          printIonContentErrorMsg(_this6.el);\n          return;\n        }\n        /**\n         * Waits for the content to be ready before querying the scroll\n         * or the background content element.\n         */\n        componentOnReady(contentEl, /*#__PURE__*/_asyncToGenerator(function* () {\n          const customScrollTarget = contentEl.querySelector(ION_CONTENT_CLASS_SELECTOR);\n          /**\n           * Query the custom scroll target (if available), first. In refresher implementations,\n           * the ion-refresher element will always be a direct child of ion-content (slot=\"fixed\"). By\n           * querying the custom scroll target first and falling back to the ion-content element,\n           * the correct scroll element will be returned by the implementation.\n           */\n          _this6.scrollEl = yield getScrollElement(customScrollTarget !== null && customScrollTarget !== void 0 ? customScrollTarget : contentEl);\n          /**\n           * Query the background content element from the host ion-content element directly.\n           */\n          _this6.backgroundContentEl = yield contentEl.getBackgroundElement();\n          /**\n           * Check if the content element is fullscreen to apply the correct styles\n           * when the refresher is refreshing. Otherwise, the refresher will be\n           * hidden because it is positioned behind the background content element.\n           */\n          _this6.contentFullscreen = contentEl.fullscreen;\n          if (yield shouldUseNativeRefresher(_this6.el, getIonMode(_this6))) {\n            _this6.setupNativeRefresher(contentEl);\n          } else {\n            _this6.gesture = (yield import('./index-39782642.js')).createGesture({\n              el: contentEl,\n              gestureName: 'refresher',\n              gesturePriority: 31,\n              direction: 'y',\n              threshold: 20,\n              passive: false,\n              canStart: () => _this6.canStart(),\n              onStart: () => _this6.onStart(),\n              onMove: ev => _this6.onMove(ev),\n              onEnd: () => _this6.onEnd()\n            });\n            _this6.disabledChanged();\n          }\n        }));\n      })();\n    }\n    disconnectedCallback() {\n      this.destroyNativeRefresher();\n      this.scrollEl = undefined;\n      if (this.gesture) {\n        this.gesture.destroy();\n        this.gesture = undefined;\n      }\n    }\n    /**\n     * Call `complete()` when your async operation has completed.\n     * For example, the `refreshing` state is while the app is performing\n     * an asynchronous operation, such as receiving more data from an\n     * AJAX request. Once the data has been received, you then call this\n     * method to signify that the refreshing has completed and to close\n     * the refresher. This method also changes the refresher's state from\n     * `refreshing` to `completing`.\n     */\n    complete() {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        if (_this7.nativeRefresher) {\n          _this7.needsCompletion = true;\n          // Do not reset scroll el until user removes pointer from screen\n          if (!_this7.pointerDown) {\n            raf(() => raf(() => _this7.resetNativeRefresher(_this7.elementToTransform, 32 /* RefresherState.Completing */)));\n          }\n        } else {\n          _this7.close(32 /* RefresherState.Completing */, '120ms');\n        }\n      })();\n    }\n    /**\n     * Changes the refresher's state from `refreshing` to `cancelling`.\n     */\n    cancel() {\n      var _this8 = this;\n      return _asyncToGenerator(function* () {\n        if (_this8.nativeRefresher) {\n          // Do not reset scroll el until user removes pointer from screen\n          if (!_this8.pointerDown) {\n            raf(() => raf(() => _this8.resetNativeRefresher(_this8.elementToTransform, 16 /* RefresherState.Cancelling */)));\n          }\n        } else {\n          _this8.close(16 /* RefresherState.Cancelling */, '');\n        }\n      })();\n    }\n    /**\n     * A number representing how far down the user has pulled.\n     * The number `0` represents the user hasn't pulled down at all. The\n     * number `1`, and anything greater than `1`, represents that the user\n     * has pulled far enough down that when they let go then the refresh will\n     * happen. If they let go and the number is less than `1`, then the\n     * refresh will not happen, and the content will return to it's original\n     * position.\n     */\n    getProgress() {\n      return Promise.resolve(this.progress);\n    }\n    canStart() {\n      if (!this.scrollEl) {\n        return false;\n      }\n      if (this.state !== 1 /* RefresherState.Inactive */) {\n        return false;\n      }\n      // if the scrollTop is greater than zero then it's\n      // not possible to pull the content down yet\n      if (this.scrollEl.scrollTop > 0) {\n        return false;\n      }\n      return true;\n    }\n    onStart() {\n      this.progress = 0;\n      this.state = 1 /* RefresherState.Inactive */;\n      this.memoizeOverflowStyle();\n      /**\n       * If the content is fullscreen, then we need to\n       * set the offset-top style on the background content\n       * element to ensure that the refresher is shown.\n       */\n      if (this.contentFullscreen && this.backgroundContentEl) {\n        this.backgroundContentEl.style.setProperty('--offset-top', '0px');\n      }\n    }\n    onMove(detail) {\n      if (!this.scrollEl) {\n        return;\n      }\n      // this method can get called like a bazillion times per second,\n      // so it's built to be as efficient as possible, and does its\n      // best to do any DOM read/writes only when absolutely necessary\n      // if multi-touch then get out immediately\n      const ev = detail.event;\n      if (ev.touches !== undefined && ev.touches.length > 1) {\n        return;\n      }\n      // do nothing if it's actively refreshing\n      // or it's in the way of closing\n      // or this was never a startY\n      if ((this.state & 56 /* RefresherState._BUSY_ */) !== 0) {\n        return;\n      }\n      const pullFactor = Number.isNaN(this.pullFactor) || this.pullFactor < 0 ? 1 : this.pullFactor;\n      const deltaY = detail.deltaY * pullFactor;\n      // don't bother if they're scrolling up\n      // and have not already started dragging\n      if (deltaY <= 0) {\n        // the current Y is higher than the starting Y\n        // so they scrolled up enough to be ignored\n        this.progress = 0;\n        this.state = 1 /* RefresherState.Inactive */;\n        if (this.appliedStyles) {\n          // reset the styles only if they were applied\n          this.setCss(0, '', false, '');\n          return;\n        }\n        return;\n      }\n      if (this.state === 1 /* RefresherState.Inactive */) {\n        // this refresh is not already actively pulling down\n        // get the content's scrollTop\n        const scrollHostScrollTop = this.scrollEl.scrollTop;\n        // if the scrollTop is greater than zero then it's\n        // not possible to pull the content down yet\n        if (scrollHostScrollTop > 0) {\n          this.progress = 0;\n          return;\n        }\n        // content scrolled all the way to the top, and dragging down\n        this.state = 2 /* RefresherState.Pulling */;\n      }\n      // prevent native scroll events\n      if (ev.cancelable) {\n        ev.preventDefault();\n      }\n      // the refresher is actively pulling at this point\n      // move the scroll element within the content element\n      this.setCss(deltaY, '0ms', true, '');\n      if (deltaY === 0) {\n        // don't continue if there's no delta yet\n        this.progress = 0;\n        return;\n      }\n      const pullMin = this.pullMin;\n      // set pull progress\n      this.progress = deltaY / pullMin;\n      // emit \"start\" if it hasn't started yet\n      if (!this.didStart) {\n        this.didStart = true;\n        this.ionStart.emit();\n      }\n      // emit \"pulling\" on every move\n      this.ionPull.emit();\n      // do nothing if the delta is less than the pull threshold\n      if (deltaY < pullMin) {\n        // ensure it stays in the pulling state, cuz its not ready yet\n        this.state = 2 /* RefresherState.Pulling */;\n        return;\n      }\n      if (deltaY > this.pullMax) {\n        // they pulled farther than the max, so kick off the refresh\n        this.beginRefresh();\n        return;\n      }\n      // pulled farther than the pull min!!\n      // it is now in the `ready` state!!\n      // if they let go then it'll refresh, kerpow!!\n      this.state = 4 /* RefresherState.Ready */;\n      return;\n    }\n    onEnd() {\n      // only run in a zone when absolutely necessary\n      if (this.state === 4 /* RefresherState.Ready */) {\n        // they pulled down far enough, so it's ready to refresh\n        this.beginRefresh();\n      } else if (this.state === 2 /* RefresherState.Pulling */) {\n        // they were pulling down, but didn't pull down far enough\n        // set the content back to it's original location\n        // and close the refresher\n        // set that the refresh is actively cancelling\n        this.cancel();\n      } else if (this.state === 1 /* RefresherState.Inactive */) {\n        /**\n         * The pull to refresh gesture was aborted\n         * so we should immediately restore any overflow styles\n         * that have been modified. Do not call this.cancel\n         * because the styles will only be reset after a timeout.\n         * If the gesture is aborted then scrolling should be\n         * available right away.\n         */\n        this.restoreOverflowStyle();\n      }\n    }\n    beginRefresh() {\n      // assumes we're already back in a zone\n      // they pulled down far enough, so it's ready to refresh\n      this.state = 8 /* RefresherState.Refreshing */;\n      // place the content in a hangout position while it thinks\n      this.setCss(this.pullMin, this.snapbackDuration, true, '');\n      // emit \"refresh\" because it was pulled down far enough\n      // and they let go to begin refreshing\n      this.ionRefresh.emit({\n        complete: this.complete.bind(this)\n      });\n    }\n    close(state, delay) {\n      // create fallback timer incase something goes wrong with transitionEnd event\n      setTimeout(() => {\n        var _a;\n        this.state = 1 /* RefresherState.Inactive */;\n        this.progress = 0;\n        this.didStart = false;\n        /**\n         * Reset any overflow styles so the\n         * user can scroll again.\n         */\n        this.setCss(0, '0ms', false, '', true);\n        /**\n         * Reset the offset-top style on the background content\n         * when the refresher is no longer refreshing and the\n         * content is fullscreen.\n         *\n         * This ensures that the behavior of background content\n         * does not change when refreshing is complete.\n         */\n        if (this.contentFullscreen && this.backgroundContentEl) {\n          (_a = this.backgroundContentEl) === null || _a === void 0 ? void 0 : _a.style.removeProperty('--offset-top');\n        }\n      }, 600);\n      // reset the styles on the scroll element\n      // set that the refresh is actively cancelling/completing\n      this.state = state;\n      this.setCss(0, this.closeDuration, true, delay);\n    }\n    setCss(y, duration, overflowVisible, delay, shouldRestoreOverflowStyle = false) {\n      if (this.nativeRefresher) {\n        return;\n      }\n      this.appliedStyles = y > 0;\n      writeTask(() => {\n        if (this.scrollEl && this.backgroundContentEl) {\n          const scrollStyle = this.scrollEl.style;\n          const backgroundStyle = this.backgroundContentEl.style;\n          scrollStyle.transform = backgroundStyle.transform = y > 0 ? `translateY(${y}px) translateZ(0px)` : '';\n          scrollStyle.transitionDuration = backgroundStyle.transitionDuration = duration;\n          scrollStyle.transitionDelay = backgroundStyle.transitionDelay = delay;\n          scrollStyle.overflow = overflowVisible ? 'hidden' : '';\n        }\n        /**\n         * Reset the overflow styles only once\n         * the pull to refresh effect has been closed.\n         * This ensures that the gesture is done\n         * and the refresh operation has either\n         * been aborted or has completed.\n         */\n        if (shouldRestoreOverflowStyle) {\n          this.restoreOverflowStyle();\n        }\n      });\n    }\n    memoizeOverflowStyle() {\n      if (this.scrollEl) {\n        const {\n          overflow,\n          overflowX,\n          overflowY\n        } = this.scrollEl.style;\n        this.overflowStyles = {\n          overflow: overflow !== null && overflow !== void 0 ? overflow : '',\n          overflowX: overflowX !== null && overflowX !== void 0 ? overflowX : '',\n          overflowY: overflowY !== null && overflowY !== void 0 ? overflowY : ''\n        };\n      }\n    }\n    restoreOverflowStyle() {\n      if (this.overflowStyles !== undefined && this.scrollEl !== undefined) {\n        const {\n          overflow,\n          overflowX,\n          overflowY\n        } = this.overflowStyles;\n        this.scrollEl.style.overflow = overflow;\n        this.scrollEl.style.overflowX = overflowX;\n        this.scrollEl.style.overflowY = overflowY;\n        this.overflowStyles = undefined;\n      }\n    }\n    render() {\n      const mode = getIonMode(this);\n      return h(Host, {\n        key: '4c36bffbfa32ef1bd28a60f4455c125842880659',\n        slot: \"fixed\",\n        class: {\n          [mode]: true,\n          // Used internally for styling\n          [`refresher-${mode}`]: true,\n          'refresher-native': this.nativeRefresher,\n          'refresher-active': this.state !== 1 /* RefresherState.Inactive */,\n          'refresher-pulling': this.state === 2 /* RefresherState.Pulling */,\n          'refresher-ready': this.state === 4 /* RefresherState.Ready */,\n          'refresher-refreshing': this.state === 8 /* RefresherState.Refreshing */,\n          'refresher-cancelling': this.state === 16 /* RefresherState.Cancelling */,\n          'refresher-completing': this.state === 32 /* RefresherState.Completing */\n        }\n      });\n    }\n    get el() {\n      return getElement(this);\n    }\n    static get watchers() {\n      return {\n        \"disabled\": [\"disabledChanged\"]\n      };\n    }\n  };\n  Refresher.style = {\n    ios: IonRefresherIosStyle0,\n    md: IonRefresherMdStyle0\n  };\n  return Refresher;\n})();\nconst RefresherContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.pullingIcon = undefined;\n    this.pullingText = undefined;\n    this.refreshingSpinner = undefined;\n    this.refreshingText = undefined;\n  }\n  componentWillLoad() {\n    if (this.pullingIcon === undefined) {\n      /**\n       * The native iOS refresher uses a spinner instead of\n       * an icon, so we need to see if this device supports\n       * the native iOS refresher.\n       */\n      const hasRubberBandScrolling = supportsRubberBandScrolling();\n      const mode = getIonMode(this);\n      const overflowRefresher = hasRubberBandScrolling ? 'lines' : arrowDown;\n      this.pullingIcon = config.get('refreshingIcon', mode === 'ios' && hasRubberBandScrolling ? config.get('spinner', overflowRefresher) : 'circular');\n    }\n    if (this.refreshingSpinner === undefined) {\n      const mode = getIonMode(this);\n      this.refreshingSpinner = config.get('refreshingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'circular'));\n    }\n  }\n  renderPullingText() {\n    const {\n      customHTMLEnabled,\n      pullingText\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"refresher-pulling-text\",\n        innerHTML: sanitizeDOMString(pullingText)\n      });\n    }\n    return h(\"div\", {\n      class: \"refresher-pulling-text\"\n    }, pullingText);\n  }\n  renderRefreshingText() {\n    const {\n      customHTMLEnabled,\n      refreshingText\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"refresher-refreshing-text\",\n        innerHTML: sanitizeDOMString(refreshingText)\n      });\n    }\n    return h(\"div\", {\n      class: \"refresher-refreshing-text\"\n    }, refreshingText);\n  }\n  render() {\n    const pullingIcon = this.pullingIcon;\n    const hasSpinner = pullingIcon != null && SPINNERS[pullingIcon] !== undefined;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'fb78d7e31f8feb31025e58903eb9de85cb928dbd',\n      class: mode\n    }, h(\"div\", {\n      key: '23f67800f09765ef8fde8cf85a843e19e667f337',\n      class: \"refresher-pulling\"\n    }, this.pullingIcon && hasSpinner && h(\"div\", {\n      key: '5a08d3b69762f8b51dcd3dcfbaf3fddb707257fa',\n      class: \"refresher-pulling-icon\"\n    }, h(\"div\", {\n      key: '42a613b029e092acdff7fe613a429375d89f157e',\n      class: \"spinner-arrow-container\"\n    }, h(\"ion-spinner\", {\n      key: '2f9cdc75938c4d306de7a717ed67901daef71c2c',\n      name: this.pullingIcon,\n      paused: true\n    }), mode === 'md' && this.pullingIcon === 'circular' && h(\"div\", {\n      key: '1f8a6347b4a46417ba55286a79f1a41f04bf9c91',\n      class: \"arrow-container\"\n    }, h(\"ion-icon\", {\n      key: '326713d11d482d420ba5a739ff4528400a37e9ca',\n      icon: caretBackSharp,\n      \"aria-hidden\": \"true\"\n    })))), this.pullingIcon && !hasSpinner && h(\"div\", {\n      key: 'ab18c7cbea7bcbfa034f90f317652af4d93660ed',\n      class: \"refresher-pulling-icon\"\n    }, h(\"ion-icon\", {\n      key: 'f488acd54acc8a61b6c5a279f0d7f9a437c370c0',\n      icon: this.pullingIcon,\n      lazy: false,\n      \"aria-hidden\": \"true\"\n    })), this.pullingText !== undefined && this.renderPullingText()), h(\"div\", {\n      key: '914ad6139442dac53af47120ea821fa11c309a38',\n      class: \"refresher-refreshing\"\n    }, this.refreshingSpinner && h(\"div\", {\n      key: '7eba732f5e2d72b90399d68a3e89617d8979b3d1',\n      class: \"refresher-refreshing-icon\"\n    }, h(\"ion-spinner\", {\n      key: '838d66d8bef6f56622c62b1068e7fed29e094302',\n      name: this.refreshingSpinner\n    })), this.refreshingText !== undefined && this.renderRefreshingText()));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nexport { Refresher as ion_refresher, RefresherContent as ion_refresher_content };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}