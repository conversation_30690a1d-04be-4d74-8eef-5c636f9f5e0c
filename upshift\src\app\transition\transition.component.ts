import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-transition',
  templateUrl: './transition.component.html',
  styleUrls: ['./transition.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class TransitionComponent implements OnInit {
  @ViewChild('videoPlayer') videoPlayer!: ElementRef<HTMLVideoElement>;

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    const video = this.videoPlayer.nativeElement;

    // Start playing the video
    video.play();

    // After 2 seconds, start speeding up and zooming in rapidly
    setTimeout(() => {
      this.startSpeedingUp(video);
    }, 2000);

    // Listen for the end of the video
    video.addEventListener('ended', () => {
      this.navigateToOnboarding();
    });
  }

  startSpeedingUp(video: HTMLVideoElement): void {
    let playbackRate = 1.0;
    let scale = 1.0;

    // Speed up and zoom in rapidly
    const interval = setInterval(() => {
      playbackRate += 0.3; // Increase speed more quickly
      scale += 0.15;      // Zoom in more quickly

      if (playbackRate <= 5.0) { // Allow higher max speed
        video.playbackRate = playbackRate;
        video.style.transform = `scale(${scale})`;
        // No need to change position - we'll handle centering in CSS
      } else {
        clearInterval(interval);
        // Add flash effect before navigating
        this.addFlashEffect();
      }
    }, 50); // Shorter interval for faster acceleration
  }

  addFlashEffect(): void {
    // Create flash element
    const flash = document.createElement('div');
    flash.className = 'flash-effect';
    document.body.appendChild(flash);

    // Trigger the flash animation
    setTimeout(() => {
      flash.style.opacity = '1';

      // Navigate after flash reaches peak brightness
      setTimeout(() => {
        this.navigateToOnboarding();
      }, 150);
    }, 10);
  }

  navigateToOnboarding(): void {
    // Navigate to onboarding page
    this.router.navigate(['/onboarding']);
  }
}