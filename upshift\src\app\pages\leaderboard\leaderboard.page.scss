:host {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --accent-color-rgb: 65, 105, 225;
  --card-bg: #1C1C1E;
  --card-border: #2C2C2E;
  --gold: #FFD700;
  --silver: #C0C0C0;
  --bronze: #CD7F32;

  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
  display: block;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
  padding-bottom: 120px !important; /* Account for navigation bar */
  color: var(--text-color);
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.leaderboard-container {
  margin-bottom: 80px;
}

.back-link {
  display: inline-block;
  margin-bottom: 20px;
  color: var(--accent-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.leaderboard-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--card-border);
}

.leaderboard-tab {
  flex: 1;
  padding: 12px;
  background-color: transparent;
  border: none;
  color: var(--secondary-text);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s, border-bottom 0.2s;
  position: relative;
  text-decoration: none;
  text-align: center;
}

.leaderboard-tab.active {
  color: var(--accent-color);
}

.leaderboard-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--accent-color);
}

.leaderboard-content {
  display: none;
}

.leaderboard-content.active {
  display: block;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: var(--card-bg);
  border-radius: 12px;
  margin-bottom: 12px;
  border: 1px solid var(--card-border);
}

.current-user {
  background-color: rgba(var(--accent-color-rgb), 0.2);
  border: 2px solid var(--accent-color);
}

.rank {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--card-border);
  border-radius: 50%;
  font-weight: 600;
  margin-right: 16px;
}

.rank.top-1 {
  background-color: var(--gold);
  color: #000;
}

.rank.top-2 {
  background-color: var(--silver);
  color: #000;
}

.rank.top-3 {
  background-color: var(--bronze);
  color: #000;
}

.entity-icon {
  font-size: 24px;
  margin-right: 16px;
}

.entity-info {
  flex-grow: 1;
}

.entity-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.entity-level {
  font-size: 14px;
  color: var(--secondary-text);
  margin-bottom: 8px;
}

.entity-xp {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.xp-category {
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.no-results {
  text-align: center;
  padding: 30px;
  color: var(--secondary-text);
}

.clickable-user {
  cursor: pointer;
  transition: background-color 0.2s;
}

.clickable-user:hover {
  background-color: rgba(65, 105, 225, 0.1);
}
