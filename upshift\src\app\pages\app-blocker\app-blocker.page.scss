/* App Blocker Page Styles */
:root {
    --background-color: #0C0C0F;
    --text-color: #FFFFFF;
    --secondary-text: #8E8E93;
    --accent-color: #4169E1;
    --quest-bg: #1C1C1E;
    --quest-border: #2C2C2E;
    --active-date: #4169E1;
    --inactive-date: #2C2C2E;
    --card-bg: #1C1C1E;
    --border-color: #2C2C2E;
    --bg-color: #0C0C0F;
    --danger-color: #FF3B30;
    --success-color: #30D158;
}

:host {
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    display: block;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
    max-width: 480px;
    margin: 0 auto;
    padding: 20px;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    height: 24px;
}

.logo span {
    font-size: 20px;
    font-weight: 600;
}

h1 {
    font-size: 20px;
    font-weight: 600;
}

.app-blocker-container {
    margin-bottom: 80px;
}

.info-card {
    background-color: rgba(65, 105, 225, 0.1);
    border: 1px solid var(--accent-color);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
}

.info-icon {
    font-size: 24px;
    margin-right: 15px;
}

.info-content p {
    font-size: 14px;
    margin-bottom: 8px;
}

.info-content p:last-child {
    margin-bottom: 0;
}

.blocker-settings {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
}

h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
}

.setting-group {
    margin-bottom: 20px;
}

.toggle-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    border-radius: 24px;
    transition: .4s;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    border-radius: 50%;
    transition: .4s;
}

input:checked + .toggle-slider {
    background-color: var(--accent-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.setting-description {
    font-size: 12px;
    color: var(--secondary-text);
}

select, input[type="text"], input[type="time"] {
    width: 100%;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
    color: var(--text-color);
    font-size: 14px;
    margin-top: 5px;
}

.custom-schedule {
    background-color: rgba(28, 28, 30, 0.5);
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
}

h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
}

.time-range {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.time-input {
    flex: 1;
}

.days-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.day-checkbox {
    display: flex;
    align-items: center;
}

.day-checkbox input[type="checkbox"] {
    margin-right: 5px;
}

.blocked-items {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
}

.blocked-list {
    margin-bottom: 20px;
}

.blocked-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
}

.blocked-item:last-child {
    border-bottom: none;
}

.item-icon {
    font-size: 20px;
    margin-right: 15px;
    width: 30px;
    text-align: center;
}

.item-info {
    flex: 1;
}

.item-name {
    font-size: 16px;
    font-weight: 500;
}

.item-type {
    font-size: 12px;
    color: var(--secondary-text);
}

.remove-btn {
    background-color: rgba(255, 59, 48, 0.1);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
    border-radius: 8px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
}

.no-items {
    text-align: center;
    padding: 20px;
    color: var(--secondary-text);
}

.add-item-form {
    background-color: rgba(28, 28, 30, 0.5);
    border-radius: 8px;
    padding: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
}

.add-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
}

.extension-section {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
}

.extension-card {
    display: flex;
    align-items: center;
}

.extension-icon {
    font-size: 24px;
    margin-right: 15px;
}

.extension-info {
    flex: 1;
}

.extension-info h3 {
    font-size: 16px;
    margin-bottom: 5px;
}

.extension-info p {
    font-size: 12px;
    color: var(--secondary-text);
    margin-bottom: 5px;
}

.extension-status {
    font-size: 12px;
    font-weight: 500;
    color: var(--danger-color);
}

.extension-status.connected {
    color: var(--success-color);
}

.extension-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
}

.save-settings-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
}

/* Navigation Styles */
.main-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #121212;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    z-index: 1000;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 600px;
    margin: 0 auto;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #888;
    padding: 5px 0;
    transition: color 0.2s ease;
    width: 20%;
}

.nav-item:hover {
    color: #fff;
}

.nav-item.active {
    color: #4D7BFF;
    position: relative;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: #4D7BFF;
    border-radius: 50%;
}

.nav-icon {
    font-size: 18px;
    margin-bottom: 4px;
}

.nav-text {
    font-size: 12px;
    font-weight: 500;
}

/* Adjust container padding to account for navigation bar */
.container {
    padding-bottom: 120px !important;
}
