// This is a script to set a user as admin in Supabase
// Run this script using Node.js to set a user as admin

import { createClient } from '@supabase/supabase-js';

// Replace with your Supabase config
const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
const supabaseServiceRoleKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; // Replace with your service role key

// Initialize Supabase with service role key (admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Function to set a user as admin
async function setUserAsAdmin(userId: string) {
  try {
    // First, check if the user exists
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error finding user:', userError.message);
      return;
    }

    if (!user) {
      console.error(`User with ID ${userId} does not exist`);
      return;
    }

    console.log('Found user:', user);

    // Update the user's username to 'admin'
    const { data, error } = await supabase
      .from('profiles')
      .update({ username: 'admin' })
      .eq('id', userId);

    if (error) {
      console.error('Error setting user as admin:', error.message);
      return;
    }

    console.log(`User ${userId} username has been set to 'admin'`);

    // Get the updated user data
    const { data: updatedUser, error: updatedError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (updatedError) {
      console.error('Error getting updated user data:', updatedError.message);
      return;
    }

    console.log('Updated user data:', updatedUser);
  } catch (error) {
    console.error('Error setting user username to admin:', error);
  }
}

// Usage:
// Replace 'USER_ID' with the actual user ID you want to set as admin
// setUserAsAdmin('USER_ID');

// To run this script:
// 1. Replace YOUR_SUPABASE_SERVICE_ROLE_KEY with your actual Supabase service role key
// 2. Replace USER_ID with the ID of the user you want to set as admin
// 3. Run the script using Node.js:
//    $ npx ts-node set-admin.ts


// Note: The only way to make a user an admin is to set their username to 'admin'.
// No other method will grant admin privileges.
