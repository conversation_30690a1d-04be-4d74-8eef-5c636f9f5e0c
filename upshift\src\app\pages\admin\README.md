# Upshift Admin Interface

This admin interface allows users with admin privileges to manage Firestore data directly from the Upshift application.

## Features

- View and manage all Firestore collections
- Add, edit, and delete documents
- JSON editor for complex document structures
- Restricted access to users with admin privileges

## How to Access

The admin interface is only accessible to users with the username "admin". A user is considered an admin if:

1. Their `username` field in Firestore is set to exactly `'admin'`

No other criteria will grant admin access.

## Setting a User as Admin

To set a user as admin, you can use the provided script or update the user document directly in Firestore:

### Using the Script

1. Open `set-admin.ts` in the `admin` folder
2. Add your Firebase configuration
3. Replace `'USER_ID'` with the actual user ID
4. Run the script with: `npx ts-node set-admin.ts`

### Directly in Firestore

1. Go to the Firebase Console
2. Navigate to Firestore Database
3. Find the user document in the `users` collection
4. Add a field `role` with the value `'admin'`

## Using the Admin Interface

Once you have admin privileges, you can access the admin interface by:

1. Logging into the Upshift application
2. Clicking on the "Admin" link in the navigation bar (visible only to admin users)

### Managing Collections

- View all collections in the admin dashboard
- Click on a collection to view its documents
- Add new collections using the form at the bottom of the dashboard

### Managing Documents

- View all documents in a collection
- Add new documents using the "Add Document" button
- Edit documents by clicking the "Edit" button
- Delete documents by clicking the "Delete" button
- Use the JSON editor for complex document structures by clicking "Edit as JSON"

## Security

The admin interface is protected by:

1. Client-side guards that check for admin privileges
2. Server-side security rules in Firestore that should also be configured to restrict write access

Make sure to set up proper Firestore security rules to prevent unauthorized access to your data.
