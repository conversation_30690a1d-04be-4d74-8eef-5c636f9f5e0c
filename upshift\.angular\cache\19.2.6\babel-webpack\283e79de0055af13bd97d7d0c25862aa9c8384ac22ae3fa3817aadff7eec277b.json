{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement, c as createEvent } from './index-527b9e34.js';\nimport { r as raf, t as transitionEndAsync, a as addEventListener, b as removeEventListener, g as getElementRoot } from './helpers-78efeec3.js';\nimport { l as chevronDown } from './index-e2cf2ceb.js';\nimport { c as config, b as getIonMode } from './ionic-global-ca86cf32.js';\nimport { p as printIonWarning } from './index-738d7504.js';\nconst accordionIosCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}:host(.accordion-next) ::slotted(ion-item[slot=header]){--border-width:0.55px 0px 0.55px 0px}\";\nconst IonAccordionIosStyle0 = accordionIosCss;\nconst accordionMdCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}\";\nconst IonAccordionMdStyle0 = accordionMdCss;\nconst Accordion = class {\n  constructor(hostRef) {\n    var _this = this;\n    registerInstance(this, hostRef);\n    this.updateListener = () => this.updateState(false);\n    this.setItemDefaults = () => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      /**\n       * For a11y purposes, we make\n       * the ion-item a button so users\n       * can tab to it and use keyboard\n       * navigation to get around.\n       */\n      ionItem.button = true;\n      ionItem.detail = false;\n      /**\n       * By default, the lines in an\n       * item should be full here, but\n       * only do that if a user has\n       * not explicitly overridden them\n       */\n      if (ionItem.lines === undefined) {\n        ionItem.lines = 'full';\n      }\n    };\n    this.getSlottedHeaderIonItem = () => {\n      const {\n        headerEl\n      } = this;\n      if (!headerEl) {\n        return;\n      }\n      /**\n       * Get the first ion-item\n       * slotted in the header slot\n       */\n      const slot = headerEl.querySelector('slot');\n      if (!slot) {\n        return;\n      }\n      // This is not defined in unit tests\n      if (slot.assignedElements === undefined) return;\n      return slot.assignedElements().find(el => el.tagName === 'ION-ITEM');\n    };\n    this.setAria = (expanded = false) => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      /**\n       * Get the native <button> element inside of\n       * ion-item because that is what will be focused\n       */\n      const root = getElementRoot(ionItem);\n      const button = root.querySelector('button');\n      if (!button) {\n        return;\n      }\n      button.setAttribute('aria-expanded', `${expanded}`);\n    };\n    this.slotToggleIcon = () => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      const {\n        toggleIconSlot,\n        toggleIcon\n      } = this;\n      /**\n       * Check if there already is a toggle icon.\n       * If so, do not add another one.\n       */\n      const existingToggleIcon = ionItem.querySelector('.ion-accordion-toggle-icon');\n      if (existingToggleIcon) {\n        return;\n      }\n      const iconEl = document.createElement('ion-icon');\n      iconEl.slot = toggleIconSlot;\n      iconEl.lazy = false;\n      iconEl.classList.add('ion-accordion-toggle-icon');\n      iconEl.icon = toggleIcon;\n      iconEl.setAttribute('aria-hidden', 'true');\n      ionItem.appendChild(iconEl);\n    };\n    this.expandAccordion = (initialUpdate = false) => {\n      const {\n        contentEl,\n        contentElWrapper\n      } = this;\n      if (initialUpdate || contentEl === undefined || contentElWrapper === undefined) {\n        this.state = 4 /* AccordionState.Expanded */;\n        return;\n      }\n      if (this.state === 4 /* AccordionState.Expanded */) {\n        return;\n      }\n      if (this.currentRaf !== undefined) {\n        cancelAnimationFrame(this.currentRaf);\n      }\n      if (this.shouldAnimate()) {\n        raf(() => {\n          this.state = 8 /* AccordionState.Expanding */;\n          this.currentRaf = raf(/*#__PURE__*/_asyncToGenerator(function* () {\n            const contentHeight = contentElWrapper.offsetHeight;\n            const waitForTransition = transitionEndAsync(contentEl, 2000);\n            contentEl.style.setProperty('max-height', `${contentHeight}px`);\n            yield waitForTransition;\n            _this.state = 4 /* AccordionState.Expanded */;\n            contentEl.style.removeProperty('max-height');\n          }));\n        });\n      } else {\n        this.state = 4 /* AccordionState.Expanded */;\n      }\n    };\n    this.collapseAccordion = (initialUpdate = false) => {\n      const {\n        contentEl\n      } = this;\n      if (initialUpdate || contentEl === undefined) {\n        this.state = 1 /* AccordionState.Collapsed */;\n        return;\n      }\n      if (this.state === 1 /* AccordionState.Collapsed */) {\n        return;\n      }\n      if (this.currentRaf !== undefined) {\n        cancelAnimationFrame(this.currentRaf);\n      }\n      if (this.shouldAnimate()) {\n        this.currentRaf = raf(/*#__PURE__*/_asyncToGenerator(function* () {\n          const contentHeight = contentEl.offsetHeight;\n          contentEl.style.setProperty('max-height', `${contentHeight}px`);\n          raf(/*#__PURE__*/_asyncToGenerator(function* () {\n            const waitForTransition = transitionEndAsync(contentEl, 2000);\n            _this.state = 2 /* AccordionState.Collapsing */;\n            yield waitForTransition;\n            _this.state = 1 /* AccordionState.Collapsed */;\n            contentEl.style.removeProperty('max-height');\n          }));\n        }));\n      } else {\n        this.state = 1 /* AccordionState.Collapsed */;\n      }\n    };\n    /**\n     * Helper function to determine if\n     * something should animate.\n     * If prefers-reduced-motion is set\n     * then we should not animate, regardless\n     * of what is set in the config.\n     */\n    this.shouldAnimate = () => {\n      if (typeof window === 'undefined') {\n        return false;\n      }\n      const prefersReducedMotion = matchMedia('(prefers-reduced-motion: reduce)').matches;\n      if (prefersReducedMotion) {\n        return false;\n      }\n      const animated = config.get('animated', true);\n      if (!animated) {\n        return false;\n      }\n      if (this.accordionGroupEl && !this.accordionGroupEl.animated) {\n        return false;\n      }\n      return true;\n    };\n    this.updateState = /*#__PURE__*/_asyncToGenerator(function* (initialUpdate = false) {\n      const accordionGroup = _this.accordionGroupEl;\n      const accordionValue = _this.value;\n      if (!accordionGroup) {\n        return;\n      }\n      const value = accordionGroup.value;\n      const shouldExpand = Array.isArray(value) ? value.includes(accordionValue) : value === accordionValue;\n      if (shouldExpand) {\n        _this.expandAccordion(initialUpdate);\n        _this.isNext = _this.isPrevious = false;\n      } else {\n        _this.collapseAccordion(initialUpdate);\n        /**\n         * When using popout or inset,\n         * the collapsed accordion items\n         * may need additional border radius\n         * applied. Check to see if the\n         * next or previous accordion is selected.\n         */\n        const nextAccordion = _this.getNextSibling();\n        const nextAccordionValue = nextAccordion === null || nextAccordion === void 0 ? void 0 : nextAccordion.value;\n        if (nextAccordionValue !== undefined) {\n          _this.isPrevious = Array.isArray(value) ? value.includes(nextAccordionValue) : value === nextAccordionValue;\n        }\n        const previousAccordion = _this.getPreviousSibling();\n        const previousAccordionValue = previousAccordion === null || previousAccordion === void 0 ? void 0 : previousAccordion.value;\n        if (previousAccordionValue !== undefined) {\n          _this.isNext = Array.isArray(value) ? value.includes(previousAccordionValue) : value === previousAccordionValue;\n        }\n      }\n    });\n    this.getNextSibling = () => {\n      if (!this.el) {\n        return;\n      }\n      const nextSibling = this.el.nextElementSibling;\n      if ((nextSibling === null || nextSibling === void 0 ? void 0 : nextSibling.tagName) !== 'ION-ACCORDION') {\n        return;\n      }\n      return nextSibling;\n    };\n    this.getPreviousSibling = () => {\n      if (!this.el) {\n        return;\n      }\n      const previousSibling = this.el.previousElementSibling;\n      if ((previousSibling === null || previousSibling === void 0 ? void 0 : previousSibling.tagName) !== 'ION-ACCORDION') {\n        return;\n      }\n      return previousSibling;\n    };\n    this.state = 1 /* AccordionState.Collapsed */;\n    this.isNext = false;\n    this.isPrevious = false;\n    this.value = `ion-accordion-${accordionIds++}`;\n    this.disabled = false;\n    this.readonly = false;\n    this.toggleIcon = chevronDown;\n    this.toggleIconSlot = 'end';\n  }\n  valueChanged() {\n    this.updateState();\n  }\n  connectedCallback() {\n    var _a;\n    const accordionGroupEl = this.accordionGroupEl = (_a = this.el) === null || _a === void 0 ? void 0 : _a.closest('ion-accordion-group');\n    if (accordionGroupEl) {\n      this.updateState(true);\n      addEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n    }\n  }\n  disconnectedCallback() {\n    const accordionGroupEl = this.accordionGroupEl;\n    if (accordionGroupEl) {\n      removeEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n    }\n  }\n  componentDidLoad() {\n    this.setItemDefaults();\n    this.slotToggleIcon();\n    /**\n     * We need to wait a tick because we\n     * just set ionItem.button = true and\n     * the button has not have been rendered yet.\n     */\n    raf(() => {\n      /**\n       * Set aria label on button inside of ion-item\n       * once the inner content has been rendered.\n       */\n      const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n      this.setAria(expanded);\n    });\n  }\n  toggleExpanded() {\n    const {\n      accordionGroupEl,\n      disabled,\n      readonly,\n      value,\n      state\n    } = this;\n    if (disabled || readonly) return;\n    if (accordionGroupEl) {\n      /**\n       * Because the accordion group may or may\n       * not allow multiple accordions open, we\n       * need to request the toggling of this\n       * accordion and the accordion group will\n       * make the decision on whether or not\n       * to allow it.\n       */\n      const expand = state === 1 /* AccordionState.Collapsed */ || state === 2 /* AccordionState.Collapsing */;\n      accordionGroupEl.requestAccordionToggle(value, expand);\n    }\n  }\n  render() {\n    const {\n      disabled,\n      readonly\n    } = this;\n    const mode = getIonMode(this);\n    const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n    const headerPart = expanded ? 'header expanded' : 'header';\n    const contentPart = expanded ? 'content expanded' : 'content';\n    this.setAria(expanded);\n    return h(Host, {\n      key: '073e1d02c18dcbc20c68648426e87c14750c031d',\n      class: {\n        [mode]: true,\n        'accordion-expanding': this.state === 8 /* AccordionState.Expanding */,\n        'accordion-expanded': this.state === 4 /* AccordionState.Expanded */,\n        'accordion-collapsing': this.state === 2 /* AccordionState.Collapsing */,\n        'accordion-collapsed': this.state === 1 /* AccordionState.Collapsed */,\n        'accordion-next': this.isNext,\n        'accordion-previous': this.isPrevious,\n        'accordion-disabled': disabled,\n        'accordion-readonly': readonly,\n        'accordion-animated': this.shouldAnimate()\n      }\n    }, h(\"div\", {\n      key: '9b4cf326de8bb6b4033992903c0c1bfd7eea9bcc',\n      onClick: () => this.toggleExpanded(),\n      id: \"header\",\n      part: headerPart,\n      \"aria-controls\": \"content\",\n      ref: headerEl => this.headerEl = headerEl\n    }, h(\"slot\", {\n      key: '464c32a37f64655eacf4218284214f5f30b14a1e',\n      name: \"header\"\n    })), h(\"div\", {\n      key: '8bb52e6a62d7de0106b253201a89a32e79d9a594',\n      id: \"content\",\n      part: contentPart,\n      role: \"region\",\n      \"aria-labelledby\": \"header\",\n      ref: contentEl => this.contentEl = contentEl\n    }, h(\"div\", {\n      key: '1d9dfd952ad493754aaeea7a8f625b33c2dd90a0',\n      id: \"content-wrapper\",\n      ref: contentElWrapper => this.contentElWrapper = contentElWrapper\n    }, h(\"slot\", {\n      key: '970dfbc55a612d739d0ca3b7b1a08e5c96d0c479',\n      name: \"content\"\n    }))));\n  }\n  static get delegatesFocus() {\n    return true;\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet accordionIds = 0;\nAccordion.style = {\n  ios: IonAccordionIosStyle0,\n  md: IonAccordionMdStyle0\n};\nconst accordionGroupIosCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){border-bottom:none}\";\nconst IonAccordionGroupIosStyle0 = accordionGroupIosCss;\nconst accordionGroupMdCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion){-webkit-box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;border-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous){border-end-end-radius:6px;border-end-start-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next){border-start-start-radius:6px;border-start-end-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion):first-of-type{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonAccordionGroupMdStyle0 = accordionGroupMdCss;\nconst AccordionGroup = /*#__PURE__*/(() => {\n  let AccordionGroup = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.ionChange = createEvent(this, \"ionChange\", 7);\n      this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n      this.animated = true;\n      this.multiple = undefined;\n      this.value = undefined;\n      this.disabled = false;\n      this.readonly = false;\n      this.expand = 'compact';\n    }\n    valueChanged() {\n      const {\n        value,\n        multiple\n      } = this;\n      if (!multiple && Array.isArray(value)) {\n        /**\n         * We do some processing on the `value` array so\n         * that it looks more like an array when logged to\n         * the console.\n         * Example given ['a', 'b']\n         * Default toString() behavior: a,b\n         * Custom behavior: ['a', 'b']\n         */\n        printIonWarning(`ion-accordion-group was passed an array of values, but multiple=\"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map(v => `'${v}'`).join(', ')}]\n`, this.el);\n      }\n      /**\n       * Do not use `value` here as that will be\n       * not account for the adjustment we make above.\n       */\n      this.ionValueChange.emit({\n        value: this.value\n      });\n    }\n    disabledChanged() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        const {\n          disabled\n        } = _this2;\n        const accordions = yield _this2.getAccordions();\n        for (const accordion of accordions) {\n          accordion.disabled = disabled;\n        }\n      })();\n    }\n    readonlyChanged() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        const {\n          readonly\n        } = _this3;\n        const accordions = yield _this3.getAccordions();\n        for (const accordion of accordions) {\n          accordion.readonly = readonly;\n        }\n      })();\n    }\n    onKeydown(ev) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        const activeElement = document.activeElement;\n        if (!activeElement) {\n          return;\n        }\n        /**\n         * Make sure focus is in the header, not the body, of the accordion. This ensures\n         * that if there are any interactable elements in the body, their keyboard\n         * interaction doesn't get stolen by the accordion. Example: using up/down keys\n         * in ion-textarea.\n         */\n        const activeAccordionHeader = activeElement.closest('ion-accordion [slot=\"header\"]');\n        if (!activeAccordionHeader) {\n          return;\n        }\n        const accordionEl = activeElement.tagName === 'ION-ACCORDION' ? activeElement : activeElement.closest('ion-accordion');\n        if (!accordionEl) {\n          return;\n        }\n        const closestGroup = accordionEl.closest('ion-accordion-group');\n        if (closestGroup !== _this4.el) {\n          return;\n        }\n        // If the active accordion is not in the current array of accordions, do not do anything\n        const accordions = yield _this4.getAccordions();\n        const startingIndex = accordions.findIndex(a => a === accordionEl);\n        if (startingIndex === -1) {\n          return;\n        }\n        let accordion;\n        if (ev.key === 'ArrowDown') {\n          accordion = _this4.findNextAccordion(accordions, startingIndex);\n        } else if (ev.key === 'ArrowUp') {\n          accordion = _this4.findPreviousAccordion(accordions, startingIndex);\n        } else if (ev.key === 'Home') {\n          accordion = accordions[0];\n        } else if (ev.key === 'End') {\n          accordion = accordions[accordions.length - 1];\n        }\n        if (accordion !== undefined && accordion !== activeElement) {\n          accordion.focus();\n        }\n      })();\n    }\n    componentDidLoad() {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        if (_this5.disabled) {\n          _this5.disabledChanged();\n        }\n        if (_this5.readonly) {\n          _this5.readonlyChanged();\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        _this5.valueChanged();\n      })();\n    }\n    /**\n     * Sets the value property and emits ionChange.\n     * This should only be called when the user interacts\n     * with the accordion and not for any update\n     * to the value property. The exception is when\n     * the app sets the value of a single-select\n     * accordion group to an array.\n     */\n    setValue(accordionValue) {\n      const value = this.value = accordionValue;\n      this.ionChange.emit({\n        value\n      });\n    }\n    /**\n     * This method is used to ensure that the value\n     * of ion-accordion-group is being set in a valid\n     * way. This method should only be called in\n     * response to a user generated action.\n     * @internal\n     */\n    requestAccordionToggle(accordionValue, accordionExpand) {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        const {\n          multiple,\n          value,\n          readonly,\n          disabled\n        } = _this6;\n        if (readonly || disabled) {\n          return;\n        }\n        if (accordionExpand) {\n          /**\n           * If group accepts multiple values\n           * check to see if value is already in\n           * in values array. If not, add it\n           * to the array.\n           */\n          if (multiple) {\n            const groupValue = value !== null && value !== void 0 ? value : [];\n            const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n            const valueExists = processedValue.find(v => v === accordionValue);\n            if (valueExists === undefined && accordionValue !== undefined) {\n              _this6.setValue([...processedValue, accordionValue]);\n            }\n          } else {\n            _this6.setValue(accordionValue);\n          }\n        } else {\n          /**\n           * If collapsing accordion, either filter the value\n           * out of the values array or unset the value.\n           */\n          if (multiple) {\n            const groupValue = value !== null && value !== void 0 ? value : [];\n            const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n            _this6.setValue(processedValue.filter(v => v !== accordionValue));\n          } else {\n            _this6.setValue(undefined);\n          }\n        }\n      })();\n    }\n    findNextAccordion(accordions, startingIndex) {\n      const nextAccordion = accordions[startingIndex + 1];\n      if (nextAccordion === undefined) {\n        return accordions[0];\n      }\n      return nextAccordion;\n    }\n    findPreviousAccordion(accordions, startingIndex) {\n      const prevAccordion = accordions[startingIndex - 1];\n      if (prevAccordion === undefined) {\n        return accordions[accordions.length - 1];\n      }\n      return prevAccordion;\n    }\n    /**\n     * @internal\n     */\n    getAccordions() {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        return Array.from(_this7.el.querySelectorAll(':scope > ion-accordion'));\n      })();\n    }\n    render() {\n      const {\n        disabled,\n        readonly,\n        expand\n      } = this;\n      const mode = getIonMode(this);\n      return h(Host, {\n        key: '82f3e77066fabb4736638ee4c487ad56efd39c63',\n        class: {\n          [mode]: true,\n          'accordion-group-disabled': disabled,\n          'accordion-group-readonly': readonly,\n          [`accordion-group-expand-${expand}`]: true\n        },\n        role: \"presentation\"\n      }, h(\"slot\", {\n        key: 'a3c791ea887fc640b512f81d429be465ae902b3d'\n      }));\n    }\n    get el() {\n      return getElement(this);\n    }\n    static get watchers() {\n      return {\n        \"value\": [\"valueChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"readonly\": [\"readonlyChanged\"]\n      };\n    }\n  };\n  AccordionGroup.style = {\n    ios: IonAccordionGroupIosStyle0,\n    md: IonAccordionGroupMdStyle0\n  };\n  return AccordionGroup;\n})();\nexport { Accordion as ion_accordion, AccordionGroup as ion_accordion_group };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}