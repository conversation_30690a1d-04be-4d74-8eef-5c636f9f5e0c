export type QuestType = 'build' | 'quit';
export type QuestPeriod = 'day' | 'week' | 'month';
export type QuestPriority = 'basic' | 'high';
export type QuestCategory = 'money' | 'health' | 'strength' | 'knowledge';
export type QuestGoalUnit = 'count' | 'steps' | 'm' | 'km' | 'sec' | 'min' | 'hr' | 'Cal' | 'g' | 'mg' | 'drink' | 'time' | 'pages' | 'books' | '%' | '€' | '$' | '£';

export interface Quest {
  id?: string; // Firestore ID
  name: string;
  description?: string;
  active: boolean;
  quest_type: QuestType;

  // User relationship
  user_id: string;
  streak: number;

  // Goal settings
  goal_value: number;
  goal_unit: QuestGoalUnit;
  goal_period: QuestPeriod;
  priority: QuestPriority;
  category: QuestCategory;

  // Period-specific settings
  task_days_of_week?: string; // "Mon,Tue,Wed"
  task_days_of_month?: string; // "1,15,28"
  custom_reminder_times?: string; // "09:00, 10:00, 11:00"

  // Additional fields
  created_at: Date;
  emoji: string;
}

export interface QuestProgress {
  id?: string; // Supabase ID
  user_id: string;
  quest_id: string;
  date: string | Date; // Can be a string (YYYY-MM-DD) or Date object
  completed: boolean;
  value_achieved: number;
}
