export interface Activity {
  id: string;
  day_tracking_id: string;
  name: string;
  emoji: string;
  hours: number;
  minutes: number;
  is_custom: boolean;
  user_id?: string; // For backward compatibility
  date?: string; // For backward compatibility
}

export interface ActivityType {
  id: string;
  name: string;
  emoji: string;
  is_active: boolean;
  order?: number;
  color?: string;
  is_default?: boolean;
}

export interface DayTracking {
  id: string;
  user_id: string;
  date: string;
  total_hours?: number;
  total_minutes?: number;
  mood_score?: number;
  notes?: string;
}
