{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, e as Host, f as getElement } from './index-527b9e34.js';\nimport { p as printIonWarning } from './index-738d7504.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { x as eye, y as eyeOff } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-ca86cf32.js';\nconst iosInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleIosStyle0 = iosInputPasswordToggleCss;\nconst mdInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleMdStyle0 = mdInputPasswordToggleCss;\nconst InputPasswordToggle = /*#__PURE__*/(() => {\n  let InputPasswordToggle = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.togglePasswordVisibility = () => {\n        const {\n          inputElRef\n        } = this;\n        if (!inputElRef) {\n          return;\n        }\n        inputElRef.type = inputElRef.type === 'text' ? 'password' : 'text';\n      };\n      this.color = undefined;\n      this.showIcon = undefined;\n      this.hideIcon = undefined;\n      this.type = 'password';\n    }\n    /**\n     * Whenever the input type changes we need to re-run validation to ensure the password\n     * toggle is being used with the correct input type. If the application changes the type\n     * outside of this component we also need to re-render so the correct icon is shown.\n     */\n    onTypeChange(newValue) {\n      if (newValue !== 'text' && newValue !== 'password') {\n        printIonWarning(`ion-input-password-toggle only supports inputs of type \"text\" or \"password\". Input of type \"${newValue}\" is not compatible.`, this.el);\n        return;\n      }\n    }\n    connectedCallback() {\n      const {\n        el\n      } = this;\n      const inputElRef = this.inputElRef = el.closest('ion-input');\n      if (!inputElRef) {\n        printIonWarning('No ancestor ion-input found for ion-input-password-toggle. This component must be slotted inside of an ion-input.', el);\n        return;\n      }\n      /**\n       * Important: Set the type in connectedCallback because the default value\n       * of this.type may not always be accurate. Usually inputs have the \"password\" type\n       * but it is possible to have the input to initially have the \"text\" type. In that scenario\n       * the wrong icon will show briefly before switching to the correct icon. Setting the\n       * type here allows us to avoid that flicker.\n       */\n      this.type = inputElRef.type;\n    }\n    disconnectedCallback() {\n      this.inputElRef = null;\n    }\n    render() {\n      var _a, _b;\n      const {\n        color,\n        type\n      } = this;\n      const mode = getIonMode(this);\n      const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : eye;\n      const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : eyeOff;\n      const isPasswordVisible = type === 'text';\n      return h(Host, {\n        key: 'd9811e25bfeb2aa197352bb9be852e9e420739d5',\n        class: createColorClasses(color, {\n          [mode]: true\n        })\n      }, h(\"ion-button\", {\n        key: '1eaea1442b248fb2b8d61538b27274e647a07804',\n        mode: mode,\n        color: color,\n        fill: \"clear\",\n        shape: \"round\",\n        \"aria-checked\": isPasswordVisible ? 'true' : 'false',\n        \"aria-label\": \"show password\",\n        role: \"switch\",\n        type: \"button\",\n        onPointerDown: ev => {\n          /**\n           * This prevents mobile browsers from\n           * blurring the input when the password toggle\n           * button is activated.\n           */\n          ev.preventDefault();\n        },\n        onClick: this.togglePasswordVisibility\n      }, h(\"ion-icon\", {\n        key: '9c88de8f4631d9bde222ce2edf6950d639e04773',\n        slot: \"icon-only\",\n        \"aria-hidden\": \"true\",\n        icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon\n      })));\n    }\n    get el() {\n      return getElement(this);\n    }\n    static get watchers() {\n      return {\n        \"type\": [\"onTypeChange\"]\n      };\n    }\n  };\n  InputPasswordToggle.style = {\n    ios: IonInputPasswordToggleIosStyle0,\n    md: IonInputPasswordToggleMdStyle0\n  };\n  return InputPasswordToggle;\n})();\nexport { InputPasswordToggle as ion_input_password_toggle };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}