# Supabase Migrations

This directory contains SQL migration scripts for the Supabase database.

## How to Apply Migrations

1. Navigate to the Supabase dashboard
2. Go to the SQL Editor
3. Copy the content of the migration file you want to apply
4. Paste it into the SQL Editor
5. Click "Run" to execute the migration

## Available Migrations

- `20250505_update_group_join_requests_policy.sql`: Updates the Row Level Security (RLS) policies for the `group_join_requests` table to allow users to see join requests where they are the invitee.

## Troubleshooting

If you encounter issues with Row Level Security (RLS) policies:

1. Check the error messages in the browser console
2. Verify that the appropriate RLS policies are in place
3. Make sure the user has the necessary permissions to access the data
4. Apply the relevant migration script to fix the issue
