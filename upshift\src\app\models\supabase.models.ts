// Supabase database models

// User profile model
export interface UserProfile {
  id: string;                      // PK, FK to auth.users.id
  username: string;                // unique
  profile_picture?: string;        // nullable
  active: boolean;                 // default false
  strength_xp: number;             // default 0
  money_xp: number;                // default 0
  health_xp: number;               // default 0
  knowledge_xp: number;            // default 0
  level: number;                   // default 0
  title: string;                   // default '🥚 Beginner'
  bio: string;                     // default ''
  timezone: string;                // default 'UTC'
  friend_code?: string;            // unique, nullable
  friend_code_expiry?: Date;       // nullable
  plan?: string;                   // nullable
  start_of_current_plan?: Date;    // nullable
  end_of_current_plan?: Date;      // nullable
  auto_renew: boolean;             // default true
  start_of_sick_days?: Date;       // nullable
  end_of_sick_days?: Date;         // nullable
  sidequests_switch: boolean;      // default true
  show_celebration: boolean;       // default true
  celebration_name: string;        // default 'Another Day, Another W'
  celebration_description: string; // default "You've completed all your quests for today. Keep up the great work!"
  celebration_emoji?: string;      // nullable
  subscription_status: string;     // default 'email marketing'
}

// User badges model
export interface UserBadge {
  id: string;                      // PK
  user_id: string;                 // FK to profiles.id, unique
  badge_newbie?: boolean;          // default false
  badge_warrior?: boolean;         // default false
  badge_monk?: boolean;            // default false
  badge_nonchalant?: boolean;      // default false
  badge_hardcore?: boolean;        // default false
  badge_disciplined_machine?: boolean; // default false
  badge_high_performer?: boolean;  // default false
  badge_master_of_consistency?: boolean; // default false
  badge_peak_performer?: boolean;  // default false
  badge_elite_operator?: boolean;  // default false
  badge_indestructible?: boolean;  // default false
  badge_ultra_human?: boolean;     // default false
  badge_professional?: boolean;    // default false
  created_at: Date;                // default now()
  updated_at: Date;                // default now()
}

// Subscription history model
export interface SubscriptionHistory {
  id: string;                      // PK
  user_id: string;                 // FK to profiles.id
  plan: string;                    // ('monthly', 'yearly')
  start_date_of_subscription: Date;
  end_date_of_subscription?: Date; // nullable
  created: Date;                   // default now()
  updated: Date;                   // default now()
}

// Quest model
export interface Quest {
  id: string;                      // PK
  user_id: string;                 // FK to profiles.id nullable
  name: string;
  description?: string;            // nullable
  active: boolean;                 // default true
  quest_type: string;              // ('build', 'quit')
  streak: number;                  // default 0
  goal_value: number;              // default 1
  goal_unit: string;               // (steps, minutes, etc.)
  goal_period: string;             // ('day', 'week', 'month')
  priority: string;                // ('basic', 'high')
  category: string;                // ('money', 'health', 'strength', 'knowledge')
  task_days_of_week?: string;      // nullable
  task_days_of_month?: string;     // nullable
  custom_reminder_times?: string;  // nullable
  created_at: Date;                // default now()
  emoji: string;                   // default '🏆'
}

// Quest progress model
export interface QuestProgress {
  id: string;                      // PK
  user_id: string;                 // FK to profiles.id
  quest_id: string;                // FK to quests.id
  date: Date;
  completed: boolean;              // default false
  value_achieved: number;          // default 0
  historical_streak?: number;      // Not stored in DB, calculated on the fly
}

// Daily sidequest pool model
export interface DailySideQuestPool {
  id: string;                      // PK
  name: string;
  description?: string;            // nullable
  goal_value: number;              // default 1
  category: string;                // ('money', 'health', 'strength', 'knowledge')
  goal_unit: string;               // ('steps', 'minutes', etc.)
  active: boolean;                 // default true
  emoji: string;                   // default '🎯'
}

// User daily sidequests model
export interface UserDailySideQuest {
  id: string;                      // PK
  user_id: string;                 // FK to profiles.id
  current_quest_id: string;        // FK to daily_sidequest_pool.id
  streak: number;                  // default 0
  last_completed_date?: string | null;    // YYYY-MM-DD format, nullable
  date_assigned: string;           // YYYY-MM-DD format
  completed: boolean;              // default false
  value_achieved: number;          // default 0
  category: string;                // ('money', 'health', 'strength', 'knowledge')
  emoji: string;                   // default '🎯'
}

// Day tracking model
export interface DayTracking {
  id: string;                      // PK
  user_id: string;                 // FK to profiles.id
  date: Date;
}

// Activities model
export interface Activity {
  id: string;                      // PK
  day_tracking_id: string;         // FK to day_tracking.id
  name: string;
  emoji: string;                   // default '⚡'
  hours: number;                   // default 0
  minutes: number;                 // default 0
  is_custom: boolean;              // default false
}

// Activity types model
export interface ActivityType {
  id: string;                      // PK
  name: string;                    // unique
  emoji: string;
  is_active: boolean;              // default true
  order: number;                   // default 0
}

// Goals model
export interface Goal {
  id: string;                      // PK
  user_id: string;                 // FK to profiles.id nullable
  name: string;
  description?: string;            // nullable
  emoji: string;                   // default '🎯'
  start_date: Date;                // default now()
  end_date?: Date;                 // nullable
  goal_value: number;              // default 1
  current_value: number;           // default 0
  goal_unit: string;
  before_photo?: string;           // nullable
  after_photo?: string;            // nullable
}

// Microgoals model
export interface MicroGoal {
  id: string;                      // PK
  goal_id: string;                 // FK to goals.id
  title: string;
  completed: boolean;              // default false
  completed_at?: Date;             // nullable
}

// Goal journal entries model
export interface GoalJournalEntry {
  id: string;                      // PK
  goal_id: string;                 // FK to goals.id
  milestone_percentage: number;    // e.g., 20, 40, 60...
  content: string;
  created_at: Date;                // default now()
}

// Friends model
export interface Friend {
  id: string;                      // PK
  user_id: string;                 // FK to profiles.id
  friend_id: string;               // FK to profiles.id
  created: Date;                   // default now()
}

// Groups model
export interface Group {
  id: string;                      // PK
  name: string;
  emoji: string;                   // default '👥'
  admin_id: string;                // FK to profiles.id
  enable_sidequests: boolean;      // default true
  created: Date;                   // default now()
  timezone: string;                // default 'UTC'
  level: number;                   // default 0
  strength_xp: number;             // default 0
  money_xp: number;                // default 0
  health_xp: number;               // default 0
  knowledge_xp: number;            // default 0
  invitation_code?: string;        // nullable
  code_expiry?: Date;              // nullable
}

// Group members model
export interface GroupMember {
  id: string;                      // PK
  group_id: string;                // FK to groups.id
  user_id: string;                 // FK to profiles.id
  nickname: string;
  is_admin: boolean;               // default false
  joined_date: Date;               // default now()
}

// Group quests model
export interface GroupQuest {
  id: string;                      // PK
  group_id: string;                // FK to groups.id
  name: string;
  description?: string;            // nullable
  emoji: string;                   // default '🎯'
  category: string;
  priority: string;                // ('basic', 'high')
  quest_type: string;              // ('build', 'quit')
  goal_value: number;              // default 1
  goal_unit: string;
  goal_period: string;
  task_days_of_week?: string;      // nullable
  task_days_of_month?: string;     // nullable
  streak: number;                  // default 0
  created: Date;                   // default now()
}

// Group quest progress model
export interface GroupQuestProgress {
  id: string;                      // PK
  quest_id: string;                // FK to group_quests.id
  user_id: string;                 // FK to profiles.id
  date: Date;
  value_achieved: number;          // default 0
  completed: boolean;              // default false
}

// Group join requests model
export interface GroupJoinRequest {
  id: string;                      // PK
  group_id: string;                // FK to groups.id
  username_invited: string;        // Username of the invited user
  invited_by: string;              // FK to profiles.id (the user who sent the invitation)
  created: Date;                   // When the request was made
}
