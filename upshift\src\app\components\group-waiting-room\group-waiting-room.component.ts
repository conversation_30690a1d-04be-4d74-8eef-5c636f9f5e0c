import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { NavigationComponent } from '../navigation/navigation.component';

@Component({
  selector: 'app-group-waiting-room',
  templateUrl: './group-waiting-room.component.html',
  styleUrls: ['./group-waiting-room.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, NavigationComponent]
})
export class GroupWaitingRoomComponent {
  @Input() groupId: string = '';
  @Input() groupName: string = 'Group';
  @Input() isAdmin: boolean = false;

  constructor(private router: Router) {}

  goToSettings() {
    if (this.groupId) {
      this.router.navigate([`/groups/${this.groupId}/settings`]);
    }
  }

  goBack() {
    this.router.navigate(['/groups']);
  }
}
