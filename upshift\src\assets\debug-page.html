<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #0C0C0F;
            color: #FFFFFF;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 20px;
            margin: 20px 0 10px;
        }
        pre {
            background-color: #1C1C1E;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            white-space: pre-wrap;
            margin-bottom: 20px;
        }
        .button {
            background-color: #4169E1;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            display: inline-block;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        .button:hover {
            background-color: #3A68E0;
        }
        #errorLog {
            color: #FF3B30;
        }
        #infoLog {
            color: #30D158;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #2C2C2E;
            border-radius: 8px;
            padding: 15px;
        }
        input, select {
            background-color: #1C1C1E;
            border: 1px solid #2C2C2E;
            color: white;
            padding: 8px;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Upshift Debug Page</h1>

    <div class="section">
        <h2>Current URL Information</h2>
        <pre id="urlInfo">Loading...</pre>
    </div>

    <div class="section">
        <h2>Browser Information</h2>
        <pre id="browserInfo">Loading...</pre>
    </div>

    <div class="section">
        <h2>Navigation Test</h2>
        <div>
            <label for="groupId">Group ID:</label>
            <input type="text" id="groupId" placeholder="Enter group ID">
        </div>
        <div>
            <button class="button" onclick="testNavigation('settings')">Test Settings</button>
            <button class="button" onclick="testNavigation('settings-original')">Test Original Settings</button>
            <button class="button" onclick="testNavigation('settings-alt')">Test Alt Settings</button>
            <button class="button" onclick="testNavigation('')">Test Group Detail</button>
        </div>
        <div>
            <button class="button" onclick="testDirectNavigation()">Test Direct Navigation</button>
            <button class="button" onclick="testWindowOpen()">Test Window.Open</button>
            <a class="button" id="simpleSettingsLink" href="#">Simple Settings Page</a>
        </div>
        <pre id="navigationLog">No navigation tests run yet.</pre>
    </div>

    <div class="section">
        <h2>Console Log</h2>
        <button class="button" onclick="clearLogs()">Clear Logs</button>
        <pre id="errorLog">No errors logged.</pre>
        <pre id="infoLog">No info logged.</pre>
    </div>

    <script>
        // Capture console logs
        const originalConsoleError = console.error;
        const originalConsoleLog = console.log;

        console.error = function() {
            const errorLog = document.getElementById('errorLog');
            errorLog.textContent = Array.from(arguments).join(' ') + '\n' + errorLog.textContent;
            originalConsoleError.apply(console, arguments);
        };

        console.log = function() {
            const infoLog = document.getElementById('infoLog');
            infoLog.textContent = Array.from(arguments).join(' ') + '\n' + infoLog.textContent;
            originalConsoleLog.apply(console, arguments);
        };

        // Display URL information
        function updateUrlInfo() {
            const urlInfo = document.getElementById('urlInfo');
            urlInfo.textContent = `Current URL: ${window.location.href}\n`;
            urlInfo.textContent += `Pathname: ${window.location.pathname}\n`;
            urlInfo.textContent += `Search: ${window.location.search}\n`;
            urlInfo.textContent += `Hash: ${window.location.hash}\n`;

            // Parse query parameters
            const urlParams = new URLSearchParams(window.location.search);
            urlInfo.textContent += `\nQuery Parameters:\n`;
            for (const [key, value] of urlParams.entries()) {
                urlInfo.textContent += `${key}: ${value}\n`;
            }
        }

        // Display browser information
        function updateBrowserInfo() {
            const browserInfo = document.getElementById('browserInfo');
            browserInfo.textContent = `User Agent: ${navigator.userAgent}\n`;
            browserInfo.textContent += `Platform: ${navigator.platform}\n`;
            browserInfo.textContent += `Cookies Enabled: ${navigator.cookieEnabled}\n`;
            browserInfo.textContent += `Language: ${navigator.language}\n`;
            browserInfo.textContent += `Online: ${navigator.onLine}\n`;
        }

        // Test navigation
        function testNavigation(path) {
            const groupId = document.getElementById('groupId').value;
            if (!groupId) {
                console.error('Please enter a group ID');
                return;
            }

            const navigationLog = document.getElementById('navigationLog');
            const url = path ? `/groups/${groupId}/${path}` : `/groups/${groupId}`;

            navigationLog.textContent = `Attempting to navigate to: ${url}\n`;

            try {
                window.location.href = url;
                navigationLog.textContent += `Navigation initiated.\n`;
            } catch (error) {
                navigationLog.textContent += `Error: ${error.message}\n`;
                console.error('Navigation error:', error);
            }
        }

        // Test direct navigation with full URL
        function testDirectNavigation() {
            const groupId = document.getElementById('groupId').value;
            if (!groupId) {
                console.error('Please enter a group ID');
                return;
            }

            const navigationLog = document.getElementById('navigationLog');
            const baseUrl = window.location.origin;
            const url = `${baseUrl}/groups/${groupId}/settings`;

            navigationLog.textContent = `Attempting direct navigation to: ${url}\n`;

            try {
                window.location.href = url;
                navigationLog.textContent += `Direct navigation initiated.\n`;
            } catch (error) {
                navigationLog.textContent += `Error: ${error.message}\n`;
                console.error('Direct navigation error:', error);
            }
        }

        // Test window.open
        function testWindowOpen() {
            const groupId = document.getElementById('groupId').value;
            if (!groupId) {
                console.error('Please enter a group ID');
                return;
            }

            const navigationLog = document.getElementById('navigationLog');
            const url = `/groups/${groupId}/settings`;

            navigationLog.textContent = `Attempting to open new window: ${url}\n`;

            try {
                const newWindow = window.open(url, '_blank');
                if (newWindow) {
                    navigationLog.textContent += `New window opened.\n`;
                } else {
                    navigationLog.textContent += `Popup blocked or failed to open.\n`;
                    console.error('Failed to open new window');
                }
            } catch (error) {
                navigationLog.textContent += `Error: ${error.message}\n`;
                console.error('Window open error:', error);
            }
        }

        // Clear logs
        function clearLogs() {
            document.getElementById('errorLog').textContent = 'No errors logged.';
            document.getElementById('infoLog').textContent = 'No info logged.';
            document.getElementById('navigationLog').textContent = 'No navigation tests run yet.';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateUrlInfo();
            updateBrowserInfo();

            // Try to get group ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const groupId = urlParams.get('id');
            if (groupId) {
                document.getElementById('groupId').value = groupId;

                // Set the simple settings link
                const simpleSettingsLink = document.getElementById('simpleSettingsLink');
                simpleSettingsLink.href = `/assets/simple-settings.html?id=${groupId}`;
            }

            console.log('Debug page loaded');
        });
    </script>
</body>
</html>
