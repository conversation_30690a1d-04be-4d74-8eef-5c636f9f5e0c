import { Injectable, inject } from '@angular/core';
import { Friend } from '../models/friend.model';
import { Observable, map, of, switchMap, from, catchError } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class FriendService {
  private supabaseService = inject(SupabaseService);

  // Friend operations
  getFriends(userId: string): Observable<Friend[]> {
    console.log('FriendService: Getting friends for user:', userId);

    // Get friends where user is either user_id or friend_id
    return from(
      this.supabaseService.supabase
        .from('friends')
        .select('*')
        .or(`user_id.eq.${userId},friend_id.eq.${userId}`)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('FriendService: Error getting friends:', response.error);
          return [];
        }

        // Process the results to normalize the data
        // We want to ensure that for each friendship, the other user's ID is in friend_id
        const normalizedFriends = response.data.map(friend => {
          // If the current user is in friend_id, swap the IDs to normalize
          if (friend.friend_id === userId) {
            return {
              ...friend,
              friend_id: friend.user_id,
              user_id: userId
            };
          }
          return friend;
        });

        // Deduplicate friends by friend_id
        const uniqueFriendIds = new Set<string>();
        const uniqueFriends = normalizedFriends.filter(friend => {
          if (uniqueFriendIds.has(friend.friend_id)) {
            return false;
          }
          uniqueFriendIds.add(friend.friend_id);
          return true;
        });

        console.log('FriendService: Found friends:', normalizedFriends);
        console.log('FriendService: Deduplicated to unique friends:', uniqueFriends);
        return uniqueFriends as Friend[];
      })
    );
  }

  getFriendsWithProfiles(userId: string): Observable<any[]> {
    console.log('FriendService: Getting friends with profiles for user:', userId);

    return this.getFriends(userId).pipe(
      switchMap(friends => {
        if (friends.length === 0) {
          console.log('FriendService: No friends found');
          return of([]);
        }

        const friendIds = friends.map(friend => friend.friend_id);
        console.log('FriendService: Friend IDs:', friendIds);

        // Get profiles one by one to avoid RLS issues
        const profilePromises = friendIds.map(friendId => {
          return new Promise<any>((resolve) => {
            try {
              this.supabaseService.supabase
                .from('profiles')
                .select('*')
                .eq('id', friendId)
                .maybeSingle()
                .then(response => {
                  if (response.error) {
                    console.error(`FriendService: Error getting profile for ${friendId}:`, response.error);
                    resolve(null);
                  } else {
                    resolve(response.data);
                  }
                });
            } catch (err: any) {
              console.error(`FriendService: Error getting profile for ${friendId}:`, err);
              resolve(null);
            }
          });
        });

        return from(Promise.all(profilePromises)).pipe(
          map(profiles => {
            console.log('FriendService: Found profiles:', profiles);

            // Create a map of profiles by ID for quick access
            const profileMap = new Map<string, any>();
            profiles.forEach((profile: any, index: number) => {
              if (profile) {
                profileMap.set(friendIds[index], profile);
              }
            });

            // Combine friends with their profiles
            return friends.map(friend => {
              return {
                ...friend,
                profile: profileMap.get(friend.friend_id) || null
              };
            });
          }),
          catchError(error => {
            console.error('FriendService: Error getting profiles:', error);
            return of([]);
          })
        );
      })
    );
  }

  async addFriend(userId: string, friendId: string): Promise<void> {
    console.log('FriendService: Adding friend relationship between', userId, 'and', friendId);

    // Create friendship in both directions
    const friendship1: Friend = {
      user_id: userId,
      friend_id: friendId,
      created: new Date()
    };

    const friendship2: Friend = {
      user_id: friendId,
      friend_id: userId,
      created: new Date()
    };

    // Add both friendships
    const { error: error1 } = await this.supabaseService.supabase
      .from('friends')
      .insert(friendship1);

    if (error1) {
      console.error('FriendService: Error adding first friendship:', error1);
      throw error1;
    }

    const { error: error2 } = await this.supabaseService.supabase
      .from('friends')
      .insert(friendship2);

    if (error2) {
      console.error('FriendService: Error adding second friendship:', error2);
      throw error2;
    }

    // Update friend count badges
    await this.updateFriendBadges(userId);
    await this.updateFriendBadges(friendId);

    console.log('FriendService: Friend relationship added successfully');
  }

  async removeFriend(userId: string, friendId: string): Promise<void> {
    console.log('FriendService: Removing friend relationship between', userId, 'and', friendId);

    // Remove friendship in both directions
    const { error: error1 } = await this.supabaseService.supabase
      .from('friends')
      .delete()
      .eq('user_id', userId)
      .eq('friend_id', friendId);

    if (error1) {
      console.error('FriendService: Error removing first friendship:', error1);
      throw error1;
    }

    const { error: error2 } = await this.supabaseService.supabase
      .from('friends')
      .delete()
      .eq('user_id', friendId)
      .eq('friend_id', userId);

    if (error2) {
      console.error('FriendService: Error removing second friendship:', error2);
      throw error2;
    }

    // Update friend count badges
    await this.updateFriendBadges(userId);
    await this.updateFriendBadges(friendId);

    console.log('FriendService: Friend relationship removed successfully');
  }

  async generateFriendCode(userId: string): Promise<string> {
    console.log('FriendService: Generating friend code for user:', userId);

    // Generate a random code that matches the username pattern
    // Using only alphanumeric characters to ensure compatibility
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < 8; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    // Set expiry to 24 hours from now
    const expiry = new Date();
    expiry.setDate(expiry.getDate() + 1);

    // Update the user with the new code
    const { error } = await this.supabaseService.supabase
      .from('profiles')
      .update({
        friend_code: code,
        friend_code_expiry: expiry
      })
      .eq('id', userId);

    if (error) {
      console.error('FriendService: Error generating friend code:', error);
      throw error;
    }

    console.log('FriendService: Friend code generated:', code);
    return code;
  }

  /**
   * Validates if a string matches the username pattern
   * @param input The string to validate
   * @returns True if the string is valid, false otherwise
   */
  validateUsernameFormat(input: string): boolean {
    // This regex matches the same pattern used in Django's UnicodeUsernameValidator
    // It allows letters, numbers, and @/./+/-/_ characters
    const usernameRegex = /^[\w.@+-]+$/;
    return usernameRegex.test(input);
  }

  async addFriendByCode(userId: string, code: string): Promise<boolean> {
    console.log('FriendService: Adding friend by code:', code, 'for user:', userId);

    // Validate the code format first
    if (!code || !this.validateUsernameFormat(code)) {
      console.log('FriendService: Invalid friend code format');
      return false;
    }

    // Find user with this code
    const { data: users, error: userError } = await this.supabaseService.supabase
      .from('profiles')
      .select('*')
      .eq('friend_code', code)
      .limit(1);

    if (userError) {
      console.error('FriendService: Error finding user by friend code:', userError);
      throw userError;
    }

    if (!users || users.length === 0) {
      console.log('FriendService: No user found with this friend code');
      return false;
    }

    const friend = users[0];
    const friendId = friend.id;

    // Check if code is expired
    const expiryDate = friend.friend_code_expiry ? new Date(friend.friend_code_expiry) : null;
    if (!expiryDate || expiryDate < new Date()) {
      console.log('FriendService: Friend code is expired');
      return false;
    }

    // Check if already friends
    const { data: existingFriends, error: friendError } = await this.supabaseService.supabase
      .from('friends')
      .select('*')
      .eq('user_id', userId)
      .eq('friend_id', friendId)
      .limit(1);

    if (friendError) {
      console.error('FriendService: Error checking existing friendship:', friendError);
      throw friendError;
    }

    if (existingFriends && existingFriends.length > 0) {
      console.log('FriendService: Already friends');
      return false;
    }

    // Add as friends
    await this.addFriend(userId, friendId);

    // Clear the friend code
    const { error: clearError } = await this.supabaseService.supabase
      .from('profiles')
      .update({
        friend_code: null,
        friend_code_expiry: null
      })
      .eq('id', friendId);

    if (clearError) {
      console.error('FriendService: Error clearing friend code:', clearError);
      // Don't throw here, as the friendship was already created
    }

    console.log('FriendService: Friend added by code successfully');
    return true;
  }

  /**
   * Search for friends by username
   * @param userId The ID of the user whose friends to search
   * @param query The search query (username)
   * @param groupId Optional group ID to exclude members who are already in the group
   * @returns Observable of usernames that match the query
   */
  searchFriendsByUsername(userId: string, query: string, groupId?: string): Observable<string[]> {
    console.log('FriendService: Searching friends by username:', query, 'for user:', userId);

    if (!query || query.length < 2) {
      return of([]);
    }

    // First get all friends of the user (already handles bidirectional friendships)
    return this.getFriends(userId).pipe(
      switchMap(friends => {
        if (friends.length === 0) {
          return of([]);
        }

        // The getFriends method now normalizes the data so that friend_id always contains the other user's ID
        const friendIds = friends.map(friend => friend.friend_id);

        // Get profiles of all friends that match the query
        return from(
          this.supabaseService.supabase
            .from('profiles')
            .select('id, username')
            .in('id', friendIds)
            .ilike('username', `%${query}%`)
            .limit(10)
        ).pipe(
          switchMap(response => {
            if (response.error) {
              console.error('FriendService: Error searching friends:', response.error);
              return of<string[]>([]);
            }

            const matchingFriends = response.data;
            console.log('FriendService: Found matching friends:', matchingFriends);

            // If no group ID provided, return the results
            if (!groupId) {
              return of<string[]>(matchingFriends.map(friend => friend.username));
            }

            // If group ID provided, exclude members who are already in the group
            return from(
              this.supabaseService.supabase
                .from('group_members')
                .select('user_id')
                .eq('group_id', groupId)
            ).pipe(
              switchMap(membersResponse => {
                if (membersResponse.error) {
                  console.error('FriendService: Error getting group members:', membersResponse.error);
                  return of<string[]>(matchingFriends.map(friend => friend.username));
                }

                const memberIds = membersResponse.data.map(member => member.user_id);
                console.log('FriendService: Group member IDs to exclude:', memberIds);

                // Filter out friends who are already members
                const filteredFriends = matchingFriends.filter(friend => !memberIds.includes(friend.id));
                console.log('FriendService: Filtered friends:', filteredFriends);

                // Now also check for pending invites
                return from(
                  this.supabaseService.supabase
                    .from('group_join_requests')
                    .select('username_invited')
                    .eq('group_id', groupId)
                ).pipe(
                  map(invitesResponse => {
                    if (invitesResponse.error) {
                      console.error('FriendService: Error getting pending invites:', invitesResponse.error);
                      return filteredFriends.map(friend => friend.username);
                    }

                    const invitedUsernames = invitesResponse.data.map(invite => invite.username_invited);
                    console.log('FriendService: Invited usernames to exclude:', invitedUsernames);

                    // Filter out friends who already have pending invites
                    const finalFilteredFriends = filteredFriends.filter(friend =>
                      !invitedUsernames.includes(friend.username)
                    );
                    console.log('FriendService: Final filtered friends:', finalFilteredFriends);

                    return finalFilteredFriends.map(friend => friend.username);
                  })
                );
              }),
              catchError(error => {
                console.error('FriendService: Error filtering friends:', error);
                return of<string[]>(matchingFriends.map(friend => friend.username));
              })
            );
          }),
          catchError(error => {
            console.error('FriendService: Error searching friends:', error);
            return of<string[]>([]);
          })
        );
      })
    );
  }

  private async updateFriendBadges(userId: string): Promise<void> {
    console.log('FriendService: Updating friend badges for user:', userId);

    // Count friends where user is user_id
    const { data: friends, error: friendError } = await this.supabaseService.supabase
      .from('friends')
      .select('*')
      .eq('user_id', userId);

    if (friendError) {
      console.error('FriendService: Error counting friends:', friendError);
      return;
    }

    const friendCount = friends ? friends.length : 0;
    console.log('FriendService: Unique friend count:', friendCount);

    // Get user badges
    const { data: badges, error: badgeError } = await this.supabaseService.supabase
      .from('user_badges')
      .select('*')
      .eq('user_id', userId)
      .limit(1);

    if (badgeError) {
      console.error('FriendService: Error getting user badges:', badgeError);
      return;
    }

    if (!badges || badges.length === 0) {
      console.log('FriendService: No badges found for user');
      return;
    }

    const badgeId = badges[0].id;
    const updates: any = {};

    // Update badges based on friend count
    if (friendCount >= 5) {
      updates.badge_friends_5 = true;
    }

    if (friendCount >= 10) {
      updates.badge_friends_10 = true;
    }

    // Only update if there are changes
    if (Object.keys(updates).length > 0) {
      console.log('FriendService: Updating badges:', updates);

      const { error: updateError } = await this.supabaseService.supabase
        .from('user_badges')
        .update(updates)
        .eq('id', badgeId);

      if (updateError) {
        console.error('FriendService: Error updating badges:', updateError);
      }
    }
  }
}
