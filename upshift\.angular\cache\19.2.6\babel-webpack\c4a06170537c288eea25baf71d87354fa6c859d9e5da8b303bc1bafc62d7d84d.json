{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, c as createEvent, h, e as Host, f as getElement, i as forceUpdate } from './index-527b9e34.js';\nimport { c as createNotchController } from './notch-controller-00d92e89.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-a96ff2ea.js';\nimport { h as inheritAttributes, d as renderHiddenInput, f as focusVisibleElement } from './helpers-78efeec3.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, m as modalController, s as safeCall } from './overlays-41a5d51b.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { w as watchForOptions } from './watch-options-c2911ace.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-ca86cf32.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-864101a3.js';\nimport './framework-delegate-2eea1763.js';\nimport './gesture-controller-314a54f6.js';\nimport './index-738d7504.js';\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonSelectIosStyle0 = selectIosCss;\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.select-bottom{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}:host(.has-focus.ion-valid),:host(.ion-touched.ion-invalid){--border-color:var(--highlight-color)}.select-bottom .error-text{display:none;color:var(--highlight-color-invalid)}.select-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .select-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .select-bottom .helper-text{display:none}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.ion-focused){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.ion-focused){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.ion-focused) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.ion-focused) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.ion-focused) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.ion-focused) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonSelectMdStyle0 = selectMdCss;\nconst Select = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-sel-${selectIds++}`;\n    this.helperTextId = `${this.inputId}-helper-text`;\n    this.errorTextId = `${this.inputId}-error-text`;\n    this.inheritedAttributes = {};\n    this.onClick = ev => {\n      const target = ev.target;\n      const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n      if (target === this.el || closestSlot === null) {\n        this.setFocus();\n        this.open(ev);\n      } else {\n        /**\n         * Prevent clicks to the start/end slots from opening the select.\n         * We ensure the target isn't this element in case the select is slotted\n         * in, for example, an item. This would prevent the select from ever\n         * being opened since the element itself has slot=\"start\"/\"end\".\n         *\n         * Clicking a slotted element also causes a click\n         * on the <label> element (since it wraps the slots).\n         * Clicking <label> dispatches another click event on\n         * the native form control that then bubbles up to this\n         * listener. This additional event targets the host\n         * element, so the select overlay is opened.\n         *\n         * When the slotted elements are clicked (and therefore\n         * the ancestor <label> element) we want to prevent the label\n         * from dispatching another click event.\n         *\n         * Do not call stopPropagation() because this will cause\n         * click handlers on the slotted elements to never fire in React.\n         * When developers do onClick in React a native \"click\" listener\n         * is added on the root element, not the slotted element. When that\n         * native click listener fires, React then dispatches the synthetic\n         * click event on the slotted element. However, if stopPropagation\n         * is called then the native click event will never bubble up\n         * to the root element.\n         */\n        ev.preventDefault();\n      }\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.isExpanded = false;\n    this.cancelText = 'Cancel';\n    this.color = undefined;\n    this.compareWith = undefined;\n    this.disabled = false;\n    this.fill = undefined;\n    this.errorText = undefined;\n    this.helperText = undefined;\n    this.interface = 'alert';\n    this.interfaceOptions = {};\n    this.justify = undefined;\n    this.label = undefined;\n    this.labelPlacement = 'start';\n    this.multiple = false;\n    this.name = this.inputId;\n    this.okText = 'OK';\n    this.placeholder = undefined;\n    this.selectedText = undefined;\n    this.toggleIcon = undefined;\n    this.expandedIcon = undefined;\n    this.shape = undefined;\n    this.value = undefined;\n    this.required = false;\n  }\n  styleChanged() {\n    this.emitStyle();\n  }\n  setValue(value) {\n    this.value = value;\n    this.ionChange.emit({\n      value\n    });\n  }\n  connectedCallback() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        el\n      } = _this;\n      _this.notchController = createNotchController(el, () => _this.notchSpacerEl, () => _this.labelSlot);\n      _this.updateOverlayOptions();\n      _this.emitStyle();\n      _this.mutationO = watchForOptions(_this.el, 'ion-select-option', /*#__PURE__*/_asyncToGenerator(function* () {\n        _this.updateOverlayOptions();\n        /**\n         * We need to re-render the component\n         * because one of the new ion-select-option\n         * elements may match the value. In this case,\n         * the rendered selected text should be updated.\n         */\n        forceUpdate(_this);\n      }));\n    })();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  componentDidLoad() {\n    /**\n     * If any of the conditions that trigger the styleChanged callback\n     * are met on component load, it is possible the event emitted\n     * prior to a parent web component registering an event listener.\n     *\n     * To ensure the parent web component receives the event, we\n     * emit the style event again after the component has loaded.\n     *\n     * This is often seen in Angular with the `dist` output target.\n     */\n    this.emitStyle();\n  }\n  disconnectedCallback() {\n    if (this.mutationO) {\n      this.mutationO.disconnect();\n      this.mutationO = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n   * depending on the `interface` property on the `ion-select`.\n   *\n   * @param event The user interface event that called the open.\n   */\n  open(event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.disabled || _this2.isExpanded) {\n        return undefined;\n      }\n      _this2.isExpanded = true;\n      const overlay = _this2.overlay = yield _this2.createOverlay(event);\n      // Add logic to scroll selected item into view before presenting\n      const scrollSelectedIntoView = () => {\n        const indexOfSelected = _this2.childOpts.findIndex(o => o.value === _this2.value);\n        if (indexOfSelected > -1) {\n          const selectedItem = overlay.querySelector(`.select-interface-option:nth-child(${indexOfSelected + 1})`);\n          if (selectedItem) {\n            /**\n             * Browsers such as Firefox do not\n             * correctly delegate focus when manually\n             * focusing an element with delegatesFocus.\n             * We work around this by manually focusing\n             * the interactive element.\n             * ion-radio and ion-checkbox are the only\n             * elements that ion-select-popover uses, so\n             * we only need to worry about those two components\n             * when focusing.\n             */\n            const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n            if (interactiveEl) {\n              selectedItem.scrollIntoView({\n                block: 'nearest'\n              });\n              // Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n              // and removing `ion-focused` style\n              interactiveEl.setFocus();\n            }\n            focusVisibleElement(selectedItem);\n          }\n        } else {\n          /**\n           * If no value is set then focus the first enabled option.\n           */\n          const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n          if (firstEnabledOption) {\n            /**\n             * Focus the option for the same reason as we do above.\n             *\n             * Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n             * and removing `ion-focused` style\n             */\n            firstEnabledOption.setFocus();\n            focusVisibleElement(firstEnabledOption.closest('ion-item'));\n          }\n        }\n      };\n      // For modals and popovers, we can scroll before they're visible\n      if (_this2.interface === 'modal') {\n        overlay.addEventListener('ionModalWillPresent', scrollSelectedIntoView, {\n          once: true\n        });\n      } else if (_this2.interface === 'popover') {\n        overlay.addEventListener('ionPopoverWillPresent', scrollSelectedIntoView, {\n          once: true\n        });\n      } else {\n        /**\n         * For alerts and action sheets, we need to wait a frame after willPresent\n         * because these overlays don't have their content in the DOM immediately\n         * when willPresent fires. By waiting a frame, we ensure the content is\n         * rendered and can be properly scrolled into view.\n         */\n        const scrollAfterRender = () => {\n          requestAnimationFrame(() => {\n            scrollSelectedIntoView();\n          });\n        };\n        if (_this2.interface === 'alert') {\n          overlay.addEventListener('ionAlertWillPresent', scrollAfterRender, {\n            once: true\n          });\n        } else if (_this2.interface === 'action-sheet') {\n          overlay.addEventListener('ionActionSheetWillPresent', scrollAfterRender, {\n            once: true\n          });\n        }\n      }\n      overlay.onDidDismiss().then(() => {\n        _this2.overlay = undefined;\n        _this2.isExpanded = false;\n        _this2.ionDismiss.emit();\n        _this2.setFocus();\n      });\n      yield overlay.present();\n      return overlay;\n    })();\n  }\n  createOverlay(ev) {\n    let selectInterface = this.interface;\n    if (selectInterface === 'action-sheet' && this.multiple) {\n      console.warn(`Select interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'popover' && !ev) {\n      console.warn(`Select interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'action-sheet') {\n      return this.openActionSheet();\n    }\n    if (selectInterface === 'popover') {\n      return this.openPopover(ev);\n    }\n    if (selectInterface === 'modal') {\n      return this.openModal();\n    }\n    return this.openAlert();\n  }\n  updateOverlayOptions() {\n    const overlay = this.overlay;\n    if (!overlay) {\n      return;\n    }\n    const childOpts = this.childOpts;\n    const value = this.value;\n    switch (this.interface) {\n      case 'action-sheet':\n        overlay.buttons = this.createActionSheetButtons(childOpts, value);\n        break;\n      case 'popover':\n        const popover = overlay.querySelector('ion-select-popover');\n        if (popover) {\n          popover.options = this.createOverlaySelectOptions(childOpts, value);\n        }\n        break;\n      case 'modal':\n        const modal = overlay.querySelector('ion-select-modal');\n        if (modal) {\n          modal.options = this.createOverlaySelectOptions(childOpts, value);\n        }\n        break;\n      case 'alert':\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n        break;\n    }\n  }\n  createActionSheetButtons(data, selectValue) {\n    const actionSheetButtons = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n        text: option.textContent,\n        cssClass: optClass,\n        handler: () => {\n          this.setValue(value);\n        }\n      };\n    });\n    // Add \"cancel\" button\n    actionSheetButtons.push({\n      text: this.cancelText,\n      role: 'cancel',\n      handler: () => {\n        this.ionCancel.emit();\n      }\n    });\n    return actionSheetButtons;\n  }\n  createAlertInputs(data, inputType, selectValue) {\n    const alertInputs = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        type: inputType,\n        cssClass: optClass,\n        label: option.textContent || '',\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled\n      };\n    });\n    return alertInputs;\n  }\n  createOverlaySelectOptions(data, selectValue) {\n    const popoverOptions = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        text: option.textContent || '',\n        cssClass: optClass,\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled,\n        handler: selected => {\n          this.setValue(selected);\n          if (!this.multiple) {\n            this.close();\n          }\n        }\n      };\n    });\n    return popoverOptions;\n  }\n  openPopover(ev) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        fill,\n        labelPlacement\n      } = _this3;\n      const interfaceOptions = _this3.interfaceOptions;\n      const mode = getIonMode(_this3);\n      const showBackdrop = mode === 'md' ? false : true;\n      const multiple = _this3.multiple;\n      const value = _this3.value;\n      let event = ev;\n      let size = 'auto';\n      const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n      /**\n       * The popover should take up the full width\n       * when using a fill in MD mode or if the\n       * label is floating/stacked.\n       */\n      if (hasFloatingOrStackedLabel || mode === 'md' && fill !== undefined) {\n        size = 'cover';\n        /**\n         * Otherwise the popover\n         * should be positioned relative\n         * to the native element.\n         */\n      } else {\n        event = Object.assign(Object.assign({}, ev), {\n          detail: {\n            ionShadowTarget: _this3.nativeWrapperEl\n          }\n        });\n      }\n      const popoverOpts = Object.assign(Object.assign({\n        mode,\n        event,\n        alignment: 'center',\n        size,\n        showBackdrop\n      }, interfaceOptions), {\n        component: 'ion-select-popover',\n        cssClass: ['select-popover', interfaceOptions.cssClass],\n        componentProps: {\n          header: interfaceOptions.header,\n          subHeader: interfaceOptions.subHeader,\n          message: interfaceOptions.message,\n          multiple,\n          value,\n          options: _this3.createOverlaySelectOptions(_this3.childOpts, value)\n        }\n      });\n      return popoverController.create(popoverOpts);\n    })();\n  }\n  openActionSheet() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const mode = getIonMode(_this4);\n      const interfaceOptions = _this4.interfaceOptions;\n      const actionSheetOpts = Object.assign(Object.assign({\n        mode\n      }, interfaceOptions), {\n        buttons: _this4.createActionSheetButtons(_this4.childOpts, _this4.value),\n        cssClass: ['select-action-sheet', interfaceOptions.cssClass]\n      });\n      return actionSheetController.create(actionSheetOpts);\n    })();\n  }\n  openAlert() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const interfaceOptions = _this5.interfaceOptions;\n      const inputType = _this5.multiple ? 'checkbox' : 'radio';\n      const mode = getIonMode(_this5);\n      const alertOpts = Object.assign(Object.assign({\n        mode\n      }, interfaceOptions), {\n        header: interfaceOptions.header ? interfaceOptions.header : _this5.labelText,\n        inputs: _this5.createAlertInputs(_this5.childOpts, inputType, _this5.value),\n        buttons: [{\n          text: _this5.cancelText,\n          role: 'cancel',\n          handler: () => {\n            _this5.ionCancel.emit();\n          }\n        }, {\n          text: _this5.okText,\n          handler: selectedValues => {\n            _this5.setValue(selectedValues);\n          }\n        }],\n        cssClass: ['select-alert', interfaceOptions.cssClass, _this5.multiple ? 'multiple-select-alert' : 'single-select-alert']\n      });\n      return alertController.create(alertOpts);\n    })();\n  }\n  openModal() {\n    const {\n      multiple,\n      value,\n      interfaceOptions\n    } = this;\n    const mode = getIonMode(this);\n    const modalOpts = Object.assign(Object.assign({}, interfaceOptions), {\n      mode,\n      cssClass: ['select-modal', interfaceOptions.cssClass],\n      component: 'ion-select-modal',\n      componentProps: {\n        header: interfaceOptions.header,\n        multiple,\n        value,\n        options: this.createOverlaySelectOptions(this.childOpts, value)\n      }\n    });\n    return modalController.create(modalOpts);\n  }\n  /**\n   * Close the select interface.\n   */\n  close() {\n    if (!this.overlay) {\n      return Promise.resolve(false);\n    }\n    return this.overlay.dismiss();\n  }\n  hasValue() {\n    return this.getText() !== '';\n  }\n  get childOpts() {\n    return Array.from(this.el.querySelectorAll('ion-select-option'));\n  }\n  /**\n   * Returns any plaintext associated with\n   * the label (either prop or slot).\n   * Note: This will not return any custom\n   * HTML. Use the `hasLabel` getter if you\n   * want to know if any slotted label content\n   * was passed.\n   */\n  get labelText() {\n    const {\n      label\n    } = this;\n    if (label !== undefined) {\n      return label;\n    }\n    const {\n      labelSlot\n    } = this;\n    if (labelSlot !== null) {\n      return labelSlot.textContent;\n    }\n    return;\n  }\n  getText() {\n    const selectedText = this.selectedText;\n    if (selectedText != null && selectedText !== '') {\n      return selectedText;\n    }\n    return generateText(this.childOpts, this.value, this.compareWith);\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  emitStyle() {\n    const {\n      disabled\n    } = this;\n    const style = {\n      'interactive-disabled': disabled\n    };\n    this.ionStyle.emit(style);\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      },\n      part: \"label\"\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the select and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"select-outline-container\"\n      }, h(\"div\", {\n        class: \"select-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'select-outline-notch': true,\n          'select-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"select-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  /**\n   * Renders either the placeholder\n   * or the selected values based on\n   * the state of the select.\n   */\n  renderSelectText() {\n    const {\n      placeholder\n    } = this;\n    const displayValue = this.getText();\n    let addPlaceholderClass = false;\n    let selectText = displayValue;\n    if (selectText === '' && placeholder !== undefined) {\n      selectText = placeholder;\n      addPlaceholderClass = true;\n    }\n    const selectTextClasses = {\n      'select-text': true,\n      'select-placeholder': addPlaceholderClass\n    };\n    const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n    return h(\"div\", {\n      \"aria-hidden\": \"true\",\n      class: selectTextClasses,\n      part: textPart\n    }, selectText);\n  }\n  /**\n   * Renders the chevron icon\n   * next to the select text.\n   */\n  renderSelectIcon() {\n    const mode = getIonMode(this);\n    const {\n      isExpanded,\n      toggleIcon,\n      expandedIcon\n    } = this;\n    let icon;\n    if (isExpanded && expandedIcon !== undefined) {\n      icon = expandedIcon;\n    } else {\n      const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n      icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n    }\n    return h(\"ion-icon\", {\n      class: \"select-icon\",\n      part: \"icon\",\n      \"aria-hidden\": \"true\",\n      icon: icon\n    });\n  }\n  get ariaLabel() {\n    var _a;\n    const {\n      placeholder,\n      inheritedAttributes\n    } = this;\n    const displayValue = this.getText();\n    // The aria label should be preferred over visible text if both are specified\n    const definedLabel = (_a = inheritedAttributes['aria-label']) !== null && _a !== void 0 ? _a : this.labelText;\n    /**\n     * If developer has specified a placeholder\n     * and there is nothing selected, the selectText\n     * should have the placeholder value.\n     */\n    let renderedLabel = displayValue;\n    if (renderedLabel === '' && placeholder !== undefined) {\n      renderedLabel = placeholder;\n    }\n    /**\n     * If there is a developer-defined label,\n     * then we need to concatenate the developer label\n     * string with the current current value.\n     * The label for the control should be read\n     * before the values of the control.\n     */\n    if (definedLabel !== undefined) {\n      renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n    }\n    return renderedLabel;\n  }\n  renderListbox() {\n    const {\n      disabled,\n      inputId,\n      isExpanded,\n      required\n    } = this;\n    return h(\"button\", {\n      disabled: disabled,\n      id: inputId,\n      \"aria-label\": this.ariaLabel,\n      \"aria-haspopup\": \"dialog\",\n      \"aria-expanded\": `${isExpanded}`,\n      \"aria-describedby\": this.getHintTextID(),\n      \"aria-invalid\": this.getHintTextID() === this.errorTextId,\n      \"aria-required\": `${required}`,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      ref: focusEl => this.focusEl = focusEl\n    });\n  }\n  getHintTextID() {\n    const {\n      el,\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n      return errorTextId;\n    }\n    if (helperText) {\n      return helperTextId;\n    }\n    return undefined;\n  }\n  /**\n   * Renders the helper text or error text values\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText,\n      helperTextId,\n      errorTextId\n    } = this;\n    return [h(\"div\", {\n      id: helperTextId,\n      class: \"helper-text\",\n      part: \"supporting-text helper-text\"\n    }, helperText), h(\"div\", {\n      id: errorTextId,\n      class: \"error-text\",\n      part: \"supporting-text error-text\"\n    }, errorText)];\n  }\n  /**\n   * Responsible for rendering helper text, and error text. This element\n   * should only be rendered if hint text is set.\n   */\n  renderBottomContent() {\n    const {\n      helperText,\n      errorText\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    if (!hasHintText) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"select-bottom\"\n    }, this.renderHintText());\n  }\n  render() {\n    const {\n      disabled,\n      el,\n      isExpanded,\n      expandedIcon,\n      labelPlacement,\n      justify,\n      placeholder,\n      fill,\n      shape,\n      name,\n      value\n    } = this;\n    const mode = getIonMode(this);\n    const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n    const justifyEnabled = !hasFloatingOrStackedLabel && justify !== undefined;\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    renderHiddenInput(true, el, name, parseValue(value), disabled);\n    /**\n     * If the label is stacked, it should always sit above the select.\n     * For floating labels, the label should move above the select if\n     * the select has a value, is open, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the select is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots);\n    return h(Host, {\n      key: 'aa7bd7fbb6479c7805486990650a406e5470fd13',\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', el),\n        'select-disabled': disabled,\n        'select-expanded': isExpanded,\n        'has-expanded-icon': expandedIcon !== undefined,\n        'has-value': hasValue,\n        'label-floating': labelShouldFloat,\n        'has-placeholder': placeholder !== undefined,\n        'ion-focusable': true,\n        [`select-${rtl}`]: true,\n        [`select-fill-${fill}`]: fill !== undefined,\n        [`select-justify-${justify}`]: justifyEnabled,\n        [`select-shape-${shape}`]: shape !== undefined,\n        [`select-label-placement-${labelPlacement}`]: true\n      })\n    }, h(\"label\", {\n      key: 'fde3cdfd0ef7d1a20263e35ff4358ee7f61a789f',\n      class: \"select-wrapper\",\n      id: \"select-label\"\n    }, this.renderLabelContainer(), h(\"div\", {\n      key: '6fb8deedc827b6be2f19f9e57a62efefaaba200f',\n      class: \"select-wrapper-inner\"\n    }, h(\"slot\", {\n      key: 'a57a204ea1cbd9c4bac338f14e196e780dab0a10',\n      name: \"start\"\n    }), h(\"div\", {\n      key: '78b83e1484a446537e038527d539d997e330cd69',\n      class: \"native-wrapper\",\n      ref: el => this.nativeWrapperEl = el,\n      part: \"container\"\n    }, this.renderSelectText(), this.renderListbox()), h(\"slot\", {\n      key: '9fc660134e5247c4e5243c7d9d71ac6cec08705d',\n      name: \"end\"\n    }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", {\n      key: '7f143285efa7fd7756dfdc5517ca33e84c8a027e',\n      class: \"select-highlight\"\n    })), this.renderBottomContent());\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"styleChanged\"],\n      \"isExpanded\": [\"styleChanged\"],\n      \"placeholder\": [\"styleChanged\"],\n      \"value\": [\"styleChanged\"]\n    };\n  }\n};\nconst getOptionValue = el => {\n  const value = el.value;\n  return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = value => {\n  if (value == null) {\n    return undefined;\n  }\n  if (Array.isArray(value)) {\n    return value.join(',');\n  }\n  return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n  if (value === undefined) {\n    return '';\n  }\n  if (Array.isArray(value)) {\n    return value.map(v => textForValue(opts, v, compareWith)).filter(opt => opt !== null).join(', ');\n  } else {\n    return textForValue(opts, value, compareWith) || '';\n  }\n};\nconst textForValue = (opts, value, compareWith) => {\n  const selectOpt = opts.find(opt => {\n    return compareOptions(value, getOptionValue(opt), compareWith);\n  });\n  return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n  ios: IonSelectIosStyle0,\n  md: IonSelectMdStyle0\n};\nconst selectOptionCss = \":host{display:none}\";\nconst IonSelectOptionStyle0 = selectOptionCss;\nconst SelectOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inputId = `ion-selopt-${selectOptionIds++}`;\n    this.disabled = false;\n    this.value = undefined;\n  }\n  render() {\n    return h(Host, {\n      key: '8c96c199ce3a3065de3fe446500f567236e0610a',\n      role: \"option\",\n      id: this.inputId,\n      class: getIonMode(this)\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nlet selectOptionIds = 0;\nSelectOption.style = IonSelectOptionStyle0;\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\nconst IonSelectPopoverIosStyle0 = selectPopoverIosCss;\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst IonSelectPopoverMdStyle0 = selectPopoverMdCss;\nconst SelectPopover = /*#__PURE__*/(() => {\n  let SelectPopover = class {\n    constructor(hostRef) {\n      registerInstance(this, hostRef);\n      this.header = undefined;\n      this.subHeader = undefined;\n      this.message = undefined;\n      this.multiple = undefined;\n      this.options = [];\n    }\n    findOptionFromEvent(ev) {\n      const {\n        options\n      } = this;\n      return options.find(o => o.value === ev.target.value);\n    }\n    /**\n     * When an option is selected we need to get the value(s)\n     * of the selected option(s) and return it in the option\n     * handler\n     */\n    callOptionHandler(ev) {\n      const option = this.findOptionFromEvent(ev);\n      const values = this.getValues(ev);\n      if (option === null || option === void 0 ? void 0 : option.handler) {\n        safeCall(option.handler, values);\n      }\n    }\n    /**\n     * Dismisses the host popover that the `ion-select-popover`\n     * is rendered within.\n     */\n    dismissParentPopover() {\n      const popover = this.el.closest('ion-popover');\n      if (popover) {\n        popover.dismiss();\n      }\n    }\n    setChecked(ev) {\n      const {\n        multiple\n      } = this;\n      const option = this.findOptionFromEvent(ev);\n      // this is a popover with checkboxes (multiple value select)\n      // we need to set the checked value for this option\n      if (multiple && option) {\n        option.checked = ev.detail.checked;\n      }\n    }\n    getValues(ev) {\n      const {\n        multiple,\n        options\n      } = this;\n      if (multiple) {\n        // this is a popover with checkboxes (multiple value select)\n        // return an array of all the checked values\n        return options.filter(o => o.checked).map(o => o.value);\n      }\n      // this is a popover with radio buttons (single value select)\n      // return the value that was clicked, otherwise undefined\n      const option = this.findOptionFromEvent(ev);\n      return option ? option.value : undefined;\n    }\n    renderOptions(options) {\n      const {\n        multiple\n      } = this;\n      switch (multiple) {\n        case true:\n          return this.renderCheckboxOptions(options);\n        default:\n          return this.renderRadioOptions(options);\n      }\n    }\n    renderCheckboxOptions(options) {\n      return options.map(option => h(\"ion-item\", {\n        class: Object.assign({\n          // TODO FW-4784\n          'item-checkbox-checked': option.checked\n        }, getClassMap(option.cssClass))\n      }, h(\"ion-checkbox\", {\n        value: option.value,\n        disabled: option.disabled,\n        checked: option.checked,\n        justify: \"start\",\n        labelPlacement: \"end\",\n        onIonChange: ev => {\n          this.setChecked(ev);\n          this.callOptionHandler(ev);\n          // TODO FW-4784\n          forceUpdate(this);\n        }\n      }, option.text)));\n    }\n    renderRadioOptions(options) {\n      const checked = options.filter(o => o.checked).map(o => o.value)[0];\n      return h(\"ion-radio-group\", {\n        value: checked,\n        onIonChange: ev => this.callOptionHandler(ev)\n      }, options.map(option => h(\"ion-item\", {\n        class: Object.assign({\n          // TODO FW-4784\n          'item-radio-checked': option.value === checked\n        }, getClassMap(option.cssClass))\n      }, h(\"ion-radio\", {\n        value: option.value,\n        disabled: option.disabled,\n        onClick: () => this.dismissParentPopover(),\n        onKeyUp: ev => {\n          if (ev.key === ' ') {\n            /**\n             * Selecting a radio option with keyboard navigation,\n             * either through the Enter or Space keys, should\n             * dismiss the popover.\n             */\n            this.dismissParentPopover();\n          }\n        }\n      }, option.text))));\n    }\n    render() {\n      const {\n        header,\n        message,\n        options,\n        subHeader\n      } = this;\n      const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n      return h(Host, {\n        key: '542367ab8fb72bfebf7e65630b91017d68827fd6',\n        class: getIonMode(this)\n      }, h(\"ion-list\", {\n        key: 'f2f0f37e1365cd7780b02de1a1698700d0df48a7'\n      }, header !== undefined && h(\"ion-list-header\", {\n        key: '4b8800a68e800f19277a44b7074ca24b70218daf'\n      }, header), hasSubHeaderOrMessage && h(\"ion-item\", {\n        key: '932b7903daf97d5a57d289b7ee49e868bb9b0cf5'\n      }, h(\"ion-label\", {\n        key: 'fc3f1b69aa2a0bc6125d35692dcad3a8a99fd160',\n        class: \"ion-text-wrap\"\n      }, subHeader !== undefined && h(\"h3\", {\n        key: 'eceab2f47afa95f04b138342b0bdbfa1f50919a8'\n      }, subHeader), message !== undefined && h(\"p\", {\n        key: '70f4e27ad1316318efd0c17efce31e5e45c8fa02'\n      }, message))), this.renderOptions(options)));\n    }\n    get el() {\n      return getElement(this);\n    }\n  };\n  SelectPopover.style = {\n    ios: IonSelectPopoverIosStyle0,\n    md: IonSelectPopoverMdStyle0\n  };\n  return SelectPopover;\n})();\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}