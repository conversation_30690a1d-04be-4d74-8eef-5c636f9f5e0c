{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, inject, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { QuestService } from '../../services/quest.service';\nimport { SideQuestService } from '../../services/sidequest.service';\nimport { UserService } from '../../services/user.service';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { Subscription, forkJoin, map, of, switchMap, take, firstValueFrom } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\nimport { CelebrationComponent } from '../../components/celebration/celebration.component';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { PreferencesService } from '../../services/preferences.service';\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\nimport { StreakCalculatorService } from '../../services/streak-calculator';\nimport { HeaderComponent } from 'src/app/components/header/header.component';\nimport { AuroraComponent } from 'src/app/components/aurora/aurora.component';\nlet TodayPage = class TodayPage {\n  // Method to load daily side quest\n  loadDailySideQuest() {\n    // Only load for today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const selectedDate = new Date(this.selectedDate);\n    selectedDate.setHours(0, 0, 0, 0);\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\n    // Reset dailyQuest if not today's date\n    if (!isTodaySelected) {\n      this.dailyQuest = null;\n      return;\n    }\n    if (this.showSidequests && isTodaySelected && this.userId) {\n      // Use the ensureUserHasDailySideQuests method\n      this.sideQuestService.ensureUserHasDailySideQuests(this.userId).pipe(take(1)).subscribe({\n        next: sideQuests => {\n          if (sideQuests && sideQuests.length > 0) {\n            const sideQuest = sideQuests[0];\n            // Get the quest details from the pool\n            this.supabaseService.getClient().from('daily_sidequest_pool').select('*').eq('id', sideQuest.current_quest_id).single().then(response => {\n              if (response.error) {\n                return;\n              }\n              const questDetails = response.data;\n              // Create the daily quest object\n              this.dailyQuest = {\n                id: sideQuest.id,\n                current_quest: {\n                  id: sideQuest.current_quest_id,\n                  name: questDetails.name || 'Daily Side Quest',\n                  description: questDetails.description || 'Complete this daily side quest',\n                  goal_value: questDetails.goal_value || 1,\n                  goal_unit: questDetails.goal_unit || 'count'\n                },\n                streak: sideQuest.streak || 0,\n                completed: sideQuest.completed || false,\n                value_achieved: sideQuest.value_achieved || 0,\n                emoji: questDetails.emoji || '🎯'\n              };\n            });\n          } else {\n            this.dailyQuest = null;\n          }\n        },\n        error: () => {\n          this.dailyQuest = null;\n        }\n      });\n    }\n  }\n  constructor() {\n    // User data\n    this.user$ = of(null);\n    this.userId = null;\n    this.showSidequests = true;\n    // Date and calendar\n    this.selectedDate = new Date();\n    this.weekDates = [];\n    this.dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];\n    this.headerText = 'Today';\n    this.weekOffset = 0;\n    // Quests\n    this.quests = [];\n    this.dailyQuest = null;\n    this.currentStep = 5;\n    this.totalSteps = 5;\n    this.emojis = ['🚀', '🪐', '⏳', '💊', '⚔️', '🧠', '🦷', '👨‍🍳', '🏃', '🥬', '🏆', '🎮', '🎯', '💻', '🚴‍♂️', '🏋️‍♂️', '💰', '💸', '🪬', '🧪', '😴', '📈', '📚', '❌', '🎓', '💪', '🧘‍♂️', '📵', '🚭', '💧'];\n    // Cache for quest data to improve performance\n    this.questCache = {};\n    // Flag to track if we're currently loading data\n    this.isLoadingData = false;\n    // Add Quest Modal\n    this.showAddQuestModal = false;\n    this.newQuest = this.getEmptyQuest();\n    this.hasHighPriorityQuest = false;\n    // Animation states\n    this.questTypeAnimated = false;\n    this.questTypeAnimating = false;\n    this.selectedQuestType = '';\n    this.categoryAnimated = false;\n    this.categoryAnimating = false;\n    this.categorySelected = false;\n    this.selectedCategory = '';\n    this.priorityAnimated = false;\n    this.priorityAnimating = false;\n    this.goalAnimated = false;\n    this.questDetailsAnimated = false;\n    this.frequencyAnimated = false;\n    this.frequencyOptionsAnimated = false;\n    this.previewAnimated = false;\n    // Celebration Modal\n    this.showCelebration = false;\n    this.currentUser = null;\n    this.celebrationShownDates = [];\n    // Days selection for new quest\n    this.weekDays = [{\n      value: 'Sun',\n      label: 'Su',\n      fullName: 'Sunday'\n    }, {\n      value: 'Mon',\n      label: 'Mo',\n      fullName: 'Monday'\n    }, {\n      value: 'Tue',\n      label: 'Tu',\n      fullName: 'Tuesday'\n    }, {\n      value: 'Wed',\n      label: 'We',\n      fullName: 'Wednesday'\n    }, {\n      value: 'Thu',\n      label: 'Th',\n      fullName: 'Thursday'\n    }, {\n      value: 'Fri',\n      label: 'Fr',\n      fullName: 'Friday'\n    }, {\n      value: 'Sat',\n      label: 'Sa',\n      fullName: 'Saturday'\n    }];\n    this.monthDays = Array.from({\n      length: 31\n    }, (_, i) => i + 1);\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    // Use inject instead of constructor injection\n    this.questService = inject(QuestService);\n    this.sideQuestService = inject(SideQuestService);\n    this.userService = inject(UserService);\n    this.supabaseService = inject(SupabaseService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n    this.preferencesService = inject(PreferencesService);\n    this.streakCalculator = inject(StreakCalculatorService);\n    this.isRedirecting = false; // Flag to prevent multiple redirects\n    // Cache for week date progress\n    this.weekProgressCache = {};\n    // Flag to track if we're currently changing weeks\n    this.isChangingWeek = false;\n    // Map to track which quests are currently being toggled\n    this.togglingQuestIds = {};\n    // Map to track which quests are currently being updated\n    this.updatingQuestIds = {};\n    // Map to track which side quests are currently being toggled\n    this.togglingSideQuestIds = {};\n    // Subscribe to query params to get date and week_offset from URL\n    this.route.queryParams.subscribe(params => {\n      const dateParam = params['date'];\n      const weekOffsetParam = params['week_offset'];\n      console.log('TodayPage: Date param from URL query:', dateParam);\n      console.log('TodayPage: Week offset param from URL query:', weekOffsetParam);\n      // Process week offset parameter\n      if (weekOffsetParam !== undefined) {\n        try {\n          this.weekOffset = parseInt(weekOffsetParam);\n          console.log('TodayPage: Week offset set to:', this.weekOffset);\n        } catch (error) {\n          console.error('TodayPage: Error parsing week offset:', error);\n          this.weekOffset = 0;\n        }\n      } else {\n        this.weekOffset = 0;\n      }\n      // Process date parameter\n      if (dateParam) {\n        try {\n          // Validate date format (YYYY-MM-DD)\n          if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateParam)) {\n            this.selectedDate = new Date(dateParam);\n            console.log('TodayPage: Selected date from URL query:', this.selectedDate);\n          } else {\n            console.error('TodayPage: Invalid date format in URL query:', dateParam);\n            this.selectedDate = new Date(); // Default to today\n          }\n        } catch (error) {\n          console.error('TodayPage: Error parsing date from URL query:', error);\n          this.selectedDate = new Date(); // Default to today\n        }\n      } else {\n        this.selectedDate = new Date(); // Default to today\n      }\n      // Initialize week dates based on selected date and week offset\n      this.generateWeekDates();\n      // Update header text and load data\n      this.updateHeaderText();\n      // Only load data if we have a userId\n      if (this.userId) {\n        this.loadData();\n      }\n    });\n    // Subscribe to auth state changes\n    this.userSubscription = this.supabaseService.currentUser$.subscribe(authUser => {\n      if (!authUser) {\n        console.log('TodayPage: No authenticated user, but not redirecting');\n        // Removed redirect to allow direct access\n        return;\n      }\n      // User is authenticated, get user data\n      this.userId = authUser.id;\n      // Get user data from Supabase\n      this.userService.getUserById(authUser.id).subscribe(userData => {\n        if (!userData) {\n          console.log('TodayPage: No user data found, but not redirecting');\n          // Removed redirect to allow direct access\n          return;\n        }\n        console.log('TodayPage: User data loaded:', userData);\n        this.loadData();\n      });\n    });\n    // Set up user$ observable for template binding\n    this.user$ = this.supabaseService.currentUser$.pipe(switchMap(authUser => {\n      if (!authUser) {\n        return of(null);\n      }\n      return this.userService.getUserById(authUser.id);\n    }));\n    // Subscribe to user$ to get user preferences\n    const userDataSubscription = this.user$.subscribe({\n      next: user => {\n        if (user) {\n          this.showSidequests = user.sidequests_switch;\n          this.currentUser = user;\n        }\n      }\n    });\n    // Add the subscription to be cleaned up\n    this.userSubscription = new Subscription();\n    this.userSubscription.add(userDataSubscription);\n  }\n  ngOnInit() {\n    // Generate week dates and preload data for all days\n    this.generateWeekDates();\n    // Preload data for all days in the week\n    setTimeout(() => {\n      this.preloadWeekData();\n    }, 0);\n    // Load celebration shown dates from localStorage and clean up old ones\n    try {\n      // Get today's date\n      const today = new Date();\n      const todayStr = this.formatDate(today);\n      // First, collect all localStorage keys\n      const allKeys = [];\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key) {\n          allKeys.push(key);\n        }\n      }\n      // Find and remove all celebration_shown keys except today's\n      allKeys.forEach(key => {\n        if (key.startsWith('celebration_shown_') && key !== `celebration_shown_${todayStr}`) {\n          localStorage.removeItem(key);\n        }\n      });\n      // Check if we have a celebration shown for today\n      const todayCelebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\n      // Add to our tracking array if found\n      this.celebrationShownDates = [];\n      if (todayCelebrationShown) {\n        this.celebrationShownDates.push(todayStr);\n      }\n    } catch (error) {\n      this.celebrationShownDates = [];\n    }\n  }\n  ionViewWillEnter() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Authentication is now handled by the AuthGuard\n      // Just get the current user and load data\n      const authUser = _this.supabaseService._currentUser.value;\n      if (!authUser) {\n        console.log('TodayPage: No authenticated user, but not redirecting');\n        return;\n      }\n      // Use the UserService to get or create the user document\n      _this.userService.ensureUserExists(authUser).subscribe(userData => {\n        if (!userData) {\n          _this.router.navigateByUrl('/signup');\n          return;\n        }\n        // Get end date\n        let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;\n        const currentDate = new Date();\n        // Compare dates properly\n        let isValidPlan = false;\n        if (endDate instanceof Date) {\n          isValidPlan = endDate > currentDate;\n        }\n        if (!isValidPlan) {\n          // Prevent multiple redirects\n          if (_this.isRedirecting) return;\n          _this.isRedirecting = true;\n          setTimeout(() => {\n            _this.router.navigateByUrl('/pricing');\n            setTimeout(() => {\n              _this.isRedirecting = false;\n            }, 2000);\n          }, 500);\n          return;\n        }\n        // Check if we have cached data for this date\n        const dateKey = _this.formatDate(_this.selectedDate);\n        if (_this.questCache[dateKey]) {\n          // Use cached data\n          _this.quests = _this.questCache[dateKey];\n          // Initialize slider backgrounds immediately\n          requestAnimationFrame(() => {\n            _this.initializeSliderBackgrounds();\n          });\n          // Load daily side quest if needed\n          _this.loadDailySideQuest();\n        } else {\n          // Load data with the current selected date\n          _this.loadData();\n        }\n      });\n      // Make sure the URL reflects the selected date and week offset\n      const route = _this.router.url;\n      const dateParam = _this.formatDate(_this.selectedDate);\n      if (route === '/today') {\n        // If we're on the base route, update to include the date and week_offset as query parameters\n        _this.router.navigate(['/today'], {\n          queryParams: {\n            date: dateParam,\n            week_offset: _this.weekOffset !== 0 ? _this.weekOffset : null\n          },\n          replaceUrl: true\n        });\n      }\n    })();\n  }\n  // Initialize all slider backgrounds\n  initializeSliderBackgrounds() {\n    // Use requestAnimationFrame for better performance\n    requestAnimationFrame(() => {\n      const sliders = document.querySelectorAll('.progress-slider');\n      if (sliders.length === 0) {\n        return;\n      }\n      sliders.forEach(slider => {\n        if (slider instanceof HTMLInputElement) {\n          // Get the slider's quest ID for debugging\n          const sliderQuestId = slider.getAttribute('data-quest-id');\n          if (!sliderQuestId) {\n            return;\n          }\n          // Get the exact value from the slider (no rounding)\n          const sliderValue = parseInt(slider.value);\n          const minValue = parseInt(slider.min);\n          const maxValue = parseInt(slider.max);\n          // Calculate the percentage value\n          const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n          // Set the background directly with hardcoded colors\n          slider.style.background = `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\n          // Add a data attribute to track the current value\n          slider.setAttribute('data-current-value', slider.value);\n        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n          // Get the slider's quest ID for debugging\n          const sliderQuestId = slider.getAttribute('data-quest-id');\n          if (!sliderQuestId) {\n            return;\n          }\n          // Get the value from the element's properties or attributes\n          const valueAttr = slider.getAttribute('value') || '0';\n          const minAttr = slider.getAttribute('min') || '0';\n          const maxAttr = slider.getAttribute('max') || '100';\n          const sliderValue = parseInt(valueAttr);\n          const minValue = parseInt(minAttr);\n          const maxValue = parseInt(maxAttr);\n          // Calculate the percentage value\n          const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n          // Set the CSS variable for the progress\n          slider.style.setProperty('--progress-value', `${percentage}%`);\n          // Add a data attribute to track the current value\n          slider.setAttribute('data-current-value', sliderValue.toString());\n        }\n      });\n    });\n  }\n  ionViewWillLeave() {\n    console.log('TodayPage: ionViewWillLeave called');\n  }\n  ngOnDestroy() {\n    console.log('TodayPage: ngOnDestroy called');\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n  loadData() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.userId) {\n        return;\n      }\n      // Update header text\n      _this2.updateHeaderText();\n      // Check if we have cached data for this date\n      const dateKey = _this2.formatDate(_this2.selectedDate);\n      if (_this2.questCache[dateKey]) {\n        // Use cached data\n        _this2.quests = _this2.questCache[dateKey];\n        // Initialize slider backgrounds immediately\n        requestAnimationFrame(() => {\n          _this2.initializeSliderBackgrounds();\n        });\n        // Load daily side quest if needed\n        _this2.loadDailySideQuest();\n        return;\n      }\n      // Set up date variables\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(_this2.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      console.log('TodayPage: Loading data for date:', _this2.formatDate(_this2.selectedDate));\n      if (isTodaySelected) {\n        // Check if we've already calculated streaks for today\n        const todayDateString = _this2.formatDate(today);\n        try {\n          const {\n            value: lastStreakCalculation\n          } = yield _this2.preferencesService.get('last_streak_calculation');\n          if (lastStreakCalculation !== todayDateString) {\n            console.log('TodayPage: First time loading today, calculating streaks');\n            // Najprv spracujeme všetky questy pomocou checkMissedDays\n            yield firstValueFrom(_this2.questService.getQuests(_this2.userId).pipe(take(1), switchMap(/*#__PURE__*/function () {\n              var _ref = _asyncToGenerator(function* (quests) {\n                // Check missed days for each quest\n                for (const quest of quests) {\n                  if (quest.id) {\n                    yield _this2.questService.checkMissedDays(quest.id);\n                  }\n                }\n                // Potom vytvoríme progress záznamy pre quit questy\n                yield _this2.questService.createQuitQuestProgressForToday();\n                // NEBUDEME tu nastavovať last_streak_calculation, aby sa mohli vypočítať streaky v ďalšej časti kódu\n                return quests;\n              });\n              return function (_x) {\n                return _ref.apply(this, arguments);\n              };\n            }())));\n            // Streaky sa vypočítajú v ďalšej časti kódu\n          } else {\n            console.log('TodayPage: Streaks already calculated for today');\n          }\n        } catch (error) {\n          console.error('TodayPage: Error checking last streak calculation:', error);\n          // Ak nastane chyba, nenastavujeme last_streak_calculation, aby sa mohli vypočítať streaky\n        }\n        // Recalculate streak for the daily side quest only for today\n        if (_this2.showSidequests) {\n          _this2.sideQuestService.recalculateSideQuestStreak(_this2.userId, _this2.selectedDate).subscribe({\n            error: error => {\n              console.error('Error recalculating side quest streak:', error);\n            }\n          });\n        }\n      }\n      // Load quests\n      _this2.questService.getQuests(_this2.userId).pipe(take(1), switchMap(quests => {\n        // Filter active quests for the selected date\n        const filteredQuests = _this2.filterQuestsForDate(quests, _this2.selectedDate);\n        if (filteredQuests.length === 0) {\n          return of([]);\n        }\n        // Sort filtered quests by creation date (newest first) or ID\n        const sortedFilteredQuests = [...filteredQuests].sort((a, b) => {\n          if (a.created_at && b.created_at) {\n            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n          }\n          return a.id && b.id ? a.id.localeCompare(b.id) : 0;\n        });\n        // Get all progress for all quests at once\n        return _this2.questService.getQuestProgressForDate(_this2.userId, _this2.selectedDate).pipe(take(1), switchMap(allProgress => {\n          // Create a lookup for quick access\n          const progressLookup = {};\n          allProgress.forEach(progress => {\n            progressLookup[progress.quest_id] = progress;\n          });\n          // For today's view, calculate streaks once per day\n          // For other days, just use the streak from the database\n          if (isTodaySelected) {\n            // Check if we've already calculated streaks for today\n            const todayDateString = _this2.formatDate(today);\n            return _this2.preferencesService.get('last_streak_calculation').then(({\n              value: lastStreakCalculation\n            }) => {\n              if (lastStreakCalculation !== todayDateString) {\n                console.log('TodayPage: First time loading today, calculating streaks');\n                // Calculate streaks using our streak calculator\n                return _this2.streakCalculator.calculateStreaks(_this2.userId, sortedFilteredQuests).then(streaks => {\n                  // Map quests with progress and calculated streaks\n                  return sortedFilteredQuests.map(quest => {\n                    const progress = progressLookup[quest.id];\n                    const calculatedStreak = streaks[quest.id] || 0;\n                    // Update the streak in the database\n                    _this2.questService.updateQuestStreak(quest.id, calculatedStreak).subscribe();\n                    return {\n                      ...quest,\n                      completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                      value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                      streak: calculatedStreak\n                    };\n                  });\n                }).then(result => {\n                  // Po výpočte streakov nastavíme last_streak_calculation\n                  _this2.preferencesService.set('last_streak_calculation', todayDateString);\n                  return result;\n                });\n              } else {\n                console.log('TodayPage: Streaks already calculated for today, using database values');\n                // Just use the streak from the database\n                return sortedFilteredQuests.map(quest => {\n                  const progress = progressLookup[quest.id];\n                  return {\n                    ...quest,\n                    completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                    value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                    streak: quest.streak || 0\n                  };\n                });\n              }\n            }).catch(error => {\n              console.error('TodayPage: Error checking last streak calculation:', error);\n              // If there's an error, just use the streak from the database\n              return sortedFilteredQuests.map(quest => {\n                const progress = progressLookup[quest.id];\n                return {\n                  ...quest,\n                  completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                  value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                  streak: quest.streak || 0\n                };\n              });\n            });\n          } else {\n            // For previous days, just use the streak from the database but set it to 0 for display\n            return Promise.resolve(sortedFilteredQuests.map(quest => {\n              const progress = progressLookup[quest.id];\n              return {\n                ...quest,\n                completed: (progress === null || progress === void 0 ? void 0 : progress.completed) || false,\n                value_achieved: (progress === null || progress === void 0 ? void 0 : progress.value_achieved) || 0,\n                streak: 0 // Don't show streak for previous days\n              };\n            }));\n          }\n        }));\n      })).subscribe({\n        next: questsWithProgress => {\n          // Sort quests by creation date (newest first) or ID\n          const sortedQuests = [...questsWithProgress].sort((a, b) => {\n            if (a.created_at && b.created_at) {\n              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n            }\n            return a.id && b.id ? a.id.localeCompare(b.id) : 0;\n          });\n          // Check if all quests are completed for today\n          _this2.checkAllQuestsCompleted(sortedQuests);\n          // Update the quests array\n          _this2.quests = sortedQuests;\n          // Cache the quests for this date\n          const dateKey = _this2.formatDate(_this2.selectedDate);\n          _this2.questCache[dateKey] = sortedQuests;\n          // Update the week date progress\n          _this2.updateWeekDateProgress();\n          // Initialize slider backgrounds\n          requestAnimationFrame(() => {\n            _this2.initializeSliderBackgrounds();\n          });\n          // Load daily side quest if needed\n          _this2.loadDailySideQuest();\n        },\n        error: error => {\n          console.error('Error loading quests:', error);\n        }\n      });\n    })();\n  }\n  generateWeekDates() {\n    const today = new Date();\n    // Calculate the start of the week based on week offset\n    // This starts on Monday (1) instead of Sunday (0)\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\n    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // Convert to Monday-based (0 = Monday)\n    const startOfWeek = new Date(today);\n    startOfWeek.setDate(today.getDate() - daysFromMonday + 7 * this.weekOffset);\n    this.weekDates = [];\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startOfWeek);\n      date.setDate(startOfWeek.getDate() + i);\n      const dateString = this.formatDate(date);\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n      // Check if we have cached progress for this date\n      const dateKey = dateString;\n      let totalQuests = 0;\n      let completedQuests = 0;\n      let completionPercentage = 0;\n      if (this.weekProgressCache[dateKey]) {\n        const cached = this.weekProgressCache[dateKey];\n        totalQuests = cached.total;\n        completedQuests = cached.completed;\n        completionPercentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n      }\n      this.weekDates.push({\n        date: dateString,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture,\n        total_quests: totalQuests,\n        completed_quests: completedQuests,\n        completion_percentage: completionPercentage\n      });\n    }\n    // Preload data for all days in the week\n    if (this.userId) {\n      // Use setTimeout to allow the UI to render first\n      setTimeout(() => {\n        this.preloadWeekData();\n      }, 0);\n    }\n  }\n  updateWeekDateProgress() {\n    if (!this.userId) return;\n    // For each date in the week, update the progress\n    this.weekDates.forEach((weekDate, index) => {\n      if (weekDate.is_future) return;\n      const date = new Date(weekDate.date);\n      const dateKey = this.formatDate(date);\n      // Check if we have cached progress for this date\n      if (this.weekProgressCache[dateKey]) {\n        const cached = this.weekProgressCache[dateKey];\n        this.weekDates[index].total_quests = cached.total;\n        this.weekDates[index].completed_quests = cached.completed;\n        this.weekDates[index].completion_percentage = cached.total > 0 ? Math.round(cached.completed / cached.total * 100) : 0;\n        return;\n      }\n      // If we have cached quests for this date, use them to calculate progress\n      if (this.questCache[dateKey]) {\n        const cachedQuests = this.questCache[dateKey];\n        const totalQuests = cachedQuests.length;\n        const completedQuests = cachedQuests.filter(q => q.completed).length;\n        // Cache the progress\n        this.weekProgressCache[dateKey] = {\n          total: totalQuests,\n          completed: completedQuests\n        };\n        // Update the week date\n        this.weekDates[index].total_quests = totalQuests;\n        this.weekDates[index].completed_quests = completedQuests;\n        this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n        return;\n      }\n    });\n    // Preload data for all days in the week\n    this.preloadWeekData();\n  }\n  // Helper method to filter quests for a specific date\n  filterQuestsForDate(quests, date) {\n    const dateObj = new Date(date);\n    const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.\n    // Django uses Monday=0, Sunday=6 format, so we need to convert\n    const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format\n    const dayOfMonth = dateObj.getDate(); // 1-31\n    console.log(`TodayPage: Filtering quests for date ${this.formatDate(date)}, day of week: ${dayOfWeek} (Django: ${djangoDayOfWeek}), day of month: ${dayOfMonth}`);\n    const filteredQuests = quests.filter(quest => {\n      console.log(`TodayPage: Checking quest ${quest.id} (${quest.name}), type: ${quest.quest_type}, period: ${quest.goal_period}, task_days_of_week: ${quest.task_days_of_week}, task_days_of_month: ${quest.task_days_of_month}`);\n      if (!quest.active) {\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is not active, filtering out`);\n        return false;\n      }\n      // Only show quests from the date they were created\n      if (quest.created_at) {\n        const createdDate = new Date(quest.created_at);\n        createdDate.setHours(0, 0, 0, 0);\n        dateObj.setHours(0, 0, 0, 0);\n        // If the selected date is before the quest was created, don't show it\n        if (dateObj < createdDate) {\n          return false;\n        }\n      }\n      // Daily quests are always shown\n      if (quest.goal_period === 'day') {\n        return true;\n      }\n      // Weekly quests are shown on specific days\n      if (quest.goal_period === 'week') {\n        if (!quest.task_days_of_week) {\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has no task_days_of_week specified, showing every day`);\n          return true; // If no days specified, show every day\n        }\n        // Parse task_days_of_week\n        let taskDays = [];\n        if (typeof quest.task_days_of_week === 'string') {\n          taskDays = quest.task_days_of_week.split(',').map(day => day.trim());\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as string: ${quest.task_days_of_week}, parsed to:`, taskDays);\n        } else if (Array.isArray(quest.task_days_of_week)) {\n          taskDays = quest.task_days_of_week;\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as array:`, taskDays);\n        }\n        // Check if current day is in task days\n        // Convert current day to different formats for comparison\n        const dayNameShort = this.getDayNameShort(djangoDayOfWeek);\n        const dayNameFull = this.getDayNameFull(djangoDayOfWeek);\n        console.log(`TodayPage: Checking if day ${dayNameFull} (${dayNameShort}, ${djangoDayOfWeek}) is in task days:`, taskDays);\n        const isIncluded = taskDays.includes(djangoDayOfWeek) || taskDays.includes(djangoDayOfWeek.toString()) || taskDays.includes(dayNameShort) || taskDays.includes(dayNameFull);\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) should be shown on ${dayNameFull}? ${isIncluded}`);\n        return isIncluded;\n      }\n      // Monthly quests are shown on specific days of month\n      if (quest.goal_period === 'month') {\n        if (!quest.task_days_of_month) return true; // If no days specified, show every day\n        // Parse task_days_of_month\n        let taskDays = [];\n        if (typeof quest.task_days_of_month === 'string') {\n          taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));\n        } else if (Array.isArray(quest.task_days_of_month)) {\n          taskDays = quest.task_days_of_month;\n        }\n        // Check if current day is in task days\n        return taskDays.includes(dayOfMonth) || taskDays.includes(dayOfMonth.toString());\n      }\n      return false;\n    });\n    console.log(`TodayPage: Filtered ${quests.length} quests to ${filteredQuests.length} for date ${this.formatDate(date)}`);\n    return filteredQuests;\n  }\n  selectDate(dateObj) {\n    if (this.isLoadingData) {\n      return;\n    }\n    const dateData = dateObj.date;\n    this.isLoadingData = true;\n    this.selectedDateData = dateObj;\n    const date = new Date(dateData);\n    this.selectedDate = date;\n    this.weekDates.forEach(weekDate => {\n      weekDate.is_selected = weekDate.date === dateData;\n    });\n    const formattedDate = this.formatDate(date);\n    this.router.navigate(['/today'], {\n      queryParams: {\n        date: formattedDate,\n        week_offset: this.weekOffset !== 0 ? this.weekOffset : null\n      },\n      replaceUrl: true\n    });\n    this.updateHeaderText();\n    setTimeout(() => {\n      this.loadData();\n      this.isLoadingData = false;\n    }, 10);\n  }\n  changeWeek(direction) {\n    // Prevent multiple rapid week changes\n    if (this.isChangingWeek) {\n      return;\n    }\n    this.isChangingWeek = true;\n    // Update the week offset\n    this.weekOffset += direction;\n    // Generate new week dates with the updated offset\n    this.generateWeekDates();\n    // Preload quest data for all days in the week\n    this.preloadWeekData();\n    // Update the URL with the new week offset while preserving the selected date\n    const dateParam = this.formatDate(this.selectedDate);\n    this.router.navigate(['/today'], {\n      queryParams: {\n        date: dateParam,\n        week_offset: this.weekOffset\n      },\n      replaceUrl: true\n    });\n    // Reset the flag after a short delay\n    setTimeout(() => {\n      this.isChangingWeek = false;\n    }, 300);\n  }\n  // Preload data for all days in the current week\n  preloadWeekData() {\n    if (!this.userId) return;\n    // Get all quests once to avoid multiple API calls\n    this.questService.getQuests(this.userId).pipe(take(1)).subscribe(allQuests => {\n      // Create an array of observables for each date\n      const dateObservables = this.weekDates.filter(weekDate => !weekDate.is_future).map(weekDate => {\n        const date = new Date(weekDate.date);\n        const dateKey = this.formatDate(date);\n        // Skip if we already have cached data\n        if (this.weekProgressCache[dateKey]) {\n          return of({\n            date: weekDate.date,\n            progress: this.weekProgressCache[dateKey]\n          });\n        }\n        // Filter active quests for this date\n        const activeQuests = this.filterQuestsForDate(allQuests, date);\n        // If no active quests, return empty progress\n        if (activeQuests.length === 0) {\n          const emptyProgress = {\n            total: 0,\n            completed: 0\n          };\n          this.weekProgressCache[dateKey] = emptyProgress;\n          return of({\n            date: weekDate.date,\n            progress: emptyProgress\n          });\n        }\n        // Get progress for this date\n        return this.questService.getQuestProgressForDate(this.userId, date).pipe(take(1), map(progressList => {\n          // Count completed quests\n          const questIds = activeQuests.map(q => q.id);\n          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\n          const completedQuests = relevantProgress.filter(p => p.completed).length;\n          const totalQuests = activeQuests.length;\n          // Create progress object\n          const progress = {\n            total: totalQuests,\n            completed: completedQuests\n          };\n          // Cache the progress\n          this.weekProgressCache[dateKey] = progress;\n          return {\n            date: weekDate.date,\n            progress\n          };\n        }));\n      });\n      // Process all date observables in parallel\n      forkJoin(dateObservables).subscribe(results => {\n        // Update the week dates with the progress\n        results.forEach(result => {\n          const index = this.weekDates.findIndex(wd => wd.date === result.date);\n          if (index >= 0) {\n            this.weekDates[index].total_quests = result.progress.total;\n            this.weekDates[index].completed_quests = result.progress.completed;\n            this.weekDates[index].completion_percentage = result.progress.total > 0 ? Math.round(result.progress.completed / result.progress.total * 100) : 0;\n          }\n        });\n      });\n    });\n  }\n  updateHeaderText() {\n    const today = new Date();\n    if (this.isSameDay(this.selectedDate, today)) {\n      this.headerText = 'Today';\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() - 1)))) {\n      this.headerText = 'Yesterday';\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() + 2)))) {\n      this.headerText = 'Tomorrow';\n    } else {\n      // Format as \"Mon, 15 Jan\"\n      this.headerText = this.selectedDate.toLocaleDateString('en-US', {\n        weekday: 'short',\n        day: 'numeric',\n        month: 'short'\n      });\n    }\n  }\n  toggleQuest(quest) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.userId || !quest.id) return;\n      // Check if this specific quest is already being toggled\n      if (_this3.togglingQuestIds[quest.id]) {\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is already being toggled, ignoring duplicate call`);\n        return;\n      }\n      // Set flag for this specific quest\n      _this3.togglingQuestIds[quest.id] = true;\n      console.log(`TodayPage: Starting toggle for quest ${quest.id} (${quest.name})`);\n      try {\n        // For normal quests, we don't want to toggle the value when clicking on the quest\n        // Instead, we want to keep the current value from the slider\n        // This is different from the original behavior where clicking would toggle between 0 and goal_value\n        // We'll just log that the quest was clicked but not change any values\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) clicked, keeping current value: ${quest.value_achieved}`);\n        // No need to update the database since we're not changing any values\n        // Just release the flag and return\n        delete _this3.togglingQuestIds[quest.id];\n        return;\n      } catch (error) {\n        console.error(`TodayPage: Error in toggleQuest for ${quest.id} (${quest.name}):`, error);\n      } finally {\n        // Reset flag for this specific quest\n        delete _this3.togglingQuestIds[quest.id];\n        console.log(`TodayPage: Finished toggle for quest ${quest.id} (${quest.name})`);\n      }\n    })();\n  }\n  updateQuestProgress(quest, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.userId || !quest.id) return;\n      // Check if this specific quest is already being updated\n      if (_this4.updatingQuestIds[quest.id]) {\n        return;\n      }\n      // Set flag for this specific quest\n      _this4.updatingQuestIds[quest.id] = true;\n      try {\n        // Store the original completed state before any changes\n        const wasCompletedBefore = quest.completed;\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) original completed state: ${wasCompletedBefore}`);\n        // Update the slider background if an event is provided\n        if (event) {\n          // Handle both standard Event and Ionic's CustomEvent\n          const slider = event.target || (event.detail ? event.detail.value : null);\n          _this4.updateSliderBackground(slider);\n          // Verify that the slider is for the correct quest\n          const sliderQuestId = slider instanceof HTMLElement ? slider.getAttribute('data-quest-id') : null;\n          if (sliderQuestId && sliderQuestId !== quest.id) {\n            delete _this4.updatingQuestIds[quest.id];\n            return;\n          }\n          // Get the value from the slider\n          let sliderValue = 0;\n          if (event.detail && event.detail.value !== undefined) {\n            // Ionic range event\n            sliderValue = event.detail.value;\n          } else if (slider instanceof HTMLInputElement) {\n            // Standard input event\n            sliderValue = parseInt(slider.value);\n          } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n            // Ionic range element\n            const valueAttr = slider.getAttribute('value') || '0';\n            sliderValue = parseInt(valueAttr);\n          }\n          // Update the quest's value_achieved with the slider value\n          quest.value_achieved = sliderValue;\n          // Update completed status based on quest type and value\n          // This exactly matches the Django implementation in toggle_quest view\n          if (quest.quest_type === 'build') {\n            // For build quests, completed when value >= goal\n            quest.completed = sliderValue >= quest.goal_value;\n          } else {\n            // 'quit' type\n            // For quit quests, completed when value < goal (opposite of build)\n            quest.completed = sliderValue < quest.goal_value;\n          }\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) new completed state: ${quest.completed}`);\n        }\n        // Make a deep copy of the quest to avoid reference issues\n        const questCopy = {\n          ...quest\n        };\n        // Call the service and get the updated values\n        const result = yield _this4.questService.toggleQuestCompletion(_this4.userId, quest.id, _this4.selectedDate, quest.value_achieved, questCopy);\n        // Update the quest in the UI with the returned values\n        quest.completed = result.completed;\n        quest.value_achieved = result.value_achieved;\n        // Get today's date for comparison\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = new Date(_this4.selectedDate);\n        selectedDate.setHours(0, 0, 0, 0);\n        const isTodaySelected = selectedDate.getTime() === today.getTime();\n        // Handle streak calculation differently based on whether we're in today's view or a previous day\n        if (isTodaySelected) {\n          // For today's view, manually calculate the streak by going backward from today\n          // until we find a non-completed progress entry\n          // Use the streak from the result (from Supabase)\n          let streak = result.streak;\n          // Get the current completed state after the update\n          const isCompletedNow = quest.completed;\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status: was ${wasCompletedBefore}, now ${isCompletedNow}`);\n          // Only update streak if the completion status has changed\n          if (wasCompletedBefore !== isCompletedNow) {\n            if (isCompletedNow) {\n              // Changed from incomplete to complete\n              streak++;\n              console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from incomplete to complete, streak increased to ${streak}`);\n            } else {\n              // Changed from complete to incomplete\n              streak = Math.max(0, streak - 1);\n              console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from complete to incomplete, streak decreased to ${streak}`);\n            }\n            // Update the streak in the database\n            _this4.questService.updateQuestStreak(quest.id, streak).subscribe({\n              next: () => {\n                console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${streak}`);\n                // Update the quest in the cache\n                const dateKey = _this4.formatDate(_this4.selectedDate);\n                if (_this4.questCache[dateKey]) {\n                  const cachedQuestIndex = _this4.questCache[dateKey].findIndex(q => q.id === quest.id);\n                  if (cachedQuestIndex >= 0) {\n                    _this4.questCache[dateKey][cachedQuestIndex].streak = streak;\n                  }\n                }\n              },\n              error: error => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\n            });\n          } else {\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status did not change, keeping streak at ${streak}`);\n          }\n        } else {\n          // For previous days, recalculate streak for today\n          console.log(`TodayPage: Quest toggled in previous day (${_this4.formatDate(_this4.selectedDate)}), recalculating streak for today`);\n          // Get the quest details\n          _this4.questService.getQuest(quest.id).subscribe(questDetails => {\n            if (!questDetails) {\n              console.error(`TodayPage: Could not get quest details for ${quest.id}`);\n              return;\n            }\n            // Calculate the streak for today using our streak calculator\n            _this4.streakCalculator.calculateStreak(_this4.userId, quest.id).then(calculatedStreak => {\n              console.log(`TodayPage: Recalculated streak for quest ${quest.id} for today: ${calculatedStreak}`);\n              // Update the streak in the database\n              _this4.questService.updateQuestStreak(quest.id, calculatedStreak).subscribe({\n                next: () => {\n                  console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${calculatedStreak}`);\n                  // Clear today's cache for next time\n                  const todayString = _this4.formatDate(today);\n                  console.log('TodayPage: Clearing today\\'s cache to force reload of updated streak next time today is viewed');\n                  delete _this4.questCache[todayString];\n                  // If we have today's date in the week view, update its progress\n                  const todayIndex = _this4.weekDates.findIndex(wd => wd.date === todayString);\n                  if (todayIndex >= 0) {\n                    delete _this4.weekProgressCache[todayString];\n                    _this4.updateProgressRingForDate(todayString);\n                  }\n                },\n                error: error => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\n              });\n            }).catch(error => {\n              console.error(`TodayPage: Error calculating streak for quest ${quest.id}:`, error);\n            });\n          });\n        }\n        // Update the UI element for this quest\n        _this4.updateQuestUI(quest);\n        // Cache the updated quest data and update progress ring\n        const dateKey = _this4.formatDate(_this4.selectedDate);\n        if (_this4.questCache[dateKey]) {\n          // Find and update the quest in the cache\n          const cachedQuestIndex = _this4.questCache[dateKey].findIndex(q => q.id === quest.id);\n          if (cachedQuestIndex >= 0) {\n            _this4.questCache[dateKey][cachedQuestIndex] = {\n              ...quest\n            };\n          }\n        }\n        // Clear the cache for this date to force a refresh\n        delete _this4.weekProgressCache[dateKey];\n        // Update the progress ring for this date\n        _this4.updateProgressRingForDate(dateKey);\n        // Check if all quests are completed\n        _this4.checkAllQuestsCompleted(_this4.quests);\n      } catch (error) {\n        console.error(`TodayPage: Error updating quest progress:`, error);\n      } finally {\n        // Reset flag for this specific quest\n        delete _this4.updatingQuestIds[quest.id];\n      }\n    })();\n  }\n  // Helper method to update the progress ring for a specific date\n  updateProgressRingForDate(dateKey) {\n    // Find the index of this date in weekDates\n    const index = this.weekDates.findIndex(wd => wd.date === dateKey);\n    if (index < 0) return;\n    // If we have cached quests for this date, use them to calculate progress\n    if (this.questCache[dateKey]) {\n      const cachedQuests = this.questCache[dateKey];\n      const totalQuests = cachedQuests.length;\n      const completedQuests = cachedQuests.filter(q => q.completed).length;\n      // Cache the progress\n      this.weekProgressCache[dateKey] = {\n        total: totalQuests,\n        completed: completedQuests\n      };\n      // Update the week date\n      this.weekDates[index].total_quests = totalQuests;\n      this.weekDates[index].completed_quests = completedQuests;\n      this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n      return;\n    }\n    // If no cached quests, fetch from server\n    if (this.userId) {\n      const date = new Date(dateKey);\n      this.questService.getQuestProgressForDate(this.userId, date).pipe(take(1)).subscribe(progressList => {\n        this.questService.getQuests(this.userId).pipe(take(1)).subscribe(quests => {\n          // Filter active quests for this date\n          const activeQuests = this.filterQuestsForDate(quests, date);\n          // Count completed quests\n          const questIds = activeQuests.map(q => q.id);\n          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\n          const completedQuests = relevantProgress.filter(p => p.completed).length;\n          const totalQuests = activeQuests.length;\n          // Cache the progress\n          this.weekProgressCache[dateKey] = {\n            total: totalQuests,\n            completed: completedQuests\n          };\n          // Update the week date\n          this.weekDates[index].total_quests = totalQuests;\n          this.weekDates[index].completed_quests = completedQuests;\n          this.weekDates[index].completion_percentage = totalQuests > 0 ? Math.round(completedQuests / totalQuests * 100) : 0;\n        });\n      });\n    }\n  }\n  // Helper method to update the UI for a specific quest\n  updateQuestUI(quest) {\n    // Find the quest element in the DOM\n    const questElement = document.querySelector(`[data-quest-id=\"${quest.id}\"]`);\n    if (!questElement) {\n      console.error(`TodayPage: Could not find quest element for ID: ${quest.id}`);\n      return;\n    }\n    // Update the completed class\n    if (quest.completed) {\n      questElement.classList.add('completed');\n    } else {\n      questElement.classList.remove('completed');\n    }\n    // Update the streak display - only show streak for today\n    const streakElements = questElement.querySelectorAll('.quest-streak');\n    if (streakElements && streakElements.length > 0) {\n      // Get today's date for comparison\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(this.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      // Only show streak for today's view\n      if (isTodaySelected) {\n        const streakValue = quest.streak || 0;\n        console.log(`TodayPage: Quest ${quest.id}, completed: ${quest.completed}, streak: ${streakValue}`);\n        // Update all streak elements (there might be multiple due to ngIf)\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            // Make sure the streak is visible\n            element.style.display = 'block';\n            element.textContent = `🔥${streakValue}d`;\n          }\n        });\n      } else {\n        // Hide streak for previous days\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            element.style.display = 'none';\n            element.textContent = '';\n          }\n        });\n      }\n    }\n    // Update the progress text\n    const progressText = questElement.querySelector('.progress-text');\n    if (progressText) {\n      var _progressText$parentE;\n      const isTimeUnit = (_progressText$parentE = progressText.parentElement) === null || _progressText$parentE === void 0 ? void 0 : _progressText$parentE.classList.contains('progress-time');\n      const unitSuffix = isTimeUnit ? 'm' : '';\n      const goalUnitSuffix = quest.goal_unit !== 'count' && !isTimeUnit ? ` ${quest.goal_unit}` : '';\n      progressText.textContent = `${quest.value_achieved}${unitSuffix}/${quest.goal_value}${unitSuffix}${goalUnitSuffix}`;\n    }\n    console.log(`TodayPage: Updated UI for quest ${quest.id}`);\n  }\n  // Update slider background based on value\n  updateSliderBackground(slider) {\n    if (!slider) {\n      return;\n    }\n    // Handle different types of slider elements\n    let sliderElement;\n    let sliderValue = 0;\n    let minValue = 0;\n    let maxValue = 100;\n    let sliderQuestId = '';\n    if (slider instanceof HTMLInputElement) {\n      // Standard HTML input range\n      sliderElement = slider;\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\n      sliderValue = parseInt(slider.value);\n      minValue = parseInt(slider.min);\n      maxValue = parseInt(slider.max);\n    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n      // Ionic range element\n      sliderElement = slider;\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\n      // Get the value from the element's properties or attributes\n      const valueAttr = slider.getAttribute('value') || '0';\n      const minAttr = slider.getAttribute('min') || '0';\n      const maxAttr = slider.getAttribute('max') || '100';\n      sliderValue = parseInt(valueAttr);\n      minValue = parseInt(minAttr);\n      maxValue = parseInt(maxAttr);\n    } else {\n      return;\n    }\n    if (!sliderQuestId) {\n      return;\n    }\n    // Calculate the percentage value\n    const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n    // For Ionic range, we need to set the CSS variable\n    if (sliderElement.tagName === 'ION-RANGE') {\n      sliderElement.style.setProperty('--progress-value', `${percentage}%`);\n    } else {\n      // Set the background directly with hardcoded colors for standard HTML input\n      sliderElement.style.background = `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\n    }\n    // Add a data attribute to track the current value\n    sliderElement.setAttribute('data-current-value', sliderValue.toString());\n  }\n  /**\n   * Toggle side quest completion\n   * This matches the Django implementation in toggle_daily_side_quest view\n   * Side quests are always toggled between 0 and goal value\n   */\n  toggleSideQuest(sideQuest) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.userId || !sideQuest.id) return;\n      // Check if this specific side quest is already being toggled\n      if (_this5.togglingSideQuestIds[sideQuest.id]) {\n        console.log(`TodayPage: Side quest ${sideQuest.id} is already being toggled, ignoring duplicate call`);\n        return;\n      }\n      // Set flag for this specific side quest\n      _this5.togglingSideQuestIds[sideQuest.id] = true;\n      console.log(`TodayPage: Starting toggle for side quest ${sideQuest.id}`);\n      try {\n        // For side quests, we always toggle between 0 and goal value\n        // This matches the Django implementation where side quests are either completed or not\n        console.log(`TodayPage: Click event on side quest ${sideQuest.id}`);\n        // Toggle the value immediately for better UI feedback\n        const newValue = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\n        const newCompletedState = newValue === sideQuest.current_quest.goal_value;\n        // Update local state first for immediate feedback\n        sideQuest.value_achieved = newValue;\n        sideQuest.completed = newCompletedState;\n        console.log(`TodayPage: Updated side quest ${sideQuest.id} value to ${sideQuest.value_achieved}, completed: ${sideQuest.completed}`);\n        // Get today's date for comparison\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = new Date(_this5.selectedDate);\n        selectedDate.setHours(0, 0, 0, 0);\n        const isToday = selectedDate.getTime() === today.getTime();\n        // Only allow toggling side quests for today\n        if (!isToday) {\n          console.log(`TodayPage: Cannot toggle side quest for past date: ${_this5.formatDate(_this5.selectedDate)}`);\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n          return;\n        }\n        // Update the UI element immediately for better feedback\n        _this5.updateSideQuestUI(sideQuest);\n        try {\n          const result = yield _this5.sideQuestService.toggleSideQuestCompletion(sideQuest.id, _this5.userId, _this5.selectedDate // Pass the selected date\n          );\n          console.log(`TodayPage: Successfully toggled side quest ${sideQuest.id}`);\n          console.log(`TodayPage: Updated values:`, result);\n          // Update the side quest in the UI with the returned values from the server\n          sideQuest.completed = result.completed;\n          sideQuest.value_achieved = result.value_achieved;\n          sideQuest.streak = result.streak;\n          // Update the UI element with the updated streak\n          _this5.updateSideQuestUI(sideQuest);\n          // Update the week date progress for the selected date\n          // Clear the cache for this date to force a refresh\n          const dateKey = _this5.formatDate(_this5.selectedDate);\n          delete _this5.weekProgressCache[dateKey];\n          // Update the progress ring for this date\n          _this5.updateProgressRingForDate(dateKey);\n          // Check if all quests are completed\n          _this5.checkAllQuestsCompleted(_this5.quests);\n          // Reset flag for this specific side quest\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n          console.log(`TodayPage: Finished toggle for side quest ${sideQuest.id}`);\n        } catch (error) {\n          console.error(`TodayPage: Error toggling side quest ${sideQuest.id}:`, error);\n          // Revert the local state if the server update failed\n          sideQuest.value_achieved = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\n          sideQuest.completed = sideQuest.value_achieved === sideQuest.current_quest.goal_value;\n          _this5.updateSideQuestUI(sideQuest);\n          delete _this5.togglingSideQuestIds[sideQuest.id];\n        }\n      } catch (error) {\n        console.error(`TodayPage: Error in toggleSideQuest for ${sideQuest.id}:`, error);\n        delete _this5.togglingSideQuestIds[sideQuest.id];\n      }\n    })();\n  }\n  // Helper method to update the UI for a specific side quest\n  updateSideQuestUI(sideQuest) {\n    // Find the side quest element in the DOM\n    const questElement = document.querySelector(`.daily-side-quest [data-quest-id=\"${sideQuest.id}\"]`);\n    if (!questElement) {\n      console.error(`TodayPage: Could not find side quest element for ID: ${sideQuest.id}`);\n      return;\n    }\n    // Update the completed class\n    if (sideQuest.completed) {\n      questElement.classList.add('completed');\n    } else {\n      questElement.classList.remove('completed');\n    }\n    // Update the streak display - only show streak for today\n    const streakElements = questElement.querySelectorAll('.quest-streak');\n    if (streakElements && streakElements.length > 0) {\n      // Get today's date for comparison\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = new Date(this.selectedDate);\n      selectedDate.setHours(0, 0, 0, 0);\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\n      // Only show streak for today's view\n      if (isTodaySelected) {\n        const streakValue = sideQuest.streak || 0;\n        console.log(`TodayPage: Side quest ${sideQuest.id}, completed: ${sideQuest.completed}, streak: ${streakValue}`);\n        // Update all streak elements (there might be multiple due to ngIf)\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            // Make sure the streak is visible\n            element.style.display = 'block';\n            element.textContent = `🔥${streakValue}d`;\n          }\n        });\n      } else {\n        // Hide streak for previous days\n        streakElements.forEach(element => {\n          if (element.parentElement && element.parentElement.contains(element)) {\n            element.style.display = 'none';\n            element.textContent = '';\n          }\n        });\n      }\n    }\n    // Update the progress text\n    const progressText = questElement.querySelector('.progress-text');\n    if (progressText) {\n      const goalUnit = sideQuest.current_quest.goal_unit !== 'count' ? ` ${sideQuest.current_quest.goal_unit}` : '';\n      progressText.textContent = `${sideQuest.value_achieved}/${sideQuest.current_quest.goal_value}${goalUnit}`;\n    }\n    // Force a repaint to ensure the UI updates\n    setTimeout(() => {\n      if (questElement.parentElement) {\n        const display = questElement.parentElement.style.display;\n        questElement.parentElement.style.display = 'none';\n        // Force a reflow\n        void questElement.parentElement.offsetHeight;\n        questElement.parentElement.style.display = display;\n      }\n    }, 0);\n    console.log(`TodayPage: Updated UI for side quest ${sideQuest.id}`);\n  }\n  openAddQuestModal(event) {\n    event.preventDefault();\n    this.showAddQuestModal = true;\n    this.newQuest = this.getEmptyQuest();\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    // Reset hasHighPriorityQuest flag\n    this.hasHighPriorityQuest = false;\n    // Reset animation states\n    this.resetAnimationStates();\n    // Start quest type animation after modal opens\n    setTimeout(() => {\n      console.log('Setting questTypeAnimated to true');\n      this.questTypeAnimated = true;\n    }, 300);\n  }\n  resetAnimationStates() {\n    this.questTypeAnimated = false;\n    this.questTypeAnimating = false;\n    this.selectedQuestType = '';\n    this.categoryAnimated = false;\n    this.categoryAnimating = false;\n    this.categorySelected = false;\n    this.selectedCategory = '';\n    this.priorityAnimated = false;\n    this.priorityAnimating = false;\n    this.goalAnimated = false;\n    this.questDetailsAnimated = false;\n    this.frequencyAnimated = false;\n    this.frequencyOptionsAnimated = false;\n    this.previewAnimated = false;\n  }\n  closeAddQuestModal() {\n    this.showAddQuestModal = false;\n    // Reset form state\n    this.newQuest = this.getEmptyQuest();\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    this.hasHighPriorityQuest = false;\n    this.resetAnimationStates();\n  }\n  // Animation methods\n  selectQuestType(type) {\n    this.selectedQuestType = type;\n    this.questTypeAnimating = true;\n    // Start slide out animation\n    setTimeout(() => {\n      this.newQuest.quest_type = type;\n      // After quest type is set, trigger category animation\n      setTimeout(() => {\n        this.categoryAnimated = true;\n      }, 100);\n    }, 300); // Half of the animation duration\n  }\n  selectCategory(category) {\n    this.selectedCategory = category;\n    this.categoryAnimating = true;\n    // Start slide out animation based on category\n    setTimeout(() => {\n      this.newQuest.category = category;\n      this.categorySelected = true;\n      this.checkCategoryPriority({\n        detail: {\n          value: category\n        }\n      });\n      // After category is set, trigger priority animation\n      setTimeout(() => {\n        this.priorityAnimated = true;\n      }, 100);\n    }, 300); // Half of the animation duration\n  }\n  selectPriority(priority) {\n    if (priority === 'high' && this.hasHighPriorityQuest) {\n      return; // Don't allow high priority if already exists\n    }\n    this.priorityAnimating = true;\n    setTimeout(() => {\n      this.newQuest.priority = priority;\n    }, 300);\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n      // If moving to step 5, trigger sequential animations\n      if (this.currentStep === 5) {\n        setTimeout(() => {\n          this.questDetailsAnimated = true;\n        }, 100);\n        setTimeout(() => {\n          this.goalAnimated = true;\n        }, 300);\n        setTimeout(() => {\n          this.frequencyAnimated = true;\n        }, 500);\n        setTimeout(() => {\n          this.previewAnimated = true;\n        }, 700);\n      }\n    }\n  }\n  prevStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  moveCaretToEnd() {\n    setTimeout(() => {\n      this.emojiInput.getInputElement().then(input => {\n        const pos = input.value.length;\n        input.setSelectionRange(pos, pos);\n        input.scrollLeft = input.scrollWidth;\n      });\n    }, 100);\n  }\n  get progress() {\n    return this.currentStep / this.totalSteps;\n  }\n  createQuest() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.userId || !_this6.newQuest.name || !_this6.newQuest.emoji || !_this6.newQuest.quest_type || !_this6.newQuest.category || !_this6.newQuest.goal_value || !_this6.newQuest.goal_unit || !_this6.newQuest.goal_period) {\n        console.error('TodayPage: Cannot create quest - missing required fields');\n        return;\n      }\n      try {\n        if (_this6.newQuest.goal_period === 'week' && _this6.selectedDaysOfWeek.length > 0) {\n          _this6.newQuest.task_days_of_week = _this6.selectedDaysOfWeek.join(',');\n        } else if (_this6.newQuest.goal_period === 'month' && _this6.selectedDaysOfMonth.length > 0) {\n          _this6.newQuest.task_days_of_month = _this6.selectedDaysOfMonth.join(',');\n        }\n        const {\n          data: userProfile,\n          error: userError\n        } = yield _this6.supabaseService.getClient().from('profiles').select('id').eq('id', _this6.userId).single();\n        if (userError || !userProfile) {\n          console.error('TodayPage: User profile not found:', userError || 'No profile found');\n          throw new Error('User profile not found. Please ensure you are logged in.');\n        }\n        const questToCreate = {\n          name: _this6.newQuest.name || '',\n          description: _this6.newQuest.description || '',\n          quest_type: _this6.newQuest.quest_type || 'build',\n          goal_value: _this6.newQuest.goal_value || 1,\n          goal_unit: _this6.newQuest.goal_unit || 'count',\n          goal_period: _this6.newQuest.goal_period || 'day',\n          priority: _this6.newQuest.priority || 'basic',\n          category: _this6.newQuest.category || 'strength',\n          emoji: _this6.newQuest.emoji || '🎯',\n          task_days_of_week: _this6.newQuest.task_days_of_week || '',\n          task_days_of_month: _this6.newQuest.task_days_of_month || '',\n          user_id: _this6.userId,\n          active: true\n        };\n        try {\n          const questId = yield _this6.questService.createQuest(questToCreate);\n          if (_this6.newQuest.quest_type === 'quit') {\n            yield _this6.questService.toggleQuestCompletion(_this6.userId, questId, new Date(), 0, {\n              ...questToCreate,\n              id: questId\n            });\n          }\n          const dateKey = _this6.formatDate(_this6.selectedDate);\n          delete _this6.questCache[dateKey];\n          delete _this6.weekProgressCache[dateKey];\n          _this6.closeAddQuestModal();\n          _this6.loadData();\n        } catch (questError) {\n          console.error('TodayPage: Error creating quest:', questError);\n          if (questError.message && questError.message.includes('foreign key constraint')) {\n            alert('Database configuration issue detected. Please run the fix_quest_constraints.sql script in the Supabase SQL Editor to fix the foreign key constraints.');\n          } else if (questError.message && questError.message.includes('fix_quest_constraints.sql')) {\n            alert(questError.message);\n          } else {\n            alert(`Error creating quest: ${questError.message}`);\n          }\n        }\n      } catch (error) {\n        console.error('TodayPage: Error in createQuest:', error);\n        alert(`Error: ${error.message || 'Unknown error occurred'}`);\n      }\n    })();\n  }\n  updateDaysOfWeek(day) {\n    const index = this.selectedDaysOfWeek.indexOf(day);\n    if (index !== -1) {\n      this.selectedDaysOfWeek.splice(index, 1);\n    } else {\n      this.selectedDaysOfWeek.push(day);\n    }\n  }\n  updateDaysOfMonth(event, day) {\n    // Handle both standard Event and Ionic's CustomEvent\n    let isChecked = false;\n    if (event.detail !== undefined) {\n      // Ionic checkbox event\n      isChecked = event.detail.checked;\n    } else if (event.target instanceof HTMLInputElement) {\n      // Standard checkbox event\n      isChecked = event.target.checked;\n    }\n    if (isChecked) {\n      this.selectedDaysOfMonth.push(day);\n    } else {\n      const index = this.selectedDaysOfMonth.indexOf(day);\n      if (index !== -1) {\n        this.selectedDaysOfMonth.splice(index, 1);\n      }\n    }\n    console.log(`TodayPage: Updated days of month: ${this.selectedDaysOfMonth.join(', ')}`);\n  }\n  updatePeriodDisplay() {\n    // Reset selections when period changes\n    this.selectedDaysOfWeek = [];\n    this.selectedDaysOfMonth = [];\n    console.log(`TodayPage: Period changed to ${this.newQuest.goal_period}, reset selections`);\n    // Trigger frequency options animation\n    setTimeout(() => {\n      this.frequencyOptionsAnimated = true;\n    }, 200);\n  }\n  toggleMonthDay(day) {\n    const index = this.selectedDaysOfMonth.indexOf(day);\n    if (index !== -1) {\n      this.selectedDaysOfMonth.splice(index, 1);\n    } else {\n      this.selectedDaysOfMonth.push(day);\n    }\n  }\n  getCategoryIcon(category) {\n    const icons = {\n      'strength': '💪',\n      'money': '💰',\n      'health': '🏥',\n      'knowledge': '🧠'\n    };\n    return icons[category] || '📌';\n  }\n  selectFrequency(period) {\n    this.newQuest.goal_period = period;\n    this.updatePeriodDisplay();\n  }\n  getFrequencyText() {\n    if (this.newQuest.goal_period === 'day') {\n      return 'daily';\n    } else if (this.newQuest.goal_period === 'week') {\n      if (this.selectedDaysOfWeek.length === 0) {\n        return 'weekly';\n      } else if (this.selectedDaysOfWeek.length === 7) {\n        return 'daily';\n      } else {\n        return `${this.selectedDaysOfWeek.length}x per week`;\n      }\n    } else if (this.newQuest.goal_period === 'month') {\n      if (this.selectedDaysOfMonth.length === 0) {\n        return 'monthly';\n      } else {\n        return `${this.selectedDaysOfMonth.length}x per month`;\n      }\n    }\n    return '';\n  }\n  checkCategoryPriority(event) {\n    if (!this.userId || !this.newQuest.category) return;\n    // If this is an Ionic event, make sure we have the latest category value\n    if (event && event.detail) {\n      this.newQuest.category = event.detail.value;\n      console.log(`TodayPage: Category changed to ${this.newQuest.category} via Ionic event`);\n    }\n    // Check if user already has a high priority quest in this category\n    this.questService.getQuests(this.userId).pipe(take(1), map(quests => {\n      return quests.some(q => q.category === this.newQuest.category && q.priority === 'high' && q.active);\n    })).subscribe({\n      next: hasHighPriority => {\n        this.hasHighPriorityQuest = hasHighPriority;\n        // If user already has a high priority quest, set this one to basic\n        if (hasHighPriority) {\n          this.newQuest.priority = 'basic';\n        }\n        console.log(`TodayPage: Category ${this.newQuest.category} has high priority quest: ${hasHighPriority}`);\n      }\n    });\n  }\n  /**\n   * Check if all quests are completed for today and show celebration if enabled\n   */\n  checkAllQuestsCompleted(quests) {\n    // Only check for today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const selectedDate = new Date(this.selectedDate);\n    selectedDate.setHours(0, 0, 0, 0);\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\n    const todayStr = this.formatDate(today);\n    if (!isTodaySelected || !this.currentUser) {\n      return;\n    }\n    // Check if celebration has already been shown for today\n    const celebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\n    if (celebrationShown) {\n      console.log('TodayPage: Celebration already shown for today:', todayStr);\n      return;\n    }\n    // Check if all quests are completed\n    const allQuestsCompleted = quests.length > 0 && quests.every(quest => quest.completed);\n    // Check if side quest is completed (if enabled)\n    const sideQuestCompleted = !this.showSidequests || !this.dailyQuest || this.dailyQuest.completed;\n    // Show celebration if all quests and side quests are completed and celebration is enabled\n    if (allQuestsCompleted && sideQuestCompleted && this.currentUser.show_celebration) {\n      // Make sure we have the latest user data\n      this.userService.getUserById(this.userId).subscribe(userData => {\n        if (userData) {\n          this.currentUser = userData;\n        }\n        // Show the celebration\n        this.showCelebration = true;\n        // Save today's date to localStorage\n        localStorage.setItem(`celebration_shown_${todayStr}`, 'true');\n        // Update our tracking array\n        if (!this.celebrationShownDates.includes(todayStr)) {\n          this.celebrationShownDates.push(todayStr);\n        }\n      });\n    }\n  }\n  /**\n   * Close the celebration modal\n   */\n  closeCelebration() {\n    this.showCelebration = false;\n  }\n  // Helper methods\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  isSameDay(date1, date2) {\n    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n  }\n  getToday() {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  // Convert Django day index (0=Monday, 6=Sunday) to short day name\n  getDayNameShort(djangoDayIndex) {\n    // Map Django day index to day name\n    const dayMap = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];\n    return dayMap[djangoDayIndex];\n  }\n  // Convert Django day index (0=Monday, 6=Sunday) to full day name\n  getDayNameFull(djangoDayIndex) {\n    // Map Django day index to full day name\n    const dayMap = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    return dayMap[djangoDayIndex];\n  }\n  getEmptyQuest() {\n    return {\n      name: '',\n      description: '',\n      quest_type: '',\n      // Empty by default, user must select\n      goal_value: 1,\n      goal_unit: 'count',\n      goal_period: 'day',\n      priority: 'basic',\n      // Default to basic priority\n      category: '',\n      emoji: '🎯'\n    };\n  }\n};\n__decorate([ViewChild('emojiInput')], TodayPage.prototype, \"emojiInput\", void 0);\nTodayPage = __decorate([Component({\n  selector: 'app-today',\n  templateUrl: './today.page.html',\n  styleUrls: ['./today.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, NavigationComponent, CelebrationComponent, EmojiInputDirective, HeaderComponent, AuroraComponent]\n})], TodayPage);\nexport { TodayPage };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "CommonModule", "FormsModule", "IonicModule", "QuestService", "SideQuestService", "UserService", "SupabaseService", "Subscription", "fork<PERSON><PERSON>n", "map", "of", "switchMap", "take", "firstValueFrom", "NavigationComponent", "CelebrationComponent", "ActivatedRoute", "Router", "PreferencesService", "EmojiInputDirective", "StreakCalculatorService", "HeaderComponent", "AuroraComponent", "TodayPage", "loadDailySideQuest", "today", "Date", "setHours", "selectedDate", "isTodaySelected", "getTime", "dailyQuest", "showSidequests", "userId", "sideQuestService", "ensureUserHasDailySideQuests", "pipe", "subscribe", "next", "sideQuests", "length", "sideQuest", "supabaseService", "getClient", "from", "select", "eq", "current_quest_id", "single", "then", "response", "error", "questDetails", "data", "id", "current_quest", "name", "description", "goal_value", "goal_unit", "streak", "completed", "value_achieved", "emoji", "constructor", "user$", "weekDates", "dayNames", "headerText", "weekOffset", "quests", "currentStep", "totalSteps", "emojis", "questCache", "isLoadingData", "showAddQuestModal", "newQuest", "getEmptyQuest", "hasHighPriorityQuest", "questTypeAnimated", "questTypeAnimating", "selectedQuestType", "categoryAnimated", "categoryAnimating", "categorySelected", "selectedCate<PERSON><PERSON>", "priorityAnimated", "priorityAnimating", "goalAnimated", "questDetailsAnimated", "frequencyAnimated", "frequencyOptionsAnimated", "previewAnimated", "showCelebration", "currentUser", "celebrationShownDates", "weekDays", "value", "label", "fullName", "monthDays", "Array", "_", "i", "selectedDaysOfWeek", "selectedDaysOfMonth", "questService", "userService", "route", "router", "preferencesService", "streakCalculator", "isRedirecting", "weekProgressCache", "isChangingWeek", "togglingQuestIds", "updatingQuestIds", "togglingSideQuestIds", "queryParams", "params", "dateParam", "weekOffsetParam", "console", "log", "undefined", "parseInt", "test", "generateWeekDates", "updateHeaderText", "loadData", "userSubscription", "currentUser$", "authUser", "getUserById", "userData", "userDataSubscription", "user", "sidequests_switch", "add", "ngOnInit", "setTimeout", "preloadWeekData", "todayStr", "formatDate", "allKeys", "localStorage", "key", "push", "for<PERSON>ach", "startsWith", "removeItem", "todayCelebrationShown", "getItem", "ionViewWillEnter", "_this", "_asyncToGenerator", "_currentUser", "ensureUserExists", "navigateByUrl", "endDate", "end_of_current_plan", "currentDate", "isValidPlan", "<PERSON><PERSON><PERSON>", "requestAnimationFrame", "initializeSliderBackgrounds", "url", "navigate", "date", "week_offset", "replaceUrl", "sliders", "document", "querySelectorAll", "slider", "HTMLInputElement", "sliderQuestId", "getAttribute", "slider<PERSON><PERSON><PERSON>", "minValue", "min", "maxValue", "max", "percentage", "style", "background", "setAttribute", "HTMLElement", "tagName", "valueAttr", "minAttr", "maxAttr", "setProperty", "toString", "ionViewWillLeave", "ngOnDestroy", "unsubscribe", "_this2", "todayDateString", "lastStreakCalculation", "get", "getQuests", "_ref", "quest", "checkMissedDays", "createQuitQuestProgressForToday", "_x", "apply", "arguments", "recalculateSideQuestStreak", "filteredQuests", "filterQuestsForDate", "sortedFilteredQuests", "sort", "a", "b", "created_at", "localeCompare", "getQuestProgressForDate", "allProgress", "progressLookup", "progress", "quest_id", "calculateStreaks", "streaks", "calculatedStreak", "updateQuestStreak", "result", "set", "catch", "Promise", "resolve", "questsWithProgress", "sortedQuests", "checkAllQuestsCompleted", "updateWeekDateProgress", "currentDay", "getDay", "daysFromMonday", "startOfWeek", "setDate", "getDate", "dateString", "isToday", "isSameDay", "isSelected", "isFuture", "totalQuests", "completedQuests", "completionPercentage", "cached", "total", "Math", "round", "day", "is_today", "is_selected", "is_future", "total_quests", "completed_quests", "completion_percentage", "weekDate", "index", "cachedQuests", "filter", "q", "date<PERSON><PERSON>j", "dayOfWeek", "djangoDayOfWeek", "dayOfMonth", "quest_type", "goal_period", "task_days_of_week", "task_days_of_month", "active", "createdDate", "taskDays", "split", "trim", "isArray", "dayNameShort", "getDayNameShort", "dayNameFull", "getDayNameFull", "isIncluded", "includes", "selectDate", "dateData", "selectedDateData", "formattedDate", "changeWeek", "direction", "allQuests", "dateObservables", "activeQuests", "emptyProgress", "progressList", "questIds", "relevantProgress", "p", "results", "findIndex", "wd", "toLocaleDateString", "weekday", "month", "toggleQuest", "_this3", "updateQuestProgress", "event", "_this4", "wasCompletedBefore", "target", "detail", "updateSliderBackground", "questCopy", "toggleQuestCompletion", "isCompletedNow", "cachedQuestIndex", "getQuest", "calculateStreak", "todayString", "todayIndex", "updateProgressRingForDate", "updateQuestUI", "questElement", "querySelector", "classList", "remove", "streakElements", "streakValue", "element", "parentElement", "contains", "display", "textContent", "progressText", "_progressText$parentE", "isTimeUnit", "unitSuffix", "goalUnitSuffix", "sliderElement", "toggleSideQuest", "_this5", "newValue", "newCompletedState", "updateSideQuestUI", "toggleSideQuestCompletion", "goalUnit", "offsetHeight", "openAddQuestModal", "preventDefault", "resetAnimationStates", "closeAddQuestModal", "selectQuestType", "type", "selectCategory", "category", "checkCategoryPriority", "selectPriority", "priority", "nextStep", "prevStep", "moveCaretToEnd", "emojiInput", "getInputElement", "input", "pos", "setSelectionRange", "scrollLeft", "scrollWidth", "createQuest", "_this6", "join", "userProfile", "userError", "Error", "questToCreate", "user_id", "questId", "questError", "message", "alert", "updateDaysOfWeek", "indexOf", "splice", "updateDaysOfMonth", "isChecked", "checked", "updatePeriodDisplay", "toggleMonthDay", "getCategoryIcon", "icons", "selectFrequency", "period", "getFrequencyText", "some", "hasHighPriority", "celebrationShown", "allQuestsCompleted", "every", "sideQuestCompleted", "show_celebration", "setItem", "closeCelebration", "year", "getFullYear", "String", "getMonth", "padStart", "date1", "date2", "get<PERSON><PERSON>y", "djangoDayIndex", "dayMap", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\today\\today.page.ts"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { QuestService } from '../../services/quest.service';\r\nimport { SideQuestService } from '../../services/sidequest.service';\r\nimport { UserService } from '../../services/user.service';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\nimport { Quest, QuestCategory, QuestGoalUnit, QuestPeriod, QuestPriority, QuestProgress } from '../../models/quest.model';\r\nimport { User } from '../../models/user.model';\r\nimport { Observable, Subscription, forkJoin, map, of, switchMap, take, firstValueFrom } from 'rxjs';\r\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\r\nimport { CelebrationComponent } from '../../components/celebration/celebration.component';\r\nimport { Activated<PERSON>oute, Router } from '@angular/router';\r\nimport { PreferencesService } from '../../services/preferences.service';\r\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\r\nimport { StreakCalculatorService } from '../../services/streak-calculator';\r\nimport { HeaderComponent } from 'src/app/components/header/header.component';\r\nimport { AuroraComponent } from 'src/app/components/aurora/aurora.component';\r\n\r\n\r\ninterface WeekDate {\r\n  date: string; // YYYY-MM-DD format\r\n  day: number;\r\n  is_today: boolean;\r\n  is_selected: boolean;\r\n  is_future: boolean;\r\n  total_quests: number;\r\n  completed_quests: number;\r\n  completion_percentage: number;\r\n}\r\n\r\ninterface DailyQuest {\r\n  id: string;\r\n  current_quest: {\r\n    id: string;\r\n    name: string;\r\n    description: string;\r\n    goal_value: number;\r\n    goal_unit: string;\r\n  };\r\n  streak: number;\r\n  completed: boolean;\r\n  value_achieved: number;\r\n  emoji: string;\r\n}\r\n\r\ninterface QuestDisplay extends Quest {\r\n  completed: boolean;\r\n  value_achieved: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-today',\r\n  templateUrl: './today.page.html',\r\n  styleUrls: ['./today.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, NavigationComponent, CelebrationComponent, EmojiInputDirective, HeaderComponent, AuroraComponent]\r\n})\r\nexport class TodayPage implements OnInit, OnDestroy {\r\n  @ViewChild('emojiInput') emojiInput: any;\r\n  // User data\r\n  user$: Observable<User | null> = of(null);\r\n  userId: string | null = null;\r\n  userSubscription: Subscription | undefined;\r\n  showSidequests = true;\r\n\r\n  // Date and calendar\r\n  selectedDate: Date = new Date();\r\n  weekDates: WeekDate[] = [];\r\n  dayNames: string[] = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];\r\n  headerText: string = 'Today';\r\n  weekOffset: number = 0;\r\n  selectedDateData: any;\r\n\r\n\r\n  // Quests\r\n  quests: QuestDisplay[] = [];\r\n  dailyQuest: DailyQuest | null = null;\r\n  currentStep = 5;\r\n  totalSteps = 5;\r\n\r\n  emojis: string[] = [\r\n    '🚀', '🪐', '⏳', '💊', '⚔️', '🧠', '🦷', '👨‍🍳',\r\n    '🏃', '🥬', '🏆', '🎮', '🎯', '💻', '🚴‍♂️', '🏋️‍♂️',\r\n    '💰', '💸', '🪬', '🧪', '😴', '📈', '📚', '❌',\r\n    '🎓', '💪', '🧘‍♂️', '📵', '🚭', '💧'\r\n  ];\r\n\r\n\r\n  // Cache for quest data to improve performance\r\n  private questCache: { [dateKey: string]: QuestDisplay[] } = {};\r\n\r\n  // Flag to track if we're currently loading data\r\n  private isLoadingData = false;\r\n\r\n  // Method to load daily side quest\r\n  private loadDailySideQuest() {\r\n    // Only load for today's date\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(this.selectedDate);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n    // Reset dailyQuest if not today's date\r\n    if (!isTodaySelected) {\r\n      this.dailyQuest = null;\r\n      return;\r\n    }\r\n\r\n    if (this.showSidequests && isTodaySelected && this.userId) {\r\n      // Use the ensureUserHasDailySideQuests method\r\n      this.sideQuestService.ensureUserHasDailySideQuests(this.userId!).pipe(\r\n        take(1)\r\n      ).subscribe({\r\n        next: (sideQuests) => {\r\n          if (sideQuests && sideQuests.length > 0) {\r\n            const sideQuest = sideQuests[0];\r\n\r\n            // Get the quest details from the pool\r\n            this.supabaseService.getClient()\r\n              .from('daily_sidequest_pool')\r\n              .select('*')\r\n              .eq('id', sideQuest.current_quest_id)\r\n              .single()\r\n              .then(response => {\r\n                if (response.error) {\r\n                  return;\r\n                }\r\n\r\n                const questDetails = response.data;\r\n\r\n                // Create the daily quest object\r\n                this.dailyQuest = {\r\n                  id: sideQuest.id!,\r\n                  current_quest: {\r\n                    id: sideQuest.current_quest_id!,\r\n                    name: questDetails.name || 'Daily Side Quest',\r\n                    description: questDetails.description || 'Complete this daily side quest',\r\n                    goal_value: questDetails.goal_value || 1,\r\n                    goal_unit: questDetails.goal_unit || 'count'\r\n                  },\r\n                  streak: sideQuest.streak || 0,\r\n                  completed: sideQuest.completed || false,\r\n                  value_achieved: sideQuest.value_achieved || 0,\r\n                  emoji: questDetails.emoji || '🎯'\r\n                };\r\n              });\r\n          } else {\r\n            this.dailyQuest = null;\r\n          }\r\n        },\r\n        error: () => {\r\n          this.dailyQuest = null;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Add Quest Modal\r\n  showAddQuestModal = false;\r\n  newQuest = this.getEmptyQuest();\r\n  hasHighPriorityQuest = false;\r\n\r\n  // Animation states\r\n  questTypeAnimated = false;\r\n  questTypeAnimating = false;\r\n  selectedQuestType = '';\r\n  categoryAnimated = false;\r\n  categoryAnimating = false;\r\n  categorySelected = false;\r\n  selectedCategory = '';\r\n  priorityAnimated = false;\r\n  priorityAnimating = false;\r\n  goalAnimated = false;\r\n  questDetailsAnimated = false;\r\n  frequencyAnimated = false;\r\n  frequencyOptionsAnimated = false;\r\n  previewAnimated = false;\r\n\r\n  // Celebration Modal\r\n  showCelebration = false;\r\n  currentUser: User | null = null;\r\n  celebrationShownDates: string[] = [];\r\n\r\n  // Days selection for new quest\r\n  weekDays = [\r\n    { value: 'Sun', label: 'Su', fullName: 'Sunday' },\r\n    { value: 'Mon', label: 'Mo', fullName: 'Monday' },\r\n    { value: 'Tue', label: 'Tu', fullName: 'Tuesday' },\r\n    { value: 'Wed', label: 'We', fullName: 'Wednesday' },\r\n    { value: 'Thu', label: 'Th', fullName: 'Thursday' },\r\n    { value: 'Fri', label: 'Fr', fullName: 'Friday' },\r\n    { value: 'Sat', label: 'Sa', fullName: 'Saturday' }\r\n  ];\r\n  monthDays = Array.from({ length: 31 }, (_, i) => i + 1);\r\n  selectedDaysOfWeek: string[] = [];\r\n  selectedDaysOfMonth: number[] = [];\r\n\r\n  // Use inject instead of constructor injection\r\n  private questService = inject(QuestService);\r\n  private sideQuestService = inject(SideQuestService);\r\n  private userService = inject(UserService);\r\n  private supabaseService = inject(SupabaseService);\r\n  private route = inject(ActivatedRoute);\r\n  private router = inject(Router);\r\n  private preferencesService = inject(PreferencesService);\r\n  private streakCalculator = inject(StreakCalculatorService);\r\n  private isRedirecting = false; // Flag to prevent multiple redirects\r\n\r\n  constructor() {\r\n    // Subscribe to query params to get date and week_offset from URL\r\n    this.route.queryParams.subscribe(params => {\r\n      const dateParam = params['date'];\r\n      const weekOffsetParam = params['week_offset'];\r\n\r\n      console.log('TodayPage: Date param from URL query:', dateParam);\r\n      console.log('TodayPage: Week offset param from URL query:', weekOffsetParam);\r\n\r\n      // Process week offset parameter\r\n      if (weekOffsetParam !== undefined) {\r\n        try {\r\n          this.weekOffset = parseInt(weekOffsetParam);\r\n          console.log('TodayPage: Week offset set to:', this.weekOffset);\r\n        } catch (error) {\r\n          console.error('TodayPage: Error parsing week offset:', error);\r\n          this.weekOffset = 0;\r\n        }\r\n      } else {\r\n        this.weekOffset = 0;\r\n      }\r\n\r\n      // Process date parameter\r\n      if (dateParam) {\r\n        try {\r\n          // Validate date format (YYYY-MM-DD)\r\n          if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateParam)) {\r\n            this.selectedDate = new Date(dateParam);\r\n            console.log('TodayPage: Selected date from URL query:', this.selectedDate);\r\n          } else {\r\n            console.error('TodayPage: Invalid date format in URL query:', dateParam);\r\n            this.selectedDate = new Date(); // Default to today\r\n          }\r\n        } catch (error) {\r\n          console.error('TodayPage: Error parsing date from URL query:', error);\r\n          this.selectedDate = new Date(); // Default to today\r\n        }\r\n      } else {\r\n        this.selectedDate = new Date(); // Default to today\r\n      }\r\n\r\n      // Initialize week dates based on selected date and week offset\r\n      this.generateWeekDates();\r\n\r\n      // Update header text and load data\r\n      this.updateHeaderText();\r\n\r\n      // Only load data if we have a userId\r\n      if (this.userId) {\r\n        this.loadData();\r\n      }\r\n    });\r\n\r\n    // Subscribe to auth state changes\r\n    this.userSubscription = this.supabaseService.currentUser$.subscribe(authUser => {\r\n\r\n\r\n      if (!authUser) {\r\n        console.log('TodayPage: No authenticated user, but not redirecting');\r\n        // Removed redirect to allow direct access\r\n        return;\r\n      }\r\n\r\n      // User is authenticated, get user data\r\n      this.userId = authUser.id;\r\n\r\n\r\n      // Get user data from Supabase\r\n      this.userService.getUserById(authUser.id).subscribe(userData => {\r\n        if (!userData) {\r\n          console.log('TodayPage: No user data found, but not redirecting');\r\n          // Removed redirect to allow direct access\r\n          return;\r\n        }\r\n\r\n        console.log('TodayPage: User data loaded:', userData);\r\n        this.loadData();\r\n      });\r\n    });\r\n\r\n    // Set up user$ observable for template binding\r\n    this.user$ = this.supabaseService.currentUser$.pipe(\r\n      switchMap(authUser => {\r\n        if (!authUser) {\r\n          return of(null);\r\n        }\r\n\r\n        return this.userService.getUserById(authUser.id);\r\n      })\r\n    );\r\n\r\n    // Subscribe to user$ to get user preferences\r\n    const userDataSubscription = this.user$.subscribe({\r\n      next: (user) => {\r\n        if (user) {\r\n          this.showSidequests = user.sidequests_switch;\r\n          this.currentUser = user;\r\n        }\r\n      }\r\n    });\r\n\r\n    // Add the subscription to be cleaned up\r\n    this.userSubscription = new Subscription();\r\n    this.userSubscription.add(userDataSubscription);\r\n  }\r\n\r\n  ngOnInit() {\r\n    // Generate week dates and preload data for all days\r\n    this.generateWeekDates();\r\n\r\n    // Preload data for all days in the week\r\n    setTimeout(() => {\r\n      this.preloadWeekData();\r\n    }, 0);\r\n\r\n    // Load celebration shown dates from localStorage and clean up old ones\r\n    try {\r\n      // Get today's date\r\n      const today = new Date();\r\n      const todayStr = this.formatDate(today);\r\n\r\n      // First, collect all localStorage keys\r\n      const allKeys: string[] = [];\r\n      for (let i = 0; i < localStorage.length; i++) {\r\n        const key = localStorage.key(i);\r\n        if (key) {\r\n          allKeys.push(key);\r\n        }\r\n      }\r\n\r\n      // Find and remove all celebration_shown keys except today's\r\n      allKeys.forEach(key => {\r\n        if (key.startsWith('celebration_shown_') && key !== `celebration_shown_${todayStr}`) {\r\n          localStorage.removeItem(key);\r\n        }\r\n      });\r\n\r\n      // Check if we have a celebration shown for today\r\n      const todayCelebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\r\n\r\n      // Add to our tracking array if found\r\n      this.celebrationShownDates = [];\r\n      if (todayCelebrationShown) {\r\n        this.celebrationShownDates.push(todayStr);\r\n      }\r\n    } catch (error) {\r\n      this.celebrationShownDates = [];\r\n    }\r\n  }\r\n\r\n  async ionViewWillEnter() {\r\n    // Authentication is now handled by the AuthGuard\r\n    // Just get the current user and load data\r\n    const authUser = this.supabaseService._currentUser.value;\r\n\r\n    if (!authUser) {\r\n      console.log('TodayPage: No authenticated user, but not redirecting');\r\n      return;\r\n    }\r\n\r\n    // Use the UserService to get or create the user document\r\n    this.userService.ensureUserExists(authUser).subscribe(userData => {\r\n      if (!userData) {\r\n        this.router.navigateByUrl('/signup');\r\n        return;\r\n      }\r\n\r\n      // Get end date\r\n      let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;\r\n      const currentDate = new Date();\r\n\r\n      // Compare dates properly\r\n      let isValidPlan = false;\r\n      if (endDate instanceof Date) {\r\n        isValidPlan = endDate > currentDate;\r\n      }\r\n\r\n      if (!isValidPlan) {\r\n        // Prevent multiple redirects\r\n        if (this.isRedirecting) return;\r\n        this.isRedirecting = true;\r\n\r\n        setTimeout(() => {\r\n          this.router.navigateByUrl('/pricing');\r\n          setTimeout(() => {\r\n            this.isRedirecting = false;\r\n          }, 2000);\r\n        }, 500);\r\n        return;\r\n      }\r\n\r\n      // Check if we have cached data for this date\r\n      const dateKey = this.formatDate(this.selectedDate);\r\n      if (this.questCache[dateKey]) {\r\n        // Use cached data\r\n        this.quests = this.questCache[dateKey];\r\n\r\n        // Initialize slider backgrounds immediately\r\n        requestAnimationFrame(() => {\r\n          this.initializeSliderBackgrounds();\r\n        });\r\n\r\n        // Load daily side quest if needed\r\n        this.loadDailySideQuest();\r\n      } else {\r\n        // Load data with the current selected date\r\n        this.loadData();\r\n      }\r\n    });\r\n\r\n    // Make sure the URL reflects the selected date and week offset\r\n    const route = this.router.url;\r\n    const dateParam = this.formatDate(this.selectedDate);\r\n\r\n    if (route === '/today') {\r\n      // If we're on the base route, update to include the date and week_offset as query parameters\r\n      this.router.navigate(['/today'], {\r\n        queryParams: {\r\n          date: dateParam,\r\n          week_offset: this.weekOffset !== 0 ? this.weekOffset : null\r\n        },\r\n        replaceUrl: true\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  // Initialize all slider backgrounds\r\n  initializeSliderBackgrounds() {\r\n    // Use requestAnimationFrame for better performance\r\n    requestAnimationFrame(() => {\r\n      const sliders = document.querySelectorAll('.progress-slider');\r\n      if (sliders.length === 0) {\r\n        return;\r\n      }\r\n\r\n      sliders.forEach(slider => {\r\n        if (slider instanceof HTMLInputElement) {\r\n          // Get the slider's quest ID for debugging\r\n          const sliderQuestId = slider.getAttribute('data-quest-id');\r\n          if (!sliderQuestId) {\r\n            return;\r\n          }\r\n\r\n          // Get the exact value from the slider (no rounding)\r\n          const sliderValue = parseInt(slider.value);\r\n          const minValue = parseInt(slider.min);\r\n          const maxValue = parseInt(slider.max);\r\n\r\n          // Calculate the percentage value\r\n          const percentage = maxValue > minValue ?\r\n            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n          // Set the background directly with hardcoded colors\r\n          slider.style.background =\r\n            `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\r\n\r\n          // Add a data attribute to track the current value\r\n          slider.setAttribute('data-current-value', slider.value);\r\n        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n          // Get the slider's quest ID for debugging\r\n          const sliderQuestId = slider.getAttribute('data-quest-id');\r\n          if (!sliderQuestId) {\r\n            return;\r\n          }\r\n\r\n          // Get the value from the element's properties or attributes\r\n          const valueAttr = slider.getAttribute('value') || '0';\r\n          const minAttr = slider.getAttribute('min') || '0';\r\n          const maxAttr = slider.getAttribute('max') || '100';\r\n\r\n          const sliderValue = parseInt(valueAttr);\r\n          const minValue = parseInt(minAttr);\r\n          const maxValue = parseInt(maxAttr);\r\n\r\n          // Calculate the percentage value\r\n          const percentage = maxValue > minValue ?\r\n            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n          // Set the CSS variable for the progress\r\n          slider.style.setProperty('--progress-value', `${percentage}%`);\r\n\r\n          // Add a data attribute to track the current value\r\n          slider.setAttribute('data-current-value', sliderValue.toString());\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  ionViewWillLeave() {\r\n    console.log('TodayPage: ionViewWillLeave called');\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    console.log('TodayPage: ngOnDestroy called');\r\n    if (this.userSubscription) {\r\n      this.userSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  async loadData() {\r\n    if (!this.userId) {\r\n      return;\r\n    }\r\n\r\n    // Update header text\r\n    this.updateHeaderText();\r\n\r\n    // Check if we have cached data for this date\r\n    const dateKey = this.formatDate(this.selectedDate);\r\n    if (this.questCache[dateKey]) {\r\n      // Use cached data\r\n      this.quests = this.questCache[dateKey];\r\n\r\n      // Initialize slider backgrounds immediately\r\n      requestAnimationFrame(() => {\r\n        this.initializeSliderBackgrounds();\r\n      });\r\n\r\n      // Load daily side quest if needed\r\n      this.loadDailySideQuest();\r\n\r\n      return;\r\n    }\r\n\r\n    // Set up date variables\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(this.selectedDate);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n    console.log('TodayPage: Loading data for date:', this.formatDate(this.selectedDate));\r\n    if (isTodaySelected) {\r\n      // Check if we've already calculated streaks for today\r\n      const todayDateString = this.formatDate(today);\r\n      try {\r\n        const { value: lastStreakCalculation } = await this.preferencesService.get('last_streak_calculation');\r\n\r\n        if (lastStreakCalculation !== todayDateString) {\r\n          console.log('TodayPage: First time loading today, calculating streaks');\r\n\r\n          // Najprv spracujeme všetky questy pomocou checkMissedDays\r\n          await firstValueFrom(this.questService.getQuests(this.userId!).pipe(\r\n            take(1),\r\n            switchMap(async quests => {\r\n              // Check missed days for each quest\r\n              for (const quest of quests) {\r\n                if (quest.id) {\r\n                  await this.questService.checkMissedDays(quest.id);\r\n                }\r\n              }\r\n\r\n              // Potom vytvoríme progress záznamy pre quit questy\r\n              await this.questService.createQuitQuestProgressForToday();\r\n\r\n              // NEBUDEME tu nastavovať last_streak_calculation, aby sa mohli vypočítať streaky v ďalšej časti kódu\r\n              return quests;\r\n            })\r\n          ));\r\n\r\n          // Streaky sa vypočítajú v ďalšej časti kódu\r\n        } else {\r\n          console.log('TodayPage: Streaks already calculated for today');\r\n        }\r\n      } catch (error) {\r\n        console.error('TodayPage: Error checking last streak calculation:', error);\r\n\r\n        // Ak nastane chyba, nenastavujeme last_streak_calculation, aby sa mohli vypočítať streaky\r\n      }\r\n\r\n      // Recalculate streak for the daily side quest only for today\r\n      if (this.showSidequests) {\r\n        this.sideQuestService.recalculateSideQuestStreak(this.userId, this.selectedDate)\r\n          .subscribe({\r\n            error: (error) => {\r\n              console.error('Error recalculating side quest streak:', error);\r\n            }\r\n          });\r\n      }\r\n    }\r\n    // Load quests\r\n    this.questService.getQuests(this.userId).pipe(\r\n      take(1),\r\n      switchMap(quests => {\r\n        // Filter active quests for the selected date\r\n        const filteredQuests = this.filterQuestsForDate(quests, this.selectedDate);\r\n\r\n        if (filteredQuests.length === 0) {\r\n          return of([]);\r\n        }\r\n\r\n        // Sort filtered quests by creation date (newest first) or ID\r\n        const sortedFilteredQuests = [...filteredQuests].sort((a, b) => {\r\n          if (a.created_at && b.created_at) {\r\n            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n          }\r\n          return a.id && b.id ? a.id.localeCompare(b.id) : 0;\r\n        });\r\n\r\n        // Get all progress for all quests at once\r\n        return this.questService.getQuestProgressForDate(this.userId!, this.selectedDate).pipe(\r\n          take(1),\r\n          switchMap(allProgress => {\r\n            // Create a lookup for quick access\r\n            const progressLookup: { [questId: string]: QuestProgress } = {};\r\n            allProgress.forEach(progress => {\r\n              progressLookup[progress.quest_id] = progress;\r\n            });\r\n\r\n            // For today's view, calculate streaks once per day\r\n            // For other days, just use the streak from the database\r\n            if (isTodaySelected) {\r\n              // Check if we've already calculated streaks for today\r\n              const todayDateString = this.formatDate(today);\r\n              return this.preferencesService.get('last_streak_calculation').then(({ value: lastStreakCalculation }) => {\r\n                if (lastStreakCalculation !== todayDateString) {\r\n                  console.log('TodayPage: First time loading today, calculating streaks');\r\n\r\n                  // Calculate streaks using our streak calculator\r\n                  return this.streakCalculator.calculateStreaks(this.userId!, sortedFilteredQuests).then(streaks => {\r\n                    // Map quests with progress and calculated streaks\r\n                    return sortedFilteredQuests.map(quest => {\r\n                      const progress = progressLookup[quest.id!];\r\n                      const calculatedStreak = streaks[quest.id!] || 0;\r\n\r\n                      // Update the streak in the database\r\n                      this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe();\r\n\r\n                      return {\r\n                        ...quest,\r\n                        completed: progress?.completed || false,\r\n                        value_achieved: progress?.value_achieved || 0,\r\n                        streak: calculatedStreak\r\n                      } as QuestDisplay;\r\n                    });\r\n                  }).then(result => {\r\n                    // Po výpočte streakov nastavíme last_streak_calculation\r\n                    this.preferencesService.set('last_streak_calculation', todayDateString);\r\n                    return result;\r\n                  });\r\n                } else {\r\n                  console.log('TodayPage: Streaks already calculated for today, using database values');\r\n\r\n                  // Just use the streak from the database\r\n                  return sortedFilteredQuests.map(quest => {\r\n                    const progress = progressLookup[quest.id!];\r\n\r\n                    return {\r\n                      ...quest,\r\n                      completed: progress?.completed || false,\r\n                      value_achieved: progress?.value_achieved || 0,\r\n                      streak: quest.streak || 0\r\n                    } as QuestDisplay;\r\n                  });\r\n                }\r\n              }).catch(error => {\r\n                console.error('TodayPage: Error checking last streak calculation:', error);\r\n\r\n                // If there's an error, just use the streak from the database\r\n                return sortedFilteredQuests.map(quest => {\r\n                  const progress = progressLookup[quest.id!];\r\n\r\n                  return {\r\n                    ...quest,\r\n                    completed: progress?.completed || false,\r\n                    value_achieved: progress?.value_achieved || 0,\r\n                    streak: quest.streak || 0\r\n                  } as QuestDisplay;\r\n                });\r\n              });\r\n            } else {\r\n              // For previous days, just use the streak from the database but set it to 0 for display\r\n              return Promise.resolve(sortedFilteredQuests.map(quest => {\r\n                const progress = progressLookup[quest.id!];\r\n\r\n                return {\r\n                  ...quest,\r\n                  completed: progress?.completed || false,\r\n                  value_achieved: progress?.value_achieved || 0,\r\n                  streak: 0 // Don't show streak for previous days\r\n                } as QuestDisplay;\r\n              }));\r\n            }\r\n          })\r\n        );\r\n\r\n\r\n      })\r\n    ).subscribe({\r\n      next: (questsWithProgress) => {\r\n        // Sort quests by creation date (newest first) or ID\r\n        const sortedQuests = [...questsWithProgress].sort((a, b) => {\r\n          if (a.created_at && b.created_at) {\r\n            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n          }\r\n          return a.id && b.id ? a.id.localeCompare(b.id) : 0;\r\n        });\r\n\r\n        // Check if all quests are completed for today\r\n        this.checkAllQuestsCompleted(sortedQuests);\r\n\r\n        // Update the quests array\r\n        this.quests = sortedQuests;\r\n\r\n        // Cache the quests for this date\r\n        const dateKey = this.formatDate(this.selectedDate);\r\n        this.questCache[dateKey] = sortedQuests;\r\n\r\n        // Update the week date progress\r\n        this.updateWeekDateProgress();\r\n\r\n        // Initialize slider backgrounds\r\n        requestAnimationFrame(() => {\r\n          this.initializeSliderBackgrounds();\r\n        });\r\n\r\n        // Load daily side quest if needed\r\n        this.loadDailySideQuest();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading quests:', error);\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  generateWeekDates() {\r\n    const today = new Date();\r\n\r\n    // Calculate the start of the week based on week offset\r\n    // This starts on Monday (1) instead of Sunday (0)\r\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\r\n    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // Convert to Monday-based (0 = Monday)\r\n    const startOfWeek = new Date(today);\r\n    startOfWeek.setDate(today.getDate() - daysFromMonday + (7 * this.weekOffset));\r\n\r\n    this.weekDates = [];\r\n    for (let i = 0; i < 7; i++) {\r\n      const date = new Date(startOfWeek);\r\n      date.setDate(startOfWeek.getDate() + i);\r\n\r\n      const dateString = this.formatDate(date);\r\n      const isToday = this.isSameDay(date, today);\r\n      const isSelected = this.isSameDay(date, this.selectedDate);\r\n      const isFuture = date > today;\r\n\r\n      // Check if we have cached progress for this date\r\n      const dateKey = dateString;\r\n      let totalQuests = 0;\r\n      let completedQuests = 0;\r\n      let completionPercentage = 0;\r\n\r\n      if (this.weekProgressCache[dateKey]) {\r\n        const cached = this.weekProgressCache[dateKey];\r\n        totalQuests = cached.total;\r\n        completedQuests = cached.completed;\r\n        completionPercentage = totalQuests > 0\r\n          ? Math.round((completedQuests / totalQuests) * 100)\r\n          : 0;\r\n      }\r\n\r\n      this.weekDates.push({\r\n        date: dateString,\r\n        day: date.getDate(),\r\n        is_today: isToday,\r\n        is_selected: isSelected,\r\n        is_future: isFuture,\r\n        total_quests: totalQuests,\r\n        completed_quests: completedQuests,\r\n        completion_percentage: completionPercentage\r\n      });\r\n    }\r\n\r\n    // Preload data for all days in the week\r\n    if (this.userId) {\r\n      // Use setTimeout to allow the UI to render first\r\n      setTimeout(() => {\r\n        this.preloadWeekData();\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  // Cache for week date progress\r\n  private weekProgressCache: { [dateKey: string]: { total: number, completed: number } } = {};\r\n\r\n  updateWeekDateProgress() {\r\n    if (!this.userId) return;\r\n\r\n    // For each date in the week, update the progress\r\n    this.weekDates.forEach((weekDate, index) => {\r\n      if (weekDate.is_future) return;\r\n\r\n      const date = new Date(weekDate.date);\r\n      const dateKey = this.formatDate(date);\r\n\r\n      // Check if we have cached progress for this date\r\n      if (this.weekProgressCache[dateKey]) {\r\n        const cached = this.weekProgressCache[dateKey];\r\n        this.weekDates[index].total_quests = cached.total;\r\n        this.weekDates[index].completed_quests = cached.completed;\r\n        this.weekDates[index].completion_percentage = cached.total > 0\r\n          ? Math.round((cached.completed / cached.total) * 100)\r\n          : 0;\r\n        return;\r\n      }\r\n\r\n      // If we have cached quests for this date, use them to calculate progress\r\n      if (this.questCache[dateKey]) {\r\n        const cachedQuests = this.questCache[dateKey];\r\n        const totalQuests = cachedQuests.length;\r\n        const completedQuests = cachedQuests.filter(q => q.completed).length;\r\n\r\n        // Cache the progress\r\n        this.weekProgressCache[dateKey] = {\r\n          total: totalQuests,\r\n          completed: completedQuests\r\n        };\r\n\r\n        // Update the week date\r\n        this.weekDates[index].total_quests = totalQuests;\r\n        this.weekDates[index].completed_quests = completedQuests;\r\n        this.weekDates[index].completion_percentage = totalQuests > 0\r\n          ? Math.round((completedQuests / totalQuests) * 100)\r\n          : 0;\r\n        return;\r\n      }\r\n    });\r\n\r\n    // Preload data for all days in the week\r\n    this.preloadWeekData();\r\n  }\r\n\r\n  // Helper method to filter quests for a specific date\r\n  private filterQuestsForDate(quests: Quest[], date: Date): Quest[] {\r\n    const dateObj = new Date(date);\r\n    const dayOfWeek = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.\r\n    // Django uses Monday=0, Sunday=6 format, so we need to convert\r\n    const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format\r\n    const dayOfMonth = dateObj.getDate(); // 1-31\r\n\r\n    console.log(`TodayPage: Filtering quests for date ${this.formatDate(date)}, day of week: ${dayOfWeek} (Django: ${djangoDayOfWeek}), day of month: ${dayOfMonth}`);\r\n\r\n    const filteredQuests = quests.filter(quest => {\r\n      console.log(`TodayPage: Checking quest ${quest.id} (${quest.name}), type: ${quest.quest_type}, period: ${quest.goal_period}, task_days_of_week: ${quest.task_days_of_week}, task_days_of_month: ${quest.task_days_of_month}`);\r\n\r\n      if (!quest.active) {\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is not active, filtering out`);\r\n        return false;\r\n      }\r\n\r\n      // Only show quests from the date they were created\r\n      if (quest.created_at) {\r\n        const createdDate = new Date(quest.created_at);\r\n        createdDate.setHours(0, 0, 0, 0);\r\n        dateObj.setHours(0, 0, 0, 0);\r\n\r\n        // If the selected date is before the quest was created, don't show it\r\n        if (dateObj < createdDate) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // Daily quests are always shown\r\n      if (quest.goal_period === 'day') {\r\n        return true;\r\n      }\r\n\r\n      // Weekly quests are shown on specific days\r\n      if (quest.goal_period === 'week') {\r\n        if (!quest.task_days_of_week) {\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has no task_days_of_week specified, showing every day`);\r\n          return true; // If no days specified, show every day\r\n        }\r\n\r\n        // Parse task_days_of_week\r\n        let taskDays: any[] = [];\r\n        if (typeof quest.task_days_of_week === 'string') {\r\n          taskDays = quest.task_days_of_week.split(',').map(day => day.trim());\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as string: ${quest.task_days_of_week}, parsed to:`, taskDays);\r\n        } else if (Array.isArray(quest.task_days_of_week)) {\r\n          taskDays = quest.task_days_of_week;\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) has task_days_of_week as array:`, taskDays);\r\n        }\r\n\r\n        // Check if current day is in task days\r\n        // Convert current day to different formats for comparison\r\n        const dayNameShort = this.getDayNameShort(djangoDayOfWeek);\r\n        const dayNameFull = this.getDayNameFull(djangoDayOfWeek);\r\n\r\n        console.log(`TodayPage: Checking if day ${dayNameFull} (${dayNameShort}, ${djangoDayOfWeek}) is in task days:`, taskDays);\r\n\r\n        const isIncluded = taskDays.includes(djangoDayOfWeek) ||\r\n          taskDays.includes(djangoDayOfWeek.toString()) ||\r\n          taskDays.includes(dayNameShort) ||\r\n          taskDays.includes(dayNameFull);\r\n\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) should be shown on ${dayNameFull}? ${isIncluded}`);\r\n\r\n        return isIncluded;\r\n      }\r\n\r\n      // Monthly quests are shown on specific days of month\r\n      if (quest.goal_period === 'month') {\r\n        if (!quest.task_days_of_month) return true; // If no days specified, show every day\r\n\r\n        // Parse task_days_of_month\r\n        let taskDays: any[] = [];\r\n        if (typeof quest.task_days_of_month === 'string') {\r\n          taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));\r\n        } else if (Array.isArray(quest.task_days_of_month)) {\r\n          taskDays = quest.task_days_of_month;\r\n        }\r\n\r\n        // Check if current day is in task days\r\n        return taskDays.includes(dayOfMonth) ||\r\n          taskDays.includes(dayOfMonth.toString());\r\n      }\r\n\r\n      return false;\r\n    });\r\n\r\n    console.log(`TodayPage: Filtered ${quests.length} quests to ${filteredQuests.length} for date ${this.formatDate(date)}`);\r\n\r\n    return filteredQuests;\r\n  }\r\n\r\n  selectDate(dateObj: any) {\r\n    if (this.isLoadingData) {\r\n      return;\r\n    }\r\n\r\n    const dateData = dateObj.date\r\n\r\n    this.isLoadingData = true;\r\n\r\n    this.selectedDateData = dateObj;\r\n\r\n    const date = new Date(dateData);\r\n    this.selectedDate = date;\r\n\r\n    this.weekDates.forEach(weekDate => {\r\n      weekDate.is_selected = weekDate.date === dateData;\r\n    });\r\n\r\n    const formattedDate = this.formatDate(date);\r\n\r\n    this.router.navigate(['/today'], {\r\n      queryParams: {\r\n        date: formattedDate,\r\n        week_offset: this.weekOffset !== 0 ? this.weekOffset : null\r\n      },\r\n      replaceUrl: true\r\n    });\r\n\r\n    this.updateHeaderText();\r\n\r\n    setTimeout(() => {\r\n      this.loadData();\r\n      this.isLoadingData = false;\r\n    }, 10);\r\n  }\r\n\r\n  // Flag to track if we're currently changing weeks\r\n  private isChangingWeek = false;\r\n\r\n  changeWeek(direction: number) {\r\n    // Prevent multiple rapid week changes\r\n    if (this.isChangingWeek) {\r\n      return;\r\n    }\r\n\r\n    this.isChangingWeek = true;\r\n\r\n    // Update the week offset\r\n    this.weekOffset += direction;\r\n\r\n    // Generate new week dates with the updated offset\r\n    this.generateWeekDates();\r\n\r\n    // Preload quest data for all days in the week\r\n    this.preloadWeekData();\r\n\r\n    // Update the URL with the new week offset while preserving the selected date\r\n    const dateParam = this.formatDate(this.selectedDate);\r\n    this.router.navigate(['/today'], {\r\n      queryParams: {\r\n        date: dateParam,\r\n        week_offset: this.weekOffset\r\n      },\r\n      replaceUrl: true\r\n    });\r\n\r\n    // Reset the flag after a short delay\r\n    setTimeout(() => {\r\n      this.isChangingWeek = false;\r\n    }, 300);\r\n  }\r\n\r\n  // Preload data for all days in the current week\r\n  private preloadWeekData() {\r\n    if (!this.userId) return;\r\n\r\n    // Get all quests once to avoid multiple API calls\r\n    this.questService.getQuests(this.userId!).pipe(\r\n      take(1)\r\n    ).subscribe(allQuests => {\r\n      // Create an array of observables for each date\r\n      const dateObservables = this.weekDates\r\n        .filter(weekDate => !weekDate.is_future)\r\n        .map(weekDate => {\r\n          const date = new Date(weekDate.date);\r\n          const dateKey = this.formatDate(date);\r\n\r\n          // Skip if we already have cached data\r\n          if (this.weekProgressCache[dateKey]) {\r\n            return of({\r\n              date: weekDate.date,\r\n              progress: this.weekProgressCache[dateKey]\r\n            });\r\n          }\r\n\r\n          // Filter active quests for this date\r\n          const activeQuests = this.filterQuestsForDate(allQuests, date);\r\n\r\n          // If no active quests, return empty progress\r\n          if (activeQuests.length === 0) {\r\n            const emptyProgress = { total: 0, completed: 0 };\r\n            this.weekProgressCache[dateKey] = emptyProgress;\r\n            return of({\r\n              date: weekDate.date,\r\n              progress: emptyProgress\r\n            });\r\n          }\r\n\r\n          // Get progress for this date\r\n          return this.questService.getQuestProgressForDate(this.userId!, date).pipe(\r\n            take(1),\r\n            map(progressList => {\r\n              // Count completed quests\r\n              const questIds = activeQuests.map(q => q.id);\r\n              const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\r\n              const completedQuests = relevantProgress.filter(p => p.completed).length;\r\n              const totalQuests = activeQuests.length;\r\n\r\n              // Create progress object\r\n              const progress = {\r\n                total: totalQuests,\r\n                completed: completedQuests\r\n              };\r\n\r\n              // Cache the progress\r\n              this.weekProgressCache[dateKey] = progress;\r\n\r\n              return {\r\n                date: weekDate.date,\r\n                progress\r\n              };\r\n            })\r\n          );\r\n        });\r\n\r\n      // Process all date observables in parallel\r\n      forkJoin(dateObservables).subscribe(results => {\r\n        // Update the week dates with the progress\r\n        results.forEach(result => {\r\n          const index = this.weekDates.findIndex(wd => wd.date === result.date);\r\n          if (index >= 0) {\r\n            this.weekDates[index].total_quests = result.progress.total;\r\n            this.weekDates[index].completed_quests = result.progress.completed;\r\n            this.weekDates[index].completion_percentage = result.progress.total > 0\r\n              ? Math.round((result.progress.completed / result.progress.total) * 100)\r\n              : 0;\r\n          }\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  updateHeaderText() {\r\n    const today = new Date();\r\n    if (this.isSameDay(this.selectedDate, today)) {\r\n      this.headerText = 'Today';\r\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() - 1)))) {\r\n      this.headerText = 'Yesterday';\r\n    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() + 2)))) {\r\n      this.headerText = 'Tomorrow';\r\n    } else {\r\n      // Format as \"Mon, 15 Jan\"\r\n      this.headerText = this.selectedDate.toLocaleDateString('en-US', {\r\n        weekday: 'short',\r\n        day: 'numeric',\r\n        month: 'short'\r\n      });\r\n    }\r\n  }\r\n\r\n  // Map to track which quests are currently being toggled\r\n  private togglingQuestIds: { [questId: string]: boolean } = {};\r\n\r\n  async toggleQuest(quest: QuestDisplay) {\r\n    if (!this.userId || !quest.id) return;\r\n\r\n    // Check if this specific quest is already being toggled\r\n    if (this.togglingQuestIds[quest.id]) {\r\n      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) is already being toggled, ignoring duplicate call`);\r\n      return;\r\n    }\r\n\r\n    // Set flag for this specific quest\r\n    this.togglingQuestIds[quest.id] = true;\r\n    console.log(`TodayPage: Starting toggle for quest ${quest.id} (${quest.name})`);\r\n\r\n    try {\r\n      // For normal quests, we don't want to toggle the value when clicking on the quest\r\n      // Instead, we want to keep the current value from the slider\r\n      // This is different from the original behavior where clicking would toggle between 0 and goal_value\r\n\r\n      // We'll just log that the quest was clicked but not change any values\r\n      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) clicked, keeping current value: ${quest.value_achieved}`);\r\n\r\n      // No need to update the database since we're not changing any values\r\n      // Just release the flag and return\r\n      delete this.togglingQuestIds[quest.id];\r\n      return;\r\n    } catch (error) {\r\n      console.error(`TodayPage: Error in toggleQuest for ${quest.id} (${quest.name}):`, error);\r\n    } finally {\r\n      // Reset flag for this specific quest\r\n      delete this.togglingQuestIds[quest.id];\r\n      console.log(`TodayPage: Finished toggle for quest ${quest.id} (${quest.name})`);\r\n    }\r\n  }\r\n\r\n  // Map to track which quests are currently being updated\r\n  private updatingQuestIds: { [questId: string]: boolean } = {};\r\n\r\n  async updateQuestProgress(quest: QuestDisplay, event?: any) {\r\n    if (!this.userId || !quest.id) return;\r\n\r\n    // Check if this specific quest is already being updated\r\n    if (this.updatingQuestIds[quest.id]) {\r\n      return;\r\n    }\r\n\r\n    // Set flag for this specific quest\r\n    this.updatingQuestIds[quest.id] = true;\r\n\r\n    try {\r\n      // Store the original completed state before any changes\r\n      const wasCompletedBefore = quest.completed;\r\n      console.log(`TodayPage: Quest ${quest.id} (${quest.name}) original completed state: ${wasCompletedBefore}`);\r\n\r\n      // Update the slider background if an event is provided\r\n      if (event) {\r\n        // Handle both standard Event and Ionic's CustomEvent\r\n        const slider = event.target || (event.detail ? event.detail.value : null);\r\n        this.updateSliderBackground(slider);\r\n\r\n        // Verify that the slider is for the correct quest\r\n        const sliderQuestId = slider instanceof HTMLElement ? slider.getAttribute('data-quest-id') : null;\r\n        if (sliderQuestId && sliderQuestId !== quest.id) {\r\n          delete this.updatingQuestIds[quest.id];\r\n          return;\r\n        }\r\n\r\n        // Get the value from the slider\r\n        let sliderValue = 0;\r\n        if (event.detail && event.detail.value !== undefined) {\r\n          // Ionic range event\r\n          sliderValue = event.detail.value;\r\n        } else if (slider instanceof HTMLInputElement) {\r\n          // Standard input event\r\n          sliderValue = parseInt(slider.value);\r\n        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n          // Ionic range element\r\n          const valueAttr = slider.getAttribute('value') || '0';\r\n          sliderValue = parseInt(valueAttr);\r\n        }\r\n\r\n        // Update the quest's value_achieved with the slider value\r\n        quest.value_achieved = sliderValue;\r\n\r\n        // Update completed status based on quest type and value\r\n        // This exactly matches the Django implementation in toggle_quest view\r\n        if (quest.quest_type === 'build') {\r\n          // For build quests, completed when value >= goal\r\n          quest.completed = sliderValue >= quest.goal_value;\r\n        } else { // 'quit' type\r\n          // For quit quests, completed when value < goal (opposite of build)\r\n          quest.completed = sliderValue < quest.goal_value;\r\n        }\r\n\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) new completed state: ${quest.completed}`);\r\n      }\r\n\r\n      // Make a deep copy of the quest to avoid reference issues\r\n      const questCopy = { ...quest };\r\n\r\n      // Call the service and get the updated values\r\n      const result = await this.questService.toggleQuestCompletion(\r\n        this.userId,\r\n        quest.id,\r\n        this.selectedDate,\r\n        quest.value_achieved,\r\n        questCopy\r\n      );\r\n\r\n      // Update the quest in the UI with the returned values\r\n      quest.completed = result.completed;\r\n      quest.value_achieved = result.value_achieved;\r\n\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n      // Handle streak calculation differently based on whether we're in today's view or a previous day\r\n      if (isTodaySelected) {\r\n        // For today's view, manually calculate the streak by going backward from today\r\n        // until we find a non-completed progress entry\r\n\r\n        // Use the streak from the result (from Supabase)\r\n        let streak = result.streak;\r\n\r\n        // Get the current completed state after the update\r\n        const isCompletedNow = quest.completed;\r\n\r\n        console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status: was ${wasCompletedBefore}, now ${isCompletedNow}`);\r\n\r\n        // Only update streak if the completion status has changed\r\n        if (wasCompletedBefore !== isCompletedNow) {\r\n          if (isCompletedNow) {\r\n            // Changed from incomplete to complete\r\n            streak++;\r\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from incomplete to complete, streak increased to ${streak}`);\r\n          } else {\r\n            // Changed from complete to incomplete\r\n            streak = Math.max(0, streak - 1);\r\n            console.log(`TodayPage: Quest ${quest.id} (${quest.name}) changed from complete to incomplete, streak decreased to ${streak}`);\r\n          }\r\n\r\n          // Update the streak in the database\r\n          this.questService.updateQuestStreak(quest.id!, streak).subscribe({\r\n            next: () => {\r\n              console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${streak}`);\r\n\r\n              // Update the quest in the cache\r\n              const dateKey = this.formatDate(this.selectedDate);\r\n              if (this.questCache[dateKey]) {\r\n                const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);\r\n                if (cachedQuestIndex >= 0) {\r\n                  this.questCache[dateKey][cachedQuestIndex].streak = streak;\r\n                }\r\n              }\r\n            },\r\n            error: (error: any) => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\r\n          });\r\n        } else {\r\n          console.log(`TodayPage: Quest ${quest.id} (${quest.name}) completion status did not change, keeping streak at ${streak}`);\r\n        }\r\n      } else {\r\n        // For previous days, recalculate streak for today\r\n        console.log(`TodayPage: Quest toggled in previous day (${this.formatDate(this.selectedDate)}), recalculating streak for today`);\r\n\r\n        // Get the quest details\r\n        this.questService.getQuest(quest.id!).subscribe(questDetails => {\r\n          if (!questDetails) {\r\n            console.error(`TodayPage: Could not get quest details for ${quest.id}`);\r\n            return;\r\n          }\r\n\r\n          // Calculate the streak for today using our streak calculator\r\n          this.streakCalculator.calculateStreak(this.userId!, quest.id!)\r\n            .then(calculatedStreak => {\r\n              console.log(`TodayPage: Recalculated streak for quest ${quest.id} for today: ${calculatedStreak}`);\r\n\r\n              // Update the streak in the database\r\n              this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe({\r\n                next: () => {\r\n                  console.log(`TodayPage: Successfully updated streak for quest ${quest.id} to ${calculatedStreak}`);\r\n\r\n                  // Clear today's cache for next time\r\n                  const todayString = this.formatDate(today);\r\n                  console.log('TodayPage: Clearing today\\'s cache to force reload of updated streak next time today is viewed');\r\n                  delete this.questCache[todayString];\r\n\r\n                  // If we have today's date in the week view, update its progress\r\n                  const todayIndex = this.weekDates.findIndex(wd => wd.date === todayString);\r\n                  if (todayIndex >= 0) {\r\n                    delete this.weekProgressCache[todayString];\r\n                    this.updateProgressRingForDate(todayString);\r\n                  }\r\n                },\r\n                error: (error: any) => console.error(`TodayPage: Error updating streak for quest ${quest.id}:`, error)\r\n              });\r\n            })\r\n            .catch(error => {\r\n              console.error(`TodayPage: Error calculating streak for quest ${quest.id}:`, error);\r\n            });\r\n        });\r\n      }\r\n\r\n      // Update the UI element for this quest\r\n      this.updateQuestUI(quest);\r\n\r\n      // Cache the updated quest data and update progress ring\r\n      const dateKey = this.formatDate(this.selectedDate);\r\n      if (this.questCache[dateKey]) {\r\n        // Find and update the quest in the cache\r\n        const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);\r\n        if (cachedQuestIndex >= 0) {\r\n          this.questCache[dateKey][cachedQuestIndex] = { ...quest };\r\n        }\r\n      }\r\n\r\n      // Clear the cache for this date to force a refresh\r\n      delete this.weekProgressCache[dateKey];\r\n\r\n      // Update the progress ring for this date\r\n      this.updateProgressRingForDate(dateKey);\r\n\r\n      // Check if all quests are completed\r\n      this.checkAllQuestsCompleted(this.quests);\r\n    } catch (error) {\r\n      console.error(`TodayPage: Error updating quest progress:`, error);\r\n    } finally {\r\n      // Reset flag for this specific quest\r\n      delete this.updatingQuestIds[quest.id];\r\n    }\r\n  }\r\n\r\n  // Helper method to update the progress ring for a specific date\r\n  private updateProgressRingForDate(dateKey: string) {\r\n    // Find the index of this date in weekDates\r\n    const index = this.weekDates.findIndex(wd => wd.date === dateKey);\r\n    if (index < 0) return;\r\n\r\n    // If we have cached quests for this date, use them to calculate progress\r\n    if (this.questCache[dateKey]) {\r\n      const cachedQuests = this.questCache[dateKey];\r\n      const totalQuests = cachedQuests.length;\r\n      const completedQuests = cachedQuests.filter(q => q.completed).length;\r\n\r\n      // Cache the progress\r\n      this.weekProgressCache[dateKey] = {\r\n        total: totalQuests,\r\n        completed: completedQuests\r\n      };\r\n\r\n      // Update the week date\r\n      this.weekDates[index].total_quests = totalQuests;\r\n      this.weekDates[index].completed_quests = completedQuests;\r\n      this.weekDates[index].completion_percentage = totalQuests > 0\r\n        ? Math.round((completedQuests / totalQuests) * 100)\r\n        : 0;\r\n\r\n      return;\r\n    }\r\n\r\n    // If no cached quests, fetch from server\r\n    if (this.userId) {\r\n      const date = new Date(dateKey);\r\n\r\n      this.questService.getQuestProgressForDate(this.userId, date).pipe(\r\n        take(1)\r\n      ).subscribe(progressList => {\r\n        this.questService.getQuests(this.userId!).pipe(\r\n          take(1)\r\n        ).subscribe(quests => {\r\n          // Filter active quests for this date\r\n          const activeQuests = this.filterQuestsForDate(quests, date);\r\n\r\n          // Count completed quests\r\n          const questIds = activeQuests.map(q => q.id);\r\n          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));\r\n          const completedQuests = relevantProgress.filter(p => p.completed).length;\r\n          const totalQuests = activeQuests.length;\r\n\r\n          // Cache the progress\r\n          this.weekProgressCache[dateKey] = {\r\n            total: totalQuests,\r\n            completed: completedQuests\r\n          };\r\n\r\n          // Update the week date\r\n          this.weekDates[index].total_quests = totalQuests;\r\n          this.weekDates[index].completed_quests = completedQuests;\r\n          this.weekDates[index].completion_percentage = totalQuests > 0\r\n            ? Math.round((completedQuests / totalQuests) * 100)\r\n            : 0;\r\n        });\r\n      });\r\n    }\r\n  }\r\n\r\n  // Helper method to update the UI for a specific quest\r\n  private updateQuestUI(quest: QuestDisplay) {\r\n    // Find the quest element in the DOM\r\n    const questElement = document.querySelector(`[data-quest-id=\"${quest.id}\"]`);\r\n    if (!questElement) {\r\n      console.error(`TodayPage: Could not find quest element for ID: ${quest.id}`);\r\n      return;\r\n    }\r\n\r\n    // Update the completed class\r\n    if (quest.completed) {\r\n      questElement.classList.add('completed');\r\n    } else {\r\n      questElement.classList.remove('completed');\r\n    }\r\n\r\n    // Update the streak display - only show streak for today\r\n    const streakElements = questElement.querySelectorAll('.quest-streak');\r\n    if (streakElements && streakElements.length > 0) {\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n      // Only show streak for today's view\r\n      if (isTodaySelected) {\r\n        const streakValue = quest.streak || 0;\r\n        console.log(`TodayPage: Quest ${quest.id}, completed: ${quest.completed}, streak: ${streakValue}`);\r\n\r\n        // Update all streak elements (there might be multiple due to ngIf)\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            // Make sure the streak is visible\r\n            (element as HTMLElement).style.display = 'block';\r\n            element.textContent = `🔥${streakValue}d`;\r\n          }\r\n        });\r\n      } else {\r\n        // Hide streak for previous days\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            (element as HTMLElement).style.display = 'none';\r\n            element.textContent = '';\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // Update the progress text\r\n    const progressText = questElement.querySelector('.progress-text');\r\n    if (progressText) {\r\n      const isTimeUnit = progressText.parentElement?.classList.contains('progress-time');\r\n      const unitSuffix = isTimeUnit ? 'm' : '';\r\n      const goalUnitSuffix = quest.goal_unit !== 'count' && !isTimeUnit ? ` ${quest.goal_unit}` : '';\r\n\r\n      progressText.textContent = `${quest.value_achieved}${unitSuffix}/${quest.goal_value}${unitSuffix}${goalUnitSuffix}`;\r\n    }\r\n\r\n    console.log(`TodayPage: Updated UI for quest ${quest.id}`);\r\n  }\r\n\r\n  // Update slider background based on value\r\n  updateSliderBackground(slider: HTMLInputElement | EventTarget | null) {\r\n    if (!slider) {\r\n      return;\r\n    }\r\n\r\n    // Handle different types of slider elements\r\n    let sliderElement: HTMLElement;\r\n    let sliderValue = 0;\r\n    let minValue = 0;\r\n    let maxValue = 100;\r\n    let sliderQuestId = '';\r\n\r\n    if (slider instanceof HTMLInputElement) {\r\n      // Standard HTML input range\r\n      sliderElement = slider;\r\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\r\n      sliderValue = parseInt(slider.value);\r\n      minValue = parseInt(slider.min);\r\n      maxValue = parseInt(slider.max);\r\n    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n      // Ionic range element\r\n      sliderElement = slider;\r\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\r\n\r\n      // Get the value from the element's properties or attributes\r\n      const valueAttr = slider.getAttribute('value') || '0';\r\n      const minAttr = slider.getAttribute('min') || '0';\r\n      const maxAttr = slider.getAttribute('max') || '100';\r\n\r\n      sliderValue = parseInt(valueAttr);\r\n      minValue = parseInt(minAttr);\r\n      maxValue = parseInt(maxAttr);\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    if (!sliderQuestId) {\r\n      return;\r\n    }\r\n\r\n    // Calculate the percentage value\r\n    const percentage = maxValue > minValue ?\r\n      ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n    // For Ionic range, we need to set the CSS variable\r\n    if (sliderElement.tagName === 'ION-RANGE') {\r\n      sliderElement.style.setProperty('--progress-value', `${percentage}%`);\r\n    } else {\r\n      // Set the background directly with hardcoded colors for standard HTML input\r\n      sliderElement.style.background =\r\n        `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\r\n    }\r\n\r\n    // Add a data attribute to track the current value\r\n    sliderElement.setAttribute('data-current-value', sliderValue.toString());\r\n  }\r\n\r\n  // Map to track which side quests are currently being toggled\r\n  private togglingSideQuestIds: { [questId: string]: boolean } = {};\r\n\r\n  /**\r\n   * Toggle side quest completion\r\n   * This matches the Django implementation in toggle_daily_side_quest view\r\n   * Side quests are always toggled between 0 and goal value\r\n   */\r\n  async toggleSideQuest(sideQuest: DailyQuest) {\r\n    if (!this.userId || !sideQuest.id) return;\r\n\r\n    // Check if this specific side quest is already being toggled\r\n    if (this.togglingSideQuestIds[sideQuest.id]) {\r\n      console.log(`TodayPage: Side quest ${sideQuest.id} is already being toggled, ignoring duplicate call`);\r\n      return;\r\n    }\r\n\r\n    // Set flag for this specific side quest\r\n    this.togglingSideQuestIds[sideQuest.id] = true;\r\n    console.log(`TodayPage: Starting toggle for side quest ${sideQuest.id}`);\r\n\r\n    try {\r\n      // For side quests, we always toggle between 0 and goal value\r\n      // This matches the Django implementation where side quests are either completed or not\r\n      console.log(`TodayPage: Click event on side quest ${sideQuest.id}`);\r\n\r\n      // Toggle the value immediately for better UI feedback\r\n      const newValue = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\r\n      const newCompletedState = newValue === sideQuest.current_quest.goal_value;\r\n\r\n      // Update local state first for immediate feedback\r\n      sideQuest.value_achieved = newValue;\r\n      sideQuest.completed = newCompletedState;\r\n\r\n      console.log(`TodayPage: Updated side quest ${sideQuest.id} value to ${sideQuest.value_achieved}, completed: ${sideQuest.completed}`);\r\n\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isToday = selectedDate.getTime() === today.getTime();\r\n\r\n      // Only allow toggling side quests for today\r\n      if (!isToday) {\r\n        console.log(`TodayPage: Cannot toggle side quest for past date: ${this.formatDate(this.selectedDate)}`);\r\n        delete this.togglingSideQuestIds[sideQuest.id];\r\n        return;\r\n      }\r\n\r\n      // Update the UI element immediately for better feedback\r\n      this.updateSideQuestUI(sideQuest);\r\n\r\n      try {\r\n        const result = await this.sideQuestService.toggleSideQuestCompletion(\r\n          sideQuest.id,\r\n          this.userId,\r\n          this.selectedDate // Pass the selected date\r\n        );\r\n\r\n        console.log(`TodayPage: Successfully toggled side quest ${sideQuest.id}`);\r\n        console.log(`TodayPage: Updated values:`, result);\r\n\r\n        // Update the side quest in the UI with the returned values from the server\r\n        sideQuest.completed = result.completed;\r\n        sideQuest.value_achieved = result.value_achieved;\r\n        sideQuest.streak = result.streak;\r\n\r\n        // Update the UI element with the updated streak\r\n        this.updateSideQuestUI(sideQuest);\r\n\r\n        // Update the week date progress for the selected date\r\n        // Clear the cache for this date to force a refresh\r\n        const dateKey = this.formatDate(this.selectedDate);\r\n        delete this.weekProgressCache[dateKey];\r\n\r\n        // Update the progress ring for this date\r\n        this.updateProgressRingForDate(dateKey);\r\n\r\n        // Check if all quests are completed\r\n        this.checkAllQuestsCompleted(this.quests);\r\n\r\n        // Reset flag for this specific side quest\r\n        delete this.togglingSideQuestIds[sideQuest.id];\r\n        console.log(`TodayPage: Finished toggle for side quest ${sideQuest.id}`);\r\n      } catch (error) {\r\n        console.error(`TodayPage: Error toggling side quest ${sideQuest.id}:`, error);\r\n\r\n        // Revert the local state if the server update failed\r\n        sideQuest.value_achieved = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;\r\n        sideQuest.completed = sideQuest.value_achieved === sideQuest.current_quest.goal_value;\r\n        this.updateSideQuestUI(sideQuest);\r\n\r\n        delete this.togglingSideQuestIds[sideQuest.id];\r\n      }\r\n    } catch (error) {\r\n      console.error(`TodayPage: Error in toggleSideQuest for ${sideQuest.id}:`, error);\r\n      delete this.togglingSideQuestIds[sideQuest.id];\r\n    }\r\n  }\r\n\r\n  // Helper method to update the UI for a specific side quest\r\n  private updateSideQuestUI(sideQuest: DailyQuest) {\r\n    // Find the side quest element in the DOM\r\n    const questElement = document.querySelector(`.daily-side-quest [data-quest-id=\"${sideQuest.id}\"]`);\r\n    if (!questElement) {\r\n      console.error(`TodayPage: Could not find side quest element for ID: ${sideQuest.id}`);\r\n      return;\r\n    }\r\n\r\n    // Update the completed class\r\n    if (sideQuest.completed) {\r\n      questElement.classList.add('completed');\r\n    } else {\r\n      questElement.classList.remove('completed');\r\n    }\r\n\r\n    // Update the streak display - only show streak for today\r\n    const streakElements = questElement.querySelectorAll('.quest-streak');\r\n    if (streakElements && streakElements.length > 0) {\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = new Date(this.selectedDate);\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n      // Only show streak for today's view\r\n      if (isTodaySelected) {\r\n        const streakValue = sideQuest.streak || 0;\r\n        console.log(`TodayPage: Side quest ${sideQuest.id}, completed: ${sideQuest.completed}, streak: ${streakValue}`);\r\n\r\n        // Update all streak elements (there might be multiple due to ngIf)\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            // Make sure the streak is visible\r\n            (element as HTMLElement).style.display = 'block';\r\n            element.textContent = `🔥${streakValue}d`;\r\n          }\r\n        });\r\n      } else {\r\n        // Hide streak for previous days\r\n        streakElements.forEach(element => {\r\n          if (element.parentElement && element.parentElement.contains(element)) {\r\n            (element as HTMLElement).style.display = 'none';\r\n            element.textContent = '';\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    // Update the progress text\r\n    const progressText = questElement.querySelector('.progress-text');\r\n    if (progressText) {\r\n      const goalUnit = sideQuest.current_quest.goal_unit !== 'count' ? ` ${sideQuest.current_quest.goal_unit}` : '';\r\n      progressText.textContent = `${sideQuest.value_achieved}/${sideQuest.current_quest.goal_value}${goalUnit}`;\r\n    }\r\n\r\n    // Force a repaint to ensure the UI updates\r\n    setTimeout(() => {\r\n      if (questElement.parentElement) {\r\n        const display = questElement.parentElement.style.display;\r\n        questElement.parentElement.style.display = 'none';\r\n        // Force a reflow\r\n        void questElement.parentElement.offsetHeight;\r\n        questElement.parentElement.style.display = display;\r\n      }\r\n    }, 0);\r\n\r\n    console.log(`TodayPage: Updated UI for side quest ${sideQuest.id}`);\r\n  }\r\n\r\n  openAddQuestModal(event: Event) {\r\n    event.preventDefault();\r\n    this.showAddQuestModal = true;\r\n    this.newQuest = this.getEmptyQuest();\r\n    this.selectedDaysOfWeek = [];\r\n    this.selectedDaysOfMonth = [];\r\n\r\n    // Reset hasHighPriorityQuest flag\r\n    this.hasHighPriorityQuest = false;\r\n\r\n    // Reset animation states\r\n    this.resetAnimationStates();\r\n\r\n    // Start quest type animation after modal opens\r\n    setTimeout(() => {\r\n      console.log('Setting questTypeAnimated to true');\r\n      this.questTypeAnimated = true;\r\n    }, 300);\r\n  }\r\n\r\n  resetAnimationStates() {\r\n    this.questTypeAnimated = false;\r\n    this.questTypeAnimating = false;\r\n    this.selectedQuestType = '';\r\n    this.categoryAnimated = false;\r\n    this.categoryAnimating = false;\r\n    this.categorySelected = false;\r\n    this.selectedCategory = '';\r\n    this.priorityAnimated = false;\r\n    this.priorityAnimating = false;\r\n    this.goalAnimated = false;\r\n    this.questDetailsAnimated = false;\r\n    this.frequencyAnimated = false;\r\n    this.frequencyOptionsAnimated = false;\r\n    this.previewAnimated = false;\r\n  }\r\n\r\n  closeAddQuestModal() {\r\n    this.showAddQuestModal = false;\r\n    // Reset form state\r\n    this.newQuest = this.getEmptyQuest();\r\n    this.selectedDaysOfWeek = [];\r\n    this.selectedDaysOfMonth = [];\r\n    this.hasHighPriorityQuest = false;\r\n    this.resetAnimationStates();\r\n  }\r\n\r\n  // Animation methods\r\n  selectQuestType(type: 'build' | 'quit') {\r\n    this.selectedQuestType = type;\r\n    this.questTypeAnimating = true;\r\n\r\n    // Start slide out animation\r\n    setTimeout(() => {\r\n      this.newQuest.quest_type = type as any;\r\n\r\n      // After quest type is set, trigger category animation\r\n      setTimeout(() => {\r\n        this.categoryAnimated = true;\r\n      }, 100);\r\n    }, 300); // Half of the animation duration\r\n  }\r\n\r\n  selectCategory(category: string) {\r\n    this.selectedCategory = category;\r\n    this.categoryAnimating = true;\r\n\r\n    // Start slide out animation based on category\r\n    setTimeout(() => {\r\n      this.newQuest.category = category as any;\r\n      this.categorySelected = true;\r\n      this.checkCategoryPriority({ detail: { value: category } });\r\n\r\n      // After category is set, trigger priority animation\r\n      setTimeout(() => {\r\n        this.priorityAnimated = true;\r\n      }, 100);\r\n    }, 300); // Half of the animation duration\r\n  }\r\n\r\n  selectPriority(priority: 'basic' | 'high') {\r\n    if (priority === 'high' && this.hasHighPriorityQuest) {\r\n      return; // Don't allow high priority if already exists\r\n    }\r\n\r\n    this.priorityAnimating = true;\r\n\r\n    setTimeout(() => {\r\n      this.newQuest.priority = priority as any;\r\n    }, 300);\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.currentStep < this.totalSteps) {\r\n      this.currentStep++;\r\n\r\n      // If moving to step 5, trigger sequential animations\r\n      if (this.currentStep === 5) {\r\n        setTimeout(() => {\r\n          this.questDetailsAnimated = true;\r\n        }, 100);\r\n\r\n        setTimeout(() => {\r\n          this.goalAnimated = true;\r\n        }, 300);\r\n\r\n        setTimeout(() => {\r\n          this.frequencyAnimated = true;\r\n        }, 500);\r\n\r\n        setTimeout(() => {\r\n          this.previewAnimated = true;\r\n        }, 700);\r\n      }\r\n    }\r\n  }\r\n\r\n  prevStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  moveCaretToEnd() {\r\n    setTimeout(() => {\r\n      this.emojiInput.getInputElement().then((input: HTMLInputElement) => {\r\n        const pos = input.value.length;\r\n        input.setSelectionRange(pos, pos);\r\n        input.scrollLeft = input.scrollWidth;\r\n      });\r\n    }, 100);\r\n  }\r\n\r\n  get progress(): number {\r\n    return (this.currentStep) / (this.totalSteps);\r\n  }\r\n\r\n\r\n  async createQuest() {\r\n    if (!this.userId || !this.newQuest.name || !this.newQuest.emoji || !this.newQuest.quest_type ||\r\n      !this.newQuest.category || !this.newQuest.goal_value || !this.newQuest.goal_unit || !this.newQuest.goal_period) {\r\n      console.error('TodayPage: Cannot create quest - missing required fields');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      if (this.newQuest.goal_period === 'week' && this.selectedDaysOfWeek.length > 0) {\r\n        this.newQuest.task_days_of_week = this.selectedDaysOfWeek.join(',');\r\n      } else if (this.newQuest.goal_period === 'month' && this.selectedDaysOfMonth.length > 0) {\r\n        this.newQuest.task_days_of_month = this.selectedDaysOfMonth.join(',');\r\n      }\r\n\r\n      const { data: userProfile, error: userError } = await this.supabaseService.getClient()\r\n        .from('profiles')\r\n        .select('id')\r\n        .eq('id', this.userId)\r\n        .single();\r\n\r\n      if (userError || !userProfile) {\r\n        console.error('TodayPage: User profile not found:', userError || 'No profile found');\r\n        throw new Error('User profile not found. Please ensure you are logged in.');\r\n      }\r\n\r\n      const questToCreate: Omit<Quest, 'id' | 'streak' | 'created_at'> = {\r\n        name: this.newQuest.name || '',\r\n        description: this.newQuest.description || '',\r\n        quest_type: this.newQuest.quest_type || 'build',\r\n        goal_value: this.newQuest.goal_value || 1,\r\n        goal_unit: this.newQuest.goal_unit || 'count',\r\n        goal_period: this.newQuest.goal_period || 'day',\r\n        priority: this.newQuest.priority || 'basic',\r\n        category: this.newQuest.category || 'strength',\r\n        emoji: this.newQuest.emoji || '🎯',\r\n        task_days_of_week: this.newQuest.task_days_of_week || '',\r\n        task_days_of_month: this.newQuest.task_days_of_month || '',\r\n        user_id: this.userId,\r\n        active: true\r\n      };\r\n\r\n      try {\r\n        const questId = await this.questService.createQuest(questToCreate);\r\n\r\n        if (this.newQuest.quest_type === 'quit') {\r\n\r\n          await this.questService.toggleQuestCompletion(\r\n            this.userId,\r\n            questId,\r\n            new Date(),\r\n            0,\r\n            { ...questToCreate, id: questId } as Quest\r\n          );\r\n        }\r\n\r\n        const dateKey = this.formatDate(this.selectedDate);\r\n        delete this.questCache[dateKey];\r\n        delete this.weekProgressCache[dateKey];\r\n\r\n        this.closeAddQuestModal();\r\n        this.loadData();\r\n      } catch (questError: any) {\r\n        console.error('TodayPage: Error creating quest:', questError);\r\n\r\n        if (questError.message && questError.message.includes('foreign key constraint')) {\r\n          alert('Database configuration issue detected. Please run the fix_quest_constraints.sql script in the Supabase SQL Editor to fix the foreign key constraints.');\r\n        } else if (questError.message && questError.message.includes('fix_quest_constraints.sql')) {\r\n          alert(questError.message);\r\n        } else {\r\n          alert(`Error creating quest: ${questError.message}`);\r\n        }\r\n      }\r\n    } catch (error: any) {\r\n      console.error('TodayPage: Error in createQuest:', error);\r\n      alert(`Error: ${error.message || 'Unknown error occurred'}`);\r\n    }\r\n  }\r\n\r\n  updateDaysOfWeek(day: string) {\r\n    const index = this.selectedDaysOfWeek.indexOf(day);\r\n\r\n    if (index !== -1) {\r\n      this.selectedDaysOfWeek.splice(index, 1);\r\n    } else {\r\n      this.selectedDaysOfWeek.push(day);\r\n    }\r\n  }\r\n\r\n  updateDaysOfMonth(event: any, day: number) {\r\n    // Handle both standard Event and Ionic's CustomEvent\r\n    let isChecked = false;\r\n\r\n    if (event.detail !== undefined) {\r\n      // Ionic checkbox event\r\n      isChecked = event.detail.checked;\r\n    } else if (event.target instanceof HTMLInputElement) {\r\n      // Standard checkbox event\r\n      isChecked = event.target.checked;\r\n    }\r\n\r\n    if (isChecked) {\r\n      this.selectedDaysOfMonth.push(day);\r\n    } else {\r\n      const index = this.selectedDaysOfMonth.indexOf(day);\r\n      if (index !== -1) {\r\n        this.selectedDaysOfMonth.splice(index, 1);\r\n      }\r\n    }\r\n\r\n    console.log(`TodayPage: Updated days of month: ${this.selectedDaysOfMonth.join(', ')}`);\r\n  }\r\n\r\n  updatePeriodDisplay() {\r\n    // Reset selections when period changes\r\n    this.selectedDaysOfWeek = [];\r\n    this.selectedDaysOfMonth = [];\r\n    console.log(`TodayPage: Period changed to ${this.newQuest.goal_period}, reset selections`);\r\n\r\n    // Trigger frequency options animation\r\n    setTimeout(() => {\r\n      this.frequencyOptionsAnimated = true;\r\n    }, 200);\r\n  }\r\n\r\n  toggleMonthDay(day: number) {\r\n    const index = this.selectedDaysOfMonth.indexOf(day);\r\n    if (index !== -1) {\r\n      this.selectedDaysOfMonth.splice(index, 1);\r\n    } else {\r\n      this.selectedDaysOfMonth.push(day);\r\n    }\r\n  }\r\n\r\n  getCategoryIcon(category: string): string {\r\n    const icons: { [key: string]: string } = {\r\n      'strength': '💪',\r\n      'money': '💰',\r\n      'health': '🏥',\r\n      'knowledge': '🧠'\r\n    };\r\n    return icons[category] || '📌';\r\n  }\r\n\r\n  selectFrequency(period: 'day' | 'week' | 'month') {\r\n    this.newQuest.goal_period = period as any;\r\n    this.updatePeriodDisplay();\r\n  }\r\n\r\n  getFrequencyText(): string {\r\n    if (this.newQuest.goal_period === 'day') {\r\n      return 'daily';\r\n    } else if (this.newQuest.goal_period === 'week') {\r\n      if (this.selectedDaysOfWeek.length === 0) {\r\n        return 'weekly';\r\n      } else if (this.selectedDaysOfWeek.length === 7) {\r\n        return 'daily';\r\n      } else {\r\n        return `${this.selectedDaysOfWeek.length}x per week`;\r\n      }\r\n    } else if (this.newQuest.goal_period === 'month') {\r\n      if (this.selectedDaysOfMonth.length === 0) {\r\n        return 'monthly';\r\n      } else {\r\n        return `${this.selectedDaysOfMonth.length}x per month`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n\r\n  checkCategoryPriority(event?: any) {\r\n    if (!this.userId || !this.newQuest.category) return;\r\n\r\n    // If this is an Ionic event, make sure we have the latest category value\r\n    if (event && event.detail) {\r\n      this.newQuest.category = event.detail.value;\r\n      console.log(`TodayPage: Category changed to ${this.newQuest.category} via Ionic event`);\r\n    }\r\n\r\n    // Check if user already has a high priority quest in this category\r\n    this.questService.getQuests(this.userId).pipe(\r\n      take(1),\r\n      map(quests => {\r\n        return quests.some(q =>\r\n          q.category === this.newQuest.category &&\r\n          q.priority === 'high' &&\r\n          q.active\r\n        );\r\n      })\r\n    ).subscribe({\r\n      next: (hasHighPriority) => {\r\n        this.hasHighPriorityQuest = hasHighPriority;\r\n\r\n        // If user already has a high priority quest, set this one to basic\r\n        if (hasHighPriority) {\r\n          this.newQuest.priority = 'basic';\r\n        }\r\n\r\n        console.log(`TodayPage: Category ${this.newQuest.category} has high priority quest: ${hasHighPriority}`);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Check if all quests are completed for today and show celebration if enabled\r\n   */\r\n  checkAllQuestsCompleted(quests: QuestDisplay[]) {\r\n    // Only check for today's date\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(this.selectedDate);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n    const isTodaySelected = selectedDate.getTime() === today.getTime();\r\n    const todayStr = this.formatDate(today);\r\n\r\n    if (!isTodaySelected || !this.currentUser) {\r\n      return;\r\n    }\r\n\r\n    // Check if celebration has already been shown for today\r\n    const celebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);\r\n    if (celebrationShown) {\r\n      console.log('TodayPage: Celebration already shown for today:', todayStr);\r\n      return;\r\n    }\r\n\r\n    // Check if all quests are completed\r\n    const allQuestsCompleted = quests.length > 0 && quests.every(quest => quest.completed);\r\n\r\n    // Check if side quest is completed (if enabled)\r\n    const sideQuestCompleted = !this.showSidequests || !this.dailyQuest || this.dailyQuest.completed;\r\n\r\n    // Show celebration if all quests and side quests are completed and celebration is enabled\r\n\r\n    if (allQuestsCompleted && sideQuestCompleted && this.currentUser.show_celebration) {\r\n      // Make sure we have the latest user data\r\n      this.userService.getUserById(this.userId!).subscribe(userData => {\r\n        if (userData) {\r\n          this.currentUser = userData;\r\n        }\r\n\r\n        // Show the celebration\r\n        this.showCelebration = true;\r\n\r\n        // Save today's date to localStorage\r\n        localStorage.setItem(`celebration_shown_${todayStr}`, 'true');\r\n\r\n        // Update our tracking array\r\n        if (!this.celebrationShownDates.includes(todayStr)) {\r\n          this.celebrationShownDates.push(todayStr);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Close the celebration modal\r\n   */\r\n  closeCelebration() {\r\n    this.showCelebration = false;\r\n  }\r\n\r\n\r\n\r\n  // Helper methods\r\n  formatDate(date: Date): string {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  isSameDay(date1: Date, date2: Date): boolean {\r\n    return date1.getFullYear() === date2.getFullYear() &&\r\n      date1.getMonth() === date2.getMonth() &&\r\n      date1.getDate() === date2.getDate();\r\n  }\r\n\r\n  getToday(): Date {\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    return today;\r\n  }\r\n\r\n  // Convert Django day index (0=Monday, 6=Sunday) to short day name\r\n  private getDayNameShort(djangoDayIndex: number): string {\r\n    // Map Django day index to day name\r\n    const dayMap = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];\r\n    return dayMap[djangoDayIndex];\r\n  }\r\n\r\n  // Convert Django day index (0=Monday, 6=Sunday) to full day name\r\n  private getDayNameFull(djangoDayIndex: number): string {\r\n    // Map Django day index to full day name\r\n    const dayMap = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\r\n    return dayMap[djangoDayIndex];\r\n  }\r\n\r\n  private getEmptyQuest(): Partial<Quest> {\r\n    return {\r\n      name: '',\r\n      description: '',\r\n      quest_type: '' as any, // Empty by default, user must select\r\n      goal_value: 1,\r\n      goal_unit: 'count' as QuestGoalUnit,\r\n      goal_period: 'day' as QuestPeriod,\r\n      priority: 'basic' as QuestPriority, // Default to basic priority\r\n      category: '' as QuestCategory,\r\n      emoji: '🎯'\r\n    };\r\n  }\r\n\r\n  // Helper methods for the template\r\n  // Note: Progress slider background is now handled via CSS variables\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAqBC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AAC/E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,eAAe,QAAQ,iCAAiC;AAGjE,SAAqBC,YAAY,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,IAAI,EAAEC,cAAc,QAAQ,MAAM;AACnG,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,oBAAoB,QAAQ,oDAAoD;AACzF,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,mBAAmB,QAAQ,wCAAwC;AAC5E,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;AAyCrE,IAAMC,SAAS,GAAf,MAAMA,SAAS;EAqCpB;EACQC,kBAAkBA,CAAA;IACxB;IACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,MAAMC,YAAY,GAAG,IAAIF,IAAI,CAAC,IAAI,CAACE,YAAY,CAAC;IAChDA,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACjC,MAAME,eAAe,GAAGD,YAAY,CAACE,OAAO,EAAE,KAAKL,KAAK,CAACK,OAAO,EAAE;IAElE;IACA,IAAI,CAACD,eAAe,EAAE;MACpB,IAAI,CAACE,UAAU,GAAG,IAAI;MACtB;IACF;IAEA,IAAI,IAAI,CAACC,cAAc,IAAIH,eAAe,IAAI,IAAI,CAACI,MAAM,EAAE;MACzD;MACA,IAAI,CAACC,gBAAgB,CAACC,4BAA4B,CAAC,IAAI,CAACF,MAAO,CAAC,CAACG,IAAI,CACnExB,IAAI,CAAC,CAAC,CAAC,CACR,CAACyB,SAAS,CAAC;QACVC,IAAI,EAAGC,UAAU,IAAI;UACnB,IAAIA,UAAU,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;YACvC,MAAMC,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;YAE/B;YACA,IAAI,CAACG,eAAe,CAACC,SAAS,EAAE,CAC7BC,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEL,SAAS,CAACM,gBAAgB,CAAC,CACpCC,MAAM,EAAE,CACRC,IAAI,CAACC,QAAQ,IAAG;cACf,IAAIA,QAAQ,CAACC,KAAK,EAAE;gBAClB;cACF;cAEA,MAAMC,YAAY,GAAGF,QAAQ,CAACG,IAAI;cAElC;cACA,IAAI,CAACtB,UAAU,GAAG;gBAChBuB,EAAE,EAAEb,SAAS,CAACa,EAAG;gBACjBC,aAAa,EAAE;kBACbD,EAAE,EAAEb,SAAS,CAACM,gBAAiB;kBAC/BS,IAAI,EAAEJ,YAAY,CAACI,IAAI,IAAI,kBAAkB;kBAC7CC,WAAW,EAAEL,YAAY,CAACK,WAAW,IAAI,gCAAgC;kBACzEC,UAAU,EAAEN,YAAY,CAACM,UAAU,IAAI,CAAC;kBACxCC,SAAS,EAAEP,YAAY,CAACO,SAAS,IAAI;iBACtC;gBACDC,MAAM,EAAEnB,SAAS,CAACmB,MAAM,IAAI,CAAC;gBAC7BC,SAAS,EAAEpB,SAAS,CAACoB,SAAS,IAAI,KAAK;gBACvCC,cAAc,EAAErB,SAAS,CAACqB,cAAc,IAAI,CAAC;gBAC7CC,KAAK,EAAEX,YAAY,CAACW,KAAK,IAAI;eAC9B;YACH,CAAC,CAAC;UACN,CAAC,MAAM;YACL,IAAI,CAAChC,UAAU,GAAG,IAAI;UACxB;QACF,CAAC;QACDoB,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACpB,UAAU,GAAG,IAAI;QACxB;OACD,CAAC;IACJ;EACF;EAqDAiC,YAAA;IAtJA;IACA,KAAAC,KAAK,GAA4BvD,EAAE,CAAC,IAAI,CAAC;IACzC,KAAAuB,MAAM,GAAkB,IAAI;IAE5B,KAAAD,cAAc,GAAG,IAAI;IAErB;IACA,KAAAJ,YAAY,GAAS,IAAIF,IAAI,EAAE;IAC/B,KAAAwC,SAAS,GAAe,EAAE;IAC1B,KAAAC,QAAQ,GAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/D,KAAAC,UAAU,GAAW,OAAO;IAC5B,KAAAC,UAAU,GAAW,CAAC;IAItB;IACA,KAAAC,MAAM,GAAmB,EAAE;IAC3B,KAAAvC,UAAU,GAAsB,IAAI;IACpC,KAAAwC,WAAW,GAAG,CAAC;IACf,KAAAC,UAAU,GAAG,CAAC;IAEd,KAAAC,MAAM,GAAa,CACjB,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAChD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EACrD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAC7C,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CACtC;IAGD;IACQ,KAAAC,UAAU,GAA0C,EAAE;IAE9D;IACQ,KAAAC,aAAa,GAAG,KAAK;IAkE7B;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,QAAQ,GAAG,IAAI,CAACC,aAAa,EAAE;IAC/B,KAAAC,oBAAoB,GAAG,KAAK;IAE5B;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,qBAAqB,GAAa,EAAE;IAEpC;IACA,KAAAC,QAAQ,GAAG,CACT;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAQ,CAAE,EACjD;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAQ,CAAE,EACjD;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAS,CAAE,EAClD;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAW,CAAE,EACpD;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAU,CAAE,EACnD;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAQ,CAAE,EACjD;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAU,CAAE,CACpD;IACD,KAAAC,SAAS,GAAGC,KAAK,CAAC1D,IAAI,CAAC;MAAEJ,MAAM,EAAE;IAAE,CAAE,EAAE,CAAC+D,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;IACvD,KAAAC,kBAAkB,GAAa,EAAE;IACjC,KAAAC,mBAAmB,GAAa,EAAE;IAElC;IACQ,KAAAC,YAAY,GAAG7G,MAAM,CAACK,YAAY,CAAC;IACnC,KAAA+B,gBAAgB,GAAGpC,MAAM,CAACM,gBAAgB,CAAC;IAC3C,KAAAwG,WAAW,GAAG9G,MAAM,CAACO,WAAW,CAAC;IACjC,KAAAqC,eAAe,GAAG5C,MAAM,CAACQ,eAAe,CAAC;IACzC,KAAAuG,KAAK,GAAG/G,MAAM,CAACkB,cAAc,CAAC;IAC9B,KAAA8F,MAAM,GAAGhH,MAAM,CAACmB,MAAM,CAAC;IACvB,KAAA8F,kBAAkB,GAAGjH,MAAM,CAACoB,kBAAkB,CAAC;IAC/C,KAAA8F,gBAAgB,GAAGlH,MAAM,CAACsB,uBAAuB,CAAC;IAClD,KAAA6F,aAAa,GAAG,KAAK,CAAC,CAAC;IAykB/B;IACQ,KAAAC,iBAAiB,GAAgE,EAAE;IAmL3F;IACQ,KAAAC,cAAc,GAAG,KAAK;IAqI9B;IACQ,KAAAC,gBAAgB,GAAmC,EAAE;IAoC7D;IACQ,KAAAC,gBAAgB,GAAmC,EAAE;IAgY7D;IACQ,KAAAC,oBAAoB,GAAmC,EAAE;IAvyC/D;IACA,IAAI,CAACT,KAAK,CAACU,WAAW,CAAClF,SAAS,CAACmF,MAAM,IAAG;MACxC,MAAMC,SAAS,GAAGD,MAAM,CAAC,MAAM,CAAC;MAChC,MAAME,eAAe,GAAGF,MAAM,CAAC,aAAa,CAAC;MAE7CG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEH,SAAS,CAAC;MAC/DE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEF,eAAe,CAAC;MAE5E;MACA,IAAIA,eAAe,KAAKG,SAAS,EAAE;QACjC,IAAI;UACF,IAAI,CAACxD,UAAU,GAAGyD,QAAQ,CAACJ,eAAe,CAAC;UAC3CC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACvD,UAAU,CAAC;QAChE,CAAC,CAAC,OAAOlB,KAAK,EAAE;UACdwE,OAAO,CAACxE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,IAAI,CAACkB,UAAU,GAAG,CAAC;QACrB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,UAAU,GAAG,CAAC;MACrB;MAEA;MACA,IAAIoD,SAAS,EAAE;QACb,IAAI;UACF;UACA,IAAI,qBAAqB,CAACM,IAAI,CAACN,SAAS,CAAC,EAAE;YACzC,IAAI,CAAC7F,YAAY,GAAG,IAAIF,IAAI,CAAC+F,SAAS,CAAC;YACvCE,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAAChG,YAAY,CAAC;UAC5E,CAAC,MAAM;YACL+F,OAAO,CAACxE,KAAK,CAAC,8CAA8C,EAAEsE,SAAS,CAAC;YACxE,IAAI,CAAC7F,YAAY,GAAG,IAAIF,IAAI,EAAE,CAAC,CAAC;UAClC;QACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;UACdwE,OAAO,CAACxE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrE,IAAI,CAACvB,YAAY,GAAG,IAAIF,IAAI,EAAE,CAAC,CAAC;QAClC;MACF,CAAC,MAAM;QACL,IAAI,CAACE,YAAY,GAAG,IAAIF,IAAI,EAAE,CAAC,CAAC;MAClC;MAEA;MACA,IAAI,CAACsG,iBAAiB,EAAE;MAExB;MACA,IAAI,CAACC,gBAAgB,EAAE;MAEvB;MACA,IAAI,IAAI,CAAChG,MAAM,EAAE;QACf,IAAI,CAACiG,QAAQ,EAAE;MACjB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACzF,eAAe,CAAC0F,YAAY,CAAC/F,SAAS,CAACgG,QAAQ,IAAG;MAG7E,IAAI,CAACA,QAAQ,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE;QACA;MACF;MAEA;MACA,IAAI,CAAC3F,MAAM,GAAGoG,QAAQ,CAAC/E,EAAE;MAGzB;MACA,IAAI,CAACsD,WAAW,CAAC0B,WAAW,CAACD,QAAQ,CAAC/E,EAAE,CAAC,CAACjB,SAAS,CAACkG,QAAQ,IAAG;QAC7D,IAAI,CAACA,QAAQ,EAAE;UACbZ,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE;UACA;QACF;QAEAD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEW,QAAQ,CAAC;QACrD,IAAI,CAACL,QAAQ,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACjE,KAAK,GAAG,IAAI,CAACvB,eAAe,CAAC0F,YAAY,CAAChG,IAAI,CACjDzB,SAAS,CAAC0H,QAAQ,IAAG;MACnB,IAAI,CAACA,QAAQ,EAAE;QACb,OAAO3H,EAAE,CAAC,IAAI,CAAC;MACjB;MAEA,OAAO,IAAI,CAACkG,WAAW,CAAC0B,WAAW,CAACD,QAAQ,CAAC/E,EAAE,CAAC;IAClD,CAAC,CAAC,CACH;IAED;IACA,MAAMkF,oBAAoB,GAAG,IAAI,CAACvE,KAAK,CAAC5B,SAAS,CAAC;MAChDC,IAAI,EAAGmG,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACzG,cAAc,GAAGyG,IAAI,CAACC,iBAAiB;UAC5C,IAAI,CAAC3C,WAAW,GAAG0C,IAAI;QACzB;MACF;KACD,CAAC;IAEF;IACA,IAAI,CAACN,gBAAgB,GAAG,IAAI5H,YAAY,EAAE;IAC1C,IAAI,CAAC4H,gBAAgB,CAACQ,GAAG,CAACH,oBAAoB,CAAC;EACjD;EAEAI,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,iBAAiB,EAAE;IAExB;IACAa,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,eAAe,EAAE;IACxB,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,IAAI;MACF;MACA,MAAMrH,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxB,MAAMqH,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACvH,KAAK,CAAC;MAEvC;MACA,MAAMwH,OAAO,GAAa,EAAE;MAC5B,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,YAAY,CAAC1G,MAAM,EAAEgE,CAAC,EAAE,EAAE;QAC5C,MAAM2C,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC3C,CAAC,CAAC;QAC/B,IAAI2C,GAAG,EAAE;UACPF,OAAO,CAACG,IAAI,CAACD,GAAG,CAAC;QACnB;MACF;MAEA;MACAF,OAAO,CAACI,OAAO,CAACF,GAAG,IAAG;QACpB,IAAIA,GAAG,CAACG,UAAU,CAAC,oBAAoB,CAAC,IAAIH,GAAG,KAAK,qBAAqBJ,QAAQ,EAAE,EAAE;UACnFG,YAAY,CAACK,UAAU,CAACJ,GAAG,CAAC;QAC9B;MACF,CAAC,CAAC;MAEF;MACA,MAAMK,qBAAqB,GAAGN,YAAY,CAACO,OAAO,CAAC,qBAAqBV,QAAQ,EAAE,CAAC;MAEnF;MACA,IAAI,CAAC/C,qBAAqB,GAAG,EAAE;MAC/B,IAAIwD,qBAAqB,EAAE;QACzB,IAAI,CAACxD,qBAAqB,CAACoD,IAAI,CAACL,QAAQ,CAAC;MAC3C;IACF,CAAC,CAAC,OAAO5F,KAAK,EAAE;MACd,IAAI,CAAC6C,qBAAqB,GAAG,EAAE;IACjC;EACF;EAEM0D,gBAAgBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpB;MACA;MACA,MAAMvB,QAAQ,GAAGsB,KAAI,CAACjH,eAAe,CAACmH,YAAY,CAAC3D,KAAK;MAExD,IAAI,CAACmC,QAAQ,EAAE;QACbV,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE;MACF;MAEA;MACA+B,KAAI,CAAC/C,WAAW,CAACkD,gBAAgB,CAACzB,QAAQ,CAAC,CAAChG,SAAS,CAACkG,QAAQ,IAAG;QAC/D,IAAI,CAACA,QAAQ,EAAE;UACboB,KAAI,CAAC7C,MAAM,CAACiD,aAAa,CAAC,SAAS,CAAC;UACpC;QACF;QAEA;QACA,IAAIC,OAAO,GAAGzB,QAAQ,CAAC0B,mBAAmB,GAAG,IAAIvI,IAAI,CAAC6G,QAAQ,CAAC0B,mBAAmB,CAAC,GAAG,IAAI;QAC1F,MAAMC,WAAW,GAAG,IAAIxI,IAAI,EAAE;QAE9B;QACA,IAAIyI,WAAW,GAAG,KAAK;QACvB,IAAIH,OAAO,YAAYtI,IAAI,EAAE;UAC3ByI,WAAW,GAAGH,OAAO,GAAGE,WAAW;QACrC;QAEA,IAAI,CAACC,WAAW,EAAE;UAChB;UACA,IAAIR,KAAI,CAAC1C,aAAa,EAAE;UACxB0C,KAAI,CAAC1C,aAAa,GAAG,IAAI;UAEzB4B,UAAU,CAAC,MAAK;YACdc,KAAI,CAAC7C,MAAM,CAACiD,aAAa,CAAC,UAAU,CAAC;YACrClB,UAAU,CAAC,MAAK;cACdc,KAAI,CAAC1C,aAAa,GAAG,KAAK;YAC5B,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,EAAE,GAAG,CAAC;UACP;QACF;QAEA;QACA,MAAMmD,OAAO,GAAGT,KAAI,CAACX,UAAU,CAACW,KAAI,CAAC/H,YAAY,CAAC;QAClD,IAAI+H,KAAI,CAACjF,UAAU,CAAC0F,OAAO,CAAC,EAAE;UAC5B;UACAT,KAAI,CAACrF,MAAM,GAAGqF,KAAI,CAACjF,UAAU,CAAC0F,OAAO,CAAC;UAEtC;UACAC,qBAAqB,CAAC,MAAK;YACzBV,KAAI,CAACW,2BAA2B,EAAE;UACpC,CAAC,CAAC;UAEF;UACAX,KAAI,CAACnI,kBAAkB,EAAE;QAC3B,CAAC,MAAM;UACL;UACAmI,KAAI,CAACzB,QAAQ,EAAE;QACjB;MACF,CAAC,CAAC;MAEF;MACA,MAAMrB,KAAK,GAAG8C,KAAI,CAAC7C,MAAM,CAACyD,GAAG;MAC7B,MAAM9C,SAAS,GAAGkC,KAAI,CAACX,UAAU,CAACW,KAAI,CAAC/H,YAAY,CAAC;MAEpD,IAAIiF,KAAK,KAAK,QAAQ,EAAE;QACtB;QACA8C,KAAI,CAAC7C,MAAM,CAAC0D,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAC/BjD,WAAW,EAAE;YACXkD,IAAI,EAAEhD,SAAS;YACfiD,WAAW,EAAEf,KAAI,CAACtF,UAAU,KAAK,CAAC,GAAGsF,KAAI,CAACtF,UAAU,GAAG;WACxD;UACDsG,UAAU,EAAE;SACb,CAAC;MACJ;IAAC;EACH;EAGA;EACAL,2BAA2BA,CAAA;IACzB;IACAD,qBAAqB,CAAC,MAAK;MACzB,MAAMO,OAAO,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,CAAC;MAC7D,IAAIF,OAAO,CAACpI,MAAM,KAAK,CAAC,EAAE;QACxB;MACF;MAEAoI,OAAO,CAACvB,OAAO,CAAC0B,MAAM,IAAG;QACvB,IAAIA,MAAM,YAAYC,gBAAgB,EAAE;UACtC;UACA,MAAMC,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC;UAC1D,IAAI,CAACD,aAAa,EAAE;YAClB;UACF;UAEA;UACA,MAAME,WAAW,GAAGrD,QAAQ,CAACiD,MAAM,CAAC7E,KAAK,CAAC;UAC1C,MAAMkF,QAAQ,GAAGtD,QAAQ,CAACiD,MAAM,CAACM,GAAG,CAAC;UACrC,MAAMC,QAAQ,GAAGxD,QAAQ,CAACiD,MAAM,CAACQ,GAAG,CAAC;UAErC;UACA,MAAMC,UAAU,GAAGF,QAAQ,GAAGF,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKE,QAAQ,GAAGF,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;UAE9D;UACAL,MAAM,CAACU,KAAK,CAACC,UAAU,GACrB,iDAAiDF,UAAU,cAAcA,UAAU,kBAAkB;UAEvG;UACAT,MAAM,CAACY,YAAY,CAAC,oBAAoB,EAAEZ,MAAM,CAAC7E,KAAK,CAAC;QACzD,CAAC,MAAM,IAAI6E,MAAM,YAAYa,WAAW,IAAIb,MAAM,CAACc,OAAO,KAAK,WAAW,EAAE;UAC1E;UACA,MAAMZ,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC;UAC1D,IAAI,CAACD,aAAa,EAAE;YAClB;UACF;UAEA;UACA,MAAMa,SAAS,GAAGf,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;UACrD,MAAMa,OAAO,GAAGhB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG;UACjD,MAAMc,OAAO,GAAGjB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;UAEnD,MAAMC,WAAW,GAAGrD,QAAQ,CAACgE,SAAS,CAAC;UACvC,MAAMV,QAAQ,GAAGtD,QAAQ,CAACiE,OAAO,CAAC;UAClC,MAAMT,QAAQ,GAAGxD,QAAQ,CAACkE,OAAO,CAAC;UAElC;UACA,MAAMR,UAAU,GAAGF,QAAQ,GAAGF,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKE,QAAQ,GAAGF,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;UAE9D;UACAL,MAAM,CAACU,KAAK,CAACQ,WAAW,CAAC,kBAAkB,EAAE,GAAGT,UAAU,GAAG,CAAC;UAE9D;UACAT,MAAM,CAACY,YAAY,CAAC,oBAAoB,EAAER,WAAW,CAACe,QAAQ,EAAE,CAAC;QACnE;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACdxE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;EACnD;EAEAwE,WAAWA,CAAA;IACTzE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,IAAI,CAACO,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACkE,WAAW,EAAE;IACrC;EACF;EAEMnE,QAAQA,CAAA;IAAA,IAAAoE,MAAA;IAAA,OAAA1C,iBAAA;MACZ,IAAI,CAAC0C,MAAI,CAACrK,MAAM,EAAE;QAChB;MACF;MAEA;MACAqK,MAAI,CAACrE,gBAAgB,EAAE;MAEvB;MACA,MAAMmC,OAAO,GAAGkC,MAAI,CAACtD,UAAU,CAACsD,MAAI,CAAC1K,YAAY,CAAC;MAClD,IAAI0K,MAAI,CAAC5H,UAAU,CAAC0F,OAAO,CAAC,EAAE;QAC5B;QACAkC,MAAI,CAAChI,MAAM,GAAGgI,MAAI,CAAC5H,UAAU,CAAC0F,OAAO,CAAC;QAEtC;QACAC,qBAAqB,CAAC,MAAK;UACzBiC,MAAI,CAAChC,2BAA2B,EAAE;QACpC,CAAC,CAAC;QAEF;QACAgC,MAAI,CAAC9K,kBAAkB,EAAE;QAEzB;MACF;MAEA;MACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAMC,YAAY,GAAG,IAAIF,IAAI,CAAC4K,MAAI,CAAC1K,YAAY,CAAC;MAChDA,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,MAAME,eAAe,GAAGD,YAAY,CAACE,OAAO,EAAE,KAAKL,KAAK,CAACK,OAAO,EAAE;MAElE6F,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE0E,MAAI,CAACtD,UAAU,CAACsD,MAAI,CAAC1K,YAAY,CAAC,CAAC;MACpF,IAAIC,eAAe,EAAE;QACnB;QACA,MAAM0K,eAAe,GAAGD,MAAI,CAACtD,UAAU,CAACvH,KAAK,CAAC;QAC9C,IAAI;UACF,MAAM;YAAEyE,KAAK,EAAEsG;UAAqB,CAAE,SAASF,MAAI,CAACvF,kBAAkB,CAAC0F,GAAG,CAAC,yBAAyB,CAAC;UAErG,IAAID,qBAAqB,KAAKD,eAAe,EAAE;YAC7C5E,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;YAEvE;YACA,MAAM/G,cAAc,CAACyL,MAAI,CAAC3F,YAAY,CAAC+F,SAAS,CAACJ,MAAI,CAACrK,MAAO,CAAC,CAACG,IAAI,CACjExB,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS;cAAA,IAAAgM,IAAA,GAAA/C,iBAAA,CAAC,WAAMtF,MAAM,EAAG;gBACvB;gBACA,KAAK,MAAMsI,KAAK,IAAItI,MAAM,EAAE;kBAC1B,IAAIsI,KAAK,CAACtJ,EAAE,EAAE;oBACZ,MAAMgJ,MAAI,CAAC3F,YAAY,CAACkG,eAAe,CAACD,KAAK,CAACtJ,EAAE,CAAC;kBACnD;gBACF;gBAEA;gBACA,MAAMgJ,MAAI,CAAC3F,YAAY,CAACmG,+BAA+B,EAAE;gBAEzD;gBACA,OAAOxI,MAAM;cACf,CAAC;cAAA,iBAAAyI,EAAA;gBAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;cAAA;YAAA,IAAC,CACH,CAAC;YAEF;UACF,CAAC,MAAM;YACLtF,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAChE;QACF,CAAC,CAAC,OAAOzE,KAAK,EAAE;UACdwE,OAAO,CAACxE,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAE1E;QACF;QAEA;QACA,IAAImJ,MAAI,CAACtK,cAAc,EAAE;UACvBsK,MAAI,CAACpK,gBAAgB,CAACgL,0BAA0B,CAACZ,MAAI,CAACrK,MAAM,EAAEqK,MAAI,CAAC1K,YAAY,CAAC,CAC7ES,SAAS,CAAC;YACTc,KAAK,EAAGA,KAAK,IAAI;cACfwE,OAAO,CAACxE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;YAChE;WACD,CAAC;QACN;MACF;MACA;MACAmJ,MAAI,CAAC3F,YAAY,CAAC+F,SAAS,CAACJ,MAAI,CAACrK,MAAM,CAAC,CAACG,IAAI,CAC3CxB,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAAC2D,MAAM,IAAG;QACjB;QACA,MAAM6I,cAAc,GAAGb,MAAI,CAACc,mBAAmB,CAAC9I,MAAM,EAAEgI,MAAI,CAAC1K,YAAY,CAAC;QAE1E,IAAIuL,cAAc,CAAC3K,MAAM,KAAK,CAAC,EAAE;UAC/B,OAAO9B,EAAE,CAAC,EAAE,CAAC;QACf;QAEA;QACA,MAAM2M,oBAAoB,GAAG,CAAC,GAAGF,cAAc,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UAC7D,IAAID,CAAC,CAACE,UAAU,IAAID,CAAC,CAACC,UAAU,EAAE;YAChC,OAAO,IAAI/L,IAAI,CAAC8L,CAAC,CAACC,UAAU,CAAC,CAAC3L,OAAO,EAAE,GAAG,IAAIJ,IAAI,CAAC6L,CAAC,CAACE,UAAU,CAAC,CAAC3L,OAAO,EAAE;UAC5E;UACA,OAAOyL,CAAC,CAACjK,EAAE,IAAIkK,CAAC,CAAClK,EAAE,GAAGiK,CAAC,CAACjK,EAAE,CAACoK,aAAa,CAACF,CAAC,CAAClK,EAAE,CAAC,GAAG,CAAC;QACpD,CAAC,CAAC;QAEF;QACA,OAAOgJ,MAAI,CAAC3F,YAAY,CAACgH,uBAAuB,CAACrB,MAAI,CAACrK,MAAO,EAAEqK,MAAI,CAAC1K,YAAY,CAAC,CAACQ,IAAI,CACpFxB,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAACiN,WAAW,IAAG;UACtB;UACA,MAAMC,cAAc,GAAyC,EAAE;UAC/DD,WAAW,CAACvE,OAAO,CAACyE,QAAQ,IAAG;YAC7BD,cAAc,CAACC,QAAQ,CAACC,QAAQ,CAAC,GAAGD,QAAQ;UAC9C,CAAC,CAAC;UAEF;UACA;UACA,IAAIjM,eAAe,EAAE;YACnB;YACA,MAAM0K,eAAe,GAAGD,MAAI,CAACtD,UAAU,CAACvH,KAAK,CAAC;YAC9C,OAAO6K,MAAI,CAACvF,kBAAkB,CAAC0F,GAAG,CAAC,yBAAyB,CAAC,CAACxJ,IAAI,CAAC,CAAC;cAAEiD,KAAK,EAAEsG;YAAqB,CAAE,KAAI;cACtG,IAAIA,qBAAqB,KAAKD,eAAe,EAAE;gBAC7C5E,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;gBAEvE;gBACA,OAAO0E,MAAI,CAACtF,gBAAgB,CAACgH,gBAAgB,CAAC1B,MAAI,CAACrK,MAAO,EAAEoL,oBAAoB,CAAC,CAACpK,IAAI,CAACgL,OAAO,IAAG;kBAC/F;kBACA,OAAOZ,oBAAoB,CAAC5M,GAAG,CAACmM,KAAK,IAAG;oBACtC,MAAMkB,QAAQ,GAAGD,cAAc,CAACjB,KAAK,CAACtJ,EAAG,CAAC;oBAC1C,MAAM4K,gBAAgB,GAAGD,OAAO,CAACrB,KAAK,CAACtJ,EAAG,CAAC,IAAI,CAAC;oBAEhD;oBACAgJ,MAAI,CAAC3F,YAAY,CAACwH,iBAAiB,CAACvB,KAAK,CAACtJ,EAAG,EAAE4K,gBAAgB,CAAC,CAAC7L,SAAS,EAAE;oBAE5E,OAAO;sBACL,GAAGuK,KAAK;sBACR/I,SAAS,EAAE,CAAAiK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjK,SAAS,KAAI,KAAK;sBACvCC,cAAc,EAAE,CAAAgK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEhK,cAAc,KAAI,CAAC;sBAC7CF,MAAM,EAAEsK;qBACO;kBACnB,CAAC,CAAC;gBACJ,CAAC,CAAC,CAACjL,IAAI,CAACmL,MAAM,IAAG;kBACf;kBACA9B,MAAI,CAACvF,kBAAkB,CAACsH,GAAG,CAAC,yBAAyB,EAAE9B,eAAe,CAAC;kBACvE,OAAO6B,MAAM;gBACf,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLzG,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;gBAErF;gBACA,OAAOyF,oBAAoB,CAAC5M,GAAG,CAACmM,KAAK,IAAG;kBACtC,MAAMkB,QAAQ,GAAGD,cAAc,CAACjB,KAAK,CAACtJ,EAAG,CAAC;kBAE1C,OAAO;oBACL,GAAGsJ,KAAK;oBACR/I,SAAS,EAAE,CAAAiK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjK,SAAS,KAAI,KAAK;oBACvCC,cAAc,EAAE,CAAAgK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEhK,cAAc,KAAI,CAAC;oBAC7CF,MAAM,EAAEgJ,KAAK,CAAChJ,MAAM,IAAI;mBACT;gBACnB,CAAC,CAAC;cACJ;YACF,CAAC,CAAC,CAAC0K,KAAK,CAACnL,KAAK,IAAG;cACfwE,OAAO,CAACxE,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;cAE1E;cACA,OAAOkK,oBAAoB,CAAC5M,GAAG,CAACmM,KAAK,IAAG;gBACtC,MAAMkB,QAAQ,GAAGD,cAAc,CAACjB,KAAK,CAACtJ,EAAG,CAAC;gBAE1C,OAAO;kBACL,GAAGsJ,KAAK;kBACR/I,SAAS,EAAE,CAAAiK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjK,SAAS,KAAI,KAAK;kBACvCC,cAAc,EAAE,CAAAgK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEhK,cAAc,KAAI,CAAC;kBAC7CF,MAAM,EAAEgJ,KAAK,CAAChJ,MAAM,IAAI;iBACT;cACnB,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA,OAAO2K,OAAO,CAACC,OAAO,CAACnB,oBAAoB,CAAC5M,GAAG,CAACmM,KAAK,IAAG;cACtD,MAAMkB,QAAQ,GAAGD,cAAc,CAACjB,KAAK,CAACtJ,EAAG,CAAC;cAE1C,OAAO;gBACL,GAAGsJ,KAAK;gBACR/I,SAAS,EAAE,CAAAiK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjK,SAAS,KAAI,KAAK;gBACvCC,cAAc,EAAE,CAAAgK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEhK,cAAc,KAAI,CAAC;gBAC7CF,MAAM,EAAE,CAAC,CAAC;eACK;YACnB,CAAC,CAAC,CAAC;UACL;QACF,CAAC,CAAC,CACH;MAGH,CAAC,CAAC,CACH,CAACvB,SAAS,CAAC;QACVC,IAAI,EAAGmM,kBAAkB,IAAI;UAC3B;UACA,MAAMC,YAAY,GAAG,CAAC,GAAGD,kBAAkB,CAAC,CAACnB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACzD,IAAID,CAAC,CAACE,UAAU,IAAID,CAAC,CAACC,UAAU,EAAE;cAChC,OAAO,IAAI/L,IAAI,CAAC8L,CAAC,CAACC,UAAU,CAAC,CAAC3L,OAAO,EAAE,GAAG,IAAIJ,IAAI,CAAC6L,CAAC,CAACE,UAAU,CAAC,CAAC3L,OAAO,EAAE;YAC5E;YACA,OAAOyL,CAAC,CAACjK,EAAE,IAAIkK,CAAC,CAAClK,EAAE,GAAGiK,CAAC,CAACjK,EAAE,CAACoK,aAAa,CAACF,CAAC,CAAClK,EAAE,CAAC,GAAG,CAAC;UACpD,CAAC,CAAC;UAEF;UACAgJ,MAAI,CAACqC,uBAAuB,CAACD,YAAY,CAAC;UAE1C;UACApC,MAAI,CAAChI,MAAM,GAAGoK,YAAY;UAE1B;UACA,MAAMtE,OAAO,GAAGkC,MAAI,CAACtD,UAAU,CAACsD,MAAI,CAAC1K,YAAY,CAAC;UAClD0K,MAAI,CAAC5H,UAAU,CAAC0F,OAAO,CAAC,GAAGsE,YAAY;UAEvC;UACApC,MAAI,CAACsC,sBAAsB,EAAE;UAE7B;UACAvE,qBAAqB,CAAC,MAAK;YACzBiC,MAAI,CAAChC,2BAA2B,EAAE;UACpC,CAAC,CAAC;UAEF;UACAgC,MAAI,CAAC9K,kBAAkB,EAAE;QAC3B,CAAC;QACD2B,KAAK,EAAGA,KAAK,IAAI;UACfwE,OAAO,CAACxE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC/C;OACD,CAAC;IAAC;EAEL;EAEA6E,iBAAiBA,CAAA;IACf,MAAMvG,KAAK,GAAG,IAAIC,IAAI,EAAE;IAExB;IACA;IACA,MAAMmN,UAAU,GAAGpN,KAAK,CAACqN,MAAM,EAAE,CAAC,CAAC;IACnC,MAAMC,cAAc,GAAGF,UAAU,KAAK,CAAC,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC,CAAC,CAAC;IAC9D,MAAMG,WAAW,GAAG,IAAItN,IAAI,CAACD,KAAK,CAAC;IACnCuN,WAAW,CAACC,OAAO,CAACxN,KAAK,CAACyN,OAAO,EAAE,GAAGH,cAAc,GAAI,CAAC,GAAG,IAAI,CAAC1K,UAAW,CAAC;IAE7E,IAAI,CAACH,SAAS,GAAG,EAAE;IACnB,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMiE,IAAI,GAAG,IAAI/I,IAAI,CAACsN,WAAW,CAAC;MAClCvE,IAAI,CAACwE,OAAO,CAACD,WAAW,CAACE,OAAO,EAAE,GAAG1I,CAAC,CAAC;MAEvC,MAAM2I,UAAU,GAAG,IAAI,CAACnG,UAAU,CAACyB,IAAI,CAAC;MACxC,MAAM2E,OAAO,GAAG,IAAI,CAACC,SAAS,CAAC5E,IAAI,EAAEhJ,KAAK,CAAC;MAC3C,MAAM6N,UAAU,GAAG,IAAI,CAACD,SAAS,CAAC5E,IAAI,EAAE,IAAI,CAAC7I,YAAY,CAAC;MAC1D,MAAM2N,QAAQ,GAAG9E,IAAI,GAAGhJ,KAAK;MAE7B;MACA,MAAM2I,OAAO,GAAG+E,UAAU;MAC1B,IAAIK,WAAW,GAAG,CAAC;MACnB,IAAIC,eAAe,GAAG,CAAC;MACvB,IAAIC,oBAAoB,GAAG,CAAC;MAE5B,IAAI,IAAI,CAACxI,iBAAiB,CAACkD,OAAO,CAAC,EAAE;QACnC,MAAMuF,MAAM,GAAG,IAAI,CAACzI,iBAAiB,CAACkD,OAAO,CAAC;QAC9CoF,WAAW,GAAGG,MAAM,CAACC,KAAK;QAC1BH,eAAe,GAAGE,MAAM,CAAC9L,SAAS;QAClC6L,oBAAoB,GAAGF,WAAW,GAAG,CAAC,GAClCK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;MACP;MAEA,IAAI,CAACtL,SAAS,CAACkF,IAAI,CAAC;QAClBqB,IAAI,EAAE0E,UAAU;QAChBY,GAAG,EAAEtF,IAAI,CAACyE,OAAO,EAAE;QACnBc,QAAQ,EAAEZ,OAAO;QACjBa,WAAW,EAAEX,UAAU;QACvBY,SAAS,EAAEX,QAAQ;QACnBY,YAAY,EAAEX,WAAW;QACzBY,gBAAgB,EAAEX,eAAe;QACjCY,qBAAqB,EAAEX;OACxB,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAACzN,MAAM,EAAE;MACf;MACA4G,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAKA8F,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC3M,MAAM,EAAE;IAElB;IACA,IAAI,CAACiC,SAAS,CAACmF,OAAO,CAAC,CAACiH,QAAQ,EAAEC,KAAK,KAAI;MACzC,IAAID,QAAQ,CAACJ,SAAS,EAAE;MAExB,MAAMzF,IAAI,GAAG,IAAI/I,IAAI,CAAC4O,QAAQ,CAAC7F,IAAI,CAAC;MACpC,MAAML,OAAO,GAAG,IAAI,CAACpB,UAAU,CAACyB,IAAI,CAAC;MAErC;MACA,IAAI,IAAI,CAACvD,iBAAiB,CAACkD,OAAO,CAAC,EAAE;QACnC,MAAMuF,MAAM,GAAG,IAAI,CAACzI,iBAAiB,CAACkD,OAAO,CAAC;QAC9C,IAAI,CAAClG,SAAS,CAACqM,KAAK,CAAC,CAACJ,YAAY,GAAGR,MAAM,CAACC,KAAK;QACjD,IAAI,CAAC1L,SAAS,CAACqM,KAAK,CAAC,CAACH,gBAAgB,GAAGT,MAAM,CAAC9L,SAAS;QACzD,IAAI,CAACK,SAAS,CAACqM,KAAK,CAAC,CAACF,qBAAqB,GAAGV,MAAM,CAACC,KAAK,GAAG,CAAC,GAC1DC,IAAI,CAACC,KAAK,CAAEH,MAAM,CAAC9L,SAAS,GAAG8L,MAAM,CAACC,KAAK,GAAI,GAAG,CAAC,GACnD,CAAC;QACL;MACF;MAEA;MACA,IAAI,IAAI,CAAClL,UAAU,CAAC0F,OAAO,CAAC,EAAE;QAC5B,MAAMoG,YAAY,GAAG,IAAI,CAAC9L,UAAU,CAAC0F,OAAO,CAAC;QAC7C,MAAMoF,WAAW,GAAGgB,YAAY,CAAChO,MAAM;QACvC,MAAMiN,eAAe,GAAGe,YAAY,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7M,SAAS,CAAC,CAACrB,MAAM;QAEpE;QACA,IAAI,CAAC0E,iBAAiB,CAACkD,OAAO,CAAC,GAAG;UAChCwF,KAAK,EAAEJ,WAAW;UAClB3L,SAAS,EAAE4L;SACZ;QAED;QACA,IAAI,CAACvL,SAAS,CAACqM,KAAK,CAAC,CAACJ,YAAY,GAAGX,WAAW;QAChD,IAAI,CAACtL,SAAS,CAACqM,KAAK,CAAC,CAACH,gBAAgB,GAAGX,eAAe;QACxD,IAAI,CAACvL,SAAS,CAACqM,KAAK,CAAC,CAACF,qBAAqB,GAAGb,WAAW,GAAG,CAAC,GACzDK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;QACL;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC1G,eAAe,EAAE;EACxB;EAEA;EACQsE,mBAAmBA,CAAC9I,MAAe,EAAEmG,IAAU;IACrD,MAAMkG,OAAO,GAAG,IAAIjP,IAAI,CAAC+I,IAAI,CAAC;IAC9B,MAAMmG,SAAS,GAAGD,OAAO,CAAC7B,MAAM,EAAE,CAAC,CAAC;IACpC;IACA,MAAM+B,eAAe,GAAGD,SAAS,KAAK,CAAC,GAAG,CAAC,GAAGA,SAAS,GAAG,CAAC,CAAC,CAAC;IAC7D,MAAME,UAAU,GAAGH,OAAO,CAACzB,OAAO,EAAE,CAAC,CAAC;IAEtCvH,OAAO,CAACC,GAAG,CAAC,wCAAwC,IAAI,CAACoB,UAAU,CAACyB,IAAI,CAAC,kBAAkBmG,SAAS,aAAaC,eAAe,oBAAoBC,UAAU,EAAE,CAAC;IAEjK,MAAM3D,cAAc,GAAG7I,MAAM,CAACmM,MAAM,CAAC7D,KAAK,IAAG;MAC3CjF,OAAO,CAACC,GAAG,CAAC,6BAA6BgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,YAAYoJ,KAAK,CAACmE,UAAU,aAAanE,KAAK,CAACoE,WAAW,wBAAwBpE,KAAK,CAACqE,iBAAiB,yBAAyBrE,KAAK,CAACsE,kBAAkB,EAAE,CAAC;MAE7N,IAAI,CAACtE,KAAK,CAACuE,MAAM,EAAE;QACjBxJ,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,gCAAgC,CAAC;QACxF,OAAO,KAAK;MACd;MAEA;MACA,IAAIoJ,KAAK,CAACa,UAAU,EAAE;QACpB,MAAM2D,WAAW,GAAG,IAAI1P,IAAI,CAACkL,KAAK,CAACa,UAAU,CAAC;QAC9C2D,WAAW,CAACzP,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChCgP,OAAO,CAAChP,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE5B;QACA,IAAIgP,OAAO,GAAGS,WAAW,EAAE;UACzB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAIxE,KAAK,CAACoE,WAAW,KAAK,KAAK,EAAE;QAC/B,OAAO,IAAI;MACb;MAEA;MACA,IAAIpE,KAAK,CAACoE,WAAW,KAAK,MAAM,EAAE;QAChC,IAAI,CAACpE,KAAK,CAACqE,iBAAiB,EAAE;UAC5BtJ,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,yDAAyD,CAAC;UACjH,OAAO,IAAI,CAAC,CAAC;QACf;QAEA;QACA,IAAI6N,QAAQ,GAAU,EAAE;QACxB,IAAI,OAAOzE,KAAK,CAACqE,iBAAiB,KAAK,QAAQ,EAAE;UAC/CI,QAAQ,GAAGzE,KAAK,CAACqE,iBAAiB,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC7Q,GAAG,CAACsP,GAAG,IAAIA,GAAG,CAACwB,IAAI,EAAE,CAAC;UACpE5J,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,sCAAsCoJ,KAAK,CAACqE,iBAAiB,cAAc,EAAEI,QAAQ,CAAC;QAC/I,CAAC,MAAM,IAAI/K,KAAK,CAACkL,OAAO,CAAC5E,KAAK,CAACqE,iBAAiB,CAAC,EAAE;UACjDI,QAAQ,GAAGzE,KAAK,CAACqE,iBAAiB;UAClCtJ,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,mCAAmC,EAAE6N,QAAQ,CAAC;QACvG;QAEA;QACA;QACA,MAAMI,YAAY,GAAG,IAAI,CAACC,eAAe,CAACb,eAAe,CAAC;QAC1D,MAAMc,WAAW,GAAG,IAAI,CAACC,cAAc,CAACf,eAAe,CAAC;QAExDlJ,OAAO,CAACC,GAAG,CAAC,8BAA8B+J,WAAW,KAAKF,YAAY,KAAKZ,eAAe,oBAAoB,EAAEQ,QAAQ,CAAC;QAEzH,MAAMQ,UAAU,GAAGR,QAAQ,CAACS,QAAQ,CAACjB,eAAe,CAAC,IACnDQ,QAAQ,CAACS,QAAQ,CAACjB,eAAe,CAAC3E,QAAQ,EAAE,CAAC,IAC7CmF,QAAQ,CAACS,QAAQ,CAACL,YAAY,CAAC,IAC/BJ,QAAQ,CAACS,QAAQ,CAACH,WAAW,CAAC;QAEhChK,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,wBAAwBmO,WAAW,KAAKE,UAAU,EAAE,CAAC;QAE5G,OAAOA,UAAU;MACnB;MAEA;MACA,IAAIjF,KAAK,CAACoE,WAAW,KAAK,OAAO,EAAE;QACjC,IAAI,CAACpE,KAAK,CAACsE,kBAAkB,EAAE,OAAO,IAAI,CAAC,CAAC;QAE5C;QACA,IAAIG,QAAQ,GAAU,EAAE;QACxB,IAAI,OAAOzE,KAAK,CAACsE,kBAAkB,KAAK,QAAQ,EAAE;UAChDG,QAAQ,GAAGzE,KAAK,CAACsE,kBAAkB,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC7Q,GAAG,CAACsP,GAAG,IAAIjI,QAAQ,CAACiI,GAAG,CAACwB,IAAI,EAAE,CAAC,CAAC;QACjF,CAAC,MAAM,IAAIjL,KAAK,CAACkL,OAAO,CAAC5E,KAAK,CAACsE,kBAAkB,CAAC,EAAE;UAClDG,QAAQ,GAAGzE,KAAK,CAACsE,kBAAkB;QACrC;QAEA;QACA,OAAOG,QAAQ,CAACS,QAAQ,CAAChB,UAAU,CAAC,IAClCO,QAAQ,CAACS,QAAQ,CAAChB,UAAU,CAAC5E,QAAQ,EAAE,CAAC;MAC5C;MAEA,OAAO,KAAK;IACd,CAAC,CAAC;IAEFvE,OAAO,CAACC,GAAG,CAAC,uBAAuBtD,MAAM,CAAC9B,MAAM,cAAc2K,cAAc,CAAC3K,MAAM,aAAa,IAAI,CAACwG,UAAU,CAACyB,IAAI,CAAC,EAAE,CAAC;IAExH,OAAO0C,cAAc;EACvB;EAEA4E,UAAUA,CAACpB,OAAY;IACrB,IAAI,IAAI,CAAChM,aAAa,EAAE;MACtB;IACF;IAEA,MAAMqN,QAAQ,GAAGrB,OAAO,CAAClG,IAAI;IAE7B,IAAI,CAAC9F,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACsN,gBAAgB,GAAGtB,OAAO;IAE/B,MAAMlG,IAAI,GAAG,IAAI/I,IAAI,CAACsQ,QAAQ,CAAC;IAC/B,IAAI,CAACpQ,YAAY,GAAG6I,IAAI;IAExB,IAAI,CAACvG,SAAS,CAACmF,OAAO,CAACiH,QAAQ,IAAG;MAChCA,QAAQ,CAACL,WAAW,GAAGK,QAAQ,CAAC7F,IAAI,KAAKuH,QAAQ;IACnD,CAAC,CAAC;IAEF,MAAME,aAAa,GAAG,IAAI,CAAClJ,UAAU,CAACyB,IAAI,CAAC;IAE3C,IAAI,CAAC3D,MAAM,CAAC0D,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BjD,WAAW,EAAE;QACXkD,IAAI,EAAEyH,aAAa;QACnBxH,WAAW,EAAE,IAAI,CAACrG,UAAU,KAAK,CAAC,GAAG,IAAI,CAACA,UAAU,GAAG;OACxD;MACDsG,UAAU,EAAE;KACb,CAAC;IAEF,IAAI,CAAC1C,gBAAgB,EAAE;IAEvBY,UAAU,CAAC,MAAK;MACd,IAAI,CAACX,QAAQ,EAAE;MACf,IAAI,CAACvD,aAAa,GAAG,KAAK;IAC5B,CAAC,EAAE,EAAE,CAAC;EACR;EAKAwN,UAAUA,CAACC,SAAiB;IAC1B;IACA,IAAI,IAAI,CAACjL,cAAc,EAAE;MACvB;IACF;IAEA,IAAI,CAACA,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAAC9C,UAAU,IAAI+N,SAAS;IAE5B;IACA,IAAI,CAACpK,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACc,eAAe,EAAE;IAEtB;IACA,MAAMrB,SAAS,GAAG,IAAI,CAACuB,UAAU,CAAC,IAAI,CAACpH,YAAY,CAAC;IACpD,IAAI,CAACkF,MAAM,CAAC0D,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;MAC/BjD,WAAW,EAAE;QACXkD,IAAI,EAAEhD,SAAS;QACfiD,WAAW,EAAE,IAAI,CAACrG;OACnB;MACDsG,UAAU,EAAE;KACb,CAAC;IAEF;IACA9B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC1B,cAAc,GAAG,KAAK;IAC7B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACQ2B,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC7G,MAAM,EAAE;IAElB;IACA,IAAI,CAAC0E,YAAY,CAAC+F,SAAS,CAAC,IAAI,CAACzK,MAAO,CAAC,CAACG,IAAI,CAC5CxB,IAAI,CAAC,CAAC,CAAC,CACR,CAACyB,SAAS,CAACgQ,SAAS,IAAG;MACtB;MACA,MAAMC,eAAe,GAAG,IAAI,CAACpO,SAAS,CACnCuM,MAAM,CAACH,QAAQ,IAAI,CAACA,QAAQ,CAACJ,SAAS,CAAC,CACvCzP,GAAG,CAAC6P,QAAQ,IAAG;QACd,MAAM7F,IAAI,GAAG,IAAI/I,IAAI,CAAC4O,QAAQ,CAAC7F,IAAI,CAAC;QACpC,MAAML,OAAO,GAAG,IAAI,CAACpB,UAAU,CAACyB,IAAI,CAAC;QAErC;QACA,IAAI,IAAI,CAACvD,iBAAiB,CAACkD,OAAO,CAAC,EAAE;UACnC,OAAO1J,EAAE,CAAC;YACR+J,IAAI,EAAE6F,QAAQ,CAAC7F,IAAI;YACnBqD,QAAQ,EAAE,IAAI,CAAC5G,iBAAiB,CAACkD,OAAO;WACzC,CAAC;QACJ;QAEA;QACA,MAAMmI,YAAY,GAAG,IAAI,CAACnF,mBAAmB,CAACiF,SAAS,EAAE5H,IAAI,CAAC;QAE9D;QACA,IAAI8H,YAAY,CAAC/P,MAAM,KAAK,CAAC,EAAE;UAC7B,MAAMgQ,aAAa,GAAG;YAAE5C,KAAK,EAAE,CAAC;YAAE/L,SAAS,EAAE;UAAC,CAAE;UAChD,IAAI,CAACqD,iBAAiB,CAACkD,OAAO,CAAC,GAAGoI,aAAa;UAC/C,OAAO9R,EAAE,CAAC;YACR+J,IAAI,EAAE6F,QAAQ,CAAC7F,IAAI;YACnBqD,QAAQ,EAAE0E;WACX,CAAC;QACJ;QAEA;QACA,OAAO,IAAI,CAAC7L,YAAY,CAACgH,uBAAuB,CAAC,IAAI,CAAC1L,MAAO,EAAEwI,IAAI,CAAC,CAACrI,IAAI,CACvExB,IAAI,CAAC,CAAC,CAAC,EACPH,GAAG,CAACgS,YAAY,IAAG;UACjB;UACA,MAAMC,QAAQ,GAAGH,YAAY,CAAC9R,GAAG,CAACiQ,CAAC,IAAIA,CAAC,CAACpN,EAAE,CAAC;UAC5C,MAAMqP,gBAAgB,GAAGF,YAAY,CAAChC,MAAM,CAACmC,CAAC,IAAIF,QAAQ,CAACZ,QAAQ,CAACc,CAAC,CAAC7E,QAAQ,CAAC,CAAC;UAChF,MAAM0B,eAAe,GAAGkD,gBAAgB,CAAClC,MAAM,CAACmC,CAAC,IAAIA,CAAC,CAAC/O,SAAS,CAAC,CAACrB,MAAM;UACxE,MAAMgN,WAAW,GAAG+C,YAAY,CAAC/P,MAAM;UAEvC;UACA,MAAMsL,QAAQ,GAAG;YACf8B,KAAK,EAAEJ,WAAW;YAClB3L,SAAS,EAAE4L;WACZ;UAED;UACA,IAAI,CAACvI,iBAAiB,CAACkD,OAAO,CAAC,GAAG0D,QAAQ;UAE1C,OAAO;YACLrD,IAAI,EAAE6F,QAAQ,CAAC7F,IAAI;YACnBqD;WACD;QACH,CAAC,CAAC,CACH;MACH,CAAC,CAAC;MAEJ;MACAtN,QAAQ,CAAC8R,eAAe,CAAC,CAACjQ,SAAS,CAACwQ,OAAO,IAAG;QAC5C;QACAA,OAAO,CAACxJ,OAAO,CAAC+E,MAAM,IAAG;UACvB,MAAMmC,KAAK,GAAG,IAAI,CAACrM,SAAS,CAAC4O,SAAS,CAACC,EAAE,IAAIA,EAAE,CAACtI,IAAI,KAAK2D,MAAM,CAAC3D,IAAI,CAAC;UACrE,IAAI8F,KAAK,IAAI,CAAC,EAAE;YACd,IAAI,CAACrM,SAAS,CAACqM,KAAK,CAAC,CAACJ,YAAY,GAAG/B,MAAM,CAACN,QAAQ,CAAC8B,KAAK;YAC1D,IAAI,CAAC1L,SAAS,CAACqM,KAAK,CAAC,CAACH,gBAAgB,GAAGhC,MAAM,CAACN,QAAQ,CAACjK,SAAS;YAClE,IAAI,CAACK,SAAS,CAACqM,KAAK,CAAC,CAACF,qBAAqB,GAAGjC,MAAM,CAACN,QAAQ,CAAC8B,KAAK,GAAG,CAAC,GACnEC,IAAI,CAACC,KAAK,CAAE1B,MAAM,CAACN,QAAQ,CAACjK,SAAS,GAAGuK,MAAM,CAACN,QAAQ,CAAC8B,KAAK,GAAI,GAAG,CAAC,GACrE,CAAC;UACP;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA3H,gBAAgBA,CAAA;IACd,MAAMxG,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,IAAI,IAAI,CAAC2N,SAAS,CAAC,IAAI,CAACzN,YAAY,EAAEH,KAAK,CAAC,EAAE;MAC5C,IAAI,CAAC2C,UAAU,GAAG,OAAO;IAC3B,CAAC,MAAM,IAAI,IAAI,CAACiL,SAAS,CAAC,IAAI,CAACzN,YAAY,EAAE,IAAIF,IAAI,CAACD,KAAK,CAACwN,OAAO,CAACxN,KAAK,CAACyN,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1F,IAAI,CAAC9K,UAAU,GAAG,WAAW;IAC/B,CAAC,MAAM,IAAI,IAAI,CAACiL,SAAS,CAAC,IAAI,CAACzN,YAAY,EAAE,IAAIF,IAAI,CAACD,KAAK,CAACwN,OAAO,CAACxN,KAAK,CAACyN,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1F,IAAI,CAAC9K,UAAU,GAAG,UAAU;IAC9B,CAAC,MAAM;MACL;MACA,IAAI,CAACA,UAAU,GAAG,IAAI,CAACxC,YAAY,CAACoR,kBAAkB,CAAC,OAAO,EAAE;QAC9DC,OAAO,EAAE,OAAO;QAChBlD,GAAG,EAAE,SAAS;QACdmD,KAAK,EAAE;OACR,CAAC;IACJ;EACF;EAKMC,WAAWA,CAACvG,KAAmB;IAAA,IAAAwG,MAAA;IAAA,OAAAxJ,iBAAA;MACnC,IAAI,CAACwJ,MAAI,CAACnR,MAAM,IAAI,CAAC2K,KAAK,CAACtJ,EAAE,EAAE;MAE/B;MACA,IAAI8P,MAAI,CAAChM,gBAAgB,CAACwF,KAAK,CAACtJ,EAAE,CAAC,EAAE;QACnCqE,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,qDAAqD,CAAC;QAC7G;MACF;MAEA;MACA4P,MAAI,CAAChM,gBAAgB,CAACwF,KAAK,CAACtJ,EAAE,CAAC,GAAG,IAAI;MACtCqE,OAAO,CAACC,GAAG,CAAC,wCAAwCgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,GAAG,CAAC;MAE/E,IAAI;QACF;QACA;QACA;QAEA;QACAmE,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,qCAAqCoJ,KAAK,CAAC9I,cAAc,EAAE,CAAC;QAEnH;QACA;QACA,OAAOsP,MAAI,CAAChM,gBAAgB,CAACwF,KAAK,CAACtJ,EAAE,CAAC;QACtC;MACF,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdwE,OAAO,CAACxE,KAAK,CAAC,uCAAuCyJ,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,IAAI,EAAEL,KAAK,CAAC;MAC1F,CAAC,SAAS;QACR;QACA,OAAOiQ,MAAI,CAAChM,gBAAgB,CAACwF,KAAK,CAACtJ,EAAE,CAAC;QACtCqE,OAAO,CAACC,GAAG,CAAC,wCAAwCgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,GAAG,CAAC;MACjF;IAAC;EACH;EAKM6P,mBAAmBA,CAACzG,KAAmB,EAAE0G,KAAW;IAAA,IAAAC,MAAA;IAAA,OAAA3J,iBAAA;MACxD,IAAI,CAAC2J,MAAI,CAACtR,MAAM,IAAI,CAAC2K,KAAK,CAACtJ,EAAE,EAAE;MAE/B;MACA,IAAIiQ,MAAI,CAAClM,gBAAgB,CAACuF,KAAK,CAACtJ,EAAE,CAAC,EAAE;QACnC;MACF;MAEA;MACAiQ,MAAI,CAAClM,gBAAgB,CAACuF,KAAK,CAACtJ,EAAE,CAAC,GAAG,IAAI;MAEtC,IAAI;QACF;QACA,MAAMkQ,kBAAkB,GAAG5G,KAAK,CAAC/I,SAAS;QAC1C8D,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,+BAA+BgQ,kBAAkB,EAAE,CAAC;QAE3G;QACA,IAAIF,KAAK,EAAE;UACT;UACA,MAAMvI,MAAM,GAAGuI,KAAK,CAACG,MAAM,KAAKH,KAAK,CAACI,MAAM,GAAGJ,KAAK,CAACI,MAAM,CAACxN,KAAK,GAAG,IAAI,CAAC;UACzEqN,MAAI,CAACI,sBAAsB,CAAC5I,MAAM,CAAC;UAEnC;UACA,MAAME,aAAa,GAAGF,MAAM,YAAYa,WAAW,GAAGb,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI;UACjG,IAAID,aAAa,IAAIA,aAAa,KAAK2B,KAAK,CAACtJ,EAAE,EAAE;YAC/C,OAAOiQ,MAAI,CAAClM,gBAAgB,CAACuF,KAAK,CAACtJ,EAAE,CAAC;YACtC;UACF;UAEA;UACA,IAAI6H,WAAW,GAAG,CAAC;UACnB,IAAImI,KAAK,CAACI,MAAM,IAAIJ,KAAK,CAACI,MAAM,CAACxN,KAAK,KAAK2B,SAAS,EAAE;YACpD;YACAsD,WAAW,GAAGmI,KAAK,CAACI,MAAM,CAACxN,KAAK;UAClC,CAAC,MAAM,IAAI6E,MAAM,YAAYC,gBAAgB,EAAE;YAC7C;YACAG,WAAW,GAAGrD,QAAQ,CAACiD,MAAM,CAAC7E,KAAK,CAAC;UACtC,CAAC,MAAM,IAAI6E,MAAM,YAAYa,WAAW,IAAIb,MAAM,CAACc,OAAO,KAAK,WAAW,EAAE;YAC1E;YACA,MAAMC,SAAS,GAAGf,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;YACrDC,WAAW,GAAGrD,QAAQ,CAACgE,SAAS,CAAC;UACnC;UAEA;UACAc,KAAK,CAAC9I,cAAc,GAAGqH,WAAW;UAElC;UACA;UACA,IAAIyB,KAAK,CAACmE,UAAU,KAAK,OAAO,EAAE;YAChC;YACAnE,KAAK,CAAC/I,SAAS,GAAGsH,WAAW,IAAIyB,KAAK,CAAClJ,UAAU;UACnD,CAAC,MAAM;YAAE;YACP;YACAkJ,KAAK,CAAC/I,SAAS,GAAGsH,WAAW,GAAGyB,KAAK,CAAClJ,UAAU;UAClD;UAEAiE,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,0BAA0BoJ,KAAK,CAAC/I,SAAS,EAAE,CAAC;QACrG;QAEA;QACA,MAAM+P,SAAS,GAAG;UAAE,GAAGhH;QAAK,CAAE;QAE9B;QACA,MAAMwB,MAAM,SAASmF,MAAI,CAAC5M,YAAY,CAACkN,qBAAqB,CAC1DN,MAAI,CAACtR,MAAM,EACX2K,KAAK,CAACtJ,EAAE,EACRiQ,MAAI,CAAC3R,YAAY,EACjBgL,KAAK,CAAC9I,cAAc,EACpB8P,SAAS,CACV;QAED;QACAhH,KAAK,CAAC/I,SAAS,GAAGuK,MAAM,CAACvK,SAAS;QAClC+I,KAAK,CAAC9I,cAAc,GAAGsK,MAAM,CAACtK,cAAc;QAE5C;QACA,MAAMrC,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1B,MAAMC,YAAY,GAAG,IAAIF,IAAI,CAAC6R,MAAI,CAAC3R,YAAY,CAAC;QAChDA,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC,MAAME,eAAe,GAAGD,YAAY,CAACE,OAAO,EAAE,KAAKL,KAAK,CAACK,OAAO,EAAE;QAElE;QACA,IAAID,eAAe,EAAE;UACnB;UACA;UAEA;UACA,IAAI+B,MAAM,GAAGwK,MAAM,CAACxK,MAAM;UAE1B;UACA,MAAMkQ,cAAc,GAAGlH,KAAK,CAAC/I,SAAS;UAEtC8D,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,4BAA4BgQ,kBAAkB,SAASM,cAAc,EAAE,CAAC;UAE/H;UACA,IAAIN,kBAAkB,KAAKM,cAAc,EAAE;YACzC,IAAIA,cAAc,EAAE;cAClB;cACAlQ,MAAM,EAAE;cACR+D,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,8DAA8DI,MAAM,EAAE,CAAC;YAChI,CAAC,MAAM;cACL;cACAA,MAAM,GAAGiM,IAAI,CAACtE,GAAG,CAAC,CAAC,EAAE3H,MAAM,GAAG,CAAC,CAAC;cAChC+D,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,8DAA8DI,MAAM,EAAE,CAAC;YAChI;YAEA;YACA2P,MAAI,CAAC5M,YAAY,CAACwH,iBAAiB,CAACvB,KAAK,CAACtJ,EAAG,EAAEM,MAAM,CAAC,CAACvB,SAAS,CAAC;cAC/DC,IAAI,EAAEA,CAAA,KAAK;gBACTqF,OAAO,CAACC,GAAG,CAAC,oDAAoDgF,KAAK,CAACtJ,EAAE,OAAOM,MAAM,EAAE,CAAC;gBAExF;gBACA,MAAMwG,OAAO,GAAGmJ,MAAI,CAACvK,UAAU,CAACuK,MAAI,CAAC3R,YAAY,CAAC;gBAClD,IAAI2R,MAAI,CAAC7O,UAAU,CAAC0F,OAAO,CAAC,EAAE;kBAC5B,MAAM2J,gBAAgB,GAAGR,MAAI,CAAC7O,UAAU,CAAC0F,OAAO,CAAC,CAAC0I,SAAS,CAACpC,CAAC,IAAIA,CAAC,CAACpN,EAAE,KAAKsJ,KAAK,CAACtJ,EAAE,CAAC;kBACnF,IAAIyQ,gBAAgB,IAAI,CAAC,EAAE;oBACzBR,MAAI,CAAC7O,UAAU,CAAC0F,OAAO,CAAC,CAAC2J,gBAAgB,CAAC,CAACnQ,MAAM,GAAGA,MAAM;kBAC5D;gBACF;cACF,CAAC;cACDT,KAAK,EAAGA,KAAU,IAAKwE,OAAO,CAACxE,KAAK,CAAC,8CAA8CyJ,KAAK,CAACtJ,EAAE,GAAG,EAAEH,KAAK;aACtG,CAAC;UACJ,CAAC,MAAM;YACLwE,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,KAAKsJ,KAAK,CAACpJ,IAAI,yDAAyDI,MAAM,EAAE,CAAC;UAC3H;QACF,CAAC,MAAM;UACL;UACA+D,OAAO,CAACC,GAAG,CAAC,6CAA6C2L,MAAI,CAACvK,UAAU,CAACuK,MAAI,CAAC3R,YAAY,CAAC,mCAAmC,CAAC;UAE/H;UACA2R,MAAI,CAAC5M,YAAY,CAACqN,QAAQ,CAACpH,KAAK,CAACtJ,EAAG,CAAC,CAACjB,SAAS,CAACe,YAAY,IAAG;YAC7D,IAAI,CAACA,YAAY,EAAE;cACjBuE,OAAO,CAACxE,KAAK,CAAC,8CAA8CyJ,KAAK,CAACtJ,EAAE,EAAE,CAAC;cACvE;YACF;YAEA;YACAiQ,MAAI,CAACvM,gBAAgB,CAACiN,eAAe,CAACV,MAAI,CAACtR,MAAO,EAAE2K,KAAK,CAACtJ,EAAG,CAAC,CAC3DL,IAAI,CAACiL,gBAAgB,IAAG;cACvBvG,OAAO,CAACC,GAAG,CAAC,4CAA4CgF,KAAK,CAACtJ,EAAE,eAAe4K,gBAAgB,EAAE,CAAC;cAElG;cACAqF,MAAI,CAAC5M,YAAY,CAACwH,iBAAiB,CAACvB,KAAK,CAACtJ,EAAG,EAAE4K,gBAAgB,CAAC,CAAC7L,SAAS,CAAC;gBACzEC,IAAI,EAAEA,CAAA,KAAK;kBACTqF,OAAO,CAACC,GAAG,CAAC,oDAAoDgF,KAAK,CAACtJ,EAAE,OAAO4K,gBAAgB,EAAE,CAAC;kBAElG;kBACA,MAAMgG,WAAW,GAAGX,MAAI,CAACvK,UAAU,CAACvH,KAAK,CAAC;kBAC1CkG,OAAO,CAACC,GAAG,CAAC,gGAAgG,CAAC;kBAC7G,OAAO2L,MAAI,CAAC7O,UAAU,CAACwP,WAAW,CAAC;kBAEnC;kBACA,MAAMC,UAAU,GAAGZ,MAAI,CAACrP,SAAS,CAAC4O,SAAS,CAACC,EAAE,IAAIA,EAAE,CAACtI,IAAI,KAAKyJ,WAAW,CAAC;kBAC1E,IAAIC,UAAU,IAAI,CAAC,EAAE;oBACnB,OAAOZ,MAAI,CAACrM,iBAAiB,CAACgN,WAAW,CAAC;oBAC1CX,MAAI,CAACa,yBAAyB,CAACF,WAAW,CAAC;kBAC7C;gBACF,CAAC;gBACD/Q,KAAK,EAAGA,KAAU,IAAKwE,OAAO,CAACxE,KAAK,CAAC,8CAA8CyJ,KAAK,CAACtJ,EAAE,GAAG,EAAEH,KAAK;eACtG,CAAC;YACJ,CAAC,CAAC,CACDmL,KAAK,CAACnL,KAAK,IAAG;cACbwE,OAAO,CAACxE,KAAK,CAAC,iDAAiDyJ,KAAK,CAACtJ,EAAE,GAAG,EAAEH,KAAK,CAAC;YACpF,CAAC,CAAC;UACN,CAAC,CAAC;QACJ;QAEA;QACAoQ,MAAI,CAACc,aAAa,CAACzH,KAAK,CAAC;QAEzB;QACA,MAAMxC,OAAO,GAAGmJ,MAAI,CAACvK,UAAU,CAACuK,MAAI,CAAC3R,YAAY,CAAC;QAClD,IAAI2R,MAAI,CAAC7O,UAAU,CAAC0F,OAAO,CAAC,EAAE;UAC5B;UACA,MAAM2J,gBAAgB,GAAGR,MAAI,CAAC7O,UAAU,CAAC0F,OAAO,CAAC,CAAC0I,SAAS,CAACpC,CAAC,IAAIA,CAAC,CAACpN,EAAE,KAAKsJ,KAAK,CAACtJ,EAAE,CAAC;UACnF,IAAIyQ,gBAAgB,IAAI,CAAC,EAAE;YACzBR,MAAI,CAAC7O,UAAU,CAAC0F,OAAO,CAAC,CAAC2J,gBAAgB,CAAC,GAAG;cAAE,GAAGnH;YAAK,CAAE;UAC3D;QACF;QAEA;QACA,OAAO2G,MAAI,CAACrM,iBAAiB,CAACkD,OAAO,CAAC;QAEtC;QACAmJ,MAAI,CAACa,yBAAyB,CAAChK,OAAO,CAAC;QAEvC;QACAmJ,MAAI,CAAC5E,uBAAuB,CAAC4E,MAAI,CAACjP,MAAM,CAAC;MAC3C,CAAC,CAAC,OAAOnB,KAAK,EAAE;QACdwE,OAAO,CAACxE,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACnE,CAAC,SAAS;QACR;QACA,OAAOoQ,MAAI,CAAClM,gBAAgB,CAACuF,KAAK,CAACtJ,EAAE,CAAC;MACxC;IAAC;EACH;EAEA;EACQ8Q,yBAAyBA,CAAChK,OAAe;IAC/C;IACA,MAAMmG,KAAK,GAAG,IAAI,CAACrM,SAAS,CAAC4O,SAAS,CAACC,EAAE,IAAIA,EAAE,CAACtI,IAAI,KAAKL,OAAO,CAAC;IACjE,IAAImG,KAAK,GAAG,CAAC,EAAE;IAEf;IACA,IAAI,IAAI,CAAC7L,UAAU,CAAC0F,OAAO,CAAC,EAAE;MAC5B,MAAMoG,YAAY,GAAG,IAAI,CAAC9L,UAAU,CAAC0F,OAAO,CAAC;MAC7C,MAAMoF,WAAW,GAAGgB,YAAY,CAAChO,MAAM;MACvC,MAAMiN,eAAe,GAAGe,YAAY,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7M,SAAS,CAAC,CAACrB,MAAM;MAEpE;MACA,IAAI,CAAC0E,iBAAiB,CAACkD,OAAO,CAAC,GAAG;QAChCwF,KAAK,EAAEJ,WAAW;QAClB3L,SAAS,EAAE4L;OACZ;MAED;MACA,IAAI,CAACvL,SAAS,CAACqM,KAAK,CAAC,CAACJ,YAAY,GAAGX,WAAW;MAChD,IAAI,CAACtL,SAAS,CAACqM,KAAK,CAAC,CAACH,gBAAgB,GAAGX,eAAe;MACxD,IAAI,CAACvL,SAAS,CAACqM,KAAK,CAAC,CAACF,qBAAqB,GAAGb,WAAW,GAAG,CAAC,GACzDK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;MAEL;IACF;IAEA;IACA,IAAI,IAAI,CAACvN,MAAM,EAAE;MACf,MAAMwI,IAAI,GAAG,IAAI/I,IAAI,CAAC0I,OAAO,CAAC;MAE9B,IAAI,CAACzD,YAAY,CAACgH,uBAAuB,CAAC,IAAI,CAAC1L,MAAM,EAAEwI,IAAI,CAAC,CAACrI,IAAI,CAC/DxB,IAAI,CAAC,CAAC,CAAC,CACR,CAACyB,SAAS,CAACoQ,YAAY,IAAG;QACzB,IAAI,CAAC9L,YAAY,CAAC+F,SAAS,CAAC,IAAI,CAACzK,MAAO,CAAC,CAACG,IAAI,CAC5CxB,IAAI,CAAC,CAAC,CAAC,CACR,CAACyB,SAAS,CAACiC,MAAM,IAAG;UACnB;UACA,MAAMiO,YAAY,GAAG,IAAI,CAACnF,mBAAmB,CAAC9I,MAAM,EAAEmG,IAAI,CAAC;UAE3D;UACA,MAAMiI,QAAQ,GAAGH,YAAY,CAAC9R,GAAG,CAACiQ,CAAC,IAAIA,CAAC,CAACpN,EAAE,CAAC;UAC5C,MAAMqP,gBAAgB,GAAGF,YAAY,CAAChC,MAAM,CAACmC,CAAC,IAAIF,QAAQ,CAACZ,QAAQ,CAACc,CAAC,CAAC7E,QAAQ,CAAC,CAAC;UAChF,MAAM0B,eAAe,GAAGkD,gBAAgB,CAAClC,MAAM,CAACmC,CAAC,IAAIA,CAAC,CAAC/O,SAAS,CAAC,CAACrB,MAAM;UACxE,MAAMgN,WAAW,GAAG+C,YAAY,CAAC/P,MAAM;UAEvC;UACA,IAAI,CAAC0E,iBAAiB,CAACkD,OAAO,CAAC,GAAG;YAChCwF,KAAK,EAAEJ,WAAW;YAClB3L,SAAS,EAAE4L;WACZ;UAED;UACA,IAAI,CAACvL,SAAS,CAACqM,KAAK,CAAC,CAACJ,YAAY,GAAGX,WAAW;UAChD,IAAI,CAACtL,SAAS,CAACqM,KAAK,CAAC,CAACH,gBAAgB,GAAGX,eAAe;UACxD,IAAI,CAACvL,SAAS,CAACqM,KAAK,CAAC,CAACF,qBAAqB,GAAGb,WAAW,GAAG,CAAC,GACzDK,IAAI,CAACC,KAAK,CAAEL,eAAe,GAAGD,WAAW,GAAI,GAAG,CAAC,GACjD,CAAC;QACP,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;EAEA;EACQ6E,aAAaA,CAACzH,KAAmB;IACvC;IACA,MAAM0H,YAAY,GAAGzJ,QAAQ,CAAC0J,aAAa,CAAC,mBAAmB3H,KAAK,CAACtJ,EAAE,IAAI,CAAC;IAC5E,IAAI,CAACgR,YAAY,EAAE;MACjB3M,OAAO,CAACxE,KAAK,CAAC,mDAAmDyJ,KAAK,CAACtJ,EAAE,EAAE,CAAC;MAC5E;IACF;IAEA;IACA,IAAIsJ,KAAK,CAAC/I,SAAS,EAAE;MACnByQ,YAAY,CAACE,SAAS,CAAC7L,GAAG,CAAC,WAAW,CAAC;IACzC,CAAC,MAAM;MACL2L,YAAY,CAACE,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;IAC5C;IAEA;IACA,MAAMC,cAAc,GAAGJ,YAAY,CAACxJ,gBAAgB,CAAC,eAAe,CAAC;IACrE,IAAI4J,cAAc,IAAIA,cAAc,CAAClS,MAAM,GAAG,CAAC,EAAE;MAC/C;MACA,MAAMf,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAMC,YAAY,GAAG,IAAIF,IAAI,CAAC,IAAI,CAACE,YAAY,CAAC;MAChDA,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,MAAME,eAAe,GAAGD,YAAY,CAACE,OAAO,EAAE,KAAKL,KAAK,CAACK,OAAO,EAAE;MAElE;MACA,IAAID,eAAe,EAAE;QACnB,MAAM8S,WAAW,GAAG/H,KAAK,CAAChJ,MAAM,IAAI,CAAC;QACrC+D,OAAO,CAACC,GAAG,CAAC,oBAAoBgF,KAAK,CAACtJ,EAAE,gBAAgBsJ,KAAK,CAAC/I,SAAS,aAAa8Q,WAAW,EAAE,CAAC;QAElG;QACAD,cAAc,CAACrL,OAAO,CAACuL,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACpE;YACCA,OAAuB,CAACnJ,KAAK,CAACsJ,OAAO,GAAG,OAAO;YAChDH,OAAO,CAACI,WAAW,GAAG,KAAKL,WAAW,GAAG;UAC3C;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAD,cAAc,CAACrL,OAAO,CAACuL,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACnEA,OAAuB,CAACnJ,KAAK,CAACsJ,OAAO,GAAG,MAAM;YAC/CH,OAAO,CAACI,WAAW,GAAG,EAAE;UAC1B;QACF,CAAC,CAAC;MACJ;IACF;IAEA;IACA,MAAMC,YAAY,GAAGX,YAAY,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACjE,IAAIU,YAAY,EAAE;MAAA,IAAAC,qBAAA;MAChB,MAAMC,UAAU,IAAAD,qBAAA,GAAGD,YAAY,CAACJ,aAAa,cAAAK,qBAAA,uBAA1BA,qBAAA,CAA4BV,SAAS,CAACM,QAAQ,CAAC,eAAe,CAAC;MAClF,MAAMM,UAAU,GAAGD,UAAU,GAAG,GAAG,GAAG,EAAE;MACxC,MAAME,cAAc,GAAGzI,KAAK,CAACjJ,SAAS,KAAK,OAAO,IAAI,CAACwR,UAAU,GAAG,IAAIvI,KAAK,CAACjJ,SAAS,EAAE,GAAG,EAAE;MAE9FsR,YAAY,CAACD,WAAW,GAAG,GAAGpI,KAAK,CAAC9I,cAAc,GAAGsR,UAAU,IAAIxI,KAAK,CAAClJ,UAAU,GAAG0R,UAAU,GAAGC,cAAc,EAAE;IACrH;IAEA1N,OAAO,CAACC,GAAG,CAAC,mCAAmCgF,KAAK,CAACtJ,EAAE,EAAE,CAAC;EAC5D;EAEA;EACAqQ,sBAAsBA,CAAC5I,MAA6C;IAClE,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA;IACA,IAAIuK,aAA0B;IAC9B,IAAInK,WAAW,GAAG,CAAC;IACnB,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIE,QAAQ,GAAG,GAAG;IAClB,IAAIL,aAAa,GAAG,EAAE;IAEtB,IAAIF,MAAM,YAAYC,gBAAgB,EAAE;MACtC;MACAsK,aAAa,GAAGvK,MAAM;MACtBE,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE;MAC1DC,WAAW,GAAGrD,QAAQ,CAACiD,MAAM,CAAC7E,KAAK,CAAC;MACpCkF,QAAQ,GAAGtD,QAAQ,CAACiD,MAAM,CAACM,GAAG,CAAC;MAC/BC,QAAQ,GAAGxD,QAAQ,CAACiD,MAAM,CAACQ,GAAG,CAAC;IACjC,CAAC,MAAM,IAAIR,MAAM,YAAYa,WAAW,IAAIb,MAAM,CAACc,OAAO,KAAK,WAAW,EAAE;MAC1E;MACAyJ,aAAa,GAAGvK,MAAM;MACtBE,aAAa,GAAGF,MAAM,CAACG,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE;MAE1D;MACA,MAAMY,SAAS,GAAGf,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;MACrD,MAAMa,OAAO,GAAGhB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG;MACjD,MAAMc,OAAO,GAAGjB,MAAM,CAACG,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;MAEnDC,WAAW,GAAGrD,QAAQ,CAACgE,SAAS,CAAC;MACjCV,QAAQ,GAAGtD,QAAQ,CAACiE,OAAO,CAAC;MAC5BT,QAAQ,GAAGxD,QAAQ,CAACkE,OAAO,CAAC;IAC9B,CAAC,MAAM;MACL;IACF;IAEA,IAAI,CAACf,aAAa,EAAE;MAClB;IACF;IAEA;IACA,MAAMO,UAAU,GAAGF,QAAQ,GAAGF,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKE,QAAQ,GAAGF,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;IAE9D;IACA,IAAIkK,aAAa,CAACzJ,OAAO,KAAK,WAAW,EAAE;MACzCyJ,aAAa,CAAC7J,KAAK,CAACQ,WAAW,CAAC,kBAAkB,EAAE,GAAGT,UAAU,GAAG,CAAC;IACvE,CAAC,MAAM;MACL;MACA8J,aAAa,CAAC7J,KAAK,CAACC,UAAU,GAC5B,iDAAiDF,UAAU,cAAcA,UAAU,kBAAkB;IACzG;IAEA;IACA8J,aAAa,CAAC3J,YAAY,CAAC,oBAAoB,EAAER,WAAW,CAACe,QAAQ,EAAE,CAAC;EAC1E;EAKA;;;;;EAKMqJ,eAAeA,CAAC9S,SAAqB;IAAA,IAAA+S,MAAA;IAAA,OAAA5L,iBAAA;MACzC,IAAI,CAAC4L,MAAI,CAACvT,MAAM,IAAI,CAACQ,SAAS,CAACa,EAAE,EAAE;MAEnC;MACA,IAAIkS,MAAI,CAAClO,oBAAoB,CAAC7E,SAAS,CAACa,EAAE,CAAC,EAAE;QAC3CqE,OAAO,CAACC,GAAG,CAAC,yBAAyBnF,SAAS,CAACa,EAAE,oDAAoD,CAAC;QACtG;MACF;MAEA;MACAkS,MAAI,CAAClO,oBAAoB,CAAC7E,SAAS,CAACa,EAAE,CAAC,GAAG,IAAI;MAC9CqE,OAAO,CAACC,GAAG,CAAC,6CAA6CnF,SAAS,CAACa,EAAE,EAAE,CAAC;MAExE,IAAI;QACF;QACA;QACAqE,OAAO,CAACC,GAAG,CAAC,wCAAwCnF,SAAS,CAACa,EAAE,EAAE,CAAC;QAEnE;QACA,MAAMmS,QAAQ,GAAGhT,SAAS,CAACqB,cAAc,KAAK,CAAC,GAAGrB,SAAS,CAACc,aAAa,CAACG,UAAU,GAAG,CAAC;QACxF,MAAMgS,iBAAiB,GAAGD,QAAQ,KAAKhT,SAAS,CAACc,aAAa,CAACG,UAAU;QAEzE;QACAjB,SAAS,CAACqB,cAAc,GAAG2R,QAAQ;QACnChT,SAAS,CAACoB,SAAS,GAAG6R,iBAAiB;QAEvC/N,OAAO,CAACC,GAAG,CAAC,iCAAiCnF,SAAS,CAACa,EAAE,aAAab,SAAS,CAACqB,cAAc,gBAAgBrB,SAAS,CAACoB,SAAS,EAAE,CAAC;QAEpI;QACA,MAAMpC,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC1B,MAAMC,YAAY,GAAG,IAAIF,IAAI,CAAC8T,MAAI,CAAC5T,YAAY,CAAC;QAChDA,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC,MAAMyN,OAAO,GAAGxN,YAAY,CAACE,OAAO,EAAE,KAAKL,KAAK,CAACK,OAAO,EAAE;QAE1D;QACA,IAAI,CAACsN,OAAO,EAAE;UACZzH,OAAO,CAACC,GAAG,CAAC,sDAAsD4N,MAAI,CAACxM,UAAU,CAACwM,MAAI,CAAC5T,YAAY,CAAC,EAAE,CAAC;UACvG,OAAO4T,MAAI,CAAClO,oBAAoB,CAAC7E,SAAS,CAACa,EAAE,CAAC;UAC9C;QACF;QAEA;QACAkS,MAAI,CAACG,iBAAiB,CAAClT,SAAS,CAAC;QAEjC,IAAI;UACF,MAAM2L,MAAM,SAASoH,MAAI,CAACtT,gBAAgB,CAAC0T,yBAAyB,CAClEnT,SAAS,CAACa,EAAE,EACZkS,MAAI,CAACvT,MAAM,EACXuT,MAAI,CAAC5T,YAAY,CAAC;WACnB;UAED+F,OAAO,CAACC,GAAG,CAAC,8CAA8CnF,SAAS,CAACa,EAAE,EAAE,CAAC;UACzEqE,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwG,MAAM,CAAC;UAEjD;UACA3L,SAAS,CAACoB,SAAS,GAAGuK,MAAM,CAACvK,SAAS;UACtCpB,SAAS,CAACqB,cAAc,GAAGsK,MAAM,CAACtK,cAAc;UAChDrB,SAAS,CAACmB,MAAM,GAAGwK,MAAM,CAACxK,MAAM;UAEhC;UACA4R,MAAI,CAACG,iBAAiB,CAAClT,SAAS,CAAC;UAEjC;UACA;UACA,MAAM2H,OAAO,GAAGoL,MAAI,CAACxM,UAAU,CAACwM,MAAI,CAAC5T,YAAY,CAAC;UAClD,OAAO4T,MAAI,CAACtO,iBAAiB,CAACkD,OAAO,CAAC;UAEtC;UACAoL,MAAI,CAACpB,yBAAyB,CAAChK,OAAO,CAAC;UAEvC;UACAoL,MAAI,CAAC7G,uBAAuB,CAAC6G,MAAI,CAAClR,MAAM,CAAC;UAEzC;UACA,OAAOkR,MAAI,CAAClO,oBAAoB,CAAC7E,SAAS,CAACa,EAAE,CAAC;UAC9CqE,OAAO,CAACC,GAAG,CAAC,6CAA6CnF,SAAS,CAACa,EAAE,EAAE,CAAC;QAC1E,CAAC,CAAC,OAAOH,KAAK,EAAE;UACdwE,OAAO,CAACxE,KAAK,CAAC,wCAAwCV,SAAS,CAACa,EAAE,GAAG,EAAEH,KAAK,CAAC;UAE7E;UACAV,SAAS,CAACqB,cAAc,GAAGrB,SAAS,CAACqB,cAAc,KAAK,CAAC,GAAGrB,SAAS,CAACc,aAAa,CAACG,UAAU,GAAG,CAAC;UAClGjB,SAAS,CAACoB,SAAS,GAAGpB,SAAS,CAACqB,cAAc,KAAKrB,SAAS,CAACc,aAAa,CAACG,UAAU;UACrF8R,MAAI,CAACG,iBAAiB,CAAClT,SAAS,CAAC;UAEjC,OAAO+S,MAAI,CAAClO,oBAAoB,CAAC7E,SAAS,CAACa,EAAE,CAAC;QAChD;MACF,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdwE,OAAO,CAACxE,KAAK,CAAC,2CAA2CV,SAAS,CAACa,EAAE,GAAG,EAAEH,KAAK,CAAC;QAChF,OAAOqS,MAAI,CAAClO,oBAAoB,CAAC7E,SAAS,CAACa,EAAE,CAAC;MAChD;IAAC;EACH;EAEA;EACQqS,iBAAiBA,CAAClT,SAAqB;IAC7C;IACA,MAAM6R,YAAY,GAAGzJ,QAAQ,CAAC0J,aAAa,CAAC,qCAAqC9R,SAAS,CAACa,EAAE,IAAI,CAAC;IAClG,IAAI,CAACgR,YAAY,EAAE;MACjB3M,OAAO,CAACxE,KAAK,CAAC,wDAAwDV,SAAS,CAACa,EAAE,EAAE,CAAC;MACrF;IACF;IAEA;IACA,IAAIb,SAAS,CAACoB,SAAS,EAAE;MACvByQ,YAAY,CAACE,SAAS,CAAC7L,GAAG,CAAC,WAAW,CAAC;IACzC,CAAC,MAAM;MACL2L,YAAY,CAACE,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;IAC5C;IAEA;IACA,MAAMC,cAAc,GAAGJ,YAAY,CAACxJ,gBAAgB,CAAC,eAAe,CAAC;IACrE,IAAI4J,cAAc,IAAIA,cAAc,CAAClS,MAAM,GAAG,CAAC,EAAE;MAC/C;MACA,MAAMf,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAMC,YAAY,GAAG,IAAIF,IAAI,CAAC,IAAI,CAACE,YAAY,CAAC;MAChDA,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,MAAME,eAAe,GAAGD,YAAY,CAACE,OAAO,EAAE,KAAKL,KAAK,CAACK,OAAO,EAAE;MAElE;MACA,IAAID,eAAe,EAAE;QACnB,MAAM8S,WAAW,GAAGlS,SAAS,CAACmB,MAAM,IAAI,CAAC;QACzC+D,OAAO,CAACC,GAAG,CAAC,yBAAyBnF,SAAS,CAACa,EAAE,gBAAgBb,SAAS,CAACoB,SAAS,aAAa8Q,WAAW,EAAE,CAAC;QAE/G;QACAD,cAAc,CAACrL,OAAO,CAACuL,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACpE;YACCA,OAAuB,CAACnJ,KAAK,CAACsJ,OAAO,GAAG,OAAO;YAChDH,OAAO,CAACI,WAAW,GAAG,KAAKL,WAAW,GAAG;UAC3C;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAD,cAAc,CAACrL,OAAO,CAACuL,OAAO,IAAG;UAC/B,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACC,aAAa,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YACnEA,OAAuB,CAACnJ,KAAK,CAACsJ,OAAO,GAAG,MAAM;YAC/CH,OAAO,CAACI,WAAW,GAAG,EAAE;UAC1B;QACF,CAAC,CAAC;MACJ;IACF;IAEA;IACA,MAAMC,YAAY,GAAGX,YAAY,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACjE,IAAIU,YAAY,EAAE;MAChB,MAAMY,QAAQ,GAAGpT,SAAS,CAACc,aAAa,CAACI,SAAS,KAAK,OAAO,GAAG,IAAIlB,SAAS,CAACc,aAAa,CAACI,SAAS,EAAE,GAAG,EAAE;MAC7GsR,YAAY,CAACD,WAAW,GAAG,GAAGvS,SAAS,CAACqB,cAAc,IAAIrB,SAAS,CAACc,aAAa,CAACG,UAAU,GAAGmS,QAAQ,EAAE;IAC3G;IAEA;IACAhN,UAAU,CAAC,MAAK;MACd,IAAIyL,YAAY,CAACO,aAAa,EAAE;QAC9B,MAAME,OAAO,GAAGT,YAAY,CAACO,aAAa,CAACpJ,KAAK,CAACsJ,OAAO;QACxDT,YAAY,CAACO,aAAa,CAACpJ,KAAK,CAACsJ,OAAO,GAAG,MAAM;QACjD;QACA,KAAKT,YAAY,CAACO,aAAa,CAACiB,YAAY;QAC5CxB,YAAY,CAACO,aAAa,CAACpJ,KAAK,CAACsJ,OAAO,GAAGA,OAAO;MACpD;IACF,CAAC,EAAE,CAAC,CAAC;IAELpN,OAAO,CAACC,GAAG,CAAC,wCAAwCnF,SAAS,CAACa,EAAE,EAAE,CAAC;EACrE;EAEAyS,iBAAiBA,CAACzC,KAAY;IAC5BA,KAAK,CAAC0C,cAAc,EAAE;IACtB,IAAI,CAACpR,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,aAAa,EAAE;IACpC,IAAI,CAAC2B,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAE7B;IACA,IAAI,CAAC3B,oBAAoB,GAAG,KAAK;IAEjC;IACA,IAAI,CAACkR,oBAAoB,EAAE;IAE3B;IACApN,UAAU,CAAC,MAAK;MACdlB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,IAAI,CAAC5C,iBAAiB,GAAG,IAAI;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAiR,oBAAoBA,CAAA;IAClB,IAAI,CAACjR,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACC,eAAe,GAAG,KAAK;EAC9B;EAEAqQ,kBAAkBA,CAAA;IAChB,IAAI,CAACtR,iBAAiB,GAAG,KAAK;IAC9B;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,aAAa,EAAE;IACpC,IAAI,CAAC2B,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC3B,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACkR,oBAAoB,EAAE;EAC7B;EAEA;EACAE,eAAeA,CAACC,IAAsB;IACpC,IAAI,CAAClR,iBAAiB,GAAGkR,IAAI;IAC7B,IAAI,CAACnR,kBAAkB,GAAG,IAAI;IAE9B;IACA4D,UAAU,CAAC,MAAK;MACd,IAAI,CAAChE,QAAQ,CAACkM,UAAU,GAAGqF,IAAW;MAEtC;MACAvN,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1D,gBAAgB,GAAG,IAAI;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEAkR,cAAcA,CAACC,QAAgB;IAC7B,IAAI,CAAChR,gBAAgB,GAAGgR,QAAQ;IAChC,IAAI,CAAClR,iBAAiB,GAAG,IAAI;IAE7B;IACAyD,UAAU,CAAC,MAAK;MACd,IAAI,CAAChE,QAAQ,CAACyR,QAAQ,GAAGA,QAAe;MACxC,IAAI,CAACjR,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACkR,qBAAqB,CAAC;QAAE7C,MAAM,EAAE;UAAExN,KAAK,EAAEoQ;QAAQ;MAAE,CAAE,CAAC;MAE3D;MACAzN,UAAU,CAAC,MAAK;QACd,IAAI,CAACtD,gBAAgB,GAAG,IAAI;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEAiR,cAAcA,CAACC,QAA0B;IACvC,IAAIA,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC1R,oBAAoB,EAAE;MACpD,OAAO,CAAC;IACV;IAEA,IAAI,CAACS,iBAAiB,GAAG,IAAI;IAE7BqD,UAAU,CAAC,MAAK;MACd,IAAI,CAAChE,QAAQ,CAAC4R,QAAQ,GAAGA,QAAe;IAC1C,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnS,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACD,WAAW,EAAE;MAElB;MACA,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1BsE,UAAU,CAAC,MAAK;UACd,IAAI,CAACnD,oBAAoB,GAAG,IAAI;QAClC,CAAC,EAAE,GAAG,CAAC;QAEPmD,UAAU,CAAC,MAAK;UACd,IAAI,CAACpD,YAAY,GAAG,IAAI;QAC1B,CAAC,EAAE,GAAG,CAAC;QAEPoD,UAAU,CAAC,MAAK;UACd,IAAI,CAAClD,iBAAiB,GAAG,IAAI;QAC/B,CAAC,EAAE,GAAG,CAAC;QAEPkD,UAAU,CAAC,MAAK;UACd,IAAI,CAAChD,eAAe,GAAG,IAAI;QAC7B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;EACF;EAEA8Q,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpS,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAqS,cAAcA,CAAA;IACZ/N,UAAU,CAAC,MAAK;MACd,IAAI,CAACgO,UAAU,CAACC,eAAe,EAAE,CAAC7T,IAAI,CAAE8T,KAAuB,IAAI;QACjE,MAAMC,GAAG,GAAGD,KAAK,CAAC7Q,KAAK,CAAC1D,MAAM;QAC9BuU,KAAK,CAACE,iBAAiB,CAACD,GAAG,EAAEA,GAAG,CAAC;QACjCD,KAAK,CAACG,UAAU,GAAGH,KAAK,CAACI,WAAW;MACtC,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEA,IAAIrJ,QAAQA,CAAA;IACV,OAAQ,IAAI,CAACvJ,WAAW,GAAK,IAAI,CAACC,UAAW;EAC/C;EAGM4S,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAzN,iBAAA;MACf,IAAI,CAACyN,MAAI,CAACpV,MAAM,IAAI,CAACoV,MAAI,CAACxS,QAAQ,CAACrB,IAAI,IAAI,CAAC6T,MAAI,CAACxS,QAAQ,CAACd,KAAK,IAAI,CAACsT,MAAI,CAACxS,QAAQ,CAACkM,UAAU,IAC1F,CAACsG,MAAI,CAACxS,QAAQ,CAACyR,QAAQ,IAAI,CAACe,MAAI,CAACxS,QAAQ,CAACnB,UAAU,IAAI,CAAC2T,MAAI,CAACxS,QAAQ,CAAClB,SAAS,IAAI,CAAC0T,MAAI,CAACxS,QAAQ,CAACmM,WAAW,EAAE;QAChHrJ,OAAO,CAACxE,KAAK,CAAC,0DAA0D,CAAC;QACzE;MACF;MAEA,IAAI;QACF,IAAIkU,MAAI,CAACxS,QAAQ,CAACmM,WAAW,KAAK,MAAM,IAAIqG,MAAI,CAAC5Q,kBAAkB,CAACjE,MAAM,GAAG,CAAC,EAAE;UAC9E6U,MAAI,CAACxS,QAAQ,CAACoM,iBAAiB,GAAGoG,MAAI,CAAC5Q,kBAAkB,CAAC6Q,IAAI,CAAC,GAAG,CAAC;QACrE,CAAC,MAAM,IAAID,MAAI,CAACxS,QAAQ,CAACmM,WAAW,KAAK,OAAO,IAAIqG,MAAI,CAAC3Q,mBAAmB,CAAClE,MAAM,GAAG,CAAC,EAAE;UACvF6U,MAAI,CAACxS,QAAQ,CAACqM,kBAAkB,GAAGmG,MAAI,CAAC3Q,mBAAmB,CAAC4Q,IAAI,CAAC,GAAG,CAAC;QACvE;QAEA,MAAM;UAAEjU,IAAI,EAAEkU,WAAW;UAAEpU,KAAK,EAAEqU;QAAS,CAAE,SAASH,MAAI,CAAC3U,eAAe,CAACC,SAAS,EAAE,CACnFC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,IAAI,EAAEuU,MAAI,CAACpV,MAAM,CAAC,CACrBe,MAAM,EAAE;QAEX,IAAIwU,SAAS,IAAI,CAACD,WAAW,EAAE;UAC7B5P,OAAO,CAACxE,KAAK,CAAC,oCAAoC,EAAEqU,SAAS,IAAI,kBAAkB,CAAC;UACpF,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;QAC7E;QAEA,MAAMC,aAAa,GAAgD;UACjElU,IAAI,EAAE6T,MAAI,CAACxS,QAAQ,CAACrB,IAAI,IAAI,EAAE;UAC9BC,WAAW,EAAE4T,MAAI,CAACxS,QAAQ,CAACpB,WAAW,IAAI,EAAE;UAC5CsN,UAAU,EAAEsG,MAAI,CAACxS,QAAQ,CAACkM,UAAU,IAAI,OAAO;UAC/CrN,UAAU,EAAE2T,MAAI,CAACxS,QAAQ,CAACnB,UAAU,IAAI,CAAC;UACzCC,SAAS,EAAE0T,MAAI,CAACxS,QAAQ,CAAClB,SAAS,IAAI,OAAO;UAC7CqN,WAAW,EAAEqG,MAAI,CAACxS,QAAQ,CAACmM,WAAW,IAAI,KAAK;UAC/CyF,QAAQ,EAAEY,MAAI,CAACxS,QAAQ,CAAC4R,QAAQ,IAAI,OAAO;UAC3CH,QAAQ,EAAEe,MAAI,CAACxS,QAAQ,CAACyR,QAAQ,IAAI,UAAU;UAC9CvS,KAAK,EAAEsT,MAAI,CAACxS,QAAQ,CAACd,KAAK,IAAI,IAAI;UAClCkN,iBAAiB,EAAEoG,MAAI,CAACxS,QAAQ,CAACoM,iBAAiB,IAAI,EAAE;UACxDC,kBAAkB,EAAEmG,MAAI,CAACxS,QAAQ,CAACqM,kBAAkB,IAAI,EAAE;UAC1DyG,OAAO,EAAEN,MAAI,CAACpV,MAAM;UACpBkP,MAAM,EAAE;SACT;QAED,IAAI;UACF,MAAMyG,OAAO,SAASP,MAAI,CAAC1Q,YAAY,CAACyQ,WAAW,CAACM,aAAa,CAAC;UAElE,IAAIL,MAAI,CAACxS,QAAQ,CAACkM,UAAU,KAAK,MAAM,EAAE;YAEvC,MAAMsG,MAAI,CAAC1Q,YAAY,CAACkN,qBAAqB,CAC3CwD,MAAI,CAACpV,MAAM,EACX2V,OAAO,EACP,IAAIlW,IAAI,EAAE,EACV,CAAC,EACD;cAAE,GAAGgW,aAAa;cAAEpU,EAAE,EAAEsU;YAAO,CAAW,CAC3C;UACH;UAEA,MAAMxN,OAAO,GAAGiN,MAAI,CAACrO,UAAU,CAACqO,MAAI,CAACzV,YAAY,CAAC;UAClD,OAAOyV,MAAI,CAAC3S,UAAU,CAAC0F,OAAO,CAAC;UAC/B,OAAOiN,MAAI,CAACnQ,iBAAiB,CAACkD,OAAO,CAAC;UAEtCiN,MAAI,CAACnB,kBAAkB,EAAE;UACzBmB,MAAI,CAACnP,QAAQ,EAAE;QACjB,CAAC,CAAC,OAAO2P,UAAe,EAAE;UACxBlQ,OAAO,CAACxE,KAAK,CAAC,kCAAkC,EAAE0U,UAAU,CAAC;UAE7D,IAAIA,UAAU,CAACC,OAAO,IAAID,UAAU,CAACC,OAAO,CAAChG,QAAQ,CAAC,wBAAwB,CAAC,EAAE;YAC/EiG,KAAK,CAAC,uJAAuJ,CAAC;UAChK,CAAC,MAAM,IAAIF,UAAU,CAACC,OAAO,IAAID,UAAU,CAACC,OAAO,CAAChG,QAAQ,CAAC,2BAA2B,CAAC,EAAE;YACzFiG,KAAK,CAACF,UAAU,CAACC,OAAO,CAAC;UAC3B,CAAC,MAAM;YACLC,KAAK,CAAC,yBAAyBF,UAAU,CAACC,OAAO,EAAE,CAAC;UACtD;QACF;MACF,CAAC,CAAC,OAAO3U,KAAU,EAAE;QACnBwE,OAAO,CAACxE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD4U,KAAK,CAAC,UAAU5U,KAAK,CAAC2U,OAAO,IAAI,wBAAwB,EAAE,CAAC;MAC9D;IAAC;EACH;EAEAE,gBAAgBA,CAACjI,GAAW;IAC1B,MAAMQ,KAAK,GAAG,IAAI,CAAC9J,kBAAkB,CAACwR,OAAO,CAAClI,GAAG,CAAC;IAElD,IAAIQ,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC9J,kBAAkB,CAACyR,MAAM,CAAC3H,KAAK,EAAE,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAAC9J,kBAAkB,CAAC2C,IAAI,CAAC2G,GAAG,CAAC;IACnC;EACF;EAEAoI,iBAAiBA,CAAC7E,KAAU,EAAEvD,GAAW;IACvC;IACA,IAAIqI,SAAS,GAAG,KAAK;IAErB,IAAI9E,KAAK,CAACI,MAAM,KAAK7L,SAAS,EAAE;MAC9B;MACAuQ,SAAS,GAAG9E,KAAK,CAACI,MAAM,CAAC2E,OAAO;IAClC,CAAC,MAAM,IAAI/E,KAAK,CAACG,MAAM,YAAYzI,gBAAgB,EAAE;MACnD;MACAoN,SAAS,GAAG9E,KAAK,CAACG,MAAM,CAAC4E,OAAO;IAClC;IAEA,IAAID,SAAS,EAAE;MACb,IAAI,CAAC1R,mBAAmB,CAAC0C,IAAI,CAAC2G,GAAG,CAAC;IACpC,CAAC,MAAM;MACL,MAAMQ,KAAK,GAAG,IAAI,CAAC7J,mBAAmB,CAACuR,OAAO,CAAClI,GAAG,CAAC;MACnD,IAAIQ,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC7J,mBAAmB,CAACwR,MAAM,CAAC3H,KAAK,EAAE,CAAC,CAAC;MAC3C;IACF;IAEA5I,OAAO,CAACC,GAAG,CAAC,qCAAqC,IAAI,CAAClB,mBAAmB,CAAC4Q,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EACzF;EAEAgB,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC7R,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7BiB,OAAO,CAACC,GAAG,CAAC,gCAAgC,IAAI,CAAC/C,QAAQ,CAACmM,WAAW,oBAAoB,CAAC;IAE1F;IACAnI,UAAU,CAAC,MAAK;MACd,IAAI,CAACjD,wBAAwB,GAAG,IAAI;IACtC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA2S,cAAcA,CAACxI,GAAW;IACxB,MAAMQ,KAAK,GAAG,IAAI,CAAC7J,mBAAmB,CAACuR,OAAO,CAAClI,GAAG,CAAC;IACnD,IAAIQ,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC7J,mBAAmB,CAACwR,MAAM,CAAC3H,KAAK,EAAE,CAAC,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAAC7J,mBAAmB,CAAC0C,IAAI,CAAC2G,GAAG,CAAC;IACpC;EACF;EAEAyI,eAAeA,CAAClC,QAAgB;IAC9B,MAAMmC,KAAK,GAA8B;MACvC,UAAU,EAAE,IAAI;MAChB,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,IAAI;MACd,WAAW,EAAE;KACd;IACD,OAAOA,KAAK,CAACnC,QAAQ,CAAC,IAAI,IAAI;EAChC;EAEAoC,eAAeA,CAACC,MAAgC;IAC9C,IAAI,CAAC9T,QAAQ,CAACmM,WAAW,GAAG2H,MAAa;IACzC,IAAI,CAACL,mBAAmB,EAAE;EAC5B;EAEAM,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC/T,QAAQ,CAACmM,WAAW,KAAK,KAAK,EAAE;MACvC,OAAO,OAAO;IAChB,CAAC,MAAM,IAAI,IAAI,CAACnM,QAAQ,CAACmM,WAAW,KAAK,MAAM,EAAE;MAC/C,IAAI,IAAI,CAACvK,kBAAkB,CAACjE,MAAM,KAAK,CAAC,EAAE;QACxC,OAAO,QAAQ;MACjB,CAAC,MAAM,IAAI,IAAI,CAACiE,kBAAkB,CAACjE,MAAM,KAAK,CAAC,EAAE;QAC/C,OAAO,OAAO;MAChB,CAAC,MAAM;QACL,OAAO,GAAG,IAAI,CAACiE,kBAAkB,CAACjE,MAAM,YAAY;MACtD;IACF,CAAC,MAAM,IAAI,IAAI,CAACqC,QAAQ,CAACmM,WAAW,KAAK,OAAO,EAAE;MAChD,IAAI,IAAI,CAACtK,mBAAmB,CAAClE,MAAM,KAAK,CAAC,EAAE;QACzC,OAAO,SAAS;MAClB,CAAC,MAAM;QACL,OAAO,GAAG,IAAI,CAACkE,mBAAmB,CAAClE,MAAM,aAAa;MACxD;IACF;IACA,OAAO,EAAE;EACX;EAEA+T,qBAAqBA,CAACjD,KAAW;IAC/B,IAAI,CAAC,IAAI,CAACrR,MAAM,IAAI,CAAC,IAAI,CAAC4C,QAAQ,CAACyR,QAAQ,EAAE;IAE7C;IACA,IAAIhD,KAAK,IAAIA,KAAK,CAACI,MAAM,EAAE;MACzB,IAAI,CAAC7O,QAAQ,CAACyR,QAAQ,GAAGhD,KAAK,CAACI,MAAM,CAACxN,KAAK;MAC3CyB,OAAO,CAACC,GAAG,CAAC,kCAAkC,IAAI,CAAC/C,QAAQ,CAACyR,QAAQ,kBAAkB,CAAC;IACzF;IAEA;IACA,IAAI,CAAC3P,YAAY,CAAC+F,SAAS,CAAC,IAAI,CAACzK,MAAM,CAAC,CAACG,IAAI,CAC3CxB,IAAI,CAAC,CAAC,CAAC,EACPH,GAAG,CAAC6D,MAAM,IAAG;MACX,OAAOA,MAAM,CAACuU,IAAI,CAACnI,CAAC,IAClBA,CAAC,CAAC4F,QAAQ,KAAK,IAAI,CAACzR,QAAQ,CAACyR,QAAQ,IACrC5F,CAAC,CAAC+F,QAAQ,KAAK,MAAM,IACrB/F,CAAC,CAACS,MAAM,CACT;IACH,CAAC,CAAC,CACH,CAAC9O,SAAS,CAAC;MACVC,IAAI,EAAGwW,eAAe,IAAI;QACxB,IAAI,CAAC/T,oBAAoB,GAAG+T,eAAe;QAE3C;QACA,IAAIA,eAAe,EAAE;UACnB,IAAI,CAACjU,QAAQ,CAAC4R,QAAQ,GAAG,OAAO;QAClC;QAEA9O,OAAO,CAACC,GAAG,CAAC,uBAAuB,IAAI,CAAC/C,QAAQ,CAACyR,QAAQ,6BAA6BwC,eAAe,EAAE,CAAC;MAC1G;KACD,CAAC;EACJ;EAEA;;;EAGAnK,uBAAuBA,CAACrK,MAAsB;IAC5C;IACA,MAAM7C,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,MAAMC,YAAY,GAAG,IAAIF,IAAI,CAAC,IAAI,CAACE,YAAY,CAAC;IAChDA,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACjC,MAAME,eAAe,GAAGD,YAAY,CAACE,OAAO,EAAE,KAAKL,KAAK,CAACK,OAAO,EAAE;IAClE,MAAMiH,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACvH,KAAK,CAAC;IAEvC,IAAI,CAACI,eAAe,IAAI,CAAC,IAAI,CAACkE,WAAW,EAAE;MACzC;IACF;IAEA;IACA,MAAMgT,gBAAgB,GAAG7P,YAAY,CAACO,OAAO,CAAC,qBAAqBV,QAAQ,EAAE,CAAC;IAC9E,IAAIgQ,gBAAgB,EAAE;MACpBpR,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEmB,QAAQ,CAAC;MACxE;IACF;IAEA;IACA,MAAMiQ,kBAAkB,GAAG1U,MAAM,CAAC9B,MAAM,GAAG,CAAC,IAAI8B,MAAM,CAAC2U,KAAK,CAACrM,KAAK,IAAIA,KAAK,CAAC/I,SAAS,CAAC;IAEtF;IACA,MAAMqV,kBAAkB,GAAG,CAAC,IAAI,CAAClX,cAAc,IAAI,CAAC,IAAI,CAACD,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC8B,SAAS;IAEhG;IAEA,IAAImV,kBAAkB,IAAIE,kBAAkB,IAAI,IAAI,CAACnT,WAAW,CAACoT,gBAAgB,EAAE;MACjF;MACA,IAAI,CAACvS,WAAW,CAAC0B,WAAW,CAAC,IAAI,CAACrG,MAAO,CAAC,CAACI,SAAS,CAACkG,QAAQ,IAAG;QAC9D,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACxC,WAAW,GAAGwC,QAAQ;QAC7B;QAEA;QACA,IAAI,CAACzC,eAAe,GAAG,IAAI;QAE3B;QACAoD,YAAY,CAACkQ,OAAO,CAAC,qBAAqBrQ,QAAQ,EAAE,EAAE,MAAM,CAAC;QAE7D;QACA,IAAI,CAAC,IAAI,CAAC/C,qBAAqB,CAAC8L,QAAQ,CAAC/I,QAAQ,CAAC,EAAE;UAClD,IAAI,CAAC/C,qBAAqB,CAACoD,IAAI,CAACL,QAAQ,CAAC;QAC3C;MACF,CAAC,CAAC;IACJ;EACF;EAEA;;;EAGAsQ,gBAAgBA,CAAA;IACd,IAAI,CAACvT,eAAe,GAAG,KAAK;EAC9B;EAIA;EACAkD,UAAUA,CAACyB,IAAU;IACnB,MAAM6O,IAAI,GAAG7O,IAAI,CAAC8O,WAAW,EAAE;IAC/B,MAAMrG,KAAK,GAAGsG,MAAM,CAAC/O,IAAI,CAACgP,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAM3J,GAAG,GAAGyJ,MAAM,CAAC/O,IAAI,CAACyE,OAAO,EAAE,CAAC,CAACwK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGJ,IAAI,IAAIpG,KAAK,IAAInD,GAAG,EAAE;EAClC;EAEAV,SAASA,CAACsK,KAAW,EAAEC,KAAW;IAChC,OAAOD,KAAK,CAACJ,WAAW,EAAE,KAAKK,KAAK,CAACL,WAAW,EAAE,IAChDI,KAAK,CAACF,QAAQ,EAAE,KAAKG,KAAK,CAACH,QAAQ,EAAE,IACrCE,KAAK,CAACzK,OAAO,EAAE,KAAK0K,KAAK,CAAC1K,OAAO,EAAE;EACvC;EAEA2K,QAAQA,CAAA;IACN,MAAMpY,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,OAAOF,KAAK;EACd;EAEA;EACQiQ,eAAeA,CAACoI,cAAsB;IAC5C;IACA,MAAMC,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzD,OAAOA,MAAM,CAACD,cAAc,CAAC;EAC/B;EAEA;EACQlI,cAAcA,CAACkI,cAAsB;IAC3C;IACA,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAChE,OAAOA,MAAM,CAACD,cAAc,CAAC;EAC/B;EAEQhV,aAAaA,CAAA;IACnB,OAAO;MACLtB,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfsN,UAAU,EAAE,EAAS;MAAE;MACvBrN,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,OAAwB;MACnCqN,WAAW,EAAE,KAAoB;MACjCyF,QAAQ,EAAE,OAAwB;MAAE;MACpCH,QAAQ,EAAE,EAAmB;MAC7BvS,KAAK,EAAE;KACR;EACH;CAID;AA7iE0BiW,UAAA,EAAxBja,SAAS,CAAC,YAAY,CAAC,C,4CAAiB;AAD9BwB,SAAS,GAAAyY,UAAA,EAPrBna,SAAS,CAAC;EACToa,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,mBAAmB;EAChCC,SAAS,EAAE,CAAC,mBAAmB,CAAC;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACna,WAAW,EAAEF,YAAY,EAAEC,WAAW,EAAEa,mBAAmB,EAAEC,oBAAoB,EAAEI,mBAAmB,EAAEE,eAAe,EAAEC,eAAe;CACnJ,CAAC,C,EACWC,SAAS,CA8iErB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}