// @ts-nocheck - Disable TypeScript checking for this file
import { Injectable, EnvironmentInjector, inject, runInInjectionContext } from '@angular/core';
import { Quest, QuestProgress } from '../models/quest.model';
import { Observable, from, map, of, catchError, switchMap } from 'rxjs';
import { UserService } from './user.service';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class QuestService {
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private injector = inject(EnvironmentInjector);

  // Quest CRUD operations
  getQuests(userId: string): Observable<Quest[]> {
    return runInInjectionContext(this.injector, () => {
      console.log('QuestService: Getting quests for user:', userId);

      return from(
        this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .filter('user_id', 'eq', userId)
          .filter('active', 'eq', true)
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('QuestService: Error getting quests:', response.error);
            return [];
          }

          console.log(`QuestService: Found ${response.data.length} quests for user:`, userId);
          return response.data as Quest[];
        }),
        catchError(error => {
          console.error('QuestService: Error getting quests:', error);
          return of([]);
        })
      );
    });
  }

  getQuest(questId: string): Observable<Quest | null> {
    return runInInjectionContext(this.injector, () => {
      console.log('QuestService: Getting quest with ID:', questId);

      return from(
        this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .filter('id', 'eq', questId)
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('QuestService: Error getting quest:', response.error);
            return null;
          }

          if (!response.data || response.data.length === 0) {
            console.log('QuestService: No quest found with ID:', questId);
            return null;
          }

          console.log('QuestService: Found quest:', response.data[0]);
          return response.data[0] as Quest;
        }),
        catchError(error => {
          console.error('QuestService: Error getting quest:', error);
          return of(null);
        })
      );
    });
  }

  async createQuest(quest: Omit<Quest, 'id' | 'created_at' | 'streak'>): Promise<string> {
    return runInInjectionContext(this.injector, async () => {
      console.log('QuestService: Creating new quest:', quest);

      try {
        // First, check if the user profile exists in the profiles table
        // Note: user_id in the quest references profiles.id
        const { data: userProfile, error: userError } = await this.supabaseService.getClient()
          .from('profiles')
          .select('id')
          .eq('id', quest.user_id)
          .maybeSingle();

        if (userError || !userProfile) {
          console.error('QuestService: User profile not found:', userError || 'No profile found');
          throw new Error('User profile not found. Please ensure you are logged in.');
        }

        console.log('QuestService: Found user profile:', userProfile);

        // Convert Date objects to ISO strings for Supabase and remove any ID if present
        const { id, ...questWithoutId } = quest as any;

        // For quit quests, set initial streak to 1 since they start as completed
        const initialStreak = questWithoutId.quest_type === 'quit' ? 1 : 0;

        const newQuest = {
          ...questWithoutId,
          created_at: new Date().toISOString(),
          streak: initialStreak
        };

        console.log('QuestService: Prepared quest for Supabase:', newQuest);

        // Insert the quest without specifying an ID (let Supabase generate it)
        const { data, error } = await this.supabaseService.getClient()
          .from('quests')
          .insert(newQuest)
          .select()
          .maybeSingle();

        if (error) {
          console.error('QuestService: Error creating quest:', error);

          // If the error is related to a foreign key constraint, provide a helpful error message
          if (error.code === '23503') {
            console.error('QuestService: Foreign key constraint error detected:', error.message);

            if (error.message.includes('quests_user_id_fkey') ||
                error.message.includes('quests_id_fkey') ||
                error.message.includes('Key is not present in table "users"')) {
              throw new Error('There is a database configuration issue with foreign key constraints. Please run the fix_quest_constraints.sql script in Supabase SQL Editor to fix it.');
            } else {
              throw new Error(`Foreign key constraint error: ${error.message}. Please check your database configuration.`);
            }
          } else {
            throw new Error(error.message);
          }
        }

        console.log('QuestService: Created quest with ID:', data.id);
        return data.id;
      } catch (error) {
        console.error('QuestService: Error in createQuest:', error);
        throw error;
      }
    });
  }

  async updateQuest(questId: string, data: Partial<Quest>): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      console.log('QuestService: Updating quest:', questId, data);

      const { error } = await this.supabaseService.getClient()
        .from('quests')
        .update(data)
        .eq('id', questId);

      if (error) {
        console.error('QuestService: Error updating quest:', error);
        throw new Error(error.message);
      }

      console.log('QuestService: Updated quest:', questId);
    });
  }

  /**
   * Update the streak value for a quest in the database
   */
  updateQuestStreak(questId: string, streak: number): Observable<void> {
    return runInInjectionContext(this.injector, () => {
      return new Observable<void>(observer => {
        const updateStreak = async () => {
          try {
            console.log(`QuestService: Updating streak for quest ${questId} to ${streak}`);

            const { error } = await this.supabaseService.getClient()
              .from('quests')
              .update({ streak })
              .eq('id', questId);

            if (error) {
              console.error(`QuestService: Error updating streak for quest ${questId}:`, error);
              observer.error(error);
              return;
            }

            console.log(`QuestService: Successfully updated streak for quest ${questId} to ${streak}`);

            // Update streak badges if needed
            if (streak >= 7) {
              this.updateStreakBadges(questId, streak);
            }

            observer.next();
            observer.complete();
          } catch (error) {
            console.error(`QuestService: Error updating streak for quest ${questId}:`, error);
            observer.error(error);
          }
        };

        updateStreak();
      });
    });
  }

  async deleteQuest(questId: string): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      console.log('QuestService: Deleting quest:', questId);

      const { error } = await this.supabaseService.getClient()
        .from('quests')
        .delete()
        .eq('id', questId);

      if (error) {
        console.error('QuestService: Error deleting quest:', error);
        throw new Error(error.message);
      }

      console.log('QuestService: Deleted quest:', questId);
    });
  }

  // Quest Progress operations
  getQuestProgress(userId: string, questId: string, date: Date): Observable<QuestProgress | null> {
    return runInInjectionContext(this.injector, () => {
      console.log('QuestService: Getting quest progress:', questId, userId, date);

      // Format date as YYYY-MM-DD
      const dateString = date.toISOString().split('T')[0];
      console.log('QuestService: Formatted date string:', dateString);

      // First check if the quest exists
      return from(
        this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('id', questId)
          .maybeSingle()
      ).pipe(
        switchMap(questResponse => {
          if (questResponse.error) {
            console.error('QuestService: Error getting quest:', questResponse.error);
            return of(null);
          }

          const quest = questResponse.data as Quest;

          // Now get the progress
          return from(
            this.supabaseService.getClient()
              .from('quest_progress')
              .select('*')
              .eq('quest_id', questId)
              .eq('user_id', userId)
              .eq('date', dateString)
              .limit(1)
          ).pipe(
            switchMap(response => {
              if (response.error) {
                // Check if it's a 406 error (Not Acceptable) or PGRST116 error
                if (response.error.code === '406' || response.error.code === 'PGRST116') {
                  console.warn(`QuestService: ${response.error.code} error. This is likely due to RLS policies or query format. Error details:`, response.error);

                  // Return a default progress object instead of null
                  return of({
                    id: '',
                    user_id: userId,
                    quest_id: questId,
                    date: dateString,
                    completed: false,
                    value_achieved: 0
                  } as QuestProgress);
                }

                console.error('QuestService: Error getting quest progress:', response.error);
                return of(null);
              }

              if (!response.data || response.data.length === 0) {
                console.log('QuestService: No progress found for quest:', questId, 'on date:', dateString);
                return of(null);
              }

              const progress = response.data[0] as QuestProgress;
              console.log('QuestService: Found progress for quest:', progress);

              // For today, use the streak from the model
              // For previous days, we don't need to show the streak
              const today = new Date();
              today.setHours(0, 0, 0, 0);
              const selectedDate = new Date(date);
              selectedDate.setHours(0, 0, 0, 0);

              // Just return the progress as is
              return of(progress);
            })
          );
        }),
        catchError(error => {
          console.error('QuestService: Error getting quest progress:', error);
          return of(null);
        })
      );
    });
  }





  getQuestProgressForQuest(userId: string, questId: string): Observable<QuestProgress[]> {
    return runInInjectionContext(this.injector, () => {
      console.log('QuestService: Getting all quest progress for quest:', questId, 'user:', userId);

      return from(
        this.supabaseService.getClient()
          .from('quest_progress')
          .select('*')
          .eq('quest_id', questId)
          .eq('user_id', userId)
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('QuestService: Error getting quest progress for quest:', response.error);
            return [];
          }

          console.log(`QuestService: Found ${response.data.length} progress entries for quest:`, questId);
          return response.data as QuestProgress[];
        }),
        catchError(error => {
          console.error('QuestService: Error getting quest progress for quest:', error);
          return of([]);
        })
      );
    });
  }

  getQuestProgressForDate(userId: string, date: Date): Observable<QuestProgress[]> {
    return runInInjectionContext(this.injector, () => {
      console.log('QuestService: Getting quest progress for date:', userId, date);

      // Format date as YYYY-MM-DD
      const dateString = date.toISOString().split('T')[0];
      console.log('QuestService: Formatted date string:', dateString);

      // First get all quests for this user
      return from(
        this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('user_id', userId)
          .eq('active', true)
      ).pipe(
        switchMap(questsResponse => {
          if (questsResponse.error) {
            console.error('QuestService: Error getting quests:', questsResponse.error);
            return of([]);
          }

          const quests = questsResponse.data as Quest[];

          if (quests.length === 0) {
            console.log('QuestService: No quests found for user:', userId);
            return of([]);
          }

          // Now get progress for these quests
          return from(
            this.supabaseService.getClient()
              .from('quest_progress')
              .select('*')
              .eq('user_id', userId)
              .eq('date', dateString)
          ).pipe(
            map(response => {
              if (response.error) {
                // Check if it's a 406 error (Not Acceptable) or PGRST116 error
                if (response.error.code === '406' || response.error.code === 'PGRST116') {
                  console.warn(`QuestService: ${response.error.code} error when getting progress for date. This is likely due to RLS policies or query format. Error details:`, response.error);

                  // Return empty array but log a warning
                  return [];
                }

                console.error('QuestService: Error getting quest progress for date:', response.error);
                return [];
              }

              console.log(`QuestService: Found ${response.data.length} progress entries for date:`, dateString);
              return response.data as QuestProgress[];
            }),
            catchError(error => {
              console.error('QuestService: Error getting quest progress for date:', error);
              return of([]);
            })
          );
        })
      );
    });
  }

  async toggleQuestCompletion(
    userId: string,
    questId: string,
    date: Date,
    valueAchieved: number,
    _quest: Quest // Renamed to _quest to indicate it's not used
  ): Promise<{ completed: boolean; value_achieved: number; streak: number }> {
    return runInInjectionContext(this.injector, async () => {
      try {
        // Format date as YYYY-MM-DD
        const dateString = date.toISOString().split('T')[0];

        // Initialize Supabase client
        const supabase = this.supabaseService.getClient();

        // Get the current streak from the quest
        const { data: questWithStreak, error: questStreakError } = await supabase
          .from('quests')
          .select('streak')
          .eq('id', questId)
          .single();

        if (questStreakError) {
          console.error('QuestService: Error getting quest streak:', questStreakError);
        }

        const currentStreak = questWithStreak?.streak || 0;
        console.log(`QuestService: Using current streak from quest: ${currentStreak}`);

        // Double-check that we're updating the correct quest
        const { data: questData, error: questError } = await supabase
          .from('quests')
          .select('*')
          .eq('id', questId)
          .maybeSingle();
        console.log(`QuestService: Quest data:`, questData);
        if (questError || !questData) {
          console.error('QuestService: Error verifying quest:', questError || 'Quest not found');
          throw new Error('Quest not found');
        }

        // Verify that the quest belongs to the user
        if (questData.user_id !== userId) {
          console.error('QuestService: Quest does not belong to user! Quest user_id:', questData.user_id, 'User ID:', userId);
          throw new Error('Quest does not belong to user');
        }

        // Use the verified quest data for the rest of the function
        const verifiedQuestId = questData.id;
        const verifiedUserId = questData.user_id;

        // Get existing progress document to check if it was completed before
        const { data: existingProgressData, error: progressError } = await supabase
          .from('quest_progress')
          .select('*')
          .eq('quest_id', verifiedQuestId)
          .eq('user_id', verifiedUserId)
          .eq('date', dateString);

        if (progressError) {
          // Check if it's a 406 error (Not Acceptable) or PGRST116 error
          if (progressError.code === '406' || progressError.code === 'PGRST116') {
            console.warn(`QuestService: ${progressError.code} error when checking quest progress. This is likely due to RLS policies or query format. Error details:`, progressError);
            // Continue with empty progress array
          } else {
            console.error('QuestService: Error checking quest progress:', progressError);
            throw new Error('Error checking quest progress');
          }
        }

        // Remember if it was completed before and the previous value achieved
        const wasCompleted = existingProgressData && existingProgressData.length > 0 ?
                            existingProgressData[0].completed : false;
        const previousValueAchieved = existingProgressData && existingProgressData.length > 0 ?
                            existingProgressData[0].value_achieved : 0;

        // Determine completion status based on quest type and value
        // This exactly matches the Django implementation in toggle_quest view
        let isCompleted: boolean;
        if (questData.quest_type === 'build') {
          // For build quests, completed when value >= goal
          isCompleted = valueAchieved >= questData.goal_value;
        } else { // 'quit' type
          // For quit quests, completed when value < goal (opposite of build)
          isCompleted = valueAchieved < questData.goal_value;
        }

        console.log(`QuestService: Quest ${questId} value: ${valueAchieved}, previous value: ${previousValueAchieved}, goal: ${questData.goal_value}, completed: ${isCompleted}, was completed: ${wasCompleted}`);

        // If there's existing progress, update it instead of deleting and recreating
        if (existingProgressData && existingProgressData.length > 0) {
          // Update the first entry and delete any duplicates
          const firstEntry = existingProgressData[0];

          // Update existing progress
          try {
            const { error: updateError } = await supabase
              .from('quest_progress')
              .update({
                value_achieved: valueAchieved,
                completed: isCompleted
              })
              .eq('id', firstEntry.id);

            if (updateError) {
              console.error('QuestService: Error updating quest progress:', updateError);
              // Don't throw an error, just log it and continue
              console.warn('QuestService: Continuing despite update error');
            }
          } catch (error) {
            console.error('QuestService: Exception during quest progress update:', error);
            // Don't throw an error, just log it and continue
            console.warn('QuestService: Continuing despite update exception');
          }

          // If there are duplicates, delete them
          if (existingProgressData.length > 1) {
            const idsToDelete = existingProgressData
              .slice(1)
              .map(p => p.id);

            if (idsToDelete.length > 0) {
              const { error: deleteError } = await supabase
                .from('quest_progress')
                .delete()
                .in('id', idsToDelete);

              if (deleteError) {
                console.error('QuestService: Error deleting duplicate progress entries:', deleteError);
              }
            }
          }
        } else {
          // Create new progress entry
          // For quit quests, set initial value_achieved to 0 and completed to true
          // For build quests, use the provided values
          const newProgress = {
            quest_id: verifiedQuestId,
            user_id: verifiedUserId,
            date: dateString,
            completed: isCompleted,
            value_achieved: valueAchieved
          };

          // Use upsert to handle conflicts
          try {
            const { error: upsertError } = await supabase
              .from('quest_progress')
              .upsert(newProgress, {
                onConflict: 'quest_id,user_id,date',
                ignoreDuplicates: false
              });

            if (upsertError) {
              console.error('QuestService: Error upserting quest progress:', upsertError);
              // Don't throw an error, just log it and continue
              console.warn('QuestService: Continuing despite upsert error');
            }
          } catch (error) {
            console.error('QuestService: Exception during quest progress upsert:', error);
            // Don't throw an error, just log it and continue
            console.warn('QuestService: Continuing despite upsert exception');
          }
        }

        // Get today's date for comparison
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const selectedDate = new Date(date);
        selectedDate.setHours(0, 0, 0, 0);
        // Check if selected date is today (not used in this function but kept for clarity)


        // Update user XP if completion status changed
        if (isCompleted !== wasCompleted) {
          console.log('TOGGLE: Completion status changed from', wasCompleted, 'to', isCompleted);
          console.log('TOGGLE: Updating XP for user', verifiedUserId, 'category', questData.category, 'priority', questData.priority);
          console.log("zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz", verifiedUserId, questData.category, isCompleted, wasCompleted, questData.priority);
          await this.updateUserXP(
            verifiedUserId,
            questData.category,
            isCompleted,
            wasCompleted,
            questData.priority
          );

          // Update category badges if quest was completed
          if (isCompleted) {
            await this.updateCategoryBadges(verifiedUserId, questData.category);
          }
        } else {
          console.log('TOGGLE: Completion status did not change. No XP update needed.');
        }







        return {
          completed: isCompleted,
          value_achieved: valueAchieved,
          streak: currentStreak
        };
      } catch (error) {
        console.error('QuestService: Error in toggleQuestCompletion:', error);
        throw error;
      }
    });
  }



  // Helper method to check if a quest should be active on a given date
  private shouldQuestBeActive(quest: Quest, date: Date): boolean {
    // Daily quests are active every day
    if (quest.goal_period === 'day') {
      return true;
    }

    // Weekly quests are active on specific days of the week
    if (quest.goal_period === 'week') {
      // If no task_days_of_week specified, active every day
      if (!quest.task_days_of_week) {
        return true;
      }

      // Get day name in 3-letter format (Mon, Tue, etc.)
      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' }).substring(0, 3);

      // Parse task_days_of_week (could be array or comma-separated string)
      let taskDays: string[] = [];
      if (typeof quest.task_days_of_week === 'string') {
        // Handle comma-separated string format
        taskDays = quest.task_days_of_week.split(',').map(day => day.trim());
      } else if (Array.isArray(quest.task_days_of_week)) {
        // Handle array format
        taskDays = quest.task_days_of_week;
      }

      console.log(`QuestService: Checking if ${dayName} is in task days:`, taskDays);

      // Check if the day is in the task_days_of_week array (case insensitive)
      return taskDays.some(day =>
        day.toLowerCase() === dayName.toLowerCase() ||
        day.toLowerCase().substring(0, 3) === dayName.toLowerCase()
      );
    }

    // Monthly quests are active on specific days of the month
    if (quest.goal_period === 'month') {
      // If no task_days_of_month specified, active every day
      if (!quest.task_days_of_month) {
        return true;
      }

      const dayOfMonth = date.getDate(); // 1-31

      // Parse task_days_of_month (could be array or comma-separated string)
      let taskDays: any[] = [];
      if (typeof quest.task_days_of_month === 'string') {
        // Handle comma-separated string format
        taskDays = quest.task_days_of_month.split(',').map(day => {
          const trimmed = day.trim();
          return isNaN(parseInt(trimmed)) ? trimmed : parseInt(trimmed);
        });
      } else if (Array.isArray(quest.task_days_of_month)) {
        // Handle array format
        taskDays = quest.task_days_of_month;
      }

      console.log(`QuestService: Checking if ${dayOfMonth} is in task days:`, taskDays);

      // Check if the day is in the task_days_of_month array
      return taskDays.some(day => {
        if (typeof day === 'number') {
          return day === dayOfMonth;
        } else if (typeof day === 'string') {
          return parseInt(day) === dayOfMonth;
        }
        return false;
      });
    }

    // Default to true if we can't determine
    return true;
  }

  /**
   * Check for missed days and update streak accordingly
   * This implementation exactly matches the Django implementation in Quest.check_missed_days method
   */
  /**
   * Simplified checkMissedDays method that only handles quit quests
   * For quit quests, it creates progress entries with completed=true for all days since the quest was created
   * For build quests, streaks are calculated by streakCalculator.calculateStreaks
   */
  async checkMissedDays(questId: string): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {
        console.log('QuestService: Checking for missed days for quest:', questId);

        // Get the quest details
        const { data: quest, error: questError } = await this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('id', questId)
          .maybeSingle();

        if (questError) {
          if (questError.code !== 'PGRST116' && questError.code !== '406') {
            console.error('QuestService: Error getting quest:', questError);
            return;
          } else {
            console.warn(`QuestService: ${questError.code} error when getting quest. This is expected if the quest doesn't exist.`);
            return;
          }
        }

        if (!quest) {
          console.error('QuestService: Quest not found:', questId);
          return;
        }

        // Only process quit quests
        if (quest.quest_type !== 'quit') {
          console.log(`QuestService: Quest ${quest.id} (${quest.name}) is not a quit quest, skipping`);
          return;
        }

        // Get the date when the quest was created
        const createdDate = new Date(quest.created_at);
        createdDate.setHours(0, 0, 0, 0);
        const createdDateStr = createdDate.toISOString().split('T')[0];

        // Get today's date
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        // If the quest was created today, no need to check for missed days
        if (createdDate.getTime() === today.getTime()) {
          return;
        }

        // Get all dates between created date and today
        const dates: Date[] = [];
        const currentDate = new Date(createdDate);

        // Get all dates between created date and today
        while (currentDate <= today) {
          const dateStr = currentDate.toISOString().split('T')[0];
          dates.push(new Date(currentDate));
          currentDate.setDate(currentDate.getDate() + 1);
        }

        // For each date, check if the quest should be shown on that day
        for (const date of dates) {
          const dateStr = date.toISOString().split('T')[0];

          // Check if the quest should be shown on this date
          let shouldShowOnDate = true;

          // Get day of week and day of month for filtering
          const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
          // Django uses Monday=0, Sunday=6 format, so we need to convert
          const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format
          const dayOfMonth = date.getDate(); // 1-31

          // Check for weekly quests with specific days
          if (quest.goal_period === 'week' && quest.task_days_of_week) {
            shouldShowOnDate = false;

            // Parse task_days_of_week
            let taskDays: string[] = [];
            if (typeof quest.task_days_of_week === 'string') {
              taskDays = quest.task_days_of_week.split(',').map(day => day.trim());
            } else if (Array.isArray(quest.task_days_of_week)) {
              taskDays = quest.task_days_of_week;
            }

            // Get day names in different formats for comparison
            const dayNameShort = this.getDayNameShort(djangoDayOfWeek);
            const dayNameFull = this.getDayNameFull(djangoDayOfWeek);

            // Check if current day is in task days
            shouldShowOnDate = taskDays.includes(djangoDayOfWeek) ||
                          taskDays.includes(djangoDayOfWeek.toString()) ||
                          taskDays.includes(dayNameShort) ||
                          taskDays.includes(dayNameFull);
          }

          // Check for monthly quests with specific days
          if (quest.goal_period === 'month' && quest.task_days_of_month) {
            shouldShowOnDate = false;

            // Parse task_days_of_month
            let taskDays: any[] = [];
            if (typeof quest.task_days_of_month === 'string') {
              taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));
            } else if (Array.isArray(quest.task_days_of_month)) {
              taskDays = quest.task_days_of_month;
            }

            // Check if current day is in task days
            shouldShowOnDate = taskDays.includes(dayOfMonth) ||
                         taskDays.includes(dayOfMonth.toString());
          }

          // Skip this date if the quest shouldn't be shown on this day
          if (!shouldShowOnDate) {
            continue;
          }

          // Check if progress already exists for this date
          const { data: existingProgress, error: progressError } = await this.supabaseService.getClient()
            .from('quest_progress')
            .select('*')
            .eq('quest_id', questId)
            .eq('user_id', quest.user_id)
            .eq('date', dateStr)
            .maybeSingle();

          if (progressError && progressError.code !== 'PGRST116' && progressError.code !== '406') {
            console.error(`QuestService: Error checking progress for quest ${quest.id} on ${dateStr}:`, progressError);
            continue;
          }

          // If progress already exists, skip this date
          if (existingProgress) {
            continue;
          }

          // DOUBLE CHECK: Make sure the date is not before the creation date
          const dateObj = new Date(dateStr);
          if (dateObj < createdDate) {
            console.error(`QuestService: ERROR! Trying to create progress for date ${dateStr} which is before creation date ${createdDateStr}. This should never happen!`);
            continue;
          }

          // Create progress entry with completed=true
          const newProgress = {
            quest_id: questId,
            user_id: quest.user_id,
            date: dateStr,
            completed: true,
            value_achieved: 0 // For quit quests, value_achieved is 0 when completed
          };

          const { error: insertError } = await this.supabaseService.getClient()
            .from('quest_progress')
            .insert(newProgress);

          if (insertError) {
            console.error(`QuestService: Error creating progress for quest ${quest.id} on ${dateStr}:`, insertError);
          }
        }

        // Note: We don't update the streak here because it will be calculated by streakCalculator.calculateStreaks

      } catch (error) {
        console.error('QuestService: Error in checkMissedDays:', error);
      }
    });
  }

  /**
   * Create progress entries for all quit quests for today
   * This is a special function to handle quit quests automatically
   * Only creates progress for quests that should be shown today based on task_days_of_week and task_days_of_month
   */
  async createQuitQuestProgressForToday(): Promise<boolean> {
    return runInInjectionContext(this.injector, async () => {
      try {
        // Get today's date
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];

        // Get day of week and day of month for filtering
        const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
        // Django uses Monday=0, Sunday=6 format, so we need to convert
        const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Django format
        const dayOfMonth = today.getDate(); // 1-31

        // Get all active quit quests
        const { data: quitQuests, error: questsError } = await this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('quest_type', 'quit')
          .eq('active', true);

        if (questsError) {
          console.error('QuestService: Error getting quit quests:', questsError);
          return false;
        }

        if (!quitQuests || quitQuests.length === 0) {
          return true;
        }

        // Process each quit quest
        for (const quest of quitQuests) {
          // Check if the quest was created today or before
          const createdDate = new Date(quest.created_at);
          createdDate.setHours(0, 0, 0, 0);
          const createdDateStr = createdDate.toISOString().split('T')[0];

          // Skip this quest if it was created after today
          if (createdDate > today) {
            continue;
          }

          // Check if this quest should be shown today based on task_days_of_week and task_days_of_month
          let shouldShowToday = true;

          // Check for weekly quests with specific days
          if (quest.goal_period === 'week' && quest.task_days_of_week) {
            shouldShowToday = false;

            // Parse task_days_of_week
            let taskDays: string[] = [];
            if (typeof quest.task_days_of_week === 'string') {
              taskDays = quest.task_days_of_week.split(',').map(day => day.trim());
            } else if (Array.isArray(quest.task_days_of_week)) {
              taskDays = quest.task_days_of_week;
            }

            // Get day names in different formats for comparison
            const dayNameShort = this.getDayNameShort(djangoDayOfWeek);
            const dayNameFull = this.getDayNameFull(djangoDayOfWeek);

            // Check if current day is in task days
            shouldShowToday = taskDays.includes(djangoDayOfWeek) ||
                          taskDays.includes(djangoDayOfWeek.toString()) ||
                          taskDays.includes(dayNameShort) ||
                          taskDays.includes(dayNameFull);
          }

          // Check for monthly quests with specific days
          if (quest.goal_period === 'month' && quest.task_days_of_month) {
            shouldShowToday = false;

            // Parse task_days_of_month
            let taskDays: any[] = [];
            if (typeof quest.task_days_of_month === 'string') {
              taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));
            } else if (Array.isArray(quest.task_days_of_month)) {
              taskDays = quest.task_days_of_month;
            }

            // Check if current day is in task days
            shouldShowToday = taskDays.includes(dayOfMonth) ||
                         taskDays.includes(dayOfMonth.toString());
          }

          // Skip this quest if it shouldn't be shown today
          if (!shouldShowToday) {
            continue;
          }

          // Check if progress already exists for today
          const { data: existingProgress, error: progressError } = await this.supabaseService.getClient()
            .from('quest_progress')
            .select('*')
            .eq('quest_id', quest.id)
            .eq('user_id', quest.user_id)
            .eq('date', todayStr);

          if (progressError) {
            console.error(`QuestService: Error checking progress for quest ${quest.id}:`, progressError);
            continue;
          }

          if (existingProgress && existingProgress.length > 0) {
            // Update existing progress to completed=true
            const { error: updateError } = await this.supabaseService.getClient()
              .from('quest_progress')
              .update({
                completed: true,
                value_achieved: 0
              })
              .eq('id', existingProgress[0].id);

            if (updateError) {
              console.error(`QuestService: Error updating progress for quest ${quest.id}:`, updateError);
            }
          } else {
            // Create new progress entry with completed=true
            const newProgress = {
              quest_id: quest.id,
              user_id: quest.user_id,
              date: todayStr,
              completed: true,
              value_achieved: 0
            };

            const { error: insertError } = await this.supabaseService.getClient()
              .from('quest_progress')
              .insert(newProgress);

            if (insertError) {
              console.error(`QuestService: Error creating progress for quest ${quest.id}:`, insertError);
            }
          }


        }

        return true;
      } catch (error) {
        console.error('QuestService: Error in createQuitQuestProgressForToday:', error);
        return false;
      }
    });
  }

  // Helper methods for day name conversion
  private getDayNameShort(djangoDayOfWeek: number): string {
    const dayNames = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];
    return dayNames[djangoDayOfWeek];
  }

  private getDayNameFull(djangoDayOfWeek: number): string {
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return dayNames[djangoDayOfWeek];
  }

  /**
   * Recalculate streaks for all quests for a user
   * This matches the Django implementation in today_view
   */
  async recalculateAllStreaks(userId: string, selectedDate: Date): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {
        console.log('QuestService: Recalculating streaks for all quests for user:', userId);

        // Get all quests for the user
        const { data: quests, error: questsError } = await this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('user_id', userId);

        if (questsError) {
          // PGRST116 is "not found" and 406 is "Not Acceptable"
          if (questsError.code !== 'PGRST116' && questsError.code !== '406') {
            console.error('QuestService: Error getting quests for streak recalculation:', questsError);
            return;
          } else {
            console.warn(`QuestService: ${questsError.code} error when getting quests for streak recalculation. This is expected if no quests exist.`);
            return;
          }
        }

        if (!quests || quests.length === 0) {
          console.log('QuestService: No quests found for user:', userId);
          return;
        }

        console.log(`QuestService: Found ${quests.length} quests for streak recalculation`);

        // Get today's date for comparison
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const selectedDateObj = new Date(selectedDate);
        selectedDateObj.setHours(0, 0, 0, 0);
        const isToday = selectedDateObj.getTime() === today.getTime();
        const dateString = selectedDate.toISOString().split('T')[0];

        if (isToday) {
          // If we're viewing today, update the actual streaks
          console.log('QuestService: Recalculating streaks for today');

          // First check for missed days for each quest
          // This matches the Django implementation where check_missed_days is called before updating streaks
          for (const quest of quests) {
            await this.checkMissedDays(quest.id);
          }

          // Then update streaks for each quest
          for (const quest of quests) {
            // TODO: Implement updateStreak method or use a different approach
            console.log(`QuestService: Need to update streak for quest ${quest.id}`);
          }
        } else {
          // For past dates, calculate historical streaks for display purposes
          console.log(`QuestService: Calculating historical streaks for ${dateString}`);

          for (const quest of quests) {
            // Get the progress for the selected date
            const { data: progressData, error: progressError } = await this.supabaseService.getClient()
              .from('quest_progress')
              .select('*')
              .eq('quest_id', quest.id)
              .eq('user_id', userId)
              .eq('date', dateString)
              .maybeSingle();

            if (progressError) {
              if (progressError.code !== 'PGRST116' && progressError.code !== '406') {
                console.error(`QuestService: Error getting progress for ${dateString}:`, progressError);
                continue;
              }
            }

            // If there's progress for this date, update the historical_streak field
            if (progressData) {
              // Calculate historical streak
              let historicalStreak = 0;

              if (progressData.completed) {
                historicalStreak = 1;

                // Count consecutive completed days before the selected date
                let currentDate = new Date(selectedDate);
                currentDate.setDate(currentDate.getDate() - 1);

                for (let i = 0; i < 100; i++) { // Limit to 100 days to prevent infinite loop
                  const currentDateString = currentDate.toISOString().split('T')[0];

                  // Check if this date should be counted based on goal_period
                  let shouldCount = true;
                  if (quest.goal_period === 'week' && quest.task_days_of_week) {
                    const dayName = currentDate.toLocaleDateString('en-US', { weekday: 'short' }).substring(0, 3);
                    const taskDays = quest.task_days_of_week.split(',').map((day: string) => day.trim());
                    shouldCount = taskDays.includes(dayName);
                  } else if (quest.goal_period === 'month' && quest.task_days_of_month) {
                    const dayNumber = currentDate.getDate();
                    const taskDays = quest.task_days_of_month.split(',').map((day: string) => parseInt(day.trim()));
                    shouldCount = taskDays.includes(dayNumber);
                  }

                  if (shouldCount) {
                    const { data: pastProgress, error: pastProgressError } = await this.supabaseService.getClient()
                      .from('quest_progress')
                      .select('*')
                      .eq('quest_id', quest.id)
                      .eq('user_id', userId)
                      .eq('date', currentDateString)
                      .eq('completed', true)
                      .maybeSingle();

                    if (pastProgressError && pastProgressError.code !== 'PGRST116' && pastProgressError.code !== '406') {
                      console.error(`QuestService: Error getting past progress for ${currentDateString}:`, pastProgressError);
                      break;
                    }

                    if (!pastProgress) {
                      break;
                    }

                    historicalStreak++;
                  }

                  // Move to the previous day
                  currentDate.setDate(currentDate.getDate() - 1);
                }
              }

              // Update the historical_streak field in the progress document
              const { error: updateError } = await this.supabaseService.getClient()
                .from('quest_progress')
                .update({ historical_streak: historicalStreak })
                .eq('id', progressData.id);

              if (updateError) {
                console.error(`QuestService: Error updating historical streak for ${dateString}:`, updateError);
              } else {
                console.log(`QuestService: Updated historical streak for quest ${quest.id} on ${dateString} to ${historicalStreak}`);
              }
            }
          }
        }

        console.log('QuestService: Finished recalculating streaks for all quests');
      } catch (error) {
        console.error('QuestService: Error in recalculateAllStreaks:', error);
      }
    });
  }

  /**
   * Update category badges when a user completes a quest in a specific category
   */
  private async updateCategoryBadges(userId: string, category: string): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {
        console.log(`QuestService: Updating category badges for user ${userId} category ${category}`);

        // Get user badges
        const { data: badges, error: badgesError } = await this.supabaseService.getClient()
          .from('user_badges')
          .select('*')
          .eq('user_id', userId);

        if (badgesError) {
          console.error('QuestService: Error getting user badges:', badgesError);
          return;
        }

        if (!badges || badges.length === 0) {
          console.log('QuestService: No badges found for user, creating new badges');

          // Create default badges
          const newBadges = {
            user_id: userId,
            badge_newbie: false,
            badge_warrior: false,
            badge_hardcore: false,
            badge_peak_performer: false,
            badge_indestructible: false,
            badge_professional: false,
            badge_streak_7_days: false,
            badge_streak_30_days: false,
            badge_streak_100_days: false,
            badge_streak_365_days: false,
            badge_sidequest_streak_7_days: false,
            badge_sidequest_streak_30_days: false,
            badge_sidequest_streak_100_days: false,
            badge_sidequest_streak_365_days: false,
            badge_friends_5: false,
            badge_friends_10: false,
            badge_strength_master: false,
            badge_money_master: false,
            badge_health_master: false,
            badge_knowledge_master: false,
            created_at: new Date(),
            updated_at: new Date()
          };

          const { data: newBadgeData, error: createError } = await this.supabaseService.getClient()
            .from('user_badges')
            .insert(newBadges)
            .select()
            .single();

          if (createError) {
            console.error('QuestService: Error creating user badges:', createError);
            return;
          }

          badges.push(newBadgeData);
        }

        const badgeDoc = badges[0];
        const badgeField = `badge_${category}_master`;

        // Only update if the badge is not already set
        if (!badgeDoc[badgeField]) {
          console.log(`QuestService: Setting ${badgeField} to true`);

          const { error: updateError } = await this.supabaseService.getClient()
            .from('user_badges')
            .update({
              [badgeField]: true,
              updated_at: new Date()
            })
            .eq('id', badgeDoc.id);

          if (updateError) {
            console.error('QuestService: Error updating category badge:', updateError);
            return;
          }

          console.log('QuestService: Updated category badge successfully');
        } else {
          console.log(`QuestService: ${badgeField} is already true, no update needed`);
        }
      } catch (error) {
        console.error('QuestService: Error updating category badges:', error);
      }
    });
  }

  /**
   * Update streak badges based on streak value
   */
  private async updateStreakBadges(questId: string, streak: number): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {
        console.log(`QuestService: Updating streak badges for quest ${questId} with streak ${streak}`);

        // Get the quest to get the user ID
        const { data: quest, error: questError } = await this.supabaseService.getClient()
          .from('quests')
          .select('user_id')
          .eq('id', questId)
          .single();

        if (questError || !quest) {
          console.error('QuestService: Error getting quest for badge update:', questError);
          return;
        }

        const userId = quest.user_id;
        if (!userId) {
          console.error('QuestService: Cannot update badges - missing user_id in quest');
          return;
        }

        console.log(`QuestService: Updating streak badges for user ${userId} with streak ${streak}`);

        // Get user badges
        const { data: badges, error: badgesError } = await this.supabaseService.getClient()
          .from('user_badges')
          .select('*')
          .eq('user_id', userId);

        if (badgesError) {
          console.error('QuestService: Error getting user badges:', badgesError);
          return;
        }

        if (!badges || badges.length === 0) {
          console.log('QuestService: No badges found for user, creating new badges');

          // Create default badges
          const newBadges = {
            user_id: userId,
            badge_newbie: false,
            badge_warrior: false,
            badge_hardcore: false,
            badge_peak_performer: false,
            badge_indestructible: false,
            badge_professional: false,
            badge_streak_7_days: false,
            badge_streak_30_days: false,
            badge_streak_100_days: false,
            badge_streak_365_days: false,
            badge_sidequest_streak_7_days: false,
            badge_sidequest_streak_30_days: false,
            badge_sidequest_streak_100_days: false,
            badge_sidequest_streak_365_days: false,
            badge_friends_5: false,
            badge_friends_10: false,
            badge_strength_master: false,
            badge_money_master: false,
            badge_health_master: false,
            badge_knowledge_master: false,
            created_at: new Date(),
            updated_at: new Date()
          };

          const { data: newBadgeData, error: createError } = await this.supabaseService.getClient()
            .from('user_badges')
            .insert(newBadges)
            .select()
            .single();

          if (createError) {
            console.error('QuestService: Error creating user badges:', createError);
            return;
          }

          badges.push(newBadgeData);
        }

        const badgeDoc = badges[0];
        const updates: any = {
          updated_at: new Date()
        };

        // Update badges based on streak
        if (streak >= 7) {
          updates.badge_streak_7_days = true;
        }
        if (streak >= 30) {
          updates.badge_streak_30_days = true;
        }
        if (streak >= 100) {
          updates.badge_streak_100_days = true;
        }
        if (streak >= 365) {
          updates.badge_streak_365_days = true;
        }

        // Only update if there are changes
        if (Object.keys(updates).length > 1) { // More than just updated_at
          console.log('QuestService: Updating badges with:', updates);

          const { error: updateError } = await this.supabaseService.getClient()
            .from('user_badges')
            .update(updates)
            .eq('id', badgeDoc.id);

          if (updateError) {
            console.error('QuestService: Error updating badges:', updateError);
            return;
          }

          console.log('QuestService: Updated streak badges successfully');
        } else {
          console.log('QuestService: No badge updates needed');
        }
      } catch (error) {
        console.error('QuestService: Error updating streak badges:', error);
      }
    });
  }

  private async updateUserXP(
    userId: string,
    category: string,
    isCompleted: boolean,
    wasCompleted: boolean,
    priority: string
  ): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {
        // Only update XP if completion status changed
        if (isCompleted === wasCompleted) {
          console.log('QUEST XP UPDATE: No change in completion status, skipping XP update');
          return;
        }

        console.log('QUEST XP UPDATE: Starting XP update for user', userId);

        // Get the field name for the category XP
        const xpField = `${category}_xp`;

        // Calculate XP value based on priority
        // In Django: basic = 1 XP, high = 2 XP
        const xpValue = priority === 'high' ? 2 : 1;
        console.log('QUEST XP UPDATE: XP value based on priority:', xpValue);

        // Get current user to get current XP value
        const { data: userData, error: userError } = await this.supabaseService.getClient()
          .from('profiles')
          .select(`${xpField}`)
          .eq('id', userId)
          .single();

        if (userError) {
          console.error('QUEST XP UPDATE: Error getting user:', userError);
          return;
        }

        // Get current XP value
        const currentXP = userData[xpField] || 0;
        console.log('QUEST XP UPDATE: Current XP for', xpField, ':', currentXP);
        console.log("xxxxxxxxxxxxxxxxxxxxxxxxxx", currentXP, xpValue);
        // Calculate new XP value
        let newXP = currentXP;

        if (isCompleted && !wasCompleted) {
          // Add XP when completing a quest
          newXP = currentXP + xpValue;
          console.log('QUEST XP UPDATE: Adding', xpValue, 'XP. New value:', newXP);
        } else if (!isCompleted && wasCompleted) {
          // Remove XP when uncompleting a quest
          newXP = Math.max(0, currentXP - xpValue);
          console.log('QUEST XP UPDATE: Removing', xpValue, 'XP. New value:', newXP);
        }

        // Update the user's XP
        const { error: updateError } = await this.supabaseService.getClient()
          .from('profiles')
          .update({ [xpField]: newXP })
          .eq('id', userId);

        if (updateError) {
          console.error('QUEST XP UPDATE: Error updating XP:', updateError);
          return;
        }

        console.log('QUEST XP UPDATE: Successfully updated', xpField, 'to', newXP);

        // Check if user can level up
        await this.userService.checkAndLevelUp(userId);

        // Refresh the current user profile to update UI
        await this.userService.refreshCurrentUserProfile();
      } catch (error) {
        console.error('QUEST XP UPDATE: Error in updateUserXP:', error);
      }
    });
  }
}
