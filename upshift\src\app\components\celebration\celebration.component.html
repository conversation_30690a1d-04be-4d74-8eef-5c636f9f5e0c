<div class="celebration-container">
  <!-- Animation container -->
  <div class="achievement-container">
    <!-- Glowing orb -->
    <div class="glowing-orb"></div>

    <!-- Particle system -->
    <div class="particle-system">
      <div class="particle" *ngFor="let particle of particles" [ngStyle]="particle.style"></div>
    </div>

    <!-- Hexagon grid -->
    <div class="hexagon-grid">
      <div class="hexagon" *ngFor="let hex of hexagons" [ngStyle]="hex.style"></div>
    </div>

    <!-- Streak counter with emoji -->
    <div class="streak-counter" *ngIf="user?.celebration_emoji">
      {{ user?.celebration_emoji }}
    </div>
    <div class="streak-counter" *ngIf="!user?.celebration_emoji">
      🎉
    </div>

    <!-- Level up text -->
    <div class="level-up">KEEP PUSHING</div>
  </div>

  <!-- Popup content -->
  <div class="celebration-popup">
    <div class="celebration-title">
      {{ user?.celebration_name || 'Another Day, Another W' }}
    </div>

    <div class="celebration-message">
      {{ user?.celebration_description || 'You\'ve completed all your quests for today. Keep up the great work!' }}
    </div>

    <button class="celebration-button" (click)="closeModal()">
      Awesome!
    </button>
  </div>
</div>
