import { Injectable, inject } from '@angular/core';
import { Observable, from, of, throwError } from 'rxjs';
import { map, catchError, switchMap, take } from 'rxjs/operators';
import { SupabaseService } from './supabase.service';
import { DailySideQuestPool, UserDailySideQuest } from '../models/supabase.models';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class SideQuestSupabaseService {
  private supabaseService = inject(SupabaseService);
  private http = inject(HttpClient);

  /**
   * Get all side quests for a user
   * In Django, we don't filter by date_assigned, we just get all side quests for the user
   */
  getUserDailySideQuests(userId: string): Observable<UserDailySideQuest[]> {
    console.log('SideQuestSupabaseService: Getting side quests for user:', userId);

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*, daily_sidequest_pool(*)')
        .eq('user_id', userId)
        .order('date_assigned', { ascending: false })
        .limit(1)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('SideQuestSupabaseService: Error getting side quests:', response.error);
          return [];
        }

        console.log(`SideQuestSupabaseService: Found ${response.data.length} side quests for user:`, userId);
        return response.data as UserDailySideQuest[];
      }),
      catchError(error => {
        console.error('SideQuestSupabaseService: Error getting side quests:', error);
        return of([]);
      })
    );
  }

  /**
   * Get a specific side quest by ID
   */
  getUserDailySideQuest(sideQuestId: string): Observable<UserDailySideQuest | null> {
    console.log('SideQuestSupabaseService: Getting side quest with ID:', sideQuestId);

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('SideQuestSupabaseService: Error getting side quest:', response.error);
          return null;
        }

        console.log('SideQuestSupabaseService: Found side quest:', response.data);
        return response.data as UserDailySideQuest;
      }),
      catchError(error => {
        console.error('SideQuestSupabaseService: Error getting side quest:', error);
        return of(null);
      })
    );
  }

  /**
   * Get the latest side quest for a user
   */
  getUserLatestDailySideQuest(userId: string): Observable<UserDailySideQuest | null> {
    console.log('SideQuestSupabaseService: Getting latest side quest for user:', userId);

    return this.getUserDailySideQuests(userId).pipe(
      map(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          console.log('SideQuestSupabaseService: No side quests found for user:', userId);
          return null;
        }

        console.log('SideQuestSupabaseService: Found side quests for user:', sideQuests);
        return sideQuests[0];
      }),
      catchError(error => {
        console.error('SideQuestSupabaseService: Error getting latest side quest for user:', error);
        return of(null);
      })
    );
  }

  /**
   * Toggle side quest completion
   * This implementation exactly matches the Django implementation in toggle_daily_side_quest view
   * and the UserDailySideQuest.toggle_completion method
   */
  toggleSideQuestCompletion(sideQuestId: string, userId: string, selectedDate?: Date): Observable<{ completed: boolean; value_achieved: number; streak: number }> {
    console.log('SideQuestSupabaseService: Toggling side quest completion:', sideQuestId, userId);

    // First, check for missed days
    return this.checkMissedDays(sideQuestId).pipe(
      switchMap(() => {
        // Use provided date or default to today
        const today = selectedDate ? new Date(selectedDate) : new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        // Get yesterday's date
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];

        console.log('SideQuestSupabaseService: Today:', todayStr);
        console.log('SideQuestSupabaseService: Yesterday:', yesterdayStr);

        // Get the side quest details
        return from(
          this.supabaseService.getClient()
            .from('user_daily_sidequests')
            .select('*, daily_sidequest_pool(*)')
            .eq('id', sideQuestId)
            .single()
        ).pipe(
          switchMap(response => {
            if (response.error) {
              console.error('SideQuestSupabaseService: Error getting side quest:', response.error);
              return throwError(() => new Error('Side quest not found'));
            }

            const sideQuest = response.data as any;
            if (!sideQuest) {
              console.error('SideQuestSupabaseService: Side quest not found:', sideQuestId);
              return throwError(() => new Error('Side quest not found'));
            }

            // Get the quest details
            const questDetails = sideQuest.daily_sidequest_pool;
            if (!questDetails) {
              console.error('SideQuestSupabaseService: Quest details not found for side quest:', sideQuestId);
              return throwError(() => new Error('Quest details not found'));
            }

            console.log('SideQuestSupabaseService: Current side quest state:', {
              id: sideQuest.id,
              completed: sideQuest.completed,
              streak: sideQuest.streak,
              last_completed_date: sideQuest.last_completed_date,
              date_assigned: sideQuest.date_assigned
            });

            // Store previous completion state
            const wasCompleted = sideQuest.completed;

            // Toggle completion state
            const newCompletedState = !wasCompleted;

            // Initialize updates object
            const updates: Partial<UserDailySideQuest> = {};

            if (newCompletedState) {
              // Completing today's quest
              updates.value_achieved = questDetails.goal_value || 1;

              console.log('SideQuestSupabaseService: Last completed date:', sideQuest.last_completed_date);
              console.log('SideQuestSupabaseService: Yesterday date:', yesterdayStr);
              console.log('SideQuestSupabaseService: Are dates equal?', sideQuest.last_completed_date === yesterdayStr);

              if (sideQuest.last_completed_date === yesterdayStr) {
                // Yesterday was completed, increment streak
                updates.streak = (sideQuest.streak || 0) + 1;
                console.log('SideQuestSupabaseService: Yesterday was completed, incrementing streak to:', updates.streak);
              } else {
                // Start new streak
                updates.streak = 1;
                console.log('SideQuestSupabaseService: Starting new streak');
              }

              updates.last_completed_date = todayStr;
            } else {
              // Uncompleting today's quest
              updates.value_achieved = 0;

              console.log('SideQuestSupabaseService: Last completed date:', sideQuest.last_completed_date);
              console.log('SideQuestSupabaseService: Today date:', todayStr);
              console.log('SideQuestSupabaseService: Are dates equal?', sideQuest.last_completed_date === todayStr);

              if (sideQuest.last_completed_date === todayStr) {
                // If uncompleting today's quest, revert to previous day's state
                if (sideQuest.streak > 1) {
                  updates.streak = sideQuest.streak - 1;
                  updates.last_completed_date = yesterdayStr;
                  console.log('SideQuestSupabaseService: Uncompleting today, reverting to streak:', updates.streak);
                } else {
                  updates.streak = 0;
                  updates.last_completed_date = yesterdayStr;  // Set to yesterday instead of null
                  console.log('SideQuestSupabaseService: Uncompleting today, resetting streak to 0 and setting last_completed_date to yesterday');
                }
              } else {
                // If last_completed_date is not today, keep the current streak
                updates.streak = sideQuest.streak;
                console.log('SideQuestSupabaseService: Last completed date is not today, keeping streak at:', updates.streak);
              }
            }

            // Set the completed state
            updates.completed = newCompletedState;

            console.log('SideQuestSupabaseService: Updating side quest with:', updates);

            return from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .update(updates)
                .eq('id', sideQuestId)
                .select()
                .single()
            ).pipe(
              switchMap(response => {
                if (response.error) {
                  console.error('SideQuestSupabaseService: Error toggling side quest completion:', response.error);
                  throw new Error(response.error.message);
                }

                console.log('SideQuestSupabaseService: Side quest completion toggled successfully:', response.data);

                // Update user XP if completion status changed
                if (wasCompleted !== newCompletedState) {
                  const xpAmount = 2; // Side quests give 2 XP in Django
                  const category = sideQuest.category || questDetails.category;

                  if (newCompletedState) {
                    // Add XP when completing
                    return this.updateUserXP(userId, category, true, xpAmount).pipe(
                      map(() => {
                        // Return the updated values to update the UI
                        return {
                          completed: response.data.completed,
                          value_achieved: response.data.value_achieved,
                          streak: response.data.streak
                        };
                      })
                    );
                  } else {
                    // Subtract XP when uncompleting
                    return this.updateUserXP(userId, category, false, xpAmount).pipe(
                      map(() => {
                        // Return the updated values to update the UI
                        return {
                          completed: response.data.completed,
                          value_achieved: response.data.value_achieved,
                          streak: response.data.streak
                        };
                      })
                    );
                  }
                }

                // If no XP update needed, just return the updated values
                return of({
                  completed: response.data.completed,
                  value_achieved: response.data.value_achieved,
                  streak: response.data.streak
                });
              }),
              catchError(error => {
                console.error('SideQuestSupabaseService: Error toggling side quest completion:', error);
                return throwError(() => error);
              })
            );
          })
        );
      })
    );
  }

  /**
   * Create a daily side quest for a user
   * This matches the Django implementation in UserDailySideQuest.objects.get_or_create
   */
  createUserDailySideQuest(userId: string): Observable<string> {
    console.log('SideQuestSupabaseService: Creating daily side quest for user:', userId);

    // First, check if the user already has a side quest
    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('user_id', userId)
    ).pipe(
      switchMap(existingResponse => {
        if (existingResponse.error) {
          console.error('SideQuestSupabaseService: Error checking existing side quests:', existingResponse.error);
          return throwError(() => new Error(existingResponse.error.message));
        }

        // If user already has a side quest, return it
        if (existingResponse.data && existingResponse.data.length > 0) {
          console.log('SideQuestSupabaseService: User already has a side quest, returning existing one:', existingResponse.data[0].id);
          return of(existingResponse.data[0].id);
        }

        console.log('SideQuestSupabaseService: No existing side quest found, creating a new one');

        // Get a random active quest from the pool
        return from(
          this.supabaseService.getClient()
            .from('daily_sidequest_pool')
            .select('*')
            .eq('active', true)
        ).pipe(
          switchMap(response => {
            if (response.error) {
              console.error('SideQuestSupabaseService: Error getting side quest pool:', response.error);
              return throwError(() => new Error(response.error.message));
            }

            if (!response.data || response.data.length === 0) {
              console.error('SideQuestSupabaseService: No active side quests available');
              return throwError(() => new Error('No active side quests available'));
            }

            console.log('SideQuestSupabaseService: Found side quest pool:', response.data);

            // Select a random side quest from the pool
            const randomIndex = Math.floor(Math.random() * response.data.length);
            const selectedQuest = response.data[randomIndex] as DailySideQuestPool;

            console.log('SideQuestSupabaseService: Selected random side quest:', selectedQuest);

            // Create a new user daily side quest
            const today = new Date();
            const dateString = today.toISOString().split('T')[0];

            const newSideQuest: Omit<UserDailySideQuest, 'id'> = {
              user_id: userId,
              current_quest_id: selectedQuest.id,
              streak: 0,
              date_assigned: dateString,
              completed: false,
              value_achieved: 0,
              category: selectedQuest.category || 'health',
              emoji: selectedQuest.emoji || '🎯',
              last_completed_date: null
            };

            return from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .insert(newSideQuest)
                .select()
                .single()
            ).pipe(
              map(insertResponse => {
                if (insertResponse.error) {
                  console.error('SideQuestSupabaseService: Error creating user daily side quest:', insertResponse.error);
                  throw new Error(insertResponse.error.message);
                }

                console.log('SideQuestSupabaseService: Created user daily side quest with ID:', insertResponse.data.id);
                return insertResponse.data.id;
              }),
              catchError(error => {
                console.error('SideQuestSupabaseService: Error creating user daily side quest:', error);
                return throwError(() => error);
              })
            );
          })
        );
      })
    );
  }

  /**
   * Assign a new quest to an existing side quest
   * This implementation exactly matches the Django implementation in UserDailySideQuest.assign_new_quest method
   */
  assignNewQuest(sideQuestId: string): Observable<void> {
    console.log('SideQuestSupabaseService: Assigning new quest for side quest:', sideQuestId);

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error) {
          console.error('SideQuestSupabaseService: Error getting side quest:', response.error);
          return throwError(() => new Error(response.error.message));
        }

        const sideQuest = response.data as UserDailySideQuest;
        if (!sideQuest) {
          console.error('SideQuestSupabaseService: Side quest not found:', sideQuestId);
          return throwError(() => new Error('Side quest not found'));
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        // Get date_assigned in YYYY-MM-DD format
        // Make sure we're comparing dates in the same format
        const dateAssignedStr = sideQuest.date_assigned;

        console.log('SideQuestSupabaseService: Today date:', todayStr);
        console.log('SideQuestSupabaseService: Date assigned:', dateAssignedStr);
        console.log('SideQuestSupabaseService: Are dates equal?', dateAssignedStr === todayStr);
        console.log('SideQuestSupabaseService: Date assigned type:', typeof dateAssignedStr);
        console.log('SideQuestSupabaseService: Today type:', typeof todayStr);

        // Only assign a new quest if it's a new day
        if (dateAssignedStr === todayStr) {
          console.log('SideQuestSupabaseService: Side quest is already assigned for today');
          return of(undefined);
        }

        // Check for missed days first
        return this.checkMissedDays(sideQuestId).pipe(
          switchMap(() => {
            // Get a random active quest from the pool that's different from the current one
            return from(
              this.supabaseService.getClient()
                .from('daily_sidequest_pool')
                .select('*')
                .eq('active', true)
                .neq('id', sideQuest.current_quest_id)
            ).pipe(
              switchMap(poolResponse => {
                if (poolResponse.error) {
                  console.error('SideQuestSupabaseService: Error getting side quest pool:', poolResponse.error);
                  return throwError(() => new Error(poolResponse.error.message));
                }

                if (!poolResponse.data || poolResponse.data.length === 0) {
                  console.error('SideQuestSupabaseService: No active side quests available');
                  return throwError(() => new Error('No active side quests available'));
                }

                // Select a random side quest from the pool
                const randomIndex = Math.floor(Math.random() * poolResponse.data.length);
                const selectedQuest = poolResponse.data[randomIndex] as DailySideQuestPool;

                console.log('SideQuestSupabaseService: Selected new random side quest:', selectedQuest);

                // Update the existing side quest with the new quest
                return from(
                  this.supabaseService.getClient()
                    .from('user_daily_sidequests')
                    .update({
                      current_quest_id: selectedQuest.id,
                      date_assigned: todayStr,
                      completed: false,
                      value_achieved: 0,
                      category: selectedQuest.category || 'health',
                      emoji: selectedQuest.emoji || '🎯'
                    })
                    .eq('id', sideQuestId)
                ).pipe(
                  map(updateResponse => {
                    if (updateResponse.error) {
                      console.error('SideQuestSupabaseService: Error updating side quest:', updateResponse.error);
                      throw new Error(updateResponse.error.message);
                    }

                    console.log('SideQuestSupabaseService: Updated side quest with new quest');
                    return undefined;
                  })
                );
              })
            );
          })
        );
      })
    );
  }

  /**
   * Check if it's time to assign a new quest and reset if needed
   * This implementation exactly matches the Django implementation in UserDailySideQuest.check_and_reset method
   */
  checkAndReset(sideQuestId: string): Observable<void> {
    console.log('SideQuestSupabaseService: Checking if side quest needs to be reset:', sideQuestId);

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error) {
          console.error('SideQuestSupabaseService: Error getting side quest:', response.error);
          return throwError(() => new Error(response.error.message));
        }

        const sideQuest = response.data as UserDailySideQuest;
        if (!sideQuest) {
          console.error('SideQuestSupabaseService: Side quest not found:', sideQuestId);
          return throwError(() => new Error('Side quest not found'));
        }

        // Get today's date in YYYY-MM-DD format
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        // Get date_assigned in YYYY-MM-DD format
        // Make sure we're comparing dates in the same format
        const dateAssignedStr = sideQuest.date_assigned;

        console.log('SideQuestSupabaseService: Today date:', todayStr);
        console.log('SideQuestSupabaseService: Date assigned:', dateAssignedStr);
        console.log('SideQuestSupabaseService: Are dates equal?', dateAssignedStr === todayStr);
        console.log('SideQuestSupabaseService: Date assigned type:', typeof dateAssignedStr);
        console.log('SideQuestSupabaseService: Today type:', typeof todayStr);

        // Compare dates as strings in YYYY-MM-DD format
        if (dateAssignedStr !== todayStr) {
          console.log('SideQuestSupabaseService: Side quest needs to be reset - date assigned is not today');
          return this.assignNewQuest(sideQuestId);
        } else {
          console.log('SideQuestSupabaseService: Side quest does not need to be reset - already assigned for today');
          return of(undefined);
        }
      })
    );
  }

  /**
   * Ensure user has daily side quests for today
   * This matches the Django implementation where we check if the user has any side quests
   * and create one if they don't
   * It also checks for missed days and resets the streak if needed
   */
  ensureUserHasDailySideQuests(userId: string): Observable<UserDailySideQuest[]> {
    console.log('SideQuestSupabaseService: Ensuring user has daily side quests:', userId);

    const today = new Date();
    const dateString = today.toISOString().split('T')[0];

    // First, check if the user already has a side quest
    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*, daily_sidequest_pool(*)')
        .eq('user_id', userId)
        .order('date_assigned', { ascending: false })
        .limit(1)
    ).pipe(
      switchMap(response => {
        if (response.error) {
          console.error('SideQuestSupabaseService: Error getting side quests:', response.error);
          return of([]);
        }

        if (response.data && response.data.length > 0) {
          console.log('SideQuestSupabaseService: User already has side quests:', response.data);

          // Check for missed days first, just like in Django
          const sideQuest = response.data[0];

          console.log('SideQuestSupabaseService: Checking missed days for side quest:', sideQuest.id);
          return this.checkMissedDays(sideQuest.id).pipe(
            switchMap(() => {
              // Check if we need to assign a new quest
              // In Django, this is done in check_and_reset
              console.log('SideQuestSupabaseService: Checking if side quest needs to be reset');
              return this.checkAndReset(sideQuest.id).pipe(
                switchMap(() => {
                  // Get the updated side quests
                  return from(
                    this.supabaseService.getClient()
                      .from('user_daily_sidequests')
                      .select('*, daily_sidequest_pool(*)')
                      .eq('user_id', userId)
                      .order('date_assigned', { ascending: false })
                      .limit(1)
                  ).pipe(
                    map(updatedResponse => {
                      if (updatedResponse.error) {
                        console.error('SideQuestSupabaseService: Error getting updated side quests:', updatedResponse.error);
                        return [];
                      }
                      return updatedResponse.data as UserDailySideQuest[];
                    })
                  );
                })
              );
            })
          );
        }

        console.log('SideQuestSupabaseService: No side quests found, creating new side quest for user');

        // Create 1 side quest for the user
        return this.createUserDailySideQuest(userId).pipe(
          switchMap(newSideQuestId => {
            console.log('SideQuestSupabaseService: Created side quest with ID:', newSideQuestId);
            return from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .select('*, daily_sidequest_pool(*)')
                .eq('user_id', userId)
                .order('date_assigned', { ascending: false })
                .limit(1)
            ).pipe(
              map(newResponse => {
                if (newResponse.error) {
                  console.error('SideQuestSupabaseService: Error getting new side quests:', newResponse.error);
                  return [];
                }
                return newResponse.data as UserDailySideQuest[];
              })
            );
          }),
          catchError(error => {
            console.error('SideQuestSupabaseService: Error creating side quests:', error);
            return of([]);
          })
        );
      })
    );
  }

  /**
   * Recalculate streak for the daily side quest
   * This matches the Django implementation in today_view
   */
  recalculateSideQuestStreak(userId: string, selectedDate?: Date): Observable<void> {
    console.log('SideQuestSupabaseService: Recalculating side quest streak for user:', userId);

    // Get the latest side quest for the user
    return this.getUserLatestDailySideQuest(userId).pipe(
      switchMap(sideQuest => {
        if (!sideQuest) {
          console.log('SideQuestSupabaseService: No side quest found for user:', userId);
          return of(undefined);
        }

        console.log('SideQuestSupabaseService: Found side quest for recalculation:', sideQuest);

        // Use provided date or default to today
        const toggleDate = selectedDate ? new Date(selectedDate) : new Date();
        toggleDate.setHours(0, 0, 0, 0);

        // Get yesterday's date relative to toggle date
        const yesterday = new Date(toggleDate);
        yesterday.setDate(toggleDate.getDate() - 1);

        // Calculate streak based on Django implementation
        let displayStreak = 0;

        // First, count consecutive completed days from yesterday backwards
        let streakCount = 0;

        // For side quests, we use a simpler streak calculation
        // We just check if yesterday was completed
        if (sideQuest.last_completed_date) {
          const lastCompletedDateStr = sideQuest.last_completed_date;
          const yesterdayStr = yesterday.toISOString().split('T')[0];

          console.log('SideQuestSupabaseService: Last completed date:', lastCompletedDateStr);
          console.log('SideQuestSupabaseService: Yesterday date:', yesterdayStr);

          if (lastCompletedDateStr === yesterdayStr) {
            // Yesterday was completed, streak is at least 1
            streakCount = 1;
            console.log('SideQuestSupabaseService: Yesterday was completed, base streak is:', streakCount);
          } else {
            console.log('SideQuestSupabaseService: Yesterday was not completed, starting new streak');
          }
        }

        // Calculate final streak based on today's completion
        if (sideQuest.completed) {
          // If completing today, add 1 to streak
          displayStreak = streakCount + 1;
          console.log('SideQuestSupabaseService: Today is completed, display streak:', displayStreak);
        } else {
          // If uncompleting today, streak is just the base streak
          displayStreak = streakCount;
          console.log('SideQuestSupabaseService: Today is not completed, display streak:', displayStreak);
        }

        // Update the streak in the database
        return from(
          this.supabaseService.getClient()
            .from('user_daily_sidequests')
            .update({ streak: displayStreak })
            .eq('id', sideQuest.id)
        ).pipe(
          map(response => {
            if (response.error) {
              console.error('SideQuestSupabaseService: Error updating side quest streak:', response.error);
              throw new Error(response.error.message);
            }

            console.log('SideQuestSupabaseService: Updated side quest streak to:', displayStreak);
            return undefined;
          })
        );
      })
    );
  }

  /**
   * Update user XP based on side quest completion
   * @param userId The user ID
   * @param category The category to update XP for
   * @param add Whether to add (true) or subtract (false) XP
   * @param amount The amount of XP to add or subtract (default: 2, matching Django)
   */
  private updateUserXP(userId: string, category: string, add: boolean = true, amount: number = 2): Observable<void> {
    console.log(`SideQuestSupabaseService: ${add ? 'Adding' : 'Subtracting'} ${amount} XP for category:`, category);

    return from(
      this.supabaseService.getClient()
        .from('profiles')
        .select(`${category}_xp`)
        .eq('id', userId)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error) {
          console.error('SideQuestSupabaseService: Error getting user XP:', response.error);
          return throwError(() => new Error(response.error.message));
        }

        const currentXP = response.data[`${category}_xp`] || 0;
        // In Django, side quests give/take 2 XP by default
        const newXP = add ? currentXP + amount : Math.max(0, currentXP - amount);

        return from(
          this.supabaseService.getClient()
            .from('profiles')
            .update({ [`${category}_xp`]: newXP })
            .eq('id', userId)
        ).pipe(
          map(updateResponse => {
            if (updateResponse.error) {
              console.error('SideQuestSupabaseService: Error updating user XP:', updateResponse.error);
              throw new Error(updateResponse.error.message);
            }

            console.log(`SideQuestSupabaseService: Updated user ${category}_xp to:`, newXP);
          }),
          catchError(error => {
            console.error('SideQuestSupabaseService: Error updating user XP:', error);
            return throwError(() => error);
          })
        );
      })
    );
  }

  /**
   * Check for missed days and update streak accordingly
   * This implementation exactly matches the Django implementation in UserDailySideQuest.check_missed_days method
   */
  checkMissedDays(sideQuestId: string): Observable<void> {
    console.log('SideQuestSupabaseService: Checking for missed days for side quest:', sideQuestId);

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error) {
          console.error('SideQuestSupabaseService: Error getting side quest:', response.error);
          return throwError(() => new Error(response.error.message));
        }

        const sideQuest = response.data as UserDailySideQuest;
        if (!sideQuest) {
          console.error('SideQuestSupabaseService: Side quest not found:', sideQuestId);
          return throwError(() => new Error('Side quest not found'));
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];

        console.log('SideQuestSupabaseService: Today:', todayStr);
        console.log('SideQuestSupabaseService: Yesterday:', yesterdayStr);
        console.log('SideQuestSupabaseService: Last completed date:', sideQuest.last_completed_date);
        console.log('SideQuestSupabaseService: Current streak:', sideQuest.streak);

        // Only reset streak if we actually missed a day
        // Get last_completed_date in YYYY-MM-DD format
        const lastCompletedDateStr = sideQuest.last_completed_date;

        console.log('SideQuestSupabaseService: Last completed date:', lastCompletedDateStr);
        console.log('SideQuestSupabaseService: Yesterday date:', yesterdayStr);
        console.log('SideQuestSupabaseService: Today date:', todayStr);
        console.log('SideQuestSupabaseService: Last completed date type:', typeof lastCompletedDateStr);

        // Check if last completed date is null or before yesterday (and not equal to yesterday)
        const missedDay = !lastCompletedDateStr ||
                         (lastCompletedDateStr < yesterdayStr &&
                          lastCompletedDateStr !== yesterdayStr);

        console.log('SideQuestSupabaseService: Missed day?', missedDay);

        if (missedDay) {
          console.log('SideQuestSupabaseService: Missed day detected, resetting streak');

          return from(
            this.supabaseService.getClient()
              .from('user_daily_sidequests')
              .update({
                streak: 0,
                last_completed_date: yesterdayStr  // Set to yesterday instead of null
              })
              .eq('id', sideQuestId)
          ).pipe(
            map(updateResponse => {
              if (updateResponse.error) {
                console.error('SideQuestSupabaseService: Error resetting streak:', updateResponse.error);
                throw new Error(updateResponse.error.message);
              }

              console.log('SideQuestSupabaseService: Reset streak successfully');

              // In Django, we also delete all previous entries as they're no longer needed
              // But in Supabase, we only have one entry per user, so we don't need to delete anything

              return undefined;
            })
          );
        } else {
          console.log('SideQuestSupabaseService: No missed days detected');
          return of(undefined);
        }
      })
    );
  }

  /**
   * Import side quests from JSON file to Supabase
   */
  importSideQuestsFromJson(): Observable<boolean> {
    console.log('SideQuestSupabaseService: Importing side quests from JSON');

    return this.http.get<any[]>('assets/data/sidequest-pool.json').pipe(
      switchMap(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          console.error('SideQuestSupabaseService: No side quests found in JSON file');
          return of(false);
        }

        console.log(`SideQuestSupabaseService: Importing ${sideQuests.length} side quests from JSON`);

        // Convert to Supabase model
        const sideQuestsToImport = sideQuests.map(quest => ({
          name: quest.name,
          description: quest.description || null,
          goal_value: quest.goal_value || 1,
          category: quest.category,
          goal_unit: quest.goal_unit || 'count',
          active: true,
          emoji: quest.emoji || '🎯'
        }));

        // Insert into Supabase
        return from(
          this.supabaseService.getClient()
            .from('daily_sidequest_pool')
            .insert(sideQuestsToImport)
        ).pipe(
          map(response => {
            if (response.error) {
              console.error('SideQuestSupabaseService: Error importing side quests:', response.error);
              return false;
            }

            console.log('SideQuestSupabaseService: Successfully imported side quests');
            return true;
          }),
          catchError(error => {
            console.error('SideQuestSupabaseService: Error importing side quests:', error);
            return of(false);
          })
        );
      })
    );
  }
}
