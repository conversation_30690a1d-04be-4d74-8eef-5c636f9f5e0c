import { Injectable } from '@angular/core';
import { createClient, SupabaseClient, User, AuthResponse } from '@supabase/supabase-js';
import { BehaviorSubject, Observable, from } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { environment } from '../../environments/environment';
import { AuthGuard } from '../guards/auth.guard';

// Create a singleton instance for direct use throughout the app
export const supabase = createClient(
  environment.supabase.url,
  environment.supabase.key,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    },
    global: {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    }
  }
);

@Injectable({
  providedIn: 'root'
})
export class SupabaseService {
  // Use the singleton instance instead of creating a new one
  public supabase: SupabaseClient = supabase;
  // Add storage and supabaseKey properties for admin service
  public storage: any = { url: environment.supabase.url };
  public supabaseKey: string = environment.supabase.key;
  _currentUser = new BehaviorSubject<User | null>(null); // Made public for direct access
  currentUser$ = this._currentUser.asObservable();

  constructor(private router: Router) {

    // Initialize user on service creation
    this.loadUser();

    // Set up auth state change listener
    this.supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Supabase auth event:', event);
      if (session?.user) {
        this._currentUser.next(session.user);
        // Store user ID in preferences
        await Preferences.set({ key: 'uid', value: session.user.id });

        // If this is a sign-in event, create profile if it doesn't exist
        if (event === 'SIGNED_IN') {
          console.log('User signed in, checking if profile exists');
          this.ensureProfileExists(session.user);

          // Check if we have a stored URL to redirect back to
          if (AuthGuard.lastAttemptedUrl) {
            console.log('Redirecting to stored URL:', AuthGuard.lastAttemptedUrl);
            const url = AuthGuard.lastAttemptedUrl;
            // Use the static method to clear the URL
            AuthGuard.clearLastAttemptedUrl();
            // Use a longer delay to ensure everything is loaded
            setTimeout(() => {
              console.log('Delayed redirect to:', url);
              // Check if the URL is still valid (not a public path)
              if (!AuthGuard.isPublicPath(url)) {
                this.router.navigateByUrl(url);
              } else {
                console.log('URL is a public path, redirecting to /today instead');
                this.router.navigateByUrl('/today');
              }
            }, 1500);
          }
        } else if (event === 'INITIAL_SESSION') {
          console.log('Initial session loaded, user is already signed in');
          // Don't redirect here - let the auth guard handle it
        }
      } else {
        this._currentUser.next(null);
        // Clear user ID from preferences
        await Preferences.remove({ key: 'uid' });

        // If this is a sign-out event, redirect to login
        if (event === 'SIGNED_OUT') {
          console.log('User signed out, redirecting to signup');
          this.router.navigateByUrl('/signup');
        }
      }
    });
  }

  private async loadUser() {
    try {
      console.log('Loading user from Supabase...');
      const { data, error } = await this.supabase.auth.getUser();

      if (error) {
        console.error('Error loading user:', error);
        this._currentUser.next(null);
        return;
      }

      if (data.user) {
        console.log('User loaded successfully:', data.user.id);
        this._currentUser.next(data.user);

        // Store user ID in preferences
        await Preferences.set({ key: 'uid', value: data.user.id });

        // Ensure profile exists
        console.log('User loaded, ensuring profile exists');
        this.ensureProfileExists(data.user);
      } else {
        console.log('No user found in session');
        this._currentUser.next(null);
      }
    } catch (error) {
      console.error('Exception loading user:', error);
      this._currentUser.next(null);
    }
  }

  // Authentication methods
  signUp(email: string, password: string): Observable<AuthResponse> {
    return from(this.supabase.auth.signUp({
      email,
      password
    })).pipe(
      tap(res => {
        if (res.data?.user) {
          console.log('User signed up:', res.data.user);
          this._currentUser.next(res.data.user);
        }
      }),
      catchError(error => {
        console.error('Error signing up:', error);
        throw error;
      })
    );
  }

  signIn(email: string, password: string): Observable<AuthResponse> {
    return from(this.supabase.auth.signInWithPassword({
      email,
      password
    })).pipe(
      tap(res => {
        if (res.data?.user) {
          console.log('User signed in:', res.data.user);
          this._currentUser.next(res.data.user);
        }
      }),
      catchError(error => {
        console.error('Error signing in:', error);
        throw error;
      })
    );
  }

  signOut(): Observable<any> {
    return from(this.supabase.auth.signOut()).pipe(
      tap(() => {
        this._currentUser.next(null);
        // Clear any stored URL when signing out
        AuthGuard.clearLastAttemptedUrl();
        this.router.navigate(['/signup/']);
      }),
      catchError(error => {
        console.error('Error signing out:', error);
        throw error;
      })
    );
  }

  signInWithGoogle(): Observable<any> {
    return from(this.supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: window.location.origin + '/'
      }
    })).pipe(
      catchError(error => {
        console.error('Error signing in with Google:', error);
        throw error;
      })
    );
  }

  signInWithApple(): Observable<any> {
    return from(this.supabase.auth.signInWithOAuth({
      provider: 'apple',
      options: {
        redirectTo: window.location.origin + '/'
      }
    })).pipe(
      catchError(error => {
        console.error('Error signing in with Apple:', error);
        throw error;
      })
    );
  }

  // User profile methods
  getCurrentUserId(): string | null {
    return this._currentUser.value?.id || null;
  }

  // Get current user with profile data
  async getCurrentUser(): Promise<any | null> {
    const userId = this.getCurrentUserId();
    if (!userId) return null;

    try {
      // Get user profile data from profiles table
      const { data, error } = await this.supabase
        .from('profiles')
        .select('*')
        .filter('id', 'eq', userId)
        .single();

      if (error) {
        console.error('Error getting current user profile:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getCurrentUser:', error);
      return null;
    }
  }

  isLoggedIn(): Observable<boolean> {
    return this.currentUser$.pipe(
      map(user => !!user)
    );
  }

  // Database access methods
  getClient(): SupabaseClient {
    return this.supabase;
  }

  // Ensure user profile exists in the database
  async ensureProfileExists(user: User): Promise<void> {
    if (!user || !user.id) {
      console.error('Cannot ensure profile exists - no user or user ID provided');
      return;
    }

    try {
      console.log('Checking if profile exists for user:', user.id);

      // Check if profile already exists by counting records
      const { count, error: countError } = await this.supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .eq('id', user.id);

      if (countError) {
        console.error('Error checking if profile exists:', countError);
        return;
      }

      if (count && count > 0) {
        console.log('Profile already exists for user:', user.id);
        return;
      }

      console.log('Profile does not exist, creating new profile for user:', user.id);

      // Create new profile
      const newProfile = {
        id: user.id,
        email: user.email,
        username: null, // Will be set later by user
        name: user.user_metadata?.['full_name'] || user.user_metadata?.['name'] || 'New User',
        profile_picture: user.user_metadata?.['avatar_url'] || null,
        registration_date: new Date(),
        last_login: new Date(),
        active: true,
        level: 0,
        title: '🥚 Beginner',
        strength_xp: 0,
        money_xp: 0,
        health_xp: 0,
        knowledge_xp: 0,
        plan: 'none',
        auto_renew: true,
        subscription_status: 'email marketing'
      };

      console.log('Creating profile with data:', newProfile);

      const { error: insertError } = await this.supabase
        .from('profiles')
        .insert(newProfile);

      if (insertError) {
        console.error('Error creating profile:', insertError);

        // Try with minimal profile as fallback
        console.log('Trying with minimal profile data');
        const minimalProfile = {
          id: user.id,
          email: user.email,
          name: user.user_metadata?.['full_name'] || user.user_metadata?.['name'] || 'New User',
          registration_date: new Date(),
          last_login: new Date(),
          active: true,
          level: 0
        };

        const { error: minimalError } = await this.supabase
          .from('profiles')
          .insert(minimalProfile);

        if (minimalError) {
          console.error('Error creating minimal profile:', minimalError);
        } else {
          console.log('Successfully created minimal profile for user:', user.id);
        }
      } else {
        console.log('Successfully created profile for user:', user.id);
      }
    } catch (error) {
      console.error('Exception during profile creation:', error);
    }
  }
}
