/* Celebration Animation Styles */
.celebration-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    pointer-events: auto;
    overflow: hidden;
    background-color: rgba(12, 12, 15, 0.85);
    backdrop-filter: blur(8px);
}

.celebration-popup {
    min-width: 300px;
    background: linear-gradient(145deg, rgba(28, 28, 30, 0.9), rgba(18, 18, 23, 0.95));
    border-radius: 24px;
    padding: 40px 30px;
    text-align: center;
    max-width: 340px;
    position: relative;
    z-index: 2500;
    animation: popIn 0.6s cubic-bezier(0.22, 1, 0.36, 1) forwards;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(77, 123, 255, 0.1), inset 0 0 0 1px rgba(255, 255, 255, 0.05);
    pointer-events: auto;
    overflow: hidden;
    transform: translateZ(0);
}

.celebration-popup::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(transparent, rgba(77, 123, 255, 0.1), transparent 30%);
    animation: rotate 7s linear infinite;
    z-index: -1;
    pointer-events: none;
}

.celebration-popup::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(transparent, rgba(77, 123, 255, 0.05), transparent 20%);
    animation: rotate 10s linear infinite reverse;
    z-index: -1;
    pointer-events: none;
}

.celebration-title {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 16px;
    color: #fff;
    letter-spacing: -0.5px;
    position: relative;
    display: inline-block;
}

.celebration-title::after {
    content: '🏆';
    position: absolute;
    top: -15px;
    right: -25px;
    font-size: 24px;
    animation: trophy-bounce 1.5s ease-in-out infinite;
}

.celebration-message {
    font-size: 16px;
    color: #8e8e93;
    margin-bottom: 32px;
    line-height: 1.5;
}

.celebration-button {
    background: linear-gradient(to right, #4d7bff, #3a68e0);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(77, 123, 255, 0.3);
}

.celebration-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.celebration-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(77, 123, 255, 0.4);
}

.celebration-button:hover::before {
    left: 100%;
    transition: 0.5s;
}

/* Achievement Unlocked Animation */
.achievement-container {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1999;
    display: flex;
    justify-content: center;
    align-items: center;
    perspective: 1200px;
    transform-style: preserve-3d;
}

/* Glowing Orb */
.glowing-orb {
    position: absolute;
    width: 140px;
    height: 140px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(77, 123, 255, 0.95) 0%, rgba(58, 104, 224, 0.8) 50%, rgba(30, 60, 150, 0) 80%);
    box-shadow: 0 0 80px 40px rgba(77, 123, 255, 0.5);
    opacity: 0;
    z-index: 1998;
    animation: orb-pulse 3s ease-out forwards, orb-float 6s ease-in-out infinite;
    filter: blur(5px);
}

.glowing-orb::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(77, 123, 255, 0.4) 0%, rgba(58, 104, 224, 0.2) 50%, rgba(30, 60, 150, 0) 80%);
    animation: orb-halo 4s linear infinite;
    z-index: -1;
}

.glowing-orb::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 20%);
    animation: orb-highlight 8s linear infinite;
}

/* Particle System */
.particle-system {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1997;
}

/* Particle */
.particle {
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: #4d7bff;
    border-radius: 50%;
    opacity: 0;
    z-index: 1997;
    filter: blur(1px);
    box-shadow: 0 0 8px 3px rgba(77, 123, 255, 0.7);
}

/* Hexagon Grid */
.hexagon-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 1; /* Changed from 0 to make it immediately visible */
    z-index: 1996;
    animation: grid-rotate 20s linear infinite;
    transform-origin: center center;
    pointer-events: none;
}

.hexagon {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: transparent;
    border: 1px solid rgba(77, 123, 255, 0.2);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    opacity: 0;
    transform: scale(0);
    transition: border-color 0.5s ease;
}

.hexagon:nth-child(3n) {
    animation: hexagon-appear 0.5s ease-out forwards, hex-pulse 3s ease-in-out infinite;
}

.hexagon:nth-child(3n+1) {
    animation: hexagon-appear 0.5s ease-out forwards, hex-pulse 4s ease-in-out 1s infinite;
}

.hexagon:nth-child(3n+2) {
    animation: hexagon-appear 0.5s ease-out forwards, hex-pulse 5s ease-in-out 2s infinite;
}

/* Streak Counter */
.streak-counter {
    position: absolute;
    top: 20%;
    font-size: 120px;
    font-weight: 800;
    color: transparent;
    -webkit-text-stroke: 2px rgba(77, 123, 255, 0.3);
    opacity: 0;
    transform: translateY(-50px);
    animation: streak-appear 1.5s ease-out 0.8s forwards;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    letter-spacing: -5px;
}

/* Level Up Text */
.level-up {
    position: absolute;
    top: 40%;
    font-size: 24px;
    font-weight: 700;
    color: #4d7bff;
    opacity: 0;
    transform: translateY(20px);
    animation: level-up-appear 1s ease-out 1.2s forwards;
    text-transform: uppercase;
    letter-spacing: 2px;
}

@keyframes orb-pulse {
    0% {
        transform: scale(0.2);
        opacity: 0;
    }
    20% {
        transform: scale(1.2);
        opacity: 0.9;
    }
    40% {
        transform: scale(0.9);
        opacity: 0.7;
    }
    60% {
        transform: scale(1.1);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
}

@keyframes particle-move {
    0% {
        transform: translate(0, 0) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    80% {
        opacity: 0.8;
    }
    100% {
        transform: translate(var(--tx), var(--ty)) scale(0);
        opacity: 0;
    }
}

/* Removed grid-fade animation since we're setting opacity to 1 directly */

@keyframes grid-rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes hex-pulse {
    0%, 100% {
        border-color: rgba(77, 123, 255, 0.2);
    }
    50% {
        border-color: rgba(77, 123, 255, 0.6);
    }
}

@keyframes hexagon-appear {
    0% {
        opacity: 0;
        transform: scale(0) rotate(30deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}



@keyframes orb-float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
}

@keyframes orb-halo {
    0% {
        transform: scale(1) rotate(0deg);
    }
    50% {
        transform: scale(1.2) rotate(180deg);
    }
    100% {
        transform: scale(1) rotate(360deg);
    }
}

@keyframes orb-highlight {
    0% {
        opacity: 0.7;
        transform: rotate(0deg);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.7;
        transform: rotate(360deg);
    }
}

@keyframes hexagon-appear {
    0% {
        opacity: 0;
        transform: scale(0) rotate(30deg);
    }
    70% {
        opacity: 0.8;
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

@keyframes streak-appear {
    0% {
        opacity: 0;
        transform: translateY(-50px) scale(0.8);
    }
    60% {
        opacity: 0.8;
        transform: translateY(10px) scale(1.1);
    }
    100% {
        opacity: 0.3;
        transform: translateY(0) scale(1);
    }
}

@keyframes level-up-appear {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes check-appear {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }
    70% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes draw-check-1 {
    0% {
        width: 0;
        opacity: 0;
    }
    100% {
        width: 35%;
        opacity: 1;
    }
}

@keyframes draw-check-2 {
    0% {
        width: 0;
        opacity: 0;
    }
    100% {
        width: 70%;
        opacity: 1;
    }
}

@keyframes popIn {
    0% {
        transform: scale(0.8) translateY(20px);
        opacity: 0;
    }
    40% {
        transform: scale(1.02) translateY(-5px);
    }
    70% {
        transform: scale(0.98) translateY(2px);
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes trophy-bounce {
    0%, 100% {
        transform: translateY(0) rotate(5deg);
    }
    50% {
        transform: translateY(-10px) rotate(-5deg);
    }
}
